<?php

namespace App\Providers;

use App\Models\Dra\T1Permission;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // 'App\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();
        try {
            $permissions = T1Permission::all();
            foreach ($permissions as $permission) {
                Gate::define($permission->permission_name, function ($user) use ($permission) {
                    return in_array($permission->id, session('permissions'));
                });
                if ($permission->route_name) {
                    Gate::define($permission->route_name, function ($user) use ($permission) {
                        return in_array($permission->id, session('permissions'));
                    });
                }
            }
        } catch (\Exception $e) {

        }
    }
}
