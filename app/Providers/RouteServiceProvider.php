<?php

namespace App\Providers;

use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'App\Http\Controllers';

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @return void
     */
    public function boot()
    {
        //

        parent::boot();
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        $this->mapApiV1Routes();

        $this->mapWebRoutes();

        $this->mapAdminRoutes();

        $this->mapTeacherRoutes();

        //
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapWebRoutes()
    {
        Route::middleware('web')
             ->namespace($this->namespace)
             ->group(base_path('routes/web.php'));
    }

    /**
     * Define the "api" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapApiV1Routes()
    {
        Route::prefix('api/v1')
             ->middleware('api')
             ->name('api.v1.')
             ->namespace($this->namespace)
             ->group(base_path('routes/api_v1.php'));
    }

    protected function mapAdminRoutes()
    {
        Route::prefix('admin')
            ->middleware('authenticate')
            ->namespace($this->namespace)
            ->group(base_path('routes/admin.php'));
    }

    protected function mapTeacherRoutes()
    {
        Route::prefix('teacher')
            ->middleware('authenticate')
            ->namespace($this->namespace)
            ->group(base_path('routes/teacher.php'));
    }
    
    /**
     * api for external server services
     *
     * @return void
     */
    protected function mapApiV2Routes()
    {
        Route::prefix('api/v2')
            // TODO: add middleware with api key
            ->namespace($this->namespace)
            ->group(base_path('routes/api_v2.php'));
    }
}
