<?php

namespace App\Imports;

use App\Models\Fu\Subject;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\RemembersRowNumber;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use App\Models\MienGiamTapTrung;
use Carbon\Carbon;
use App\Models\Fu\User;
use App\Models\Fu\Term;
use App\Models\Dra\StudentSubject;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\SystemLog;

class MienGiamImport implements ToModel, WithChunkReading
{
    use RemembersRowNumber;
    /**
    * @param Collection $collection
    */
    private $user;
    public $importValidate;
    function __construct($user)
    {
        $this->user = $user;
    }
    public function model(array $row)
    {
        $rowIndex = $this->getRowNumber();
        if ($rowIndex > 1) {
            if ($row[0] && $row[1]) {
                $user = User::where([
                    'user_login' => $row[0],
                    'user_level' => 3
                    ])->first();
                if ($user) {
                    if ($user->study_status != 8) {
                        $term = Term::where('term_name', $row[5])->first();
                        if ($term) {
                            $subject_code = $row[1];
                            $subject = Subject::where('subject_code', $subject_code)->orWhere('skill_code', $subject_code)->first();
                            if($subject) {
                                $type = 'mg';
                                $note = $row[5];
                                StudentSubject::where('student_login', $user->user_login)
                                    ->where(function ($query) use($subject_code) {
                                        $query->where('subject_code', $subject_code)->orWhere('skill_code', $subject_code);
                                    })
                                    ->update([
                                        'status' => 2,
                                        'is_lock_edit' => 1,
                                        'resourse' => 'credit transfer',
                                        'in_result' => $row[2],
                                        'grade' => $row[4],
                                        'note' => $note,
                                        'pass_term_id' => $term->id,
                                        'type' => 2,
                                    'is_start' => ($row[2] == 1) ? 1: 0
                                ]);
                                MienGiamTapTrung::create([
                                    'user_code' => $user->user_code,
                                    'student_login' => $user->user_login,
                                    'subject_code' => $subject_code,
                                    'skill_code' => $subject->skill_code,
                                    'subject_code_new' => "",
                                    'skill_code_new' => "",
                                    'type' => 2,
                                    'term_name' => $term->term_name,
                                    'so_quyet_dinh' => "",
                                    'time_action' => "0000:00:00",
                                    'user_action' => "",
                                    'status' => 0,
                                    'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                                    'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
                                    'created_by' => Auth::user()->user_login,
                                    'term_id' => $term->id
                                ]);
                                SystemLog::create([
                                    'object_name' => "final_grade",
                                    'actor' => Auth::user()->user_login,
                                    'log_time' => Carbon::now()->format('Y-m-d H:i:s'),
                                    'action' => "transfer credit",
                                    'description' => 'transfer credit type ' . $type . ': ' . $subject_code . ' with note ' . $note,
                                    'object_id' => 0,
                                    'brief' => 'transfer credit',
                                    'from_ip' => "",
                                    'relation_login' => $user->user_login,
                                    'relation_id' => $user->id,
                                    'nganh_cu' => "",
                                    'nganh_moi' => "",
                                    'ky_chuyen_den' => "",
                                    'ky_thu_cu' => ""
                                ]);
                            }
                        } else {
                            //term not found
                            $this->importValidate[] = "Dòng thứ $rowIndex: Không tìm thấy kỳ.";
                        }
                    } else {
                        // user da tot nghiep
                        $this->importValidate[] = "Dòng thứ $rowIndex: Sinh viên đã tốt nghiệp.";
                    }
                } else {
                    $this->importValidate[] = "Dòng thứ $rowIndex: Sinh viên không tồn tại.";
                }
            } else {
                $this->importValidate[] = "Dòng thứ $rowIndex: Dữ liệu trống.";
            }
        }
    }

    public function chunkSize(): int
    {
        return 1000;
    }

    public function getValidate() {
        return $this->importValidate;
    }
}
