<?php

namespace App\Imports;

use App\Models\ChangeSubjectStudent;
use App\Models\Dra\StudentSubject;
use App\Models\Fu\Subject;
use App\Models\Fu\SubjectUpdateAble;
use App\Models\Fu\User;
use App\Models\MienGiamTapTrung;
use App\Models\SystemLog;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Collection;
use Illuminate\Support\MessageBag;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithValidation;

class MienGiamTapTrungImport implements ToCollection, WithHeadingRow
{
    public $importErrors = [];
    
    public function getErrors() {
        return $this->importErrors;
    }

    public function collection(Collection $row)
    {
        try {
            DB::beginTransaction();
            // Kiểm tra dữ liệu tại đây
            $row = $row->toArray();

            foreach($row as $item) {
                // if (!in_array($item['type'], [1,2])) {
                //     continue;
                // }
                $item['type'] = 1;

                $data = [
                    'student_login' => $item['ma_sinh_vien'] ?? null,
                    'subject_code' => $item['ma_mon'] ?? null,
                    'skill_code' => $item['ma_chuyen_doi'] ?? null,
                    'subject_code_new' => $item['ma_mon_moi'] ?? null,
                    'skill_code_new' =>  $item['ma_chuyen_doi_moi'] ?? null,
                    'type' => $item['type'] ?? null,
                    'so_quyet_dinh' => $item['so_quyet_dinh'] ?? null,
                    'time_action' => now(),
                    'status' => 0,
                    'created_at' => date('Y-m-d H:i:s', time()),
                    'updated_at' => date('Y-m-d H:i:s', time()),
                ];

                $check_data = $this->isValid($data);
                if (!($check_data['check'])) {
                    $this->importErrors[] = $check_data['message'];
                    continue;
                }
                
                $description = "transfer credit: Thay thế môn học " . $data['subject_code'] . " with note: " . ($data['note'] ?? "");
                
                SystemLog::create([
                    'object_name' => 'Thay thế môn',
                    'actor' => auth()->user()->user_login,
                    'log_time' => date('Y-m-d H:i:s', time()),
                    'action' => 'transfer credit',
                    'description' => $description,
                    'object_id' => 0,
                    'brief' => 'transfer credit',
                    'from_ip'=> request()->ip(),
                    'relation_login' => $item['ma_sinh_vien'],
                    'relation_id' => 0,
                    'nganh_cu' => '',
                    'nganh_moi' => '',
                    'ky_chuyen_den' => 0,
                    'ky_thu_cu' => 0
                ]);

                $user = User::where('user_login', $data['student_login'])->first();

                ChangeSubjectStudent::create([
                    'student_login' => $user->user_login,
                    'old_subject' => $data['subject_code'],
                    'new_subject' => $data['subject_code_new']
                ]);

                MienGiamTapTrung::create([
                    'user_code' => $user->user_code,
                    'student_login' => $user->user_login,
                    'subject_code' => $data['subject_code'],
                    'skill_code' => $data['skill_code'],
                    'subject_code_new' => $data['subject_code_new'],
                    'skill_code_new' => $data['skill_code_new'],
                    'type' => 1,
                    'term_name' => "",
                    'so_quyet_dinh' => $data['so_quyet_dinh'],
                    'time_action' => "0000-00-00",
                    'user_action' => "",
                    'status' => 0,
                    'created_at' => null,
                    'updated_at' => null,
                    'created_by' => auth()->user()->user_login,
                    'term_id'  => null
                ]);

                StudentSubject::where('skill_code', $data['skill_code'])
                    ->where('student_login', $user->user_login)->update([
                        'active' => -1,
                        'is_lock_edit' => 1
                    ]);
            }

            DB::commit();
        } catch (\Throwable $th) {
            //throw $th;
            DB::rollBack();
            throw $th;
        }
    }

    private function isValid(array $data)
    {
        // check student_login

        if(!empty($data['student_login'])){
            $check_user = User::where('user_login', $data['student_login'])
                ->orWhere('user_code', $data['student_login'])
                ->exists();

            if(!$check_user){
                return [
                    'check' => false,
                    'message' => 'Sinh viên không tồn tại'
                ];
            }
        }else{
            return [
                'check' => false,
                'message' => 'Mã sinh viên không được bỏ trống'
            ];
        }
        // check môn thay thế dựa vào ma trận môn thay thế
        $checkSubjectUpdateAble = SubjectUpdateAble::where('old_subject_code', $data['subject_code'])
            ->where('new_subject_code', $data['subject_code_new'])
            ->exists();

        if(!$checkSubjectUpdateAble) {
            return [
                'check' => false,
                'message' => "không cho phép thay thế môn {$data['subject_code']} bằng môn {$data['subject_code_new']}."
            ];
        }


        return [
            'check' => true,
            'message' => null
        ];
    }
}
