<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\RemembersRowNumber;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use App\Models\Fu\User;
use App\Models\Fu\Subject;
use App\Models\T7\CourseResult;
use App\Models\Dra\StudentSubject;
use App\Models\ChangeSubjectStudent;
use App\Models\MienGiamTapTrung;
use App\Models\SystemLog;
use Illuminate\Support\Facades\DB;
use App\Models\Fu\Term;
use Illuminate\Support\Carbon;
use App\Models\SubjectUpdateAble;
use Illuminate\Support\Facades\Auth;

class ChangeSubjectStudentImport implements ToModel
{
    use RemembersRowNumber;
    protected $actor;
    /**
    * @param Collection $collection
    */
    public $validate = [];
    public function __construct($actor)
    {
        $this->actor = $actor;
    }

    public function model(array $row)
    {
        $rowIndex = $this->getRowNumber();
        if ($rowIndex > 1) {
            $readRow =  count(explode(";", $row[0])) > 0 ? explode(";", $row[0]) : explode(",", $row[0]);
            if (count($readRow) < 4) {
                $readRow = $row;
            }
            $student_code = trim($readRow[0]);
            $old_subject_code = trim($readRow[1]);
            $new_subject_code = trim($readRow[2]);
            $number_decision = $readRow[3];

            $user = User::where('user_code', $student_code)->first();
            if ($user) {
                $old_subject = Subject::where('subject_code', $old_subject_code)->orWhere('skill_code', $old_subject_code)->first();
                $new_subject = Subject::where('subject_code', $new_subject_code)->orWhere('skill_code', $new_subject_code)->first();
                if ($new_subject) {
                    $subjects_able = SubjectUpdateAble::join(
                        'subject',
                        'subject.subject_code', 'subject_update_able.new_subject_code'
                    )
                    ->where('old_skill_code', $old_subject->skill_code)
                    ->where('new_skill_code', $new_subject->skill_code)
                    ->get()->toArray();
                    if(!$subjects_able || count($subjects_able) == 0){
                        DB::rollBack();
                        $this->validate[] = "Fail, không cho phép thay thế môn $old_subject_code bằng môn $new_subject_code.";
                    }else{
                        DB::beginTransaction();
                        
                        try {
                            // $courseResult = CourseResult::where('psubject_code', $subject->subject_code)->where('student_login', $user->user_login)->first();
                            $period_ordering = StudentSubject::where('subject_code', $old_subject_code)->where('student_login', $user->user_login)->first();
                            if ($period_ordering) {
                                $studentSubject = StudentSubject::updateOrCreate(
                                    [
                                        "subject_code" => $new_subject->subject_code,
                                        "student_login" => $user->user_login,
                                        "subject_id" => $new_subject->id,
                                    ],
                                    [
                                    "student_login" => $user->user_login,
                                    "subject_id" => $new_subject->id,
                                    "number_of_credit" => $new_subject->num_of_credit,
                                    "skill_code" => $new_subject->skill_code,
                                    "subject_name" => $new_subject->subject_name,
                                    "subject_code" => $new_subject->subject_code,
                                    "is_start" => 0,
                                    "is_lock_edit" => 0,
                                    "number_decide" => $number_decision,
                                    "note" => "",
                                    "period_ordering" => $period_ordering->period_ordering,
                                ]);
                            }
                            if (isset($studentSubject) && $studentSubject) {
                                // $term = Term::where('id', $courseResult->term_id)->first();
                                $changeSubjectStudent = ChangeSubjectStudent::create([
                                    'student_login' => $user->user_login,
                                    'old_subject' => $old_subject_code,
                                    'new_subject' => $new_subject_code
                                ]);
    
                                $mienGiamTapTrung = MienGiamTapTrung::create([
                                    'user_code' => $user->user_code,
                                    'student_login' => $user->user_login,
                                    'subject_code' => $old_subject_code,
                                    'skill_code' => $old_subject->skill_code,
                                    'subject_code_new' => $new_subject->subject_code,
                                    'skill_code_new' => $new_subject->skill_code,
                                    'type' => 1,
                                    'term_name' => "",
                                    'so_quyet_dinh' => $number_decision,
                                    'time_action' => "0000-00-00",
                                    'user_action' => "",
                                    'status' => 0,
                                    'created_at' => null,
                                    'updated_at' => null,
                                    'created_by' => Auth::user()->user_login,
                                    'term_id'  => null
                                ]);
    
                                $updateStudentSubject = StudentSubject::where('skill_code', $old_subject->skill_code)
                                ->where('student_login', $user->user_login)->update([
                                    'active' => -1,
                                    'is_lock_edit' => 1
                                ]);
    
                                $systemLog = SystemLog::create([
                                    'object_name' => "Thay thế môn",
                                    'actor' => $this->actor->user_login,
                                    'log_time' => Carbon::now()->format('Y-m-d H:i:s'),
                                    'action' => "Thay thế môn khác",
                                    'description' => "Thay thế môn $old_subject_code bằng môn $new_subject_code cho sinh viên $user->user_login.",
                                    'object_id' => $changeSubjectStudent->id,
                                    'brief' => "",
                                    'from_ip' => "",
                                    'relation_login' => $user->user_login,
                                    'relation_id' => $user->id,
                                    'nganh_cu' => "",
                                    'nganh_moi' => "",
                                    'ky_chuyen_den' => "",
                                    'ky_thu_cu' => ""
                                ]);
                                DB::commit();
                            } else {
                                DB::rollBack();
                                $this->validate[] = "Fail, không cho phép thay thế môn $old_subject_code bằng môn $new_subject_code.";
                            }
                        } catch (\Throwable $th) {
                            DB::rollback();
                            throw $th;
                        }
                    };
                } else {
                    DB::rollBack();
                    $this->validate[] = "$rowIndex fail, Môn học không tồn tại.";
                }
            } else {
                DB::rollBack();
                $this->validate[] = "$rowIndex fail, User không tồn tại";
            }
        }
    }

    public function getValidate() {
        return $this->validate;
    }
}
