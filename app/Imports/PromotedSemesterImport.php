<?php

namespace App\Imports;

use App\Models\HistoryLenky;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class PromotedSemesterImport implements ToModel, WithHeadingRow
{
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        return new HistoryLenky([
            'ma_sinh_vien' => $row['user_code'],
            'ky_thu' => $row['kythu'],
        ]);
    }
}
