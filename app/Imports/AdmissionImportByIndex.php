<?php

namespace App\Imports;

use App\Models\Admission;
use Maatwebsite\Excel\Concerns\ToModel;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AdmissionImportByIndex implements ToModel
{
    protected $results = [];
    protected $currentRow = 0;

    public function model(array $row)
    {
        $this->currentRow++;



        // Skip first 3 rows (title, headers, field names)
        if ($this->currentRow <= 3) {
            return null;
        }

        try {
            // Skip empty rows
            $hasData = false;
            for ($i = 0; $i <= 4; $i++) { // Check first 5 columns
                if (!empty(trim($row[$i] ?? ''))) {
                    $hasData = true;
                    break;
                }
            }
            
            if (!$hasData) {
                return null;
            }

            // Map by column index (0-based)
            $data = [
                'user_surname' => trim($row[0] ?? ''), // A: ho
                'user_middlename' => trim($row[1] ?? ''), // B: ten_dem
                'user_givenname' => trim($row[2] ?? ''), // C: ten
                'user_birthday' => $this->parseDate($row[3] ?? ''), // D: ngay_sinh
                'user_gender' => $this->parseGender($row[4] ?? ''), // E: gioi_tinh
                'user_code' => !empty($row[5]) ? trim($row[5]) : null, // F: ma_tuyen_sinh
                'user_email' => !empty($row[6]) ? trim($row[6]) : null, // G: email
                'user_telephone' => !empty($row[7]) ? trim($row[7]) : null, // H: so_dien_thoai
                'user_address' => trim($row[8] ?? ''), // I: dia_chi
                'user_province' => trim($row[9] ?? ''), // J: tinh_thanh_pho
                'user_district' => !empty($row[10]) ? trim($row[10]) : null, // K: quan_huyen
                'user_ward' => !empty($row[11]) ? trim($row[11]) : null, // L: phuong_xa
                'training_level' => trim($row[12] ?? ''), // M: bac_dao_tao
                'training_program' => trim($row[13] ?? ''), // N: chuong_trinh_dao_tao
                'cultural_level' => trim($row[14] ?? ''), // O: trinh_do_van_hoa
                'admission_period' => trim($row[15] ?? ''), // P: thoi_gian_tuyen_sinh
                'priority_object' => !empty($row[16]) ? trim($row[16]) : null, // Q: doi_tuong_uu_tien
                'note' => !empty($row[17]) ? trim($row[17]) : null, // R: ghi_chu
                'status' => Admission::STATUS_PENDING,
                'created_by' => auth()->user()->user_login ?? 'import_system'
            ];

            // Basic validation
            $this->validateData($data, $row);

            // Check duplicates
            $this->checkDuplicates($data);

            // Create admission
            $admission = Admission::create($data);

            $this->results[] = [
                'row' => $this->currentRow,
                'status' => 'success',
                'message' => 'Import thành công',
                'data' => [
                    'id' => $admission->id,
                    'ho_ten' => $admission->user_surname . ' ' . ($admission->user_middlename ? $admission->user_middlename . ' ' : '') . $admission->user_givenname,
                    'ma_tuyen_sinh' => $admission->user_code,
                    'email' => $admission->user_email,
                    'chuong_trinh' => $admission->training_program
                ]
            ];

            return $admission;

        } catch (\Exception $e) {
            $this->results[] = [
                'row' => $this->currentRow,
                'status' => 'error',
                'message' => $e->getMessage(),
                'data' => $this->formatRowData($row)
            ];

            return null;
        }
    }

    private function validateData(array $data, array $row): void
    {
        $errors = [];

        if (empty($data['user_surname'])) $errors[] = "Thiếu họ (cột A)";
        if (empty($data['user_givenname'])) $errors[] = "Thiếu tên (cột C)";
        if (empty($data['user_birthday'])) $errors[] = "Thiếu ngày sinh (cột D)";
        if ($data['user_gender'] === null) $errors[] = "Thiếu giới tính (cột E)";
        if (empty($data['user_address'])) $errors[] = "Thiếu địa chỉ (cột I)";
        if (empty($data['user_province'])) $errors[] = "Thiếu tỉnh/thành phố (cột J)";
        if (empty($data['training_level'])) $errors[] = "Thiếu bậc đào tạo (cột M)";
        if (empty($data['training_program'])) $errors[] = "Thiếu chương trình đào tạo (cột N)";
        if (empty($data['cultural_level'])) $errors[] = "Thiếu trình độ văn hóa (cột O)";
        if (empty($data['admission_period'])) $errors[] = "Thiếu thời gian tuyển sinh (cột P)";

        if (!empty($errors)) {
            throw new \Exception(implode('; ', $errors));
        }
    }

    private function checkDuplicates(array $data): void
    {
        if (!empty($data['user_code'])) {
            $existing = Admission::where('user_code', $data['user_code'])->first();
            if ($existing) {
                throw new \Exception("Mã tuyển sinh '{$data['user_code']}' đã tồn tại (ID: {$existing->id})");
            }
        }

        if (!empty($data['user_email'])) {
            $existing = Admission::where('user_email', $data['user_email'])->first();
            if ($existing) {
                throw new \Exception("Email '{$data['user_email']}' đã được sử dụng (ID: {$existing->id})");
            }
        }
    }

    private function parseDate($dateString): ?string
    {
        if (empty($dateString)) return null;

        $dateString = trim($dateString);

        try {
            // Handle Excel serial number
            if (is_numeric($dateString) && $dateString > 1) {
                if ($dateString > 59) $dateString = $dateString - 1;
                $excelEpoch = Carbon::create(1900, 1, 1);
                $date = $excelEpoch->addDays($dateString - 1);
                return $date->format('Y-m-d');
            }

            // Try common formats
            $formats = ['Y-m-d', 'd/m/Y', 'd-m-Y', 'm/d/Y'];
            foreach ($formats as $format) {
                try {
                    $date = Carbon::createFromFormat($format, $dateString);
                    if ($date && $date->year >= 1900 && $date->year <= date('Y')) {
                        return $date->format('Y-m-d');
                    }
                } catch (\Exception $e) {
                    continue;
                }
            }

            throw new \Exception("Ngày sinh không hợp lệ: '{$dateString}'");
        } catch (\Exception $e) {
            throw new \Exception("Ngày sinh không hợp lệ (cột D): '{$dateString}'. Vui lòng nhập dd/mm/yyyy");
        }
    }

    private function parseGender($genderString): ?int
    {
        if (empty($genderString)) return null;

        $gender = strtolower(trim($genderString));
        if (in_array($gender, ['nam', 'male', 'm', '1'])) return Admission::GENDER_MALE;
        if (in_array($gender, ['nữ', 'nu', 'female', 'f', '0'])) return Admission::GENDER_FEMALE;

        throw new \Exception("Giới tính không hợp lệ (cột E): '{$genderString}'. Vui lòng nhập 'Nam' hoặc 'Nữ'");
    }

    private function formatRowData(array $row): array
    {
        return [
            'ho_ten' => trim(($row[0] ?? '') . ' ' . ($row[1] ?? '') . ' ' . ($row[2] ?? '')),
            'ma_tuyen_sinh' => $row[5] ?? '',
            'email' => $row[6] ?? '',
            'ngay_sinh' => $row[3] ?? '',
            'gioi_tinh' => $row[4] ?? '',
            'chuong_trinh' => $row[13] ?? ''
        ];
    }

    public function getResults(): array
    {
        return $this->results;
    }

    public function getSuccessCount(): int
    {
        return count(array_filter($this->results, function($r) {
            return $r['status'] === 'success';
        }));
    }

    public function getErrorCount(): int
    {
        return count(array_filter($this->results, function($r) {
            return $r['status'] === 'error';
        }));
    }

    public function getErrors(): array
    {
        return array_filter($this->results, function($r) {
            return $r['status'] === 'error';
        });
    }
}
