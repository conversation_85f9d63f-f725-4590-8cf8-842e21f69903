<?php

namespace App\Imports;

use App\Models\Fee\DngRecord;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class VATImport implements ToModel, WithHeadingRow
{
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        return new DngRecord([
            'dng_id' => $row['DNG_ID'],
            'vat' => $row['VAT']
        ]);
    }
}
