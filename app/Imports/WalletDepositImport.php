<?php

namespace App\Imports;

use App\Models\Fee\StudentWallet;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class WalletDepositImport implements ToCollection, WithHeadingRow, WithBatchInserts, WithChunkReading
{

    protected $results = [
        'total_rows' => 0,
        'success_count' => 0,
        'error_count' => 0,
        'errors' => [],
        'deposits' => []
    ];

    /**
     * Process the collection of rows
     */
    public function collection(Collection $rows)
    {
        DB::beginTransaction();
        try {
            foreach ($rows as $index => $row) {
                $rowNumber = $index + 2; // +2 vì bắt đầu từ dòng 2 (dòng 1 là header)

                if (!$this->isRowNotEmpty($row)) {
                    continue;
                }

                $this->results['total_rows']++;
                $this->processDepositMoney($row, $rowNumber);
            }

            DB::commit();
            Log::info("Wallet deposit import chunk processed", [
                'success_count' => $this->results['success_count'],
                'error_count' => $this->results['error_count']
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Wallet deposit import failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Process money deposit
     */
    protected function processDepositMoney($row, $rowNumber)
    {
        try {
            $userCode = strtoupper(trim($row['ma_sinh_vien'] ?? ''));
            $amount = floatval($row['so_tien'] ?? 0);
            $description = trim($row['mo_ta'] ?? 'Nạp tiền (Import)');
            $paymentMethod = trim($row['phuong_thuc'] ?? 'bank_transfer');

            // Validate required fields
            if (empty($userCode)) {
                $this->addError($rowNumber, 'Mã sinh viên không được để trống');
                return;
            }

            if ($amount < 1000) {
                $this->addError($rowNumber, 'Số tiền tối thiểu là 1,000 VNĐ');
                return;
            }

            // Find wallet
            $wallet = StudentWallet::where('user_code', $userCode)->first();
            if (!$wallet) {
                $this->addError($rowNumber, "Không tìm thấy ví của sinh viên {$userCode}");
                return;
            }

            if ($wallet->is_locked) {
                $this->addError($rowNumber, "Ví của sinh viên {$userCode} đã bị khóa");
                return;
            }

            // Add money to wallet using model method
            $wallet->addMoney(
                $amount,
                $description,
                Auth::user()->user_login ?? 'system',
                $paymentMethod
            );

            $this->results['success_count']++;
            $this->results['deposits'][] = [
                'user_code' => $userCode,
                'amount' => $amount,
                'description' => $description
            ];
        } catch (\Exception $e) {
            $this->addError($rowNumber, 'Lỗi nạp tiền: ' . $e->getMessage());
        }
    }

    /**
     * Check if row is not empty
     */
    protected function isRowNotEmpty($row): bool
    {
        return !empty($row['ma_sinh_vien']) || !empty($row['so_tien']);
    }

    /**
     * Add error to results
     */
    protected function addError($rowNumber, $message)
    {
        $this->results['error_count']++;
        $this->results['errors'][] = "Dòng {$rowNumber}: {$message}";

        Log::warning("Wallet deposit import error", [
            'row' => $rowNumber,
            'message' => $message
        ]);
    }

    /**
     * Get import results
     */
    public function getResults(): array
    {
        return $this->results;
    }



    /**
     * Batch size for processing
     */
    public function batchSize(): int
    {
        return 100;
    }

    /**
     * Chunk size for reading
     */
    public function chunkSize(): int
    {
        return 500;
    }
}
