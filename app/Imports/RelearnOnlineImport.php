<?php

namespace App\Imports;

use App\Models\RelearnOnline;
use Maatwebsite\Excel\Concerns\ToModel;

class RelearnOnlineImport implements ToModel
{
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
//    private $group_id;
//
//    public function setGroupId(int $group_id)
//    {
//        $this->group_id = $group_id;
//    }

    public function model(array $row)
    {
//        dd($this->group_id);

        return new RelearnOnline([
            'user_login' => $row[0],
            'user_code' => $row[1],
            'point' => $row[3],
        ]);
    }
}
