<?php

namespace App\Imports;

use App\Models\Fu\OnlineServices\OnlineService;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class OnlineServiceHulkClosingImport implements ToModel, WithHeadingRow
{
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        return new OnlineService([
            'id' => $row['id'],
        ]);
    }
}
