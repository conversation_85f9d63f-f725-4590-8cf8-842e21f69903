<?php

namespace App\Imports;

use App\Models\Fee\FeeSubject;
use App\Models\Fee\StudentDebt;
use App\Models\Fu\Term;
use App\Models\Fee\FeeType;
use App\Models\Fu\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class DebtBulkImport implements ToCollection, WithHeadingRow, WithValidation, SkipsOnFailure, WithBatchInserts, WithChunkReading
{
    use SkipsFailures;

    protected $results = [
        'total_rows' => 0,
        'success_count' => 0,
        'error_count' => 0,
        'errors' => [],
        'created_debts' => []
    ];

    protected $termCache = [];
    protected $feeTypeCache = [];
    protected $isFirstChunk = true;
    const STUDYFEETYPE = ['HP', 'HL', 'TL'];
    const DEBT_STATUS_PENDING = 0;  // Chưa thanh toán
    const DEBT_STATUS_PAID = 1;     // Đã thanh toán
    const DEBT_STATUS_CANCELED = 2; // Đã hủy
    
    public function __construct()
    {
        // Pre-load terms and fee types for better performance
        $this->loadCacheData();
    }

    /**
     * Process the collection of rows
     */
    public function collection(Collection $rows)
    {
        // Filter out completely empty rows before processing
        $nonEmptyRows = $rows->filter(function ($row) {
            return $this->isRowNotEmpty($row);
        });

        // Only update total_rows if we have non-empty rows
        if ($nonEmptyRows->isNotEmpty()) {
            $this->results['total_rows'] += $nonEmptyRows->count();

            if ($this->isFirstChunk) {
                Log::info("Starting debt import with {$nonEmptyRows->count()} non-empty rows in first chunk (filtered from {$rows->count()} total rows)");
                $this->isFirstChunk = false;
            } else {
                Log::debug("Processing additional chunk with {$nonEmptyRows->count()} non-empty rows (Total so far: {$this->results['total_rows']})");
            }
        } else {
            Log::debug('No non-empty rows in chunk, skipping');
            return;
        }

        DB::beginTransaction();
        try {
            $processedCount = 0;
            foreach ($rows as $row) {
                // Calculate actual row number considering all previous chunks
                $actualRowNumber = $this->results['total_rows'] - $nonEmptyRows->count() + $processedCount + 2;

                // Skip empty rows
                if (!$this->isRowNotEmpty($row)) {
                    Log::debug("Skipping empty row {$actualRowNumber}");
                    continue;
                }

                $this->processRow($row, $actualRowNumber);
                $processedCount++;
            }

            DB::commit();
            Log::debug("Chunk processed. Current totals - Created: {$this->results['success_count']}, Errors: {$this->results['error_count']}");
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Debt import chunk failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Process a single row
     */
    protected function processRow($row, $rowNumber)
    {
        try {
            Log::debug("Processing row {$rowNumber}", ['raw_data' => $row]);

            // Validate and clean data
            $cleanData = $this->validateAndCleanRow($row, $rowNumber);
            if (!$cleanData) {
                Log::warning("Row {$rowNumber} failed validation");
                return; // Skip this row due to validation errors
            }

            // Check for duplicate debt
            if ($this->isDuplicateDebt($cleanData)) {
                $this->addError($rowNumber, $row, 'Công nợ đã tồn tại cho sinh viên này trong kỳ học này');
                Log::warning("Row {$rowNumber} is duplicate debt");
                return;
            }

            // Tính toán discount cho Excel import
            $originalAmount = $cleanData['original_amount'];
            $discountAmount = $cleanData['discount_amount'] ?? 0;
            $discountPercentage = $cleanData['discount_percentage'] ?? 0;

            // Đảm bảo mặc định discount_percentage = 0
            if ($discountPercentage < 0 || $discountPercentage > 100) {
                $discountPercentage = 0;
            }

            // Tính số tiền cuối cùng
            $amount = $originalAmount;
            if ($discountPercentage > 0) {
                $discount = intval(round($originalAmount * ($discountPercentage / 100), 2));
                $amount = intval($originalAmount - $discount);
                $discountAmount = 0;
            } elseif ($discountAmount > 0) {
                $amount = intval($originalAmount - $discountAmount);
                $discountPercentage = 0;
            } else {
                $discountAmount = 0;
                $discountPercentage = 0;
            }

            // Create debt record với discount
            $debtData = [
                'user_code' => $cleanData['user_code'],
                'user_login' => $cleanData['user_login'],
                'term_id' => $cleanData['term_id'],
                'term_name' => $cleanData['term_name'],
                'fee_type_id' => $cleanData['fee_type_id'],
                'fee_type_code' => $cleanData['fee_type_code'],
                'original_amount' => $originalAmount,
                'discount_amount' => $discountAmount,
                'discount_percentage' => $discountPercentage,
                'discount_reason' => $cleanData['discount_reason'],
                'amount' => $amount, // Số tiền thực tế phải trả
                'paid_amount' => 0,
                'status' => $cleanData['status'] ?? 0,
                'description' => $cleanData['notes'] ?? null,
                'created_by' => $cleanData['created_by'],
                'subject_id' => $cleanData['subject_id'] ?? null,
                'subject_code' => $cleanData['subject_code'] ?? null,
                'subject_name' => $cleanData['subject_name'] ?? null,
            ];
            $debt = StudentDebt::create($debtData);
            $this->results['success_count']++;
            $this->results['created_debts'][] = $debt->id;
        } catch (\Exception $e) {
            $this->addError($rowNumber, $row, 'Lỗi tạo công nợ: ' . $e->getMessage());
            Log::error("Error processing row {$rowNumber}: " . $e->getMessage(), [
                'row_data' => $row,
                'exception' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Validate and clean row data
     */
    protected function validateAndCleanRow($row, $rowNumber)
    {
        $errors = [];
        // Required fields validation - handle both string and number from Excel
        $userCode = trim(strtoupper(strval($row['user_code'] ?? '')));
        $termName = trim(strval($row['term_name'] ?? ''));
        $feeTypeCode = trim(strval($row['fee_type_code'] ?? ''));
        $originAmount = intval($row['original_amount'] ?? 0);
        $discountPercentage = intval($row['discount_percentage'] ?? 0);
        $discountAmount = intval($row['discount_amount'] ?? 0);
        $discountReason = trim(strval($row['discount_reason'] ?? ''));
        $status = intval($row['stat$status'] ?? 0);
        $notes = trim(strval($row['notes'] ?? ''));
        $subject_code = trim(strtoupper($row['subject_code'])) ?? null; 

        if (empty($userCode)) $errors[] = 'Mã sinh viên không được để trống';
        if (empty($termName)) $errors[] = 'Tên kỳ học không được để trống';
        if (empty($feeTypeCode)) $errors[] = 'Loại phí không được để trống';

        // Check if user exists
        if (!empty($userCode)) {
            $user = User::where('user_code', $userCode)->first();
            if (!$user) {
                // Check if user_code exists with different login
                $userByCode = User::where('user_code', $userCode)->first();
                if (!$userByCode) {
                    $errors[] = "Không tìm thấy sinh viên với mã '{$userCode}'";
                }
            }
        }

        // Validate term exists
        $term = $this->findTerm($termName);
        if (!$term) {
            $errors[] = "Không tìm thấy kỳ học: {$termName}";
            Log::warning("Term not found", [
                'term_name' => $termName,
                'available_terms' => array_keys($this->termCache)
            ]);
        }

        // Validate fee type exists
        $feeType = $this->findFeeType($feeTypeCode);
        if (!$feeType) {
            $errors[] = "Không tìm thấy loại phí: {$feeTypeCode}";
            Log::warning("Fee type not found", [
                'fee_type_code' => $feeTypeCode,
                'available_fee_types' => array_keys($this->feeTypeCache)
            ]);
        }

        if (in_array($feeTypeCode, self::STUDYFEETYPE)) {
            if ($subject_code) {
                $feeSubject = FeeSubject::where('subject_code', $subject_code)
                    ->where('curriculum_id', $user->curriculum_id)
                    ->where('status', 1)
                    ->first();
                if (!$feeSubject) {
                    $errors[] = "Không tìm thấy biểu phí môn '{$subject_code}' trong khung trương trình của sinh viên {$userCode}";
                } else {
                    $subject_id = $feeSubject->id;
                    $subject_code = $feeSubject->subject_code;
                    $subject_name = $feeSubject->subject_name;
                    switch ($feeTypeCode) {
                        case 'HP':
                            $originAmount = $feeSubject->tuition_fee;
                            break;
                        case 'HL':
                            $originAmount = $feeSubject->relearn_fee;
                            break;
                        case 'TL':
                            $originAmount = $feeSubject->retest_fee;
                            break;
                        default:
                            $errors[] = "Loại phí không hợp lệ cho môn học: {$feeTypeCode}";
                            break;
                    }
                }
            }
        }
        if ($originAmount <= 0) $errors[] = 'Số tiền phải lớn hơn 0';
        if (!empty($errors)) {
            $this->addError($rowNumber, $row, $errors);
            return null;
        }

        // Validate discount fields từ Excel
        $originalAmount = intval($row['original_amount']);
        $discountAmount = intval($row['discount_amount'] ?? 0);
        $discountPercentage = intval($row['discount_percentage'] ?? 0);
        $discountReason = trim($row['discount_reason'] ?? '');

        // Validate discount
        if ($discountPercentage < 0 || $discountPercentage > 100) {
            $errors[] = 'Phần trăm giảm giá phải từ 0 đến 100';
        }
        if ($discountAmount < 0 || $discountAmount > $originalAmount) {
            $errors[] = 'Số tiền giảm giá không hợp lệ';
        }
        if ($discountPercentage > 0 && $discountAmount > 0) {
            $errors[] = 'Chỉ được chọn một trong hai: phần trăm giảm giá hoặc số tiền giảm giá';
        }

        // Kiểm tra lại errors sau khi validate discount
        if (!empty($errors)) {
            $this->addError($rowNumber, $row, $errors);
            return null;
        }

        return [
            'user_code' => $user->user_code,
            'user_login' => $user->user_login,
            'term_id' => $term->id,
            'term_name' => $term->term_name,
            'fee_type_id' => $feeType->id,
            'fee_type_code' => $feeType->code,
            'original_amount' => $originalAmount,
            'discount_amount' => $discountAmount,
            'discount_percentage' => $discountPercentage,
            'discount_reason' => $discountReason,
            'original_amount' => $originAmount,
            'paid_amount' => 0, // Default to 0 for new debts
            'status' => $status, // Default to 0 (pending)
            'description' => $notes,
            'created_by' => Auth::user()->user_login ?? 'system',
            'subject_id' => $subject_id ?? null,
            'subject_code' => $subject_code != '' ? $subject_code : null,
            'subject_name' => $subject_name ?? null,
        ];
    }

    /**
     * Check if debt already exists
     */
    protected function isDuplicateDebt($data)
    {
        return StudentDebt::where('user_code', $data['user_code'])
        ->where('term_id', $data['term_id'])
        ->where('fee_type_id', $data['fee_type_id'])
        ->where('status', '=', self::DEBT_STATUS_PENDING)
        ->when($data['subject_code'], function ($query) use ($data) {
            $query->where('subject_code', $data['subject_code']);
        }, function ($query) {
            $query->whereNull('subject_code');
        })
        ->exists();
    }

    /**
     * Load cache data for better performance
     */
    protected function loadCacheData()
    {
        // Cache terms
        Term::all()->each(function ($term) {
            $this->termCache[strtolower($term->term_name)] = $term;
        });

        // Cache fee types
        FeeType::where('is_active', true)->get()->each(function ($feeType) {
            $this->feeTypeCache[strtolower($feeType->code)] = $feeType;
        });

        Log::info("Cached " . count($this->termCache) . " terms and " . count($this->feeTypeCache) . " fee types");
    }

    /**
     * Find term by name (case insensitive)
     */
    protected function findTerm($termName)
    {
        return $this->termCache[strtolower($termName)] ?? null;
    }

    /**
     * Find fee type by name (case insensitive)
     */
    protected function findFeeType($feeTypeCode)
    {
        return $this->feeTypeCache[strtolower($feeTypeCode)] ?? null;
    }

    /**
     * Add error to results
     */
    protected function addError($rowNumber, $row, $message)
    {
        $this->results['error_count']++;

        // Handle both array and string messages
        $errorMessage = is_array($message) ? $message : [$message];

        $this->results['errors'][] = [
            'row' => $rowNumber,
            'data' => $row,
            'errors' => $errorMessage,
            'message' => implode('; ', $errorMessage) // For backward compatibility
        ];
    }

    /**
     * Check if a row is not empty (has at least one non-empty value)
     */
    protected function isRowNotEmpty($row)
    {
        if (!is_array($row) && !($row instanceof Collection)) {
            return false;
        }

        $values = $row instanceof Collection ? $row->toArray() : $row;

        foreach ($values as $value) {
            if ($value !== null && trim(strval($value)) !== '') {
                return true;
            }
        }

        return false;
    }

    /**
     * Laravel Excel validation rules - only apply to non-empty rows
     */
    public function rules(): array
    {
        return [
            'user_code' => 'nullable', // Accept both string and number
            'user_login' => 'nullable', // Accept both string and number
            'term_name' => 'nullable|string',
            'fee_type_code' => 'nullable|string',
            'amount' => 'nullable|numeric|min:0.01',
        ];
    }

    /**
     * Conditional validation - only validate if row is not empty
     */
    public function withValidator($validator)
    {
        $validator->sometimes(['user_code', 'user_login', 'term_name', 'fee_type_code', 'amount'], 'required', function ($input) {
            // Only require fields if the row has any data
            return $this->isRowNotEmpty($input);
        });
    }

    /**
     * Batch size for better performance
     */
    public function batchSize(): int
    {
        return 100;
    }

    /**
     * Chunk size for reading large files
     */
    public function chunkSize(): int
    {
        return 500;
    }

    /**
     * Handle validation failures from Laravel Excel
     */
    public function onFailure(\Maatwebsite\Excel\Validators\Failure ...$failures)
    {
        foreach ($failures as $failure) {
            // Skip validation failures for completely empty rows
            if (!$this->isRowNotEmpty($failure->values())) {
                Log::debug("Skipping validation failure for empty row {$failure->row()}");
                continue;
            }

            $this->results['error_count']++;
            $this->results['errors'][] = [
                'row' => $failure->row(),
                'data' => $failure->values(),
                'errors' => $failure->errors(),
                'message' => 'Validation failed: ' . implode(', ', $failure->errors())
            ];

            Log::warning("Laravel Excel validation failed", [
                'row' => $failure->row(),
                'attribute' => $failure->attribute(),
                'errors' => $failure->errors(),
                'values' => $failure->values()
            ]);
        }
    }

    /**
     * Get import results
     */
    public function getResults()
    {
        return $this->results;
    }
}
