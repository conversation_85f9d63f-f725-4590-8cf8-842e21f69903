<?php

namespace App\Imports;

use App\Models\T7\DisciplineExamRegulation;
use Maatwebsite\Excel\Concerns\ToModel;

class DisciplineExamRegulationImport implements ToModel
{
    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        return new DisciplineExamRegulation([
            //
        ]);
    }

    public function headingRow(): int
    {
        return 3;
    }
}
