<?php

namespace App\Imports;

use App\Models\Fu\OnlineServices\ServiceRolePermission;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class OnlineServicePermissionImport implements ToModel, WithHeadingRow
{
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        return new ServiceRolePermission([
            'service_code' => $row['service_code'],
            'user_role' => $row['user_role'],
            'step' => $row['step'],
            'action' => $row['action'],
        ]);
    }
}
