<?php

namespace App\Imports;

use App\Models\Admission;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class AdmissionImport implements
    ToModel,
    WithHeadingRow,
    WithValidation,
    WithBatchInserts,
    WithChunkReading,
    SkipsOnError,
    SkipsOnFailure
{
    use SkipsErrors, SkipsFailures;

    protected $results = [];
    protected $currentRow = 0;

    public function model(array $row)
    {
        $this->currentRow++;



        try {
            // Skip empty rows - check if all important fields are empty
            $hasData = false;
            $requiredFields = ['ho', 'ten', 'ngay_sinh', 'gioi_tinh'];
            foreach ($requiredFields as $field) {
                if (!empty(trim($row[$field] ?? ''))) {
                    $hasData = true;
                    break;
                }
            }

            if (!$hasData) {
                return null; // Skip empty rows silently
            }

            // Clean and prepare data
            $data = $this->prepareData($row);

            // Validate required fields with detailed error messages
            $this->validateRequiredFields($data, $row);

            // Additional duplicate checks
            $this->checkDuplicates($data, $row);

            // Create admission record
            $admission = Admission::create($data);

            $this->results[] = [
                'row' => $this->currentRow,
                'status' => 'success',
                'message' => 'Import thành công',
                'data' => [
                    'id' => $admission->id,
                    'ho_ten' => $admission->user_surname . ' ' . ($admission->user_middlename ? $admission->user_middlename . ' ' : '') . $admission->user_givenname,
                    'ma_tuyen_sinh' => $admission->user_code,
                    'email' => $admission->user_email,
                    'chuong_trinh' => $admission->training_program
                ]
            ];



            return $admission;
        } catch (\Illuminate\Database\QueryException $e) {
            // Handle database constraint violations
            $errorMessage = $this->parseDatabaseError($e, $row);

            $this->results[] = [
                'row' => $this->currentRow,
                'status' => 'error',
                'message' => $errorMessage,
                'data' => $this->formatRowData($row)
            ];



            return null;
        } catch (\Exception $e) {
            $this->results[] = [
                'row' => $this->currentRow,
                'status' => 'error',
                'message' => $e->getMessage(),
                'data' => $this->formatRowData($row)
            ];

            return null;
        }
    }

    public function rules(): array
    {
        return [
            'ho' => 'required|string|max:255',
            'ten' => 'required|string|max:255',
            'ngay_sinh' => 'required',
            'gioi_tinh' => 'required|in:Nam,Nữ,nam,nữ,NAM,NỮ,Male,Female,male,female,M,F,m,f,1,0',
            'dia_chi' => 'required|string|max:500',
            'tinh_thanh_pho' => 'required|string|max:255',
            'bac_dao_tao' => 'required|in:cao đẳng,liên thông,trung cấp',
            'chuong_trinh_dao_tao' => 'required|in:báo chí,công nghệ kỹ thuật điện điện tử,kế toán doanh nghiệp,tin học ứng dụng',
            'trinh_do_van_hoa' => 'required|in:trung học cơ sở,trung học phổ thông',
            'thoi_gian_tuyen_sinh' => 'required|string|max:255',
            // Optional fields
            'ten_dem' => 'nullable|string|max:255',
            'ma_tuyen_sinh' => 'nullable|string|max:50|unique:admissions,user_code',
            'email' => 'nullable|email|max:255|unique:admissions,user_email',
            'so_dien_thoai' => 'nullable|string|max:20',
            'quan_huyen' => 'nullable|string|max:255',
            'phuong_xa' => 'nullable|string|max:255',
            'doi_tuong_uu_tien' => 'nullable|string|max:255',
            'ghi_chu' => 'nullable|string|max:1000'
        ];
    }

    public function customValidationMessages()
    {
        return [
            'ho.required' => 'Họ không được bỏ trống',
            'ten.required' => 'Tên không được bỏ trống',
            'ngay_sinh.required' => 'Ngày sinh không được bỏ trống',
            'gioi_tinh.required' => 'Giới tính không được bỏ trống',
            'gioi_tinh.in' => 'Giới tính phải là Nam hoặc Nữ',
            'dia_chi.required' => 'Địa chỉ không được bỏ trống',
            'tinh_thanh_pho.required' => 'Tỉnh/Thành phố không được bỏ trống',
            'bac_dao_tao.required' => 'Bậc đào tạo không được bỏ trống',
            'bac_dao_tao.in' => 'Bậc đào tạo phải là: cao đẳng, liên thông, trung cấp',
            'chuong_trinh_dao_tao.required' => 'Chương trình đào tạo không được bỏ trống',
            'chuong_trinh_dao_tao.in' => 'Chương trình đào tạo phải là: báo chí, công nghệ kỹ thuật điện điện tử, kế toán doanh nghiệp, tin học ứng dụng',
            'trinh_do_van_hoa.required' => 'Trình độ văn hóa không được bỏ trống',
            'trinh_do_van_hoa.in' => 'Trình độ văn hóa phải là: trung học cơ sở, trung học phổ thông',
            'thoi_gian_tuyen_sinh.required' => 'Thời gian tuyển sinh không được bỏ trống',
            'ma_tuyen_sinh.unique' => 'Mã tuyển sinh đã tồn tại trong hệ thống',
            'email.email' => 'Email không đúng định dạng',
            'email.unique' => 'Email đã được sử dụng cho hồ sơ khác'
        ];
    }

    public function headingRow(): int
    {
        return 3; // Field names are on row 3 (after title, Vietnamese headers)
    }

    public function batchSize(): int
    {
        return 100;
    }

    public function chunkSize(): int
    {
        return 100;
    }

    private function prepareData(array $row): array
    {
        // Map Excel columns to database fields
        $data = [
            'user_surname' => trim($row['ho'] ?? ''),
            'user_middlename' => trim($row['ten_dem'] ?? ''),
            'user_givenname' => trim($row['ten'] ?? ''),
            'user_birthday' => $this->parseDate($row['ngay_sinh'] ?? ''),
            'user_gender' => $this->parseGender($row['gioi_tinh'] ?? ''),
            'user_code' => !empty($row['ma_tuyen_sinh']) ? trim($row['ma_tuyen_sinh']) : null,
            'user_email' => !empty($row['email']) ? trim($row['email']) : null,
            'user_telephone' => !empty($row['so_dien_thoai']) ? trim($row['so_dien_thoai']) : null,
            'user_address' => trim($row['dia_chi'] ?? ''),
            'user_province' => trim($row['tinh_thanh_pho'] ?? ''),
            'user_district' => !empty($row['quan_huyen']) ? trim($row['quan_huyen']) : null,
            'user_ward' => !empty($row['phuong_xa']) ? trim($row['phuong_xa']) : null,
            'training_level' => trim($row['bac_dao_tao'] ?? ''),
            'training_program' => trim($row['chuong_trinh_dao_tao'] ?? ''),
            'cultural_level' => trim($row['trinh_do_van_hoa'] ?? ''),
            'admission_period' => trim($row['thoi_gian_tuyen_sinh'] ?? ''),
            'priority_object' => !empty($row['doi_tuong_uu_tien']) ? trim($row['doi_tuong_uu_tien']) : null,
            'note' => !empty($row['ghi_chu']) ? trim($row['ghi_chu']) : null,
            'status' => Admission::STATUS_PENDING,
            'created_by' => auth()->user()->user_login ?? 'import_system'
        ];

        return $data;
    }

    private function parseDate($dateString): ?string
    {
        if (empty($dateString)) {
            return null;
        }

        $dateString = trim($dateString);

        try {
            // Handle Excel date serial number first
            if (is_numeric($dateString) && $dateString > 1) {
                // Excel date serial number (days since 1900-01-01)
                // Excel incorrectly treats 1900 as a leap year, so we need to adjust
                if ($dateString > 59) {
                    $dateString = $dateString - 1; // Adjust for Excel's leap year bug
                }

                $excelEpoch = Carbon::create(1900, 1, 1);
                $date = $excelEpoch->addDays($dateString - 1);

                return $date->format('Y-m-d');
            }

            // Try different date formats commonly used in Vietnam
            $formats = [
                'Y-m-d',           // 1998-08-12
                'd/m/Y',           // 12/08/1998 (Vietnamese format)
                'd-m-Y',           // 12-08-1998
                'm/d/Y',           // 08/12/1998 (US format)
                'Y/m/d',           // 1998/08/12
                'd.m.Y',           // 12.08.1998
                'Y.m.d',           // 1998.08.12
                'd/m/y',           // 12/08/98
                'd-m-y',           // 12-08-98
                'm/d/y',           // 08/12/98
                'y/m/d',           // 98/08/12
                'Y-n-j',           // 1998-8-12 (no leading zeros)
                'j/n/Y',           // 12/8/1998 (no leading zeros)
                'j-n-Y',           // 12-8-1998 (no leading zeros)
            ];

            foreach ($formats as $format) {
                try {
                    $date = Carbon::createFromFormat($format, $dateString);
                    if ($date && $date->year >= 1900 && $date->year <= date('Y')) {
                        return $date->format('Y-m-d');
                    }
                } catch (\Exception $e) {
                    // Continue to next format
                    continue;
                }
            }

            // Try Carbon's flexible parsing as last resort
            try {
                $date = Carbon::parse($dateString);
                if ($date && $date->year >= 1900 && $date->year <= date('Y')) {
                    return $date->format('Y-m-d');
                }
            } catch (\Exception $e) {
                // Continue to error
            }

            throw new \Exception("Không thể parse ngày: {$dateString}");
        } catch (\Exception $e) {
            throw new \Exception("Ngày sinh không hợp lệ (cột D): '{$dateString}'. Vui lòng nhập định dạng dd/mm/yyyy (VD: 12/08/1998). Lưu ý: Không click vào ô ngày sinh sau khi nhập để tránh Excel chuyển thành số");
        }
    }

    private function parseGender($genderString): int
    {
        $gender = strtolower(trim($genderString));

        if (in_array($gender, ['nam', 'male', 'm', '1'])) {
            return Admission::GENDER_MALE;
        } elseif (in_array($gender, ['nữ', 'nu', 'female', 'f', '0'])) {
            return Admission::GENDER_FEMALE;
        }

        throw new \Exception("Giới tính không hợp lệ (cột E): '{$genderString}'. Vui lòng nhập 'Nam' hoặc 'Nữ'");
    }

    public function getResults(): array
    {
        return $this->results;
    }

    public function getSuccessCount(): int
    {
        return count(array_filter($this->results, function ($result) {
            return $result['status'] === 'success';
        }));
    }

    public function getErrorCount(): int
    {
        return count(array_filter($this->results, function ($result) {
            return $result['status'] === 'error';
        }));
    }

    public function getErrors(): array
    {
        return array_filter($this->results, function ($result) {
            return $result['status'] === 'error';
        });
    }

    /**
     * Validate required fields with detailed error messages
     */
    private function validateRequiredFields(array $data, array $row): void
    {
        $errors = [];

        // Check required fields
        if (empty($data['user_surname'])) {
            $errors[] = "Thiếu họ (cột A)";
        }

        if (empty($data['user_givenname'])) {
            $errors[] = "Thiếu tên (cột C)";
        }

        if (empty($data['user_birthday'])) {
            $errors[] = "Thiếu ngày sinh (cột D) - Vui lòng nhập định dạng dd/mm/yyyy";
        }

        if ($data['user_gender'] === null) {
            $errors[] = "Thiếu giới tính (cột E) - Vui lòng nhập 'Nam' hoặc 'Nữ'";
        }

        if (empty($data['user_address'])) {
            $errors[] = "Thiếu địa chỉ (cột I)";
        }

        if (empty($data['user_province'])) {
            $errors[] = "Thiếu tỉnh/thành phố (cột J)";
        }

        if (empty($data['training_level'])) {
            $errors[] = "Thiếu bậc đào tạo (cột M) - Vui lòng nhập: cao đẳng, liên thông, hoặc trung cấp";
        }

        if (empty($data['training_program'])) {
            $errors[] = "Thiếu chương trình đào tạo (cột N) - Vui lòng nhập: báo chí, công nghệ kỹ thuật điện điện tử, kế toán doanh nghiệp, hoặc tin học ứng dụng";
        }

        if (empty($data['cultural_level'])) {
            $errors[] = "Thiếu trình độ văn hóa (cột O) - Vui lòng nhập: trung học cơ sở hoặc trung học phổ thông";
        }

        if (empty($data['admission_period'])) {
            $errors[] = "Thiếu thời gian tuyển sinh (cột P)";
        }

        // Check email format if provided
        if (!empty($data['user_email']) && !filter_var($data['user_email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Email không đúng định dạng (cột G)";
        }

        // Check valid values for specific fields
        if (!empty($data['training_level']) && !in_array(strtolower($data['training_level']), ['cao đẳng', 'liên thông', 'trung cấp'])) {
            $errors[] = "Bậc đào tạo không hợp lệ (cột M) - Chỉ chấp nhận: cao đẳng, liên thông, trung cấp";
        }

        if (!empty($data['training_program']) && !in_array(strtolower($data['training_program']), ['báo chí', 'công nghệ kỹ thuật điện điện tử', 'kế toán doanh nghiệp', 'tin học ứng dụng'])) {
            $errors[] = "Chương trình đào tạo không hợp lệ (cột N) - Chỉ chấp nhận: báo chí, công nghệ kỹ thuật điện điện tử, kế toán doanh nghiệp, tin học ứng dụng";
        }

        if (!empty($data['cultural_level']) && !in_array(strtolower($data['cultural_level']), ['trung học cơ sở', 'trung học phổ thông'])) {
            $errors[] = "Trình độ văn hóa không hợp lệ (cột O) - Chỉ chấp nhận: trung học cơ sở, trung học phổ thông";
        }

        if (!empty($errors)) {
            throw new \Exception(implode('; ', $errors));
        }
    }

    /**
     * Check for duplicates in database
     */
    private function checkDuplicates(array $data, array $row): void
    {
        // Check duplicate user_code if provided
        if (!empty($data['user_code'])) {
            $existingCode = Admission::where('user_code', $data['user_code'])->first();
            if ($existingCode) {
                throw new \Exception("Mã tuyển sinh '{$data['user_code']}' đã tồn tại (ID: {$existingCode->id})");
            }
        }

        // Check duplicate email if provided
        if (!empty($data['user_email'])) {
            $existingEmail = Admission::where('user_email', $data['user_email'])->first();
            if ($existingEmail) {
                throw new \Exception("Email '{$data['user_email']}' đã được sử dụng (ID: {$existingEmail->id})");
            }
        }

        // Check potential duplicate person (same name + birthday)
        $duplicatePerson = Admission::where('user_surname', $data['user_surname'])
            ->where('user_givenname', $data['user_givenname'])
            ->where('user_birthday', $data['user_birthday'])
            ->first();

        if ($duplicatePerson) {
            $fullName = $data['user_surname'] . ' ' . ($data['user_middlename'] ? $data['user_middlename'] . ' ' : '') . $data['user_givenname'];
            throw new \Exception("Có thể trùng người: '{$fullName}' sinh ngày {$data['user_birthday']} đã tồn tại (ID: {$duplicatePerson->id})");
        }
    }

    /**
     * Parse database error messages
     */
    private function parseDatabaseError(\Illuminate\Database\QueryException $e, array $row): string
    {
        $errorMessage = $e->getMessage();

        if (strpos($errorMessage, 'user_code') !== false) {
            return "Mã tuyển sinh '{$row['ma_tuyen_sinh']}' đã tồn tại trong hệ thống";
        }

        if (strpos($errorMessage, 'user_email') !== false) {
            return "Email '{$row['email']}' đã được sử dụng cho hồ sơ khác";
        }

        if (strpos($errorMessage, 'Duplicate entry') !== false) {
            return "Dữ liệu bị trùng lặp trong hệ thống";
        }

        return "Lỗi cơ sở dữ liệu: " . $errorMessage;
    }

    /**
     * Format row data for error display
     */
    private function formatRowData(array $row): array
    {
        $hoTen = trim(($row['ho'] ?? '') . ' ' . ($row['ten_dem'] ?? '') . ' ' . ($row['ten'] ?? ''));

        return [
            'ho_ten' => !empty($hoTen) ? $hoTen : 'Chưa nhập họ tên',
            'ma_tuyen_sinh' => !empty($row['ma_tuyen_sinh']) ? $row['ma_tuyen_sinh'] : 'Chưa có mã',
            'email' => !empty($row['email']) ? $row['email'] : 'Chưa có email',
            'ngay_sinh' => !empty($row['ngay_sinh']) ? $row['ngay_sinh'] : 'Chưa nhập',
            'gioi_tinh' => !empty($row['gioi_tinh']) ? $row['gioi_tinh'] : 'Chưa nhập',
            'chuong_trinh' => !empty($row['chuong_trinh_dao_tao']) ? $row['chuong_trinh_dao_tao'] : 'Chưa chọn',
            'dia_chi' => !empty($row['dia_chi']) ? $row['dia_chi'] : 'Chưa nhập',
            'tinh_thanh_pho' => !empty($row['tinh_thanh_pho']) ? $row['tinh_thanh_pho'] : 'Chưa nhập'
        ];
    }
}
