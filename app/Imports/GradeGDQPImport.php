<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\RemembersRowNumber;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use App\Models\Fu\User;
use App\Models\Fu\Subject;
use App\Models\T7\CourseResult;
use App\Models\Dra\StudentSubject;
use App\Models\ChangeSubjectStudent;
use App\Models\Fu\SubjectUpdateAble;
use App\Models\MienGiamTapTrung;
use App\Models\SystemLog;
use Illuminate\Support\Facades\DB;
use App\Models\Fu\Term;
use App\Models\GradeGdqp;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class GradeGDQPImport implements ToModel
{
    use RemembersRowNumber;
    protected $actor;
    /**
    * @param Collection $collection
    */
    public $validate = [];
    public function __construct($actor)
    {
        $this->actor = $actor;
    }

    public function model(array $row)
    {
        $rowIndex = $this->getRowNumber();
        if ($rowIndex > 1) {
            $readRow =  count(explode(";", $row[0])) > 0 ? explode(";", $row[0]) : explode(",", $row[0]);
            if (count($readRow) < 4) {
                $readRow = $row;
            }
            $studentCode = trim($readRow[0]); 
            $studyStatus = trim(Str::upper($readRow[1]));
            $ky = trim($readRow[2]);
            $termName = trim(Str::lower($readRow[3]));

            if ($studyStatus == 'P') {
                $studyStatus = 1;
            } else {
                $studyStatus = 2;
            }

            // bỏ qua dòng dữ liệu rỗng
            if (empty($studentCode) || empty($studyStatus) || empty($ky) || empty($termName)) {
                return;
            }

            //check tồn tại user?
            $user = User::where('user_code', $studentCode)->where('user_level', 3)->first();
            // kiểm tra đã tồn tại user đó trong bảng GradeGdqp chưa
            $CheckIssetGDQP = GradeGdqp::where('student_code', $studentCode)->first();
            // kiểm tra học kỳ có tồn tại hay không
            $CheckIssetTerm = Term::pluck('term_name')->toArray();
            $CheckIssetTermLower = array_map('strtolower', $CheckIssetTerm);
            if (!$user) {
                DB::rollback();
                $this->validate[] = "$rowIndex fail, Sinh viên $studentCode không tồn tại";
                return;
            } elseif ($CheckIssetGDQP) {
                DB::rollback();
                $this->validate[] = "$rowIndex fail, Lỗi $studentCode đã tồn tại.";
                return;
            } elseif (!in_array($termName, $CheckIssetTermLower)) {
                DB::rollback();
                $this->validate[] = "$rowIndex fail, Học kì không tồn tại.";
                return;
            }
         
            DB::beginTransaction();
            try {
                $data = [
                    'student_login' => $user->user_login,
                    'student_code' => $studentCode,
                    'study_status' => $user->study_status,
                    'status' => $studyStatus,
                    'ky' => $ky,
                    'term_name' => $termName,
                ];
                $inSertData = GradeGdqp::insert($data);   
                DB::commit();
            } catch (\Throwable $th) {
                DB::rollback();
                Log::info(["info1", $th]);
                $this->validate[] = "$rowIndex fail, Lỗi khi import.";
            }

        }
    }

    public function getValidate() {
        return $this->validate;
    }
}
