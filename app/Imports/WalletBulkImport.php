<?php

namespace App\Imports;

use App\Models\Fee\StudentWallet;
use App\Models\Fee\WalletTransaction;
use App\Models\Fu\User;
use App\Repositories\Admin\StudentWalletRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class WalletBulkImport implements ToCollection, WithHeadingRow, WithBatchInserts, WithChunkReading
{

    protected $results = [
        'total_rows' => 0,
        'success_count' => 0,
        'error_count' => 0,
        'errors' => [],
        'created_wallets' => [],
        'updated_wallets' => []
    ];

    protected $action; // Chỉ hỗ trợ 'create' - tạo ví hàng loạt

    public function __construct($action = 'create')
    {
        // Chỉ cho phép tạo ví hàng loạt, không cho phép import nạp tiền
        if ($action !== 'create') {
            throw new \InvalidArgumentException('Chỉ hỗ trợ import tạo ví hàng loạt. Nạp tiền vui lòng thực hiện từng sinh viên một.');
        }
        $this->action = $action;
    }

    /**
     * Process the collection of rows
     */
    public function collection(Collection $rows)
    {
        DB::beginTransaction();
        try {
            foreach ($rows as $index => $row) {
                $rowNumber = $index + 2; // +2 vì bắt đầu từ dòng 2 (dòng 1 là header)

                if (!$this->isRowNotEmpty($row)) {
                    continue;
                }

                $this->results['total_rows']++;
                $this->processCreateWallet($row, $rowNumber);
            }

            DB::commit();
            Log::info("Wallet bulk import chunk processed", [
                'action' => $this->action,
                'success_count' => $this->results['success_count'],
                'error_count' => $this->results['error_count']
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Wallet bulk import failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Process wallet creation with enhanced validation
     */
    protected function processCreateWallet($row, $rowNumber)
    {
        try {
            $userCode = strtoupper(trim($row['ma_sinh_vien'] ?? ''));
            $userLogin = trim($row['tai_khoan'] ?? '');

            // Validate required fields
            if (empty($userCode) || empty($userLogin)) {
                $this->addError($rowNumber, 'Mã sinh viên và tài khoản không được để trống');
                return;
            }

            // Check if wallet already exists
            $existingWallet = StudentWallet::where('user_code', $userCode)->first();
            if ($existingWallet) {
                $this->addError($rowNumber, "Sinh viên {$userCode} đã có ví");
                return;
            }

            // Validate user using helper method
            $validation = StudentWalletRepository::validateUserForWallet($userCode, $userLogin);
            if (!$validation['valid']) {
                $this->addError($rowNumber, $validation['message']);
                return;
            }

            $user = $validation['user']; // Use the validated user

            // Create wallet with balance = 0
            $wallet = StudentWallet::create([
                'user_code' => $userCode,
                'user_login' => $userLogin,
                'balance' => 0,
                'is_locked' => false
            ]);

            // Create initial transaction record
            WalletTransaction::create([
                'user_code' => $userCode,
                'user_login' => $userLogin,
                'wallet_type' => 'main',
                'transaction_type' => 'create',
                'amount' => 0,
                'balance_before' => 0,
                'balance_after' => 0,
                'description' => 'Tạo ví sinh viên (Import)',
                'created_by' => Auth::user()->user_login ?? 'system'
            ]);

            $this->results['success_count']++;
            $this->results['created_wallets'][] = $userCode;
        } catch (\Exception $e) {
            $this->addError($rowNumber, 'Lỗi tạo ví: ' . $e->getMessage());
        }
    }



    /**
     * Check if row is not empty
     */
    protected function isRowNotEmpty($row): bool
    {
        // Chỉ kiểm tra mã sinh viên và tài khoản
        return !empty($row['ma_sinh_vien']) || !empty($row['tai_khoan']);
    }

    /**
     * Add error to results
     */
    protected function addError($rowNumber, $message)
    {
        $this->results['error_count']++;
        $this->results['errors'][] = "Dòng {$rowNumber}: {$message}";

        Log::warning("Wallet import error", [
            'row' => $rowNumber,
            'message' => $message,
            'action' => $this->action
        ]);
    }

    /**
     * Get import results
     */
    public function getResults(): array
    {
        return $this->results;
    }



    /**
     * Batch size for processing
     */
    public function batchSize(): int
    {
        return 100;
    }

    /**
     * Chunk size for reading
     */
    public function chunkSize(): int
    {
        return 500;
    }
}
