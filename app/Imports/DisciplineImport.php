<?php

namespace App\Imports;

use App\Models\T7\Discipline;
use Maatwebsite\Excel\Concerns\ToModel;

class DisciplineImport implements ToModel
{
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        return new Discipline([
            //
        ]);
    }

    public function headingRow(): int
    {
        return 2;
    }
}
