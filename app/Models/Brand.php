<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Brand extends Model
{
    protected $table = 'brand';
    public $timestamps = false;
    protected $fillable = ['code', 'name', 'major', 'description', 'major_code', 'specialized_code', 'sub_specialized_code'];
    
    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
