<?php

namespace App\Models\sms;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudentSmsLog extends Model
{
    
    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
    protected $table = 'student_sms_log';
    protected $fillable = ['relation_login', 'action', 'action_detail', 'term_id', 'created_at'];
    public $timestamps = false;
}
