<?php

namespace App\Models\Sms;
use App\Models\Fu\User;
use Illuminate\Database\Eloquent\Model;

class Account extends Model
{
    protected $fillable = ['account','phone','student_login','student_code','student_name','owner_type','owner_name','is_active','telco','balance','eos_date','created_on','notified','zalo_activated','followed','zalo_welcome','del','grade'];
    protected $table = 'account';
    public $timestamps = false;
    public function user()
    {
        return $this->belongsTo(User::class,'student_code', 'user_code');
    }
}