<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MienGiamTapTrung extends Model
{
    protected $table = 'mien_giam_tap_trung';
    const TYPE_THAY_THE = 1;
    const TYPE_MIEN_GIAM = 2;

    protected $fillable = [
        'user_code',
        'student_login',
        'subject_code',
        'skill_code',
        'subject_code_new',
        'skill_code_new',
        'type',
        'term_name',
        'so_quyet_dinh',
        'time_action',
        'user_action',
        'status',
        'term_id',
        'created_by'
    ];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
