<?php

namespace App\Models\EBook;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EBook extends Model
{
    protected $table = 'ebook';
    protected $fillabale = [
        'id',
        'student_code',
        'group_id',
        'group_name',
        'subject_code',
        'book_name',
        'created_at',
        'updated_at',
        'staff',
        'url',
        'code',
        'expired_period',
        'note',

    ];
    public $timestamps = true;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
