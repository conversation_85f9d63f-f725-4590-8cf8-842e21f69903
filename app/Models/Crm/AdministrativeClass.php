<?php

namespace App\Models\Crm;

use App\Models\Dra\Period;
use App\Models\Dra\PeriodSubject;
use Illuminate\Database\Eloquent\Model;
use App\Models\Crm\AdministrativeClassStudent;
use App\Models\Fu\User;
use Illuminate\Support\Facades\DB;

class AdministrativeClass extends Model
{
    protected $table = 'administrative_class';
    protected $fillable = [
        'code',
        'name',
        'curriculum_id',
        'brand_code',
        'num_of_student',
        'num_of_subject',
        'advisor_login',
        'grade_created',
        'created_at',
        'updated_at',
    ];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    public function subjects()
    {
        return $this->hasManyThrough(
            PeriodSubject::class,
            Period::class,
            'curriculum_id',        // Foreign key trên periods (period.curriculum_id)
            'period_id',            // Foreign key trên period_subjects (period_subject.period_id)
            'curriculum_id',        // Local key trên administrative_class (administrative_class.curriculum_id)
            'id'                    // Local key trên periods (period.id)
        )->select(
            'period.id as period_id',
            'period.curriculum_id',
            'period.period_name',
            'period_subject.id',
            'period_subject.period_id',
            'period_subject.subject_code',
            'period_subject.subject_name',
            'period_subject.skill_code',
            'period_subject.number_of_credit',
        );
    }

    public function students()
    {
        return $this->hasManyThrough(
            User::class,
            AdministrativeClassStudent::class,
            'class_id',
            'user_code',
            'id',
            'student_code'
        )->select(
            'user.id',
            'user.user_code as student_code',
            'user.user_login as student_login',
            'user.user_surname',
            'user.user_middlename',
            'user.user_givenname',
            'user.user_email',
            'user.user_address',
            'user.user_telephone',
            'user.study_status',
            'user.study_status_code',
            'user.gender',
        );
    }
}