<?php

namespace App\Models\Crm;

use Illuminate\Database\Eloquent\Model;

class AdministrativeClassStudent extends Model
{
    protected $table = 'administrative_class_member';
    protected $fillable = [
        'class_id',
        'student_code',
        'created_at',
        'created_by',
    ];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }
}