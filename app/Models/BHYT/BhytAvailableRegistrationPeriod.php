<?php

namespace App\Models\BHYT;

use App\Models\Fu\Term;
use Illuminate\Database\Eloquent\Model;

class BhytAvailableRegistrationPeriod extends Model
{

    protected $table = 'bhyt_available_registration_periods';

    protected $fillable = [
        'name',
        'fee',
        'term_id',
        'start_date',
        'end_date',
        'note',
        'created_at',
        'updated_at',
    ];
    public $timestamps = true;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function term()
    {
        return $this->belongsTo(Term::class, 'term_id', 'id');
    }

    public function registers()
    {
        return $this->hasMany(BhytStudentRegister::class, 'available_registration_period_id', 'id');
    }
}
