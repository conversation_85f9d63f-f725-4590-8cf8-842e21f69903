<?php

namespace App\Models\BHYT;


use Illuminate\Database\Eloquent\Model;

class BhytStudentFamilyMember extends Model
{
    protected $table = 'bhyt_student_family_member';
    protected $fillabale = [
        'fullname',
        'gender',
        'dob',
        'cccd',
        'country_code',
        'ethnic_code',
        'telephone',
        'bhxh_code',
        'bc_province_id',
        'bc_district_id',
        'bc_subdistrict_id',
        'household_relationship',
        'bhyt_register_id',
        'order_registation_book',
        'house_holder',
    ];
    public $timestamps = false;


    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
