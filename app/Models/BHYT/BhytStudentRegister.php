<?php

namespace App\Models\BHYT;

use Illuminate\Database\Eloquent\Model;
use App\Models\BHYT\BhytStudentFamilyMember;

class BhytStudentRegister extends Model
{
    /**
     * Trạng thái đăng ký - Chờ thanh toán
     */
    public const STATUS_PENDING_PAYMENT = 0;
    /**
     * Trạng thái đăng ký - <PERSON> toán
     */
    public const STATUS_COMPLETED_PAYMENT = 1;
    /**
     * Trạng thái đăng ký - Đã hủy
     */
    public const STATUS_CANCELLED_PAID_ORDER = 2;

    /**
     * Trạng thái đăng ký - Đã hủy
     */
    public const STATUS_CANCELLED_UNPAID_ORDER = -1;

    protected $table = 'bhyt_student_register';
    protected $fillabale = [
        'fullname',
        'student_code',
        'dob',
        'cccd',
        'gender',
        'available_registration_period_id',
        'telephone',
        'address',
        'bhxh_code',
        'nearest_time_bhyt',
        'ethnic_code',
        'country_code',
        'household_relationship',
        'bc_province_id',
        'bc_district_id',
        'bc_subdistrict_id',
        'location_living_province_id',
        'location_living_district_id',
        'location_living_subdistrict_id',
        'attachment',
        'fee',
        'status',
        'created_at',
        'updated_at',
    ];
    public $timestamps = true;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function members()
    {
        return $this->hasMany(BhytStudentFamilyMember::class, 'bhyt_register_id', 'id');
    }

    public function availableRegistrationPeriod()
    {
        return $this->belongsTo(BhytAvailableRegistrationPeriod::class, 'available_registration_period_id', 'id');
    }
}
