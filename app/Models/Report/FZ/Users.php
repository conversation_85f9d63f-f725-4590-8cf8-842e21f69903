<?php

namespace App\Models\Report\FZ;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Users extends Model
{
    use HasFactory;
    
    protected $table = 'fz_users';
    public $timestamps = true;
    protected $fillable = ['user_login' , 'student_code' , 'family_name' , 'middle_name' , 'given_name' , 'dob' , 'email' , 'address' , 'cccd_number' , 'cccd_date_of_issue' , 'cccd_place_of_issue' , 'ethnic_group' , 'gender' , 'admission_date' , 'admission_campus_code' , 'admission_course' , 'campaign'];
}
