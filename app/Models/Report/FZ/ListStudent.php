<?php

namespace App\Models\Report\FZ;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Fu\OnlineServices\OnlineService;
use App\Models\Fee\FeeStudyBySemester;
use App\Models\T7\CourseResult;
use App\Models\Fu\ServiceLog;
use App\Models\Fu\User;
use App\Models\Transcript;

class ListStudent extends Model
{
    use HasFactory;
    
    protected $table = 'fz_list_student';
    public $timestamps = true;
    protected $fillable = ['student_code', 'semester', 'admission_id', 'major_id', 'status', 'term_id'];

    public function fee_study_by_semester()
    {
        return $this->hasMany(FeeStudyBySemester::class, 'user_code', 'student_code');
    }

    public function course_result()
    {
        return $this->hasMany(CourseResult::class, 'student_code', 'student_code');
    }

    public function service_log()
    {
        return $this->hasMany(ServiceLog::class, 'user_code', 'student_code');
    }

    public function users() {
        return $this->hasMany(User::class, 'user_code', 'student_code');
    }

    public function transcript() {
        return $this->hasMany(Transcript::class, 'user_code', 'student_code');
    }
}
