<?php

namespace App\Models\Report;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Fu_3_PartiesConfirmation_Detail extends Model
{
    use HasFactory;
    public $connection = 'ap_report';
    protected $table = 'fu_3_parties_confirmation_detail';
    public $timestamps = true;
    protected $fillable = ['id', 'fu_3_parties_confirmation_id', 'file_id', 'trainning', 'student_affairs', 'administrative', 'trainning_file_id', 'student_affairs_file_id', 'administrative_file_id', 'statistic', 'created_by', 'created_at', 'updated_at'];

    public function file() {
        return $this->hasOne(File::class, 'id', 'file_id');
    }

    public function fu_3_PartiesConfirmation() {
        return $this->hasOne(Fu_3_PartiesConfirmation::class, 'id', 'fu_3_parties_confirmation_id');
    }
}
