<?php

namespace App\Models\Report;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Fu_3_PartiesConfirmation extends Model
{
    use HasFactory;
    public $connection = 'ap_report';
    protected $table = 'fu_3_parties_confirmation';
    public $timestamps = true;
    protected $fillable = ['id', 'name', 'term_id', 'campus_code', 'status'];

    public function fu_3_parties_confirmation_detail() {
        return $this->hasMany(Fu_3_PartiesConfirmation_Detail::class, 'fu_3_parties_confirmation_id', 'id');
    }
}
