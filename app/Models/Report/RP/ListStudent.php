<?php

namespace App\Models\Report\RP;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\Models\Fu\OnlineServices\OnlineService;
use App\Models\Fee\FeeStudyBySemester;
use App\Models\T7\CourseResult;
use App\Models\Fu\ServiceLog;
use App\Models\Fu\User;
use App\Models\Transcript;

class ListStudent extends Model
{
    public $connection = 'ap_report';
    protected $table = 'rp_list_student';
    public $timestamps = true;
    protected $fillable = ['student_code', 'semester', 'admission_id', 'major_id', 'status', 'term_id', 'campus_code'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    public static function statusStatistic($campus_code = null){
        $list_focus_status = ["DH","HL","TN","BH","CXL","CTN","TNG"];
        $raw_data_query = self::query();
        if($campus_code != null){
            $raw_data_query->where('campus_code', $campus_code);
        }
        $raw_data = $raw_data_query->select('status', DB::raw('count(*) as total'))
            ->groupBy('status')
            ->get();
        $statistic_data = (object)[
            "DH" => 0, "HL" => 0, "TN" => 0, "BH" => 0, "CXL" => 0, "CTN" => 0, "TNG" => 0
        ];
        $others = 0;
        foreach ($raw_data as $status) {
            if (in_array($status->status, $list_focus_status)) {
                $statistic_data->{$status->status} = $status->total;
            }else{
                $others += $status->total;
            }
        }
        $statistic_data->OTHERS = $others;
        return $statistic_data;
    }

    public function fee_study_by_semester()
    {
        return $this->hasMany(FeeStudyBySemester::class, 'user_code', 'student_code');
    }

    public function course_result() {
        return $this->hasMany(CourseResult::class, 'student_code', 'student_code');
    }

    public function service_log()
    {
        return $this->hasMany(ServiceLog::class, 'user_code', 'student_code');
    }

    public function users() {
        return $this->hasMany(User::class, 'user_code', 'student_code');
    }

    public function transcript() {
        return $this->hasMany(Transcript::class, 'user_code', 'student_code');
    }
}
