<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GroupGraduate extends Model
{
    protected $table = 'group_graduate';
    protected $fillable = [
        'id', 
        'group_id', 
        'activity_id',
        'group_name', 
        'student_login', 
        'lastmodifier_login', 
        'date_graduate', 
        'psubject_code', 
        'psubject_name', 
        'term_id', 
        'course_session',
        'status',
        'reason_fail'
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }
}
