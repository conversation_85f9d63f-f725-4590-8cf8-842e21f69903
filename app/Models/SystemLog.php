<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Fu\Group;

class SystemLog extends Model
{
    protected $table = 'system_log';
    protected $fillable = ['object_name','actor','log_time','action','description','object_id','brief','from_ip','relation_login','relation_id','nganh_cu','nganh_moi','ky_chuyen_den','ky_thu_cu'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
    public function group()
    {
        return $this->belongsTo(Group::class, 'object_id', 'id');
    }
}
