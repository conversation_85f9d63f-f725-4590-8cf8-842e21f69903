<?php

namespace App\Models\Ho;

use Illuminate\Database\Eloquent\Model;

class TranscriptDetailHo extends Model
{
    /**
     * type - 0: b<PERSON><PERSON> thườ<PERSON>, 1: mi<PERSON><PERSON> g<PERSON>, 2:thay thế, 3: môn thự<PERSON> tập, 4: môn tương đương
     * status - 0: ch<PERSON><PERSON>, 1: đ<PERSON><PERSON>, -1: tr<PERSON><PERSON><PERSON> điể<PERSON> danh
     */
    protected $table = 'transcript_details';
    public $timestamps = true;
    
    protected $fillable = [
        'transcript_id',
        'subject_code',
        'subject_name',
        'subject_id',
        'skill_code',
        'type',
        'status',
        'num_of_credit',
        'period_subject_id',
        'ki_thu',
        'created_at',
        'updated_at',
    ];

    public function transcript()
    {
        return $this->belongsTo('App\Models\Ho\TranscriptHo', 'transcript_id', 'id');
    }
}
