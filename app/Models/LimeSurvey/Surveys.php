<?php

namespace App\Models\LimeSurvey;

use Illuminate\Database\Eloquent\Model;

class Surveys extends Model
{
    protected $table = 'surveys';
    
    public $timestamps = false;
    protected $fillable = ['sid', 'owner_id', 'gsid', 'admin', 'active', 'expires', 'startdate', 'adminemail', 'anonymized', 'format', 'savetimings', 'template', 'language', 'additional_languages', 'datestamp', 'usecookie', 'allowregister', 'allowsave', 'autonumber_start', 'autoredirect', 'allowprev', 'printanswers', 'ipaddr', 'ipanonymize', 'refurl', 'datecreated', 'showsurveypolicynotice', 'publicstatistics', 'publicgraphs', 'listpublic', 'htmlemail', 'sendconfirmation', 'tokenanswerspersistence', 'assessments', 'usecaptcha', 'usetokens', 'bounce_email', 'attributedescriptions', 'emailresponseto', 'emailnotificationto', 'tokenlength', 'showxquestions', 'showgroupinfo', 'shownoanswer', 'showqnumcode', 'bouncetime', 'bounceprocessing', 'bounceaccounttype', 'bounceaccounthost', 'bounceaccountpass', 'bounceaccountencryption', 'bounceaccountuser', 'showwelcome', 'showprogress', 'questionindex', 'navigationdelay', 'nokeyboard', 'alloweditaftercompletion', 'googleanalyticsstyle', 'googleanalyticsapikey', 'tokenencryptionoptions'];
}