<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class RelearnOnlineCalendar extends Model
{
    protected $table = 'relearn_online_calendar';
    protected $fillable = ['group_id','term_name','retest_plan_id','campus_code','room_name','area','skill_code','subject_id','subject_code','subject_name','type','created_by','course_id','syllabus_id','slot','start_day','created_by','max_user','required_point','status','activity_id','teacher','updated_by'];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function room()
    {
        return $this->belongsTo('App\Models\Fu\Room','room_name','id');
    }

    public function areaInfo()
    {
        return $this->belongsTo('App\Models\Fu\Area','area','id');
    }

    public function slotInfo()
    {
        return $this->belongsTo('App\Models\Fu\Slot','slot','id');
    }

    public function statusDetail()
    {
        $status = $this->status;
        $start_day = Carbon::createFromDate($this->start_day);
        $next_day = Carbon::createFromDate($this->start_day)->addDay();
        $today = now();
        if ($today->lessThan($start_day)) {
            return '<span class="kt-font-warning kt-font-boldest">Chưa thi</span>';
        } else if ($today->greaterThanOrEqualTo($start_day) && $today->lessThan($next_day))
        {
            return '<span class="kt-font-danger kt-font-boldest">Thi hôm nay</span>';
        } else {
            return '<span class="kt-font-success kt-font-boldest">Đã kết thúc</span>';
        }
    }

    public function startDayDetail()
    {
        return Carbon::createFromDate($this->start_day)->format('Y-m-d');
    }

    public function calendarUser()
    {
        return $this->hasMany('App\Models\RelearnOnlineCalendarUser','calendar_id','id');
    }
}
