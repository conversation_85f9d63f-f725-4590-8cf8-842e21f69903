<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;

class TuitionPeriodSubject extends Model
{
    const SUBJECT_DEBT_STATUS_PENDING = 0;      // Chưa thanh toán
    const SUBJECT_DEBT_STATUS_PAID = 1;         // Đã thanh toán
    const SUBJECT_DEBT_STATUS_CANCEL = -1;      // Đã huỷ

    protected $table = 'fee_tuition_period_subject';
    public $timestamps = false;
    protected $fillable = [
        'tuition_period_id',
        'student_code',
        'subject_code',
        'amount',
        'curriculum_id',
        'subject_name',
        'created_at',
    ];
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

}
