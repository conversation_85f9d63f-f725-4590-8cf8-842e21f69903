<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
    public const TYPE_HP = 'HP';
    protected $table = 'fee_transactions';
    protected $fillable = ['user_code','subject_code','skill_code','type','type_extension','invoice_id','note','amount','execute','term_name','created_by','user_login','service_log_id','in_out','invoice_date_create','dng_id','dng_transaction_id', 'balance', 'term_id'];
    //

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function service()
    {
        return $this->belongsTo('App\Models\Fu\Service','type','id');
    }
}
