<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model as Model;

class FeeCreatingRequest extends Model
{
    protected $connection = 'fee_creating_request';
    protected $collection;
    public function __construct(array $attributes = [])
    {
        $this->collection = session('campus_db');
        parent::__construct($attributes);
    }
    protected $fillable = ['student_code', 'campus_db', 'item_id', 'service_code', 'service_type', 'student_name', 'email', 'status', 'created_at', 'updated_at','details','cancellation_detail'];

    public function setCollection($campus_db){
        $this->collection = $campus_db;
    }

    /**
     * const status trạng thái của request
     * 0: pending - chờ xử lý - default (W)
     */
    public const STATUS_PENDING = 0;

    /**
     * const status trạng thái của request
     * 1: processing - đang xử lý
     */
    public const STATUS_PROCESSING = 1;

    /**
     * const status trạng thái của request
     * 2: success - tạo công nợ thành công (R)
     */
    public const STATUS_SUCCESS = 2;

    /**
     * const status trạng thái của request
     * 3: paid - đã thanh toán công nợ (R)
     */
    public const STATUS_PAID = 3;

    /**
     * const status trạng thái của request
     * 4: completed - đã thanh toán, đã xử lý (U)
     */
    public const STATUS_COMPLETED = 4;

    /**
     * const status trạng thái của request
     * -1: fail - tạo công nợ thất bại
     */
    public const STATUS_FAIL = -1;

    /**
     * const status trạng thái của request
     * -2: fail - huỷ công nợ (U)
     */
    public const STATUS_CANCELED = -2;
    /**
     * const status trạng thái của request
     * -3: pending - yêu cầu huỷ công nợ (U)
     */
    public const STATUS_REQUEST_CANCELED = -3;
}
