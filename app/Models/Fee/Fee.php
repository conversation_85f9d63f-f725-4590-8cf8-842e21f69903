<?php

namespace App\Models\Fee;

use App\Models\Fu\Term;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class Fee extends Model
{
    protected $table = 'fees';
    protected $fillable = ['user_login','user_code','study_status','curriculum_id','brand_code','fee_plan_id','version','vuot_ky','ki_thu','cash_out','cash_in','study_wallet','relearn_wallet','etc_wallet','promotion_wallet','mien_giam','bo_sung','pf_transfer'];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function details()
    {
        return $this->hasMany('App\Models\Fee\FeeDetail', 'fee_id');
    }

    public function user()
    {
        return $this->belongsTo('App\User', 'user_code', 'user_code');
    }

    public function logs()
    {
        return$this->hasMany('App\Models\Fee\FeeLog','fee_id');
    }

    public function save(array $attributes = [], array $options = [])
    {
        // write all to log
        if ($this->id == null) {
            Log::channel('fee-daily')->info('Create wallet for ' . ($this->user_login ?? ""));
        } else {
            Log::channel('fee-daily')->info('Update wallet for ' . ($this->user_login ?? "") . ": [study_wallet: $this->study_wallet] [relearn_wallet: $this->relearn_wallet] [etc_wallet: $this->etc_wallet] [promotion_wallet: $this->promotion_wallet] " );
        }

        // save the fee
        return parent::save();
    }
    
    public static function getListUserOverSemester($termId)
    {
        $lastTerm = Term::where('id', '!=', $termId)
        ->orderBy('id', 'desc')
        ->first();
        $listUserOver = FeeMail::select([
            'user_login',
            'user_code',
            'term_id',
            'term_name',
            'vuot_ky',
        ])
        ->where('vuot_ky', 1)
        ->where('term_id', $lastTerm->id)
        ->get();
        
        $res = [];
        foreach ($listUserOver as $user) {
            $res[] = $user->user_code;
        }
        
        return $res;
    }
}
