<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;

class TuitionPeriodStudent extends Model
{
    const STUDENT_DEBT_STATUS_PENDING = 0;      // Chờ thanh toán
    const STUDENT_DEBT_STATUS_PAID = 1;         // Đ<PERSON> hoàn thành
    const STUDENT_DEBT_STATUS_CANCEL = -1;      // Đã huỷ
    
    protected $table = 'fee_tuition_period_student';
    public $timestamps = false;
    protected $fillable = [
        'fee_tuition_period_id',
        'student_code',
        'kithu',
        'fee_type',
        'status',
        'created_at'
    ];
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }
    public function subjects()
    {
        return $this->hasMany(TuitionPeriodSubject::class, 'student_code', 'student_code');
    }
}
