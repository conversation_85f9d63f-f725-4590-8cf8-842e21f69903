<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;

class Plan extends Model
{
    protected $table = 'fee_plans';
    protected $fillable = [
        'curriculum_id',
        'brand_code',
        'term_id',
        'term_name',
        'version'
    ];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function details()
    {
        return $this->hasMany('App\Models\Fee\PlanDetail', 'fee_plan_id');
    }
}
