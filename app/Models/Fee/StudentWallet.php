<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;

class StudentWallet extends Model
{
    protected $table = 'student_wallets';

    protected $fillable = [
        'user_code',
        'user_login',
        'balance',
        'is_locked',
        'lock_reason'
    ];

    public function transactions()
    {
        return $this->hasMany(WalletTransaction::class, 'user_code', 'user_code');
    }

    public function user()
    {
        return $this->belongsTo(\App\Models\Fu\User::class, 'user_code', 'user_code');
    }

    /**
     * Get formatted balance
     */
    public function getFormattedBalanceAttribute()
    {
        return number_format($this->balance, 0, ',', '.') . ' VNĐ';
    }

    /**
     * Check if wallet has sufficient balance
     */
    public function hasSufficientBalance($amount)
    {
        return $this->balance >= $amount;
    }

    /**
     * Add money to wallet
     */
    public function addMoney($amount, $description = 'Nạp tiền', $createdBy = 'system', $paymentMethod = null, $invoiceId = null)
    {
        $balanceBefore = $this->balance;
        $this->balance += $amount;
        $this->save();

        // Create transaction record
        WalletTransaction::create([
            'user_code' => $this->user_code,
            'user_login' => $this->user_login,
            'wallet_type' => 'main',
            'transaction_type' => 'deposit',
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $this->balance,
            'payment_method' => $paymentMethod,
            'invoice_id' => $invoiceId,
            'description' => $description,
            'created_by' => $createdBy
        ]);

        // Clear wallet statistics cache
        $this->clearWalletStatisticsCache();

        return $this;
    }

    /**
     * Deduct money from wallet
     */
    public function deductMoney($amount, $description = 'Thanh toán', $createdBy = 'system')
    {
        if (!$this->hasSufficientBalance($amount)) {
            throw new \Exception('Số dư không đủ');
        }

        $balanceBefore = $this->balance;
        $this->balance -= $amount;
        $this->save();

        // Create transaction record
        WalletTransaction::create([
            'user_code' => $this->user_code,
            'user_login' => $this->user_login,
            'wallet_type' => 'main',
            'transaction_type' => 'payment',
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $this->balance,
            'payment_method' => 'wallet',
            'description' => $description,
            'created_by' => $createdBy
        ]);

        // Clear wallet statistics cache
        $this->clearWalletStatisticsCache();

        return $this;
    }

    /**
     * Clear wallet statistics cache
     */
    public function clearWalletStatisticsCache()
    {
        // Clear cache patterns for wallet statistics
        cache()->forget('wallet_statistics_' . md5(serialize([])));

        // For production, you might want to use a more sophisticated cache tagging system
        // For now, we'll just clear the basic cache
    }
}
