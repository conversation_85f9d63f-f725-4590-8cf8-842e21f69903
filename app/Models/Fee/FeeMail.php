<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;

class FeeMail extends Model
{
    protected $table = 'fee_mails';
    const TYPE_STUDENT_HDI = 0;
    const TYPE_STUDENT_CHANGE_BRAND = 1;
    const TYPE_STUDENT_NHTL = 2;
    const TYPE_STUDENT_HL = 3;
    const TYPE_STUDENT_THO = 4;
    const TYPE_STUDENT_TNG = 5;
    const TYPE_STUDENT_BB1 = 6;
    const TYPE_STUDENT_BB2 = 7;
    const TYPE_STUDENT_BL = 8;
    const TYPE_STUDENT_CCS = 9;

    const STATUS_ENGLISH_NO_PROCES = 0;
    const STATUS_ENGLISH_PROCES_HDI = 1;
    const STATUS_ENGLISH_PROCES_HDI_UNKNOWN = 2;
    const STATUS_ENGLISH_PROCES_RELEARN_50 = 3;
    const STATUS_ENGLISH_PROCES_RELEARN_100 = 4;
    const STATUS_ENGLISH_PROCES_IMPROVE = 5;
    const STATUS_ENGLISH_PROCES_REFUND = 6;
    const STATUS_ENGLISH_PROCES_REFUND_BUT_LEARN = 7;

    // Danh sách loại sinh viên
    const LIST_TYPE_STUDENT = [
        self::TYPE_STUDENT_HDI => 'Học đi',
        self::TYPE_STUDENT_CHANGE_BRAND => 'Chuyển Ngành',
        self::TYPE_STUDENT_NHTL => 'Nhập học trở lại',
        self::TYPE_STUDENT_HL => 'Học Lại',
        self::TYPE_STUDENT_THO => 'Thôi Học',
        self::TYPE_STUDENT_TNG => 'Tốt nghiệp',
        self::TYPE_STUDENT_BB1 => 'Kỷ luật',
        self::TYPE_STUDENT_BB2 => 'Chờ Tốt nghiệp',
        self::TYPE_STUDENT_BL => 'Bảo Lưu',
        self::TYPE_STUDENT_CCS => 'Chuyển cơ sở',
    ];

    // Danh sách các trạng thái để set loại sinh viên
    const LIST_TYPE_STUDENT_CONDITION = [
        self::TYPE_STUDENT_HDI => [1],
        self::TYPE_STUDENT_HL => [10,16,17,11],
        self::TYPE_STUDENT_THO => [4],
        self::TYPE_STUDENT_TNG => [8],
        self::TYPE_STUDENT_BB1 => [12],
        self::TYPE_STUDENT_BB2 => [13],
        self::TYPE_STUDENT_BL => [3],
        self::TYPE_STUDENT_CCS => [5],
    ];

    // Danh sách Các trạng thái Tiếng anh
    const LIST_STATUS_ENGLISH= [
        self::STATUS_ENGLISH_NO_PROCES => 'Chưa xử lý',
        self::STATUS_ENGLISH_PROCES_HDI => 'Đã xử lý - Trừ học đi',
        self::STATUS_ENGLISH_PROCES_HDI_UNKNOWN => 'Đã xử lý - Trừ học đi (không xác định)',
        self::STATUS_ENGLISH_PROCES_RELEARN_50 => 'Đã xử lý - Trừ học lại 50%',
        self::STATUS_ENGLISH_PROCES_RELEARN_100 => 'Đã xử lý - Trừ học lại 100%',
        self::STATUS_ENGLISH_PROCES_IMPROVE => 'Đã xử lý - Trừ học cải thiện',
        self::STATUS_ENGLISH_PROCES_REFUND => 'Đã xử lý - Đã hồi phí',
        self::STATUS_ENGLISH_PROCES_REFUND_BUT_LEARN => 'Đã xử lý - Đã hồi phí (được xếp lớp)',
    ];

    protected $fillable = [
        'user_login',
        'user_code',
        'term_id',
        'term_name',
        'brand_code',
        'ki_thu',
        'amount',
        'hoc_ky',
        'tien_sach',
        'tieng_anh',
        'version',
        'status',
        'status_fee',
        'english_level',
        'last_english_status',
        'english_status',
        'note',
        'study_status',
        'type_student',
        'is_locked',
        'vuot_ky',
        'discount_english'
    ];



    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function user()
    {
        return $this->belongsTo('App\User', 'user_login', 'user_login');
    }
}
