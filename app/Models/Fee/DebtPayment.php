<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;

class DebtPayment extends Model
{
    protected $table = 'debt_payments';

    protected $fillable = [
        'debt_id',
        'user_code',
        'user_login',
        'amount',
        'payment_method',
        'transaction_id',
        'invoice_id',
        'note',
        'created_by'
    ];

    public function debt()
    {
        return $this->belongsTo(StudentDebt::class, 'debt_id');
    }
}
