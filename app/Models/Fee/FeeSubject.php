<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;
use App\Models\Fee\FeeSubjectLog;

class FeeSubject extends Model
{
    protected $table = 'fee_subject';
    protected $fillable = [
        'subject_id',
        'subject_code',
        'subject_name',
        'tuition_fee',
        'relearn_fee',
        'retest_fee',
        'curriculum_id',
        'term_id',
        'detail',
        'status',
        'created_at',
        'updated_at',
        'created_by',
    ];

    /**
     * Get logs for this subject fee
     */
    public function logs()
    {
        return $this->hasMany(FeeSubjectLog::class, 'fee_subject_id');
    }
}
