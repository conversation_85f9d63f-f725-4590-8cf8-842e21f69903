<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class FeeStudyBySemester extends Model
{
    use HasFactory;

    protected $table = 'fee_study_by_semester';

    protected $fillable = ['user_code', 'term_id', 'completed', 'last_modifier'];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
