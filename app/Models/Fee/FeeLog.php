<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;

class FeeLog extends Model
{
    protected $table = 'fee_logs';
    protected $fillable = ['fee_plan_id','brand_code','ki_thu','type_fee','amount','discount','term_id','term_name','version','fee_id','mien_giam','bo_sung'];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
