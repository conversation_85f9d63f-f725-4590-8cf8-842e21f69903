<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;

class TuitionPeriod extends Model
{
    const PERIOD_STATUS_PENDING = 0;            // Nháp
    const PERIOD_STATUS_IN_PROGRESS = 1;        // <PERSON><PERSON>, đang triển khai
    const PERIOD_STATUS_CLOSE = 2;              // Đ<PERSON> đóng đợt
    const PERIOD_STATUS_CANCEL = -1;            // Đã huỷ đợt

    protected $table = 'fee_tuition_period';
    protected $fillable = [
        'name',
        'term_id',
        'khoa_nhap_hoc',
        'brand_code',
        'start_date',
        'end_date',
        'status',
        'created_by',
        'approved_by',
        'description',
        'created_at',
        'updated_at'
    ];
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    public function paidStudents()
    {
        return $this->hasMany(TuitionPeriodStudent::class, 'tuition_period_id', 'id')->where('status', 1);
    }

    public function pendingStudents()
    {
        return $this->hasMany(TuitionPeriodStudent::class, 'tuition_period_id', 'id')->where('status', 0);
    }
}
