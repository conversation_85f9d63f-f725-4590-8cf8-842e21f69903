<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;

class WalletTransaction extends Model
{
    protected $table = 'wallet_transactions';

    protected $fillable = [
        'user_code',
        'user_login',
        'wallet_type',
        'transaction_type',
        'amount',
        'balance_before',
        'balance_after',
        'reference_id',
        'payment_method',
        'invoice_id',
        'description',
        'created_by'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'balance_before' => 'decimal:2',
        'balance_after' => 'decimal:2'
    ];

    public function wallet()
    {
        return $this->belongsTo(StudentWallet::class, 'user_code', 'user_code');
    }
}
