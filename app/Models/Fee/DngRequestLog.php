<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;

class DngRequestLog extends Model
{
    protected $table = 'fee_dng_request_logs';
    protected $fillable = ['ItemId','CampusCode','CheckSum','LogContent','IsChecked','data'];
    public $timestamps = true;
    
    // public function __construct(array $attributes = [])
    // {
    //     
    //     parent::__construct($attributes);
    // }
}