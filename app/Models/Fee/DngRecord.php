<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;

class DngRecord extends Model
{
    protected $table = 'fee_dng_records';
    protected $fillable = ['vat', 'check_sum', 'dng_type', 'item_id', 'dng_id', 'user_login', 'student_code', 'amount', 'service_code', 'transaction_id', 'is_paid', 'paid_at', 'paid_chanel', 'client_transaction_id', 'wallet_type', 'service_amount', 'note', 'has_account'];
    public $timestamps = true;

    /**
     * dng_type for KHAC process - array
     */
    public const DNG_TYPE_KHAC_PROCESS = ['KHAC', 'CN', 'GDQP', 'XNSV', 'BHYT','NHTL','TSV'];

    /**
     * dng_type for RELEARN process - array
     */
    public const DNG_TYPE_RELEARN_PROCESS = ['HL', 'PTL'];

    /**
     * dng_type for STUDY process - array
     */
    public const DNG_TYPE_STUDY_PROCESS = ['HP', 'HPTA', 'HPTU', 'HPDH', 'HP6', 'HP3', 'HP9', 'HPTA2', 'HPTBS'];

    /**
     * dng_type for BHYT process - array
     */
    public const DNG_TYPE_BHYT_PROCESS = ['BHYT'];

    /**
     * has_account
     * 1: đã có tài khoản ví khi tạo công nợ
     */
    public const HAS_ACCOUNT = 1;
    /**
     * has_account
     * 0: chưa có tài khoản ví khi tạo công nợ
     */
    public const NOT_HAS_ACCOUNT = 0;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
    public function dng_invoice()
    {
        return $this->hasOne(DngInvoice::class, 'ItemId', 'item_id');
    }
}
