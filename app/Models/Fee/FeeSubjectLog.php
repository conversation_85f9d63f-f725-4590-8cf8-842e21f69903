<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;

class FeeSubjectLog extends Model
{
    protected $table = 'fee_subject_logs';
    protected $fillable = [
        'fee_subject_id',
        'subject_code',
        'subject_name',
        'credits',
        'old_amount',
        'new_amount',
        'brand_code',
        'curriculum_id',
        'old_version',
        'new_version',
        'user_id',
        'user_name',
        'note'
    ];

    /**
     * Get the subject fee that this log belongs to
     */
    public function feeSubject()
    {
        return $this->belongsTo(FeeSubject::class, 'fee_subject_id');
    }
}
