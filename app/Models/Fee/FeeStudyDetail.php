<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FeeStudyDetail extends Model
{
    protected $table = 'fee_study_details';

    public const STATUS_NONE = 0;
    /**
     * const học phí dự thu đã push dng thành công
     */
    public const STATUS_PUSHED_DNG_SUCCESS = 1;
    /**
     * const học phí dự thu push dng thất bại
     */
    public const STATUS_PUSHED_DNG_FAILED = -1;
    /**
     * const học phí dự thu đã gửi mail thành công
     */
    public const STATUS_SENT_MAIL = 2;
    /**
     * const học phí dự thu gửi mail thất bại
     */
    public const STATUS_FAILED_MAIL = -2;

    /**
     * const học phí dự thu đã thanh toán
     */
    public const STATUS_PAID = 10;
    /**
     * const hủy dự thu học phí
     */
    public const STATUS_CANCEL = -10;

    /**
     * const học phí dự kiến
     */
    public const TYPE_STUDY_FEE = 'HP';

    /**
     * const học phí dự kiến bổ sung
     */
    public const TYPE_ADDITIONAL_STUDY_FEE = 'HPTBS';


    protected $fillable = [
        'fee_study_session_id',
        'fee_study_plan_id',
        'user_code',
        'grade_create',
        'curriculum_id',
        'semester',
        'english_level',
        'english_fee',
        'study_material_fee',
        'scholarship_fee',
        'examption_fee',
        'additional_fee',
        'transferred_subject_fee',
        'laptop_support_fee',
        'current_balance',
        'study_fee',
        'final_amount',
        'status'
    ];

    public $timestamps = true;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
