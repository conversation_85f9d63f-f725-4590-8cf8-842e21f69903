<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;
use App\Models\Fu\Term;
use App\Traits\LogsDebtActivity;

class StudentDebt extends Model
{
    use LogsDebtActivity;
    protected $table = 'student_debts';

    // Status constants
    const STATUS_PENDING = 0;      // Chờ thanh toán
    const STATUS_PAID = 1;         // Đã thanh toán đủ
    const STATUS_PARTIAL_PAID = 2; // Đã thanh toán một phần
    const STATUS_CANCELLED = 3;    // Đã hủy

    protected $fillable = [
        'user_code',
        'user_login',
        'term_id',
        'term_name',
        'fee_type_id',
        'fee_type_code',
        'original_amount',
        'discount_amount',
        'discount_percentage',
        'discount_reason',
        'amount',
        'paid_amount',
        'status',
        'description',
        'created_by',
        'canceled_by',
        'canceled_at',
        'tuition_period_subject_id',
        'subject_id',
        'subject_code',
        'subject_name',
    ];

    public function term()
    {
        return $this->belongsTo(Term::class, 'term_id');
    }

    public function feeType()
    {
        return $this->belongsTo(FeeType::class, 'fee_type_id');
    }

    public function payments()
    {
        return $this->hasMany(DebtPayment::class, 'debt_id');
    }

    public function fee()
    {
        return $this->belongsTo(Fee::class, 'fee_id');
    }

    /**
     * Lấy tên loại phí chi tiết
     */
    public function getTypeFeeNameAttribute()
    {
        $typeNames = [
            1 => 'Phí học kỳ',
            2 => 'Phí sách',
            3 => 'Tiếng anh cơ bản',
            4 => 'Tiếng anh nâng cao',
            5 => 'Bổ sung'
        ];

        return $typeNames[$this->type_fee] ?? 'Không xác định';
    }

    /**
     * Tính toán số tiền sau discount
     */
    public function calculateFinalAmount()
    {
        if ($this->discount_percentage > 0) {
            return $this->original_amount * (1 - $this->discount_percentage / 100);
        }

        return $this->original_amount - $this->discount_amount;
    }

    /**
     * Check if debt can be paid
     */
    public function canBePaid()
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_PARTIAL_PAID]);
    }

    /**
     * Get remaining amount to be paid
     */
    public function getRemainingAmountAttribute()
    {
        return max(0, $this->amount - $this->paid_amount);
    }

    /**
     * Get status name
     */
    public function getStatusNameAttribute()
    {
        $statusNames = [
            self::STATUS_PENDING => 'Chờ thanh toán',
            self::STATUS_PAID => 'Đã thanh toán',
            self::STATUS_PARTIAL_PAID => 'Thanh toán một phần',
            self::STATUS_CANCELLED => 'Đã hủy'
        ];

        return $statusNames[$this->status] ?? 'Không xác định';
    }

    /**
     * Get status badge class for UI
     */
    public function getStatusBadgeClassAttribute()
    {
        $badgeClasses = [
            self::STATUS_PENDING => 'kt-badge--warning',
            self::STATUS_PAID => 'kt-badge--success',
            self::STATUS_PARTIAL_PAID => 'kt-badge--info',
            self::STATUS_CANCELLED => 'kt-badge--danger'
        ];

        return $badgeClasses[$this->status] ?? 'kt-badge--secondary';
    }

    /**
     * Kiểm tra có discount không
     */
    public function hasDiscount()
    {
        return $this->discount_amount > 0 || $this->discount_percentage > 0;
    }



    /**
     * Kiểm tra đã thanh toán đủ chưa
     */
    public function isFullyPaid()
    {
        return $this->paid_amount >= $this->amount;
    }

    /**
     * Lấy phần trăm đã thanh toán
     */
    public function getPaidPercentageAttribute()
    {
        if ($this->amount <= 0) {
            return 0;
        }

        return round(($this->paid_amount / $this->amount) * 100, 2);
    }

    /**
     * Lấy số tiền discount formatted
     */
    public function getFormattedDiscountAmountAttribute()
    {
        if (!$this->hasDiscount()) {
            return null;
        }

        return number_format($this->discount_amount, 0, ',', '.') . ' VNĐ';
    }

    /**
     * Lấy số tiền gốc formatted
     */
    public function getFormattedOriginalAmountAttribute()
    {
        return number_format($this->original_amount, 0, ',', '.') . ' VNĐ';
    }

    /**
     * Lấy số tiền cuối formatted
     */
    public function getFormattedAmountAttribute()
    {
        return number_format($this->amount, 0, ',', '.') . ' VNĐ';
    }

    /**
     * Lấy số tiền còn lại formatted
     */
    public function getFormattedRemainingAmountAttribute()
    {
        return number_format($this->remaining_amount, 0, ',', '.') . ' VNĐ';
    }

    /**
     * Kiểm tra trạng thái chờ thanh toán
     */
    public function isPending()
    {
        return $this->status == self::STATUS_PENDING;
    }

    /**
     * Kiểm tra trạng thái đã thanh toán đủ
     */
    public function isPaid()
    {
        return $this->status == self::STATUS_PAID;
    }

    /**
     * Kiểm tra trạng thái đã thanh toán một phần
     */
    public function isPartiallyPaid()
    {
        return $this->status == self::STATUS_PARTIAL_PAID;
    }

    /**
     * Kiểm tra trạng thái đã hủy
     */
    public function isCancelled()
    {
        return $this->status == self::STATUS_CANCELLED;
    }







    /**
     * Tự động cập nhật trạng thái dựa trên số tiền đã thanh toán
     */
    public function updateStatusBasedOnPayment()
    {
        if ($this->paid_amount >= $this->amount) {
            $this->status = self::STATUS_PAID;
        } elseif ($this->paid_amount > 0) {
            $this->status = self::STATUS_PARTIAL_PAID;
        } else {
            $this->status = self::STATUS_PENDING;
        }

        return $this;
    }
}
