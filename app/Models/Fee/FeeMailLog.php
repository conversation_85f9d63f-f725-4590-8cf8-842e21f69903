<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;

class FeeMailLog extends Model
{
    protected $table = 'fee_mail_log';
    
    const ACTION_REFUND = 'Refund';
    const ACTION_UPDATE = 'Update';
    const ACTION_SEND_MAIL = 'Send Mail';
    const ACTION_PROCESS_FEE = 'Process fee';
    const ACTION_CHANGE_STATUS = 'Change status';
    const LIST_NAME_LOG_DATA = [
        'old_brand' => 'Ngành cũ',
        'new_brand' => 'Ngành Mơi',
        'refund' => 'Hồi phí',
        'amount' => 'Số Tiền',
    ];

    protected $fillable = [
        'id',
        'fee_mail_id',
        'auth',
        'action',
        'user_login',
        'description',
        'data',
        'id_action'
    ];

}
