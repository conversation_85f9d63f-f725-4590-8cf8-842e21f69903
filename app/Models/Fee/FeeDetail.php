<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;

class FeeDetail extends Model
{
    protected $table = 'fee_details';
    protected $fillable = ['fee_id', 'ki_thu', 'type_fee', 'amount', 'discount', 'version', 'status', 'english_level_id', 'english_level', 'debt_id', 'user_code', 'user_login', 'term_id'];

    public function __construct(array $attributes = [])
    {

        parent::__construct($attributes);
    }

    public function typeName()
    {
        $type_name = '';
        $type_fee = $this->type_fee;
        if ($type_fee == 1) {
            $type_name = '<PERSON><PERSON> họ<PERSON> k<PERSON>';
        } else if ($type_fee == 2) {
            $type_name = '<PERSON><PERSON> sách';
        } else if ($type_fee == 3 || $type_fee == 4) {
            $type_name = 'Tiếng anh';
        } else if ($type_fee == 5) {
            $type_name = 'Bổ sung';
        }

        return $type_name;
    }

    public function studentDebt()
    {
        return $this->belongsTo(StudentDebt::class, 'debt_id');
    }

    public function fee()
    {
        return $this->belongsTo(Fee::class, 'fee_id');
    }
}
