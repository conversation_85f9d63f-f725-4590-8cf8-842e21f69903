<?php

namespace App\Models\Dra;

use Illuminate\Database\Eloquent\Model;

class CurriCulum extends Model
{
    protected $table = 'curriculum';
    protected $fillable = [
        "name",
        "number_of_period",
        "description",
        "program_id",
        "mod_of_study",
        "number_of_credit",
        "number_of_subject",
        "status",
        "number_compulsory_credit",
        "number_compulsory_subject",
        "compulsory_subject_list",
        "number_optional_credit",
        "number_optional_subject",
        "optional_subject_list",
        "branch_object_id",
        "branch_object_code",
        "branch_table_name",
        "brand_code",
        "khoa",
        "nganh",
        "chuyen_nganh",
        "noi_dung",
        "nganh_in_bang",
        "nganh_in_bang_en"
    ];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    public function period_subject()
    {
        return $this->hasMany('App\Models\Dra\Period', 'curriculum_id', 'id');
    }

    public function user()
    {
        return $this->hasMany('App\Models\Fu\User', 'curriculum_id', 'id');
    }

    public function brand()
    {
        return $this->belongsTo('App\Models\Brand', 'brand_code', 'code');
    }
}
