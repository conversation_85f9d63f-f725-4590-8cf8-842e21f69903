<?php

namespace App\Models\Dra;

use Illuminate\Database\Eloquent\Model;

class T1Role extends Model
{
    protected $table = 'role';
    protected $fillable = ['role_name','role_description','is_active'];
    public $timestamps = false;
    
//    public function __construct(array $attributes = [])
//    {
//        
//        parent::__construct($attributes);
//    }
    

    public function permissions()
    {
        return $this->hasMany('App\Models\Dra\T1RolePermission', 'role_id');
    }
}
