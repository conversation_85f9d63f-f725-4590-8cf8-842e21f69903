<?php

namespace App\Models\Dra;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class T1UserRole extends Model
{
    protected $table = 'user_role';
    protected $fillable = ['user_login','role_id'];
    public $timestamps = false;
    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    /**
     * Kiểm tra quyền của cán bộ
     * 
     * <AUTHOR>
     * @since 21/10/2022
     */
    public static function checkPermission($user, $roleType)
    {
        if ($roleType == null) {
            return false;
        }

        // Kiểm tra có phải tk Admin không
        // if ($user->user_level == 1) {
        //     return true;
        // }

        // Lấy danh sách id quyền của user
        $listPermission = DB::connection()
        ->select("SELECT permission_id FROM role_permission
        WHERE role_id IN (
            SELECT role_id 
            FROM user_role 
            WHERE user_role.user_login = ?
        ) GROUP BY permission_id", [$user->user_login]);

        $listPermissionId = array_map(function ($a) {
            return $a->permission_id;
        }, $listPermission);

        // lấy danh sách quyền của user
        $listRoleName = T1Permission::whereIn('id', $listPermissionId)
        ->pluck('permission_name');
        if(count($listRoleName) > 0) {
            $listRoleName = $listRoleName->toArray();
        } else {
            $listRoleName = [];
        }

        // so sánh quyền truyền lên
        if (in_array($roleType, $listRoleName)) {
            return true;
        }

        return false;
    }

    public function permissions()
    {
        return $this->hasMany('App\Models\Dra\T1RolePermission', 'role_id', 'role_id');
    }

    public function user()
    {
        return $this->belongsTo('App\User', 'user_login', 'user_login');
    }
}
