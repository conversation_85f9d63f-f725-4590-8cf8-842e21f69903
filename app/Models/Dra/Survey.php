<?php

namespace App\Models\Dra;

use Illuminate\Database\Eloquent\Model;

class Survey extends Model
{
    protected $table = 'survey';
    public $timestamps = false;
    protected $fillable = [
        'id',
        'survey_id',
        'survey_name',
        'is_active',
        'question_num',
        'survey_url',
        'compulsory',
        'start_date',
        'end_date',
    ];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}