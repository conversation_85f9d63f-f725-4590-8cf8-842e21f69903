<?php

namespace App\Models\Dra;

use Illuminate\Database\Eloquent\Model;

class PeriodSubject extends Model
{
    protected $table = 'period_subject';
    public $timestamps = false;
    protected $fillable = [
        'period_id',
        'subject_id',
        'subject_name',
        'is_compulsory',
        'number_of_credit',
        'skill_code',
        'in_result',
        'replacement_skill_code',
        'replacement_subject_code',
        'replacement_subject',
        'max_attempt',
        'fee_first_time',
        'fee_next_time',
        'curriculum_id',
        'curriculum_name',
        'period_name',
        'prerequisite_subject_code',
        'prerequisite_skill_code',
        'prerequisite_skill_code_attend',
        'prerequisite_subject_code_attend',
        'subject_code',
    ];
    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function period()
    {
        return $this->belongsTo('App\Models\Dra\Period', 'period_id', 'id');
    }
}
