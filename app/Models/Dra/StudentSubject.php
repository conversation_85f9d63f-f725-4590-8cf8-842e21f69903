<?php

namespace App\Models\Dra;

use Illuminate\Database\Eloquent\Model;

class StudentSubject extends Model
{
    protected $table = 'student_subject';
    protected $fillable = [
        "student_login",
        "subject_id",
        "number_of_credit",
        "skill_code",
        "subject_name",
        "subject_code",
        "is_start",
        "is_lock_edit",
        "number_decide",
        "note",
        "period_ordering"
    ];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
