<?php

namespace App\Models\Dra;

use Illuminate\Database\Eloquent\Model;

class StudentHistory extends Model
{
    protected $table = 'student_history';
    protected $fillable = ['student_login','action_name','date_taken','actor','description','old_status','current_status','student_code','term_id','cusfield1'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
