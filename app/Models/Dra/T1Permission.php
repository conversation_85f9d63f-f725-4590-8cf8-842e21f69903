<?php

namespace App\Models\Dra;

use Illuminate\Database\Eloquent\Model;

class T1Permission extends Model
{
    protected $table = 'permission';
    protected $fillable = ['permission_title','permission_name','permission_description','route_name'];
    public $timestamps = false;
    

    public function roles(){
        return $this->hasMany('App\Models\Dra\T1RolePermission', 'permission_id');
    }
}
