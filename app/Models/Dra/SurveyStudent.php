<?php

namespace App\Models\Dra;

use Illuminate\Database\Eloquent\Model;

class SurveyStudent extends Model
{
    protected $table = 'survey_student';
    public $timestamps = false;
    protected $fillable = [
        'id',
        'survey_id',
        'user_code',
        'user_login',
        'status',
        'created_at',
        'updated_at',
    ];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}