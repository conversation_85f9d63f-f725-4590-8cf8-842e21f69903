<?php

namespace App\Models\Dra;

use Illuminate\Database\Eloquent\Model;

class T1RoleLevel extends Model
{
    protected $table = 'role_level';
    protected $fillable = ['id','role_id','role_level','parent_role_id'];
    public $timestamps = false;
    
//    public function __construct(array $attributes = [])
//    {
//        
//        parent::__construct($attributes);
//    }
    

    public function role()
    {
        return $this->belongsTo('App\Models\Dra\T1Role', 'role_id', 'id');
    }
    public function parent_role()
    {
        return $this->belongsTo('App\Models\Dra\T1Role', 'role_id', 'id');
    }
}
