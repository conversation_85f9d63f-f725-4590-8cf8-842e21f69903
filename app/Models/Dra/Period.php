<?php

namespace App\Models\Dra;

use Illuminate\Database\Eloquent\Model;

class Period extends Model
{
    protected $table = 'period';
    public $timestamps = false;
    protected $fillable = [
        'curriculum_id',
        'period_name',
        'period_description',
        'presiquisite_subject',
        'not_count_subject',
        'ordering',
        'number_of_subject',
        'minimum_notpass_subject',
        'notpass_subject_restrict',
        'minimum_notpass_credit',
        'notpass_credit_restrict',
        'created_date',
        'updated_by',
        'is_valid',
        'number_optional_subject',
        'optional_subject_list',
        'optional_subject_pass',
        'optional_subject_credit_pass',
        'phase_id',
        'period_code',
        'fee_amount',
        'book_fee',
        'book_rent_fee',
        'is_current',
        'fee_dead_line',
        'start_date',
        'is_plan',
        'is_done',
    ];
    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function period_subject()
    {
        return $this->hasMany('App\Models\Dra\PeriodSubject', 'period_id', 'id');
    }
}
