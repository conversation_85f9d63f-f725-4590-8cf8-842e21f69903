<?php

namespace App\Models;

use App\Models\Fu\Term;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudentStatusLog extends Model
{
    use HasFactory;
    
    protected $table = 'student_status_logs';
    protected $fillable = ['user_login', 'user_code', 'study_status_old', 'study_status_code_old', 'study_status_new', 'study_status_code_new', 'term_id', 'update_by', 'note'];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function term() {
        return $this->belongsTo(Term::class, 'term_id', 'id');
    }
}
