<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class EnglishDetail extends Model
{
    protected $table = 'english_details';
    protected $fillable = ['user_code','user_login','study_status','curriculum_id','level_1','level_2','level_3','level_4','level_5','level_6','type','finish','brand_code'];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function level()
    {
        return $this->hasMany('App\Models\EnglishDetailLevel','english_id');
    }

    public function getLevel1StatusAttribute()
    {;
        $text = ['style' => 'kt-font-primary', 'text' => '--'];
        $status = $this->level_1;
        if ($status == 1) {
            $text = ['style' => 'kt-font-success', 'text' => 'Đạt'];
        } else if ($status == -1) {
            $text = ['style' => 'kt-font-danger', 'text' => 'Trượt điểm danh'];
        } else if ($status == -2) {
            $text = ['style' => 'kt-font-info', 'text' => 'Đang học'];
        } else if ($status == -3) {
            $text = ['style' => 'kt-font-danger', 'text' => 'Chưa đạt'];
        } else if ($status == -4) {
            $text = ['style' => 'kt-font-warning', 'text' => 'Thi lại'];
        } else if ($status == 2) {
            $text = ['style' => 'kt-font-success', 'text' => 'Miễn giảm'];
        }

        return (object) $text;
    }

    public function getLevel2StatusAttribute()
    {;
        $text = ['style' => 'kt-font-primary', 'text' => '--'];
        $status = $this->level_2;
        if ($status == 1) {
            $text = ['style' => 'kt-font-success', 'text' => 'Đạt'];
        } else if ($status == -1) {
            $text = ['style' => 'kt-font-danger', 'text' => 'Trượt điểm danh'];
        } else if ($status == -2) {
            $text = ['style' => 'kt-font-info', 'text' => 'Đang học'];
        } else if ($status == -3) {
            $text = ['style' => 'kt-font-danger', 'text' => 'Chưa đạt'];
        } else if ($status == -4) {
            $text = ['style' => 'kt-font-warning', 'text' => 'Thi lại'];
        } else if ($status == 2) {
            $text = ['style' => 'kt-font-success', 'text' => 'Miễn giảm'];
        }

        return (object) $text;
    }

    public function getLevel3StatusAttribute()
    {;
        $text = ['style' => 'kt-font-primary', 'text' => '--'];
        $status = $this->level_3;
        if ($status == 1) {
            $text = ['style' => 'kt-font-success', 'text' => 'Đạt'];
        } else if ($status == -1) {
            $text = ['style' => 'kt-font-danger', 'text' => 'Trượt điểm danh'];
        } else if ($status == -2) {
            $text = ['style' => 'kt-font-info', 'text' => 'Đang học'];
        } else if ($status == -3) {
            $text = ['style' => 'kt-font-danger', 'text' => 'Chưa đạt'];
        } else if ($status == -4) {
            $text = ['style' => 'kt-font-warning', 'text' => 'Thi lại'];
        } else if ($status == 2) {
            $text = ['style' => 'kt-font-success', 'text' => 'Miễn giảm'];
        }

        return (object) $text;
    }

    public function getLevel4StatusAttribute()
    {;
        $text = ['style' => 'kt-font-primary', 'text' => '--'];
        $status = $this->level_4;
        if ($status == 1) {
            $text = ['style' => 'kt-font-success', 'text' => 'Đạt'];
        } else if ($status == -1) {
            $text = ['style' => 'kt-font-danger', 'text' => 'Trượt điểm danh'];
        } else if ($status == -2) {
            $text = ['style' => 'kt-font-info', 'text' => 'Đang học'];
        } else if ($status == -3) {
            $text = ['style' => 'kt-font-danger', 'text' => 'Chưa đạt'];
        } else if ($status == -4) {
            $text = ['style' => 'kt-font-warning', 'text' => 'Thi lại'];
        } else if ($status == 2) {
            $text = ['style' => 'kt-font-success', 'text' => 'Miễn giảm'];
        }

        return (object) $text;
    }
}
