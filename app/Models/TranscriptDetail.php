<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TranscriptDetail extends Model
{
    /**
     * type - 0: b<PERSON><PERSON> thườ<PERSON>, 1: mi<PERSON><PERSON> g<PERSON>, 2:thay thế, 3: môn thực tập, 4: môn tương đương
     * status - 0: ch<PERSON><PERSON>, 1: đ<PERSON><PERSON>, -1: t<PERSON><PERSON><PERSON><PERSON> điể<PERSON> danh
     */
    protected $table = 'transcript_details';
    public $timestamps = true;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function transcript()
    {
        return $this->belongsTo('App\Models\Transcript', 'transcript_id', 'id');
    }
}
