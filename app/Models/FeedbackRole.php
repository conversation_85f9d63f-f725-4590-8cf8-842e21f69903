<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FeedbackRole extends Model
{
    protected $table = 'feedback_roles';
    protected $fillable = ['slug','name','skill_code','master','sub_master'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function details()
    {
        return $this->hasMany('App\Models\FeedbackRoleUser', 'role_id');
    }
}
