<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GroupAssignmentSlot extends Model
{
    protected $table = 'group_assignment_slot';
    protected $fillable = ['id', 'group_id', 'alias', 'slot', 'date_assignment', 'slot_start', 'slot_end'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function slotDetail()
    {
        return $this->belongsTo('App\Models\Fu\Slot', 'slot', 'id');
    }
}
