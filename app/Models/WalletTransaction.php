<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class WalletTransaction extends Model
{
    use HasFactory;

    protected $table = 'wallet_transaction';
    public $timestamps = true;

    protected $fillable = [
        'user_code',
        'type',
        'amount',
        'balance_after',
        'description',
        'reference',
        'payment_method',
        'status',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'balance_after' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the student that owns this transaction
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'user_code', 'user_code');
    }

    /**
     * Get the wallet for this transaction
     */
    public function wallet()
    {
        return $this->belongsTo(StudentWallet::class, 'user_code', 'user_code');
    }

    /**
     * Get transaction type text
     */
    public function getTypeTextAttribute()
    {
        $typeMap = [
            'deposit' => 'Nạp tiền',
            'withdraw' => 'Rút tiền',
            'payment' => 'Thanh toán',
            'refund' => 'Hoàn tiền',
            'fee' => 'Phí dịch vụ',
            'tuition' => 'Học phí',
        ];

        return $typeMap[$this->type] ?? 'Khác';
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute()
    {
        $prefix = in_array($this->type, ['deposit', 'refund']) ? '+' : '-';
        return $prefix . number_format($this->amount, 0, ',', '.') . ' VNĐ';
    }

    /**
     * Get formatted balance after
     */
    public function getFormattedBalanceAfterAttribute()
    {
        return number_format($this->balance_after, 0, ',', '.') . ' VNĐ';
    }

    /**
     * Get status text
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            'pending' => 'Đang xử lý',
            'completed' => 'Hoàn thành',
            'failed' => 'Thất bại',
            'cancelled' => 'Đã hủy',
        ];

        return $statusMap[$this->status] ?? 'Không xác định';
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute()
    {
        $colorMap = [
            'pending' => 'warning',
            'completed' => 'success',
            'failed' => 'danger',
            'cancelled' => 'secondary',
        ];

        return $colorMap[$this->status] ?? 'secondary';
    }

    /**
     * Scope for deposits
     */
    public function scopeDeposits($query)
    {
        return $query->whereIn('type', ['deposit', 'refund']);
    }

    /**
     * Scope for withdrawals
     */
    public function scopeWithdrawals($query)
    {
        return $query->whereIn('type', ['withdraw', 'payment', 'fee', 'tuition']);
    }

    /**
     * Scope for completed transactions
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for pending transactions
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for transactions in date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }
}
