<?php
namespace App\Models;
class Order
{
    private $id;
    private $student_user_login;
    private $student_user_code;
    private $status_name;
    private $student_note;
    private $retest_type_id;
    private $retest_subject;
    private $black_list_calendar = [];
    private $busy_calendar;
    private $assigned_plan = null;
    private $isFinal = false;
    private $created_at = "";

    public function __construct($id, $student, $type, $subject, $busy, $student_user_code, $status_name, $student_note, $created_at)
    {
        $this->id = $id;
        $this->student_user_login = $student;
        $this->retest_subject = $subject;
        $this->retest_type_id = $type;
        $this->busy_calendar = $busy;
        $this->status_name = $status_name;
        $this->student_user_code = $student_user_code;
        $this->student_note = $student_note;
        $this->created_at = $created_at;
    }

    public function setFinal(){
        $this->isFinal = true;
    }

    public function isFinal(){
        return $this->isFinal;
    }

    public function getData()
    {
        return [
            'id' => $this->id,
            'student_user_login' => $this->student_user_login,
            'retest_type_id' => $this->retest_type_id,
            'retest_subject' => $this->retest_subject,
            'student_user_code' => $this->student_user_code,
            'status_name' => $this->status_name,
            'student_note' => $this->student_note,
            'busy_schedules' => (array) $this->busy_calendar,
            'created_at' => $this->created_at,
        ];
    }

    public function getId()
    {
        return $this->id;
    }

    public function getRetestTypeId()
    {
        return intval($this->retest_type_id);
    }

    public function setAssignPlan(Plan $plan)
    {
        $this->assigned_plan = $plan;
        $this->black_list_calendar[] = [
            'slot' => $plan->getSlot(),
            'date' => $plan->getDate(),
            'student_user_login' => $this->student_user_login,
            'isAssignedPlan' => 1
        ];
    }

    public function removeAssignedPlan(Plan $plan)
    {
        foreach ($this->black_list_calendar as $key => $calendar) {
            if ($plan->getDate() == $calendar['date'] && $plan->getSlot() == $calendar['slot'] && $calendar['isAssignedPlan'] == 1) {
                unset($this->black_list_calendar[$key]);
                break;
            }
        }
        $this->assigned_plan = null;
    }

    public function getAssignPlan()
    {
        return $this->assigned_plan;
    }

    public function getStudentUserLogin()
    {
        return $this->student_user_login;
    }

    public function isSameSubject($list_subject_code)
    {
        return in_array($this->retest_subject,$list_subject_code);
    }

    public function isBusy($data)
    {
        $date = $data['date'];
        $slot = $data['slot'];
        $id = $data['id'];
        if (in_array($id, $this->black_list_calendar)) {
            return true;
        }
        $busy = [];
        $busy = array_filter($this->busy_calendar, function ($e) use ($date, $slot) {
            return $e['slot'] == $slot && $e['date'] == $date;
        });
        if (count($busy) > 0) {
            $this->black_list_calendar[] = $id;
            return true;
        } else {
            return false;
        }
    }
}

class Plan
{
    protected $id = 0;
    protected $date;
    protected $slot;
    protected $retest_subjects;
    protected $list_order = [];
    protected $list_order_id = [];
    protected $min = 1;
    protected $max;
    protected $supervisor1 = ""; 
    protected $supervisor2 = "";
    protected $rom_type = 0;
    protected $room_value = "";

    public function __construct($id, $date, $slot, $retest_subjects, $min, $max, $supervisor1, $supervisor2, $room_type, $room_value)
    {
        $this->id = $id;
        $this->date = $date;
        $this->slot = $slot;
        $this->retest_subjects = $retest_subjects;
        $this->min = $min;
        $this->max = $max;
        $this->supervisor1 = $supervisor1;
        $this->supervisor2 = $supervisor2;
        $this->room_type = $room_type;
        $this->room_value = $room_value;
    }

    public function getTimeData()
    {
        return [
            'id' => $this->id,
            'date' => $this->date,
            'slot' => $this->slot
        ];
    }

    public function getSubjectCode()
    {
        return $this->retest_subjects;
    }

    public function getSameSubjectPoint()
    {
        $totalPoint = 0;
        foreach ($this->list_order as $order) {
            if ($order->isSameSubject($this->retest_subjects)) {
                $totalPoint++;
            }
        }
        return $totalPoint;
    }

    public function setOrderIntoPlan(Order &$order)
    {
        $this->list_order[] = $order;
        $this->list_order_id[] = $order->getId();
        $order->setAssignPlan($this);
    }

    public function getOrderInPlan(){
        return $this->list_order;
    }

    public function getListAssignedOrderId()
    {
        return $this->list_order_id;
    }

    public function removeOrderFromPlan(Order &$order)
    {
        foreach ($this->list_order as $key => $_order) {
            if ($order->getId() == $_order->getId()) {
                unset($this->list_order[$key]);
                break;
            }
        }
        foreach ($this->list_order_id as $key => $_order_id) {
            if ($order->getId() == $_order_id) {
                unset($this->list_order_id[$key]);
                break;
            }
        }

        $order->removeAssignedPlan($this);
    }

    public function getPointNumberOrder()
    {
        if ((count($this->list_order) - $this->max) >= 2) {
            return 6;
        }
        $result = count($this->list_order)/$this->max;
        if ($result >= 1) {
            return 1 + $result;
        }else{
            return $result;
        }
    }

    public function isFull()
    {
        return count($this->list_order) >= $this->max;
    }

    public function getSlot()
    {
        return $this->slot;
    }
    public function getDate()
    {
        return $this->date;
    }
}

class OralPlan extends Plan
{
    public function getPlanInfo()
    {
        $list_order = [];
        foreach ($this->list_order as $order) {
            $list_order[] = $order->getData();
        }
        return [
            'id' => $this->id,
            'date' => $this->date,
            'slot' => $this->slot,
            'retest_subjects' => $this->retest_subjects,
            'min' => $this->min,
            'max' => $this->max,
            'list_order' => $list_order,
            'retest_type_id' => 2,
            'retest_type' => 'Oral test',
            'supervisor1' => $this->supervisor1,
            'supervisor2' => $this->supervisor2,
            'room_type' => $this->room_type,
            'room_value' => $this->room_value
        ];
    }
}

class PracticePlan extends Plan
{
    public function getPlanInfo()
    {
        $list_order = [];
        foreach ($this->list_order as $order) {
            $list_order[] = $order->getData();
        }
        return [
            'id' => $this->id,
            'date' => $this->date,
            'slot' => $this->slot,
            'retest_subjects' => $this->retest_subjects,
            'min' => $this->min,
            'max' => $this->max,
            'list_order' => $list_order,
            'retest_type_id' => 3,
            'retest_type' => 'Practice test',
            'supervisor1' => $this->supervisor1,
            'supervisor2' => $this->supervisor2,
            'room_type' => $this->room_type,
            'room_value' => $this->room_value
        ];
    }
}

class EOSPlan extends Plan
{
    public function getPlanInfo()
    {
        $list_order = [];
        foreach ($this->list_order as $order) {
            $list_order[] = $order->getData();
        }
        return [
            'id' => $this->id,
            'date' => $this->date,
            'slot' => $this->slot,
            'retest_subjects' => $this->retest_subjects,
            'min' => $this->min,
            'max' => $this->max,
            'list_order' => $list_order,
            'retest_type_id' => 1,
            'retest_type' => 'EOS test',
            'supervisor1' => $this->supervisor1,
            'supervisor2' => $this->supervisor2,
            'room_type' => $this->room_type,
            'room_value' => $this->room_value
        ];
    }
}
