<?php

namespace App\Models;

use Carbon\Carbon;
use App\Models\Sms\Account;
use App\Models\Sms\StudentSmsPermission;
use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable
{
    const ROLE_ADMIN = 1;
    protected $table = 'user';
    public $timestamps = false;

    public function roles()
    {
        return $this->hasMany('App\Models\Dra\T1UserRole', 'user_login', 'user_login');
    }

    public function isRoleAdmin()
    {
        return in_array(self::ROLE_ADMIN, session('roles'));
    }
    public function fullname()
    {
        $first = $this->user_surname;
        $middle = $this->user_middlename;
        $last = $this->user_givenname;
        $full_name = '';
        if ($first != '') {
            $full_name .= $first;
        }
        if ($middle != '') {
            $full_name .= " $middle";
        }
        if ($last != '') {
            $full_name .= " $last";
        }
        return $full_name;
    }

    public function getUserPermissions()
    {
        return $this->hasMany(StudentSmsPermission::class, 'user_login', 'user_login');
    }

    public function dob()
    {
        return Carbon::createFromDate($this->user_DOB);
    }
    public function account()
    {
        return $this->hasMany(Account::class, 'student_code', 'student_code')->orderBy('created_on', 'DESC');
    }
}
