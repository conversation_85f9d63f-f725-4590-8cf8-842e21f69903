<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Transcript extends Model
{
    protected $table = 'transcripts';
    public $timestamps = true;
    protected $fillable = ['user_login','user_code','curriculum_id','version','study_status','created_at','updated_at'];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function details()
    {
        return $this->hasMany('App\Models\TranscriptDetail');
    }

    public function totalLearned() {
        return $this->hasMany(TranscriptDetail::class)->where('transcript_details.status', 1);
    }
}
