<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class EnglishDetailLevel extends Model
{
    protected $table = 'english_detail_level';
    protected $fillable = ['english_id','term_id','term_name','subject_code','skill_code','subject_name','status','level','payment_status','refund','refund_id','course_result_id','create_time','fee_detail_id','version','amount','discount','note', 'is_transfer'];
    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function statusText()
    {
        $text = ['style' => 'kt-font-primary', 'text' => '--'];
        $status = $this->status;
        if ($status == 1) {
            $text = ['style' => 'kt-font-success', 'text' => 'Đạt'];
        } else if ($status == -1) {
            $text = ['style' => 'kt-font-danger', 'text' => 'Trượt điểm danh'];
        } else if ($status == -2) {
            $text = ['style' => 'kt-font-info', 'text' => 'Đang học'];
        } else if ($status == -3) {
            $text = ['style' => 'kt-font-danger', 'text' => 'Chưa đạt'];
        } else if ($status == -4) {
            $text = ['style' => 'kt-font-warning', 'text' => 'Thi lại'];
        } else if ($status == 2) {
            $text = ['style' => 'kt-font-success', 'text' => 'Miễn giảm'];
        }

        return (object) $text;
    }
}
