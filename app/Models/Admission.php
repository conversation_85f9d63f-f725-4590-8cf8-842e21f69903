<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Fu\User;

class Admission extends Model
{
    use SoftDeletes;

    protected $table = 'admissions';

    protected $fillable = [
        'user_surname',
        'user_middlename',
        'user_givenname',
        'user_birthday',
        'user_gender',
        'user_code',
        'user_email',
        'user_telephone',
        'user_address',
        'user_province',
        'user_district',
        'user_ward',
        'training_level',
        'training_program',
        'cultural_level',
        'admission_period',
        'priority_object',
        'note',
        'status',
        'created_by',
        'approved_by',
        'approved_at',
        'rejected_at',
        'rejection_note'
    ];

    protected $dates = [
        'user_birthday',
        'approved_at',
        'rejected_at',
        'deleted_at'
    ];

    protected $casts = [
        'user_birthday' => 'date',
        'user_gender' => 'integer',
        'status' => 'integer',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime'
    ];

    // Status constants
    const STATUS_PENDING = 0;
    const STATUS_APPROVED = 1;
    const STATUS_REJECTED = 2;

    // Gender constants
    const GENDER_FEMALE = 0;
    const GENDER_MALE = 1;

    /**
     * Scope for search
     */
    public function scopeSearch($query, $search)
    {
        if ($search) {
            return $query->where(function($q) use ($search) {
                $q->where('user_surname', 'like', "%{$search}%")
                  ->orWhere('user_middlename', 'like', "%{$search}%")
                  ->orWhere('user_givenname', 'like', "%{$search}%")
                  ->orWhere('user_code', 'like', "%{$search}%")
                  ->orWhere('user_email', 'like', "%{$search}%")
                  ->orWhere('user_telephone', 'like', "%{$search}%");
            });
        }
        return $query;
    }

    /**
     * Scope for status filter
     */
    public function scopeByStatus($query, $status)
    {
        if ($status !== null && $status !== '') {
            return $query->where('status', $status);
        }
        return $query;
    }

    /**
     * Scope for admission period filter
     */
    public function scopeByAdmissionPeriod($query, $period)
    {
        if ($period) {
            return $query->where('admission_period', $period);
        }
        return $query;
    }

    /**
     * Get creator relationship
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by', 'user_login');
    }

    /**
     * Get approver relationship
     */
    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by', 'user_login');
    }

    /**
     * Check if admission is approved
     */
    public function isApproved()
    {
        return $this->status === self::STATUS_APPROVED;
    }

    /**
     * Check if admission is rejected
     */
    public function isRejected()
    {
        return $this->status === self::STATUS_REJECTED;
    }

    /**
     * Check if admission is pending
     */
    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Get full name attribute
     */
    public function getFullNameAttribute()
    {
        return trim($this->user_surname . ' ' . $this->user_middlename . ' ' . $this->user_givenname);
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute()
    {
        switch ($this->status) {
            case self::STATUS_PENDING:
                return 'Chờ duyệt';
            case self::STATUS_APPROVED:
                return 'Đã duyệt';
            case self::STATUS_REJECTED:
                return 'Từ chối';
            default:
                return 'Không xác định';
        }
    }

    /**
     * Get gender label
     */
    public function getGenderLabelAttribute()
    {
        return $this->user_gender === self::GENDER_MALE ? 'Nam' : 'Nữ';
    }
}
