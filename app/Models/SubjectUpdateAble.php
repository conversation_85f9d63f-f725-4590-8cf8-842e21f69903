<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubjectUpdateAble extends Model
{
    protected $table = 'subject_update_able';
    protected $fillable = [
        "id",
        "old_subject_code",
        "new_subject_code",
        "old_skill_code",
        "new_skill_code",
        "old_subject_name",
        "new_subject_name",
        "created_at",
        "updated_at"
    ];
    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
