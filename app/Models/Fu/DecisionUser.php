<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class DecisionUser extends Model
{
    protected $table = 'decision_user';
    protected $fillable = [
        'decision_id', 'user_code'
    ];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function decision()
    {
        return $this->belongsTo('App\Models\Fu\Decision', 'decision_id', 'id');
    }
}
