<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class DngLog extends Model
{
    protected $table = 'dng_logs';
    protected $fillable = ['id','user_login','bill_number','amount'];
    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_at = $model->freshTimestamp();
        });
    }

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
