<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Decision extends Model
{
    use SoftDeletes;

    const TYPE_KHEN_THUONG = 25;
    const TYPE_KY_LUAT = 26;
    const TYPE_MIEN_GIAM = 27;

    const TYPES = [
        1 => [
            'key' => 1,
            'code' => 'DH',
            'name' => 'DH ( Đang học )',
        ],
        2 => [
            'key' => 2, 
            'code' => 'HL',
            'name' => 'Học lại (HL)', 
        ],
        3 => [
            'key' => 3, 
            'code' => 'TN',
            'name' => 'Tạm nghỉ (TN)', 
        ],
        4 => [
            'key' => 4, 
            'code' => 'BH',
            'name' => 'Bỏ học (BH)', 
        ],
        5 => [
            'key' => 5, 
            'code' => 'CXL',
            'name' => 'Ch<PERSON> xếp lớp (CXL)', 
        ],
        6 => [
            'key' => 6, 
            'code' => 'CTN',
            'name' => 'Chờ tốt nghiệp (CTN)', 
        ],
        7 => [
            'key' => 7, 
            'code' => 'TNG',
            'name' => 'Đã tốt nghiệp (TNG)', 
        ],
        21 => [
            'key' => 21,
            'code' => 'SV-MG',
            'name' => 'MG - Miễn giảm môn học',
        ],
        23 => [
            'key' => 23,
            'code' => 'SV-XNSV',
            'name' => 'XNSV - Xác nhận sinh viên',
        ],
        25 => [
            'key' => 25,
            'code' => 'KT',
            'name' => 'KT - Khen thưởng',
        ],
        26 => [
            'key' => 26,
            'code' => 'KL',
            'name' => 'KL do Vi phạm quy chế thi (không bao gồm thi hộ, nhờ người thi hộ)',
        ],
    ];

    protected $table = 'decision';
    public $timestamps = false;
    protected $fillable = [
        'id',
        'name', // Tên quyết định
        'decision_num', // số quyết định
        'term_id', // Kỳ triển khai
        'from', // loại quyết định từ CĐ hay ĐH
        'type', // Loại 
        'signer', // Người ký
        'sign_day', // Ngày ký
        'effective_time', // Thời bắt đầu có hiệu lực
        'file', // File Quyết định 
        'file_status', //  Trạng thái file 
        'note', // ghi chú
        'created_by', // Người up quyết định 
        'log', // Người up quyết định 
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    
    static public function getTypeDecision($type = null)
    {
        if ($type == null) return false;
        
        $searchData = array_search($type, array_combine(array_keys(self::TYPES), array_column(self::TYPES, 'code')));
        if ($searchData === false) {
            return $searchData;
        }

        return self::TYPES[$searchData];
    }

    public function decisionUser()
    {
        return $this->hasMany('App\Models\Fu\DecisionUser', 'decision_id', 'id');
    }
}
