<?php

namespace App\Models\Fu;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ChangeScheduled extends Model
{
    protected $table = 'change_scheduled_service';
    protected $fillable = ['id', 'teacher', 'phone', 'dean', 'department_id', 'reason_change_scheduled', 'form_change_scheduled', 'explain_change_scheduled', 'step', 'note', 'staff', 'created_at', 'updated_at', 'closed_at'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function  listScheduledWannaChange(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(ChangeScheduledDetail::class, 'change_scheduled_order_id', 'id');
    }

    public function  listLog(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(ChangeScheduledLogs::class, 'change_scheduled_order_id', 'id');
    }
}
