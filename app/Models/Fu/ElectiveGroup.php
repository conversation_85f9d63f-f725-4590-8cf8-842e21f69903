<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ElectiveGroup extends Model
{
    use HasFactory;
    protected $table = 'elective_group';
    protected $fillable = [
        'id',
        'name',
        'curriculum_id',
        'status',
        'ki_thu',
        'note',
        'is_pull',
        // 'created_at',
        // 'updated_at'
    ];


    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }


    public function electiveSubject()
    {
        return $this->hasMany('App\Models\Fu\ElectiveSubject', 'elective_group_id', 'id');
    }

}
