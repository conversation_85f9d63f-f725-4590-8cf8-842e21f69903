<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ElectiveSubject extends Model
{
    use HasFactory;
    protected $table = 'elective_subject';
    public $timestamps = false;
    protected $fillable = [
        'id',
        'elective_group_id',
        'subject_id',
        'skill_code',
        'subject_code',
        'subject_name',
        'main_subject',
        'note'
    ];



    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function electiveGroup()
    {
        return $this->hasOne('App\Models\Fu\ElectiveSubject', 'elective_group_id', 'id');
    }

}
