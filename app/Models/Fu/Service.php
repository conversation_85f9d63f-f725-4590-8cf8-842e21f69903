<?php

namespace App\Models\Fu;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class Service extends Model
{
    protected $table = 'services';
    protected $fillable = ['name','cost','start_day','end_day','title','term_id','term_name'];
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    public static function checkTimeService($nameService = null)
    {
        if ($nameService == null) return false;

        $timeNow = new Carbon();
        $check = Service::where('name', $nameService)
            ->where('start_day', '<=', $timeNow->format('Y-m-d'))
            ->where('end_day', '>=', $timeNow->format('Y-m-d'))
            ->count();
        
        if ($check > 0) {
            return true;
        }

        return false;
    }
}
