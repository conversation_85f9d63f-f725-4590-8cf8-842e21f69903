<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class Subject extends Model
{
    # Traditional → Học tru<PERSON> thống (<PERSON>ọc trực tiếp tại lớp với giảng viên)
    # Integrated → Học tích hợp (<PERSON><PERSON><PERSON> hợp giữa lý thuyết và thực hành, hoặc giữa nhiều phương pháp giảng dạy)
    # Online → Học trực tuyến (Học qua nền tảng trực tuyến mà không cần đến lớp)
    # Blended → <PERSON>ọ<PERSON> kết hợp (<PERSON><PERSON><PERSON> cả trực tuyến và trực tiếp tùy theo nội dung môn học)
    protected $table = 'subject';
    public $timestamps = false;

    const SUBJECT_TYPE = [
        'traditional' => 'Học truyền thống',
        'integrated' => 'Học tích hợp',
        'online' => 'Học trự<PERSON> tuyến',
        'blended' => 'Học kết hợp',
    ];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public static function getListSubjectOnline($onlySubjectCode = false)
    {
        $res = [];
        $data = self::query()
            ->select(['subject_code', 'skill_code'])
            ->where('subject_type', 'Online')
            ->get();
            
        foreach ($data as $key => $value) {
            $res[$value->skill_code][] = $value->subject_code;
        }

        if ($onlySubjectCode == true) {
            $res = array_merge([], ...array_values($res));
        }

        return $res;
    }

    public function Department() {
        return $this->belongsTo('App\Models\Fu\Department', 'department_id', 'id');
    }

    public function GradeSyllabus() {
        return $this->hasMany('App\Models\T7\GradeSyllabus', 'subject_id', 'id');
    }

    public function SyllabusPlan() {
        return $this->hasMany('App\Models\T7\SyllabusPlan', 'subject_id', 'id');
    }
}
