<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class TransferIndustry extends Model
{
    protected $table = 'service_transfer_industries';
    protected $fillable = ['order_id', 'mien_giam', 'mien_giam_subject_code', 'mien_giam_cost', 'bo_sung', 'bo_sung_subject_code', 'bo_sung_cost', 'hoc_di', 'ma_nganh_cu', 'ma_nganh_moi', 'id_nganh_cu', 'id_nganh_moi', 'ki_thu_cu', 'ki_thu_moi', 'term_id', 'term_id_transfer', 'tien_hoc_ky', 'tien_tieng_anh', 'tong_tien', 'status','hoc_di_subject_code','bao_luu_hoc_lai','link_storage'];
    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
