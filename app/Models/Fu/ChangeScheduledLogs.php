<?php

namespace App\Models\Fu;
use Illuminate\Database\Eloquent\Model;
class ChangeScheduledLogs extends Model
{
    protected $table = 'change_scheduled_logs';
    protected $fillable = ['id', 'change_scheduled_order_id', 'staff_user_login', 'note', 'action_note', 'step_at_time', 'created_at', 'updated_at'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
