<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class DisciplineExamRegulation extends Model
{
    protected $table = 'discipline_exam_regulations';
    protected $fillable = ['id', 'term_id', 'decision_name', 'decision_no', 'file_student_name', 'date_affected', 'modifier_login', 'created_at', 'updated_at', 'note'];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function term()
    {
        return $this->belongsTo('App\Models\Fu\Term', 'term_id');
    }

    public function students()
    {
        return $this->hasMany('App\Models\Fu\DisciplineExamRegulationsStudent', 'discipline_exam_regulation_id');
    }

    public function decision()
    {
        return $this->belongsTo('App\Models\Fu\Decision', 'decision_no');
    }
}
