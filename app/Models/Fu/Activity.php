<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;
use App\Models\Fu\Attendance;

class Activity extends Model
{
    protected $table = 'activity';
    protected $fillable = [
        'id', 
        'day', 
        'slot', 
        'room_id', 
        'groupid', 
        'groupid2', 
        'course_slot', 
        'leader_login2', 
        'leader_login', 
        'lastmodifier_login', 
        'repeat_id', 
        'done', 
        'create_time', 
        'lastmodified_time', 
        'description', 
        'room_name', 
        'psubject_name', 
        'short_subject_name', 
        'psubject_code', 
        'session_description', 
        'tutor_login', 
        'pterm_name', 
        'term_id', 
        'group_name', 
        'course_id', 
        'psubject_id', 
        'session_type', 
        'psyllabus_id', 
        'area_id', 
        'area_name', 
        'department_id', 
        'noi_dung', 
        'nv_sinh_vien', 
        'hoc_lieu_mon', 
        'nv_giang_vien', 
        'tai_lieu_tk', 
        'tu_hoc', 
        'tl_buoi_hoc', 
        'muc_tieu', 
        'custom_edit', 
        'start_time', 
        'end_time', 
        'is_online','url_room_online'
    ];
    public $timestamps = false;
    protected $attributes = [
        'groupid2' => 1,
        'repeat_id' => 0,
        'done' => 0,
        'tutor_login' => '',
        'noi_dung' => '',
        'nv_sinh_vien' => '',
        'hoc_lieu_mon' => '',
        'nv_giang_vien' => '',
        'tai_lieu_tk' => '',
        'tu_hoc' => '',
        'tl_buoi_hoc' => '',
        'muc_tieu' => '',
        'custom_edit' => 0,
    ];
    const BLOCK_NAME = ['Block 1','Block 2'];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function slotDetail()
    {
        return $this->belongsTo('App\Models\Fu\Slot', 'slot', 'id');
    }

    public function group()
    {
        return $this->belongsTo(Group::class, 'groupid','id');
    }

    public function attendance() {
        return $this->hasMany(Attendance::class, 'activity_id','id');
    }
    // public function groupMembers(){
    //     $from = date('2021-12-31');
    //     $to = date('2021-12-20');
    //     $groups = Group::with(['groupMembers', function($q){
    //         $q->where('member_login','=','tienbvph16555');
    //     }])->where('list_group.block_id','>=',98)->pluck('id')->toArray();
        
    //     // $activities = $this->with('group')->whereIn('activity.groupid','=',$groups)
    //     // ->whereBetween('day', [$from, $to])                
    //     // ->get();
    //     return $groups;
    // }
}
