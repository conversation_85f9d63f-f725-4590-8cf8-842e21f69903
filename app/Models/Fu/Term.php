<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class Term extends Model
{
    protected $table = 'term';

    public $timestamps = false;

    protected $fillable = [
        "term_name",
        "ordering",
        "startday",
        "endday"
    ];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function block() {
        return $this->hasMany(Block::class, 'term_id', 'id');
    }
}
