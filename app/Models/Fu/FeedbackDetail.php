<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class FeedbackDetail extends Model
{
    protected $table = 'feedback_detail';
    protected $dates = ['time'];
    public $timestamps = false;
    protected $fillable = [
        'edited_comment'
    ];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function gpa()
    {
        return ($this->Q1 + $this->Q2 + $this->Q3 + $this->Q4 + $this->Q5) / 5;
    }
}
