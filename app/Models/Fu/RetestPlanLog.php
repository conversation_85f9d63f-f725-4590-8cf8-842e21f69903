<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class RetestPlanLog extends Model
{
    protected $table = 'fu_retest_plan_logs';    
    protected $fillable = ['id','available_service_id','retest_plan_id','action_note','updated_by', 'created_at'];
    public $timestamps = false;
    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_at = $model->freshTimestamp();
        });
    }
    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function retest_plans()
    {
        return $this->hasMany('App\Models\Fu\RetestPlan','retest_plan_id','id');
    }
    
}
