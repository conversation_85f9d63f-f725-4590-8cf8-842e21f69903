<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DisciplineExamRegulationsStudent extends Model
{
    protected $table = 'discipline_exam_regulations_students';
    protected $fillable = ['id', 'discipline_exam_regulation_id', 'student_code', 'subject_code', 'register_relearn_order_id', 'reason', 'groupid', 'created_at', 'updated_at'];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function discipline()
    {
        return $this->belongsTo('App\Models\DisciplineExamRegulation', 'discipline_exam_regulation_id');
    }
}
