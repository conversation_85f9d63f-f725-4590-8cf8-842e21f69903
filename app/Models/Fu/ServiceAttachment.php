<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class ServiceAttachment extends Model
{
    protected $table = 'service_attachments';
    protected $fillable = ['service_log_id','src','user_login','original_name','type','user_code'];
    //
    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function getIcon()
    {
        switch ($this->type) {
            case 'images':
                $icon = 'flaticon2-image-file kt-font-warning';
                break;
            case 'pdf':
                $icon = 'flaticon2-file kt-font-info';
                break;
            default:
                $icon = 'flaticon2-file-2';
        }
        return $icon;
    }
}
