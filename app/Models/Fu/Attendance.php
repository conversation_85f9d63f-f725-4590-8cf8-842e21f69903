<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class Attendance extends Model
{
    protected $table = 'attendance';
    protected $fillable = ['lastmodifier_login', 'id', 'activity_id', 'groupid', 'group_name', 'psubject_name', 'short_subject_name', 'user_login', 'val', 'create_time', 'lastmodified_time', 'description', 'day', 'psubject_code', 'term_id', 'is_group_member'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'lastmodified_time';

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function user()
    {
        return $this->belongsTo('App\User', 'user_login', 'user_login');
    }
}
