<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class ActionLog extends Model
{

    protected $table = 'action_log';
    protected $fillable = [
        'id',
        'object',        //<PERSON><PERSON><PERSON> tượng thực hiện
        'auth',          // <PERSON><PERSON><PERSON>i thực hiện
        'action',        // Hành động
        'description',   // <PERSON>ô tả
        'object_id',     // Id của object
        'data_changed',
        'ip',
        'created_at'
    ];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }


}
