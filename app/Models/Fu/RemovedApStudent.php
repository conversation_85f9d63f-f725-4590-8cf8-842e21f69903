<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class RemovedApStudent extends Model
{
    protected $table = 'removed_ap_students';
    protected $fillable = ['id','user_code','removed_date_time', 'created_by', 'confirm_by'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

}
