<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SubjectUpdateAble extends Model
{
    protected $table = 'subject_update_able';
    protected $fillable = [
        'id',
        'id_change',        //id này để group lại các môn thay đổi lại
        'old_subject_code', // mã của môn bị thay thế
        'new_subject_code', // mã của môn thay thế
        'old_skill_code',   // mã kĩ năng của môn bị thay thế
        'new_skill_code',   // mã kĩ năng của môn thay thế
        'old_subject_name', // tên môn  bị thay thế
        'new_subject_name', // tên môn thay thế
        'decision_id',      // id của quyết định
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    public function SubjectUpdateAbleDecision()
    {
        return $this->belongsTo('App\Models\Fu\Decision', 'decision_id', 'id');
    }
}
