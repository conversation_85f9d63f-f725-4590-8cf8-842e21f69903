<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class UserDelete extends Model
{
    protected $table = 'user_delete';
    public $timestamps = false;
    protected $fillable = ['user_pass','user_level','user_login','user_surname','user_middlename','user_givenname','user_code','user_DOB','user_email','user_address','user_telephone','office_id','id','note','cmt','alternative_code','user_status','ngaycap','noicap','created_date','brand_name','gender','grade','learn_start_day','parent1_name','parent2_name','cust10','curriculum_id','current_period','study_status','period_pass','next_period','update_time','back_year','all_status','all_tuition_fee','all_other_fee','parent1_mobile','parent2_mobile','people_id','total_debt_subject'];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
