<?php

namespace App\Models\Fu;

use App\Models\Sms\Account;
use App\Models\T7\CourseResult;
use Illuminate\Database\Eloquent\Model;

class GroupMember extends Model
{
    protected $table = 'group_member';
    protected $fillable = ['groupid','member_login','date','note','attend_time','fee_status','fee_code','current_status','skill_code','period_id','period_fee_status','fee_due_date','fee_submit_date','lock_date','curriculum_id','subject_id','term_id','group_name','subject_code','period_ordering','full_name','study_status','group_member_status','IsCancelled','leader_login','loai','note_comment','time_evaluate','time_ evaluate','id','groupid','member_login','user_code','date','note','attend_time','fee_status','fee_code','current_status','skill_code','period_id','period_fee_status','fee_due_date','fee_submit_date','lock_date','curriculum_id','subject_id','term_id','group_name','subject_code','period_ordering','full_name','study_status','start_date','end_date','temp','is_hoc_lai','group_member_status','IsCancelled','leader_login','loai','note_comment','time_evaluate','time_ evaluate','id','groupid','member_login','user_code','date','note','attend_time','fee_status','fee_code','current_status','skill_code','period_id','period_fee_status'];
    public $timestamps = false;
    protected $attributes = [
        'attend_time' => 0,
        'fee_status' => 0,
        'fee_code' => '',
        'current_status' => 0,
        'skill_code' => '',
        'period_id' => '0',
        'period_fee_status' => '0',
        'fee_due_date' => '2025-01-01',
        'fee_submit_date' => '2025-01-01',
        'lock_date' => '2025-01-01',
        'curriculum_id' => '0',
        'subject_id' => '0',
        'term_id' => '0',
        'group_name' => '',
        'subject_code' => '',
        'period_ordering' => '0',
        'full_name' => '',
        'study_status' => '0',
        'start_date' => '2025-01-01',
        'end_date' => '2025-01-01',
        'temp' => '0',
        'is_hoc_lai' => '0',
        'group_member_status' => '0',
        'IsCancelled' => '0',
        'leader_login' => '',
        'loai' => '',
        'note_comment' => '',
        'time_evaluate' => '2025-01-01',
        'time_ evaluate' => '2025-01-01',
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    public function user()
    {
        return $this->belongsTo('App\User', 'member_login', 'user_login');
    }

    public function group()
    {
        return $this->belongsTo('App\Models\Fu\Group', 'groupid','id');
    }

    public function my_phone()
    {
        return $this->hasOne(Account::class,'student_login','member_login')->where('owner_type', 0);
    }

}
