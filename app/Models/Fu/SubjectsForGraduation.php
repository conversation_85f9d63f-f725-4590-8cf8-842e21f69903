<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubjectsForGraduation extends Model
{
    use HasFactory;
    
    protected $table = 'subjects_for_graduation';
    protected $fillable = ['subject_id', 'subject_code', 'skill_code', 'subject_name', 'subject_type', 'status', 'created_at', 'updated_at', 'updated_by'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }
}
