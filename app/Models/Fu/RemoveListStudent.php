<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class RemoveListStudent extends Model
{
    protected $table = 'remove_list_students';
    protected $fillable = ['id', 'user_code', 'group_id', 'group_name', 'subject_code', 'type', 'created_by', 'created_at', 'updated_at'];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

}
