<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class Feedback extends Model
{
    protected $table = 'feedback';
    protected $fillable = ['groupID', 'open', 'opener_login', 'open_day', 'planer_login', 'teacher_login', 'day'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    public function group()
    {
        return $this->belongsTo('App\Models\Fu\Group', 'groupID');
    }
}
