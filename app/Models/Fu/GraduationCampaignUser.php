<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class GraduationCampaignUser extends Model
{
    protected $table = 'graduation_campaign_users';
    protected $fillable = [
        'campaign_id', 
        'user_code', 
        'user_login', 
        'full_name', 
        'brand_code', 
        'curriculum_id', 
        'dob', 
        'gender', 
        'dan_toc', 
        'loai_hoc', 
        'khoa_nhap_hoc', 
        'khoa_thuc_hoc', 
        'diem_ly_thuyet', 
        'diem_thuc_hanh', 
        'type', 
        'nganh_in_bang', 
        'nganh_in_bang_en', 
        'nganh', 
        'chuyen_nganh', 
        'loai_tot_nghiep', 
        'diem_tot_nghiep', 
        'diem_tot_nghiep_kt', 
        'diem_trung_binh', 
        'tin_chi_dat', 
        'tin_chi_mien_giam', 
        'tong_tin_chi', 
        'mon_dat', 
        'mon_chua_dat', 
        'tong_mon', 
        'the_chat', 
        'chinh_tri', 
        'gdqp', 
        'gdqp_point',
        'thuc_tap', 
        'bang_cap_3', 
        'loai_tot_nghiep_en', 
        'gdqp_tc', 
        'the_chat_tc', 
        'thuc_tap_tc','diem_tot_nghiep_thang_4', 
        'percented_relearn', 
        'disciplined', 
        'mark_down_rank'
    ];
    

    public function campaign()
    {
        return $this->belongsTo('App\Models\Fu\GraduationCampaign', 'campaign_id','id');
    }
}
