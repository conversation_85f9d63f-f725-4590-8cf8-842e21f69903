<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class HoSystemLog extends Model
{

    protected $table = 'qldt_system_log';
    protected $fillable = [
        'id',
        'object',        //<PERSON><PERSON><PERSON> tượng thực hiện
        'auth',          // <PERSON><PERSON><PERSON>i thực hiện
        'action',        // Hành động
        'description',   // <PERSON>ô tả
        'object_id',     // Id của object
        'data_changed',
        'created_at',
        'uodated_at'
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }


}
