<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;
use App\Models\Fu\Category;

class Content extends Model
{
    protected $table = 'content';
    protected $fillable = ['title', 'full_text', 'state', 'start_time', 'end_time', 'catid', 'created', 'ordering', 'created_by', 'last_modified'];
    const CREATED_AT = 'created';
    const UPDATED_AT = 'modified';

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'catid', 'id');
    }
}
