<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PhaseService extends Model
{
    use HasFactory;
    
    protected $table = 'phase_service';
    protected $fillable = ['id', 'term_id', 'order', 'start_date', 'end_date', 'note', 'type', 'created_by', 'created_at', 'updated_at'];
    public $timestamps = false;
    const RELEARN_TYPE = 2; // <PERSON>ọ<PERSON> lại
    const RETEST_TYPE = 23; // Thi lại

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
