<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class ActivityGroup extends Model
{
    protected $table = 'activity_groups';
    protected $fillable = ['activity_id','groupid','term_id_cache','group_name','session_type_group'];
    public $timestamps = false;
    protected $attributes = [
    ];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
