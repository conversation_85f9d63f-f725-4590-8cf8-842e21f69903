<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class Course extends Model
{
    protected $table = 'course';
    public $timestamps = false;

    protected $fillable = [
        'lastmodifier_login', 
        'id', 
        'subject_id',
        'term_id', 
        'psubject_name',
        'psubject_code',
        'num_of_credit',
        'syllabus_id',
        'syllabus_code',
        'syllabus_name',
        'attendance_required',
        'grade_required',
        'pterm_name',
        'num_of_group',
        'is_started',
        'pull_insert',
        'pull_last_update'
    ];

    public function nameSplit()
    {
        return "$this->psubject_code - $this->pterm_name";
    }

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function groups() {
        return $this->hasMany(Group::class, 'body_id', 'id');
    }

    public function term() {
        return $this->belongsTo(Term::class, 'term_id', 'id');
    }

    public function subject() {
        return $this->belongsTo(Subject::class, 'subject_id', 'id');
    }
    
    
}
