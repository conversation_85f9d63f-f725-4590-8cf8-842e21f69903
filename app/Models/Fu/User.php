<?php

namespace App\Models\Fu;

use App\Models\Dra\Period;
use App\Models\Dra\StudentSubject;
use Carbon\Carbon;
use App\Models\Fu\Activity;
use App\Models\Sms\Account;
use App\Models\T7\CourseResult;
use Illuminate\Database\Eloquent\Model;
use App\Models\Sms\StudentSmsPermission;

class User extends Model
{
    const ROLE_ADMIN = 1;
    const LEGAL_ENTITY_UNIT = 0;
    const LEGAL_ENTITY_UNIVERSITY = 1;
    const LEGAL_ENTITY = [
        0 => 'Khối',
        1 => 'Trường'
    ];

    protected $table = 'user';
    protected $fillable = [
        'user_surname', 
        'user_middlename', 
        'user_givenname', 
        'user_login', 
        'user_code', 
        'user_pass',
        'old_user_code',
        'user_DOB', 
        'user_email', 
        'user_address', 
        'user_telephone', 
        'user_level', 
        'gender', 
        'so_bang', 
        'cmt',
        'noicap',
        'curriculum_id',
        'study_status',
        'study_status_code',
        'dantoc',
        'quoctich',
        'grade_create',
        'created_date',
        'brand_name',
        'kithu',
        'Legal_entity',
        'last_term_id',
        'created_at',
        'updated_at',
        'admission_date'
    ];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    public function study_status()
    {
        if ($this->study_status == null || $this->study_status == 0) {
            return (object) ['value' => ''];
        }
        $status = config('status')->trang_thai_hoc;
        return (object) $status[$this->study_status];
    }

    public function activity()
    {
        return $this->hasMany(Activity::class, 'leader_login', 'user_login');
    }


    public function group_member()
    {
        return $this->hasMany('App\Models\Fu\GroupMember', 'member_login', 'user_login');
    }

    public function course_result()
    {
        return $this->hasMany(CourseResult::class, 'student_login', 'user_login');
    }

    public function current_semester_subjects()
    {
        return $this->hasMany('App\Models\Fu\GroupMember', 'member_login', 'user_login');
    }

    public function change_subject_student()
    {
        return $this->hasMany('App\Models\ChangeSubjectStudent', 'student_login', 'user_login');
    }

    public function fullname()
    {
        $first = $this->user_surname;
        $middle = $this->user_middlename;
        $last = $this->user_givenname;
        $full_name = '';
        if ($first != '') {
            $full_name .= $first;
        }
        if ($middle != '') {
            $full_name .= " $middle";
        }
        if ($last != '') {
            $full_name .= " $last";
        }
        return $full_name;
    }

    public function roles()
    {
        return $this->hasMany('App\Models\Dra\T1UserRole', 'user_login', 'user_login');
    }

    public function isRoleAdmin()
    {
        return in_array(self::ROLE_ADMIN, session('roles'));
    }

    public function courseResult()
    {
        return $this->hasMany('App\Models\T7\CourseResult', 'student_login', 'user_login');
    }

    public function oldUser()
    {
        if ($this->old_user_code == null || $this->old_user_code == '') {
            return null;
        }
        return User::where('user_code', 'LIKE', $this->old_user_code)->first();
    }
    public function account()
    {
        return $this->hasMany(Account::class, 'student_code', 'student_code')->orderBy('created_on', 'DESC');
    }
    public function getFullNameAttribute()
    {
        $first = $this->user_surname;
        $middle = $this->user_middlename;
        $last = $this->user_givenname;
        $full_name = '';
        if ($first != '') {
            $full_name .= $first;
        }
        if ($middle != '') {
            $full_name .= " $middle";
        }
        if ($last != '') {
            $full_name .= " $last";
        }
        return $full_name;
    }

    public function dob()
    {
        return Carbon::createFromDate($this->user_DOB);
    }

    public function curriculum()
    {
        return $this->belongsTo('App\Models\Dra\CurriCulum');
    }

    public function getNgayNhapHocAttribute()
    {
        return Carbon::createFromDate($this->created_date)->format('d-m-Y');
    }

    public function getNgayCapCmtAttribute()
    {
        return Carbon::createFromDate($this->ngaycap)->format('d-m-Y');
    }

    public function getTrangThaiHocAttribute()
    {
        $status = config('status')->trang_thai_hoc;
        return (object)$status[$this->study_status];
    }

    static public function getFullnameByCode($Usercode)
    {
        $data = self::query()
            ->select(['user_surname', 'user_middlename', 'user_givenname'])
            ->where('user_code', $Usercode)
            ->first();

        if (!$data) return "";

        $first = $data->user_surname;
        $middle = $data->user_middlename;
        $last = $data->user_givenname;
        $full_name = '';
        if ($first != '') {
            $full_name .= $first;
        }
        if ($middle != '') {
            $full_name .= " $middle";
        }
        if ($last != '') {
            $full_name .= " $last";
        }
        return $full_name;
    }
    public function getUserPermissions()
    {
        return $this->hasMany(StudentSmsPermission::class, 'user_login', 'user_login');
    }
    public function getStudentSubjects() 
    {
        return $this->hasManyThrough(StudentSubject::class, Period::class, );
    }
}
