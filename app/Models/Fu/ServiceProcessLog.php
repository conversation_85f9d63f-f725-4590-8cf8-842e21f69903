<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class ServiceProcessLog extends Model
{
    protected $table = 'service_process_logs';
    protected $fillable = ['service_log_id','user_login','note_report','status','user_code'];
    //
    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function statusDetail()
    {
        $status = config('status')->register_status;
        return (object) $status[$this->status];
    }
}
