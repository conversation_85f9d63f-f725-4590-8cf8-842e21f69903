<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Announcement extends Model
{
    protected $table = 'announcements';

    protected $fillable = [
        'title',
        'content',
        'excerpt',
        'type',
        'priority',
        'author',
        'author_department',
        'is_published',
        'is_featured',
        'is_urgent',
        'published_at',
        'expires_at',
        'target_audience',
        'attachments',
        'views_count',
        'slug',
        'metadata'
    ];

    protected $casts = [
        'target_audience' => 'array',
        'attachments' => 'array',
        'metadata' => 'array',
        'is_published' => 'boolean',
        'is_featured' => 'boolean',
        'is_urgent' => 'boolean',
        'published_at' => 'datetime',
        'expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Constants cho type
    const TYPE_ACADEMIC = 'academic';
    const TYPE_FINANCE = 'finance';
    const TYPE_EVENT = 'event';
    const TYPE_REGULATION = 'regulation';
    const TYPE_OPPORTUNITY = 'opportunity';
    const TYPE_EMERGENCY = 'emergency';

    // Constants cho priority
    const PRIORITY_LOW = 'low';
    const PRIORITY_MEDIUM = 'medium';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    /**
     * Scope: Chỉ lấy thông báo đã xuất bản
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope: Chỉ lấy thông báo chưa hết hạn
     */
    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->where('expires_at', '>=', Carbon::now())
              ->orWhereNull('expires_at');
        });
    }

    /**
     * Scope: Chỉ lấy thông báo đã đến thời gian xuất bản
     */
    public function scopePublishedTime($query)
    {
        return $query->where(function ($q) {
            $q->where('published_at', '<=', Carbon::now())
              ->orWhereNull('published_at');
        });
    }

    /**
     * Scope: Lấy thông báo theo đối tượng (target_audience là JSON array)
     */
    public function scopeForAudience($query, $audience)
    {
        return $query->where(function ($q) use ($audience) {
            $q->whereJsonContains('target_audience', $audience)
              ->orWhereJsonContains('target_audience', 'all')
              ->orWhereNull('target_audience'); // Nếu null thì hiển thị cho tất cả
        });
    }

    /**
     * Scope: Chỉ lấy thông báo dành cho sinh viên
     */
    public function scopeForStudents($query)
    {
        return $query->forAudience('student');
    }

    /**
     * Scope: Lấy thông báo theo type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope: Lấy thông báo nổi bật
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope: Lấy thông báo khẩn cấp
     */
    public function scopeUrgent($query)
    {
        return $query->where('is_urgent', true);
    }

    /**
     * Scope: Sắp xếp theo độ ưu tiên và thời gian
     */
    public function scopeOrderByPriority($query)
    {
        return $query->orderByRaw("FIELD(priority, 'urgent', 'high', 'medium', 'low')")
                    ->orderBy('is_urgent', 'desc')
                    ->orderBy('is_featured', 'desc')
                    ->orderBy('published_at', 'desc')
                    ->orderBy('created_at', 'desc');
    }

    /**
     * Scope: Lấy thông báo hiển thị cho sinh viên (tổng hợp)
     */
    public function scopeForStudentDisplay($query)
    {
        return $query->published()
                    ->notExpired()
                    ->publishedTime()
                    ->forStudents()
                    ->orderByPriority();
    }

    /**
     * Accessor: Lấy excerpt hoặc cắt từ content
     */
    public function getExcerptAttribute($value)
    {
        if ($value) {
            return $value;
        }

        // Nếu không có excerpt, cắt từ content
        $content = strip_tags($this->content);
        return strlen($content) > 200 ? substr($content, 0, 200) . '...' : $content;
    }

    /**
     * Accessor: Kiểm tra thông báo có hết hạn không
     */
    public function getIsExpiredAttribute()
    {
        if (!$this->expires_at) {
            return false;
        }

        return Carbon::now()->gt($this->expires_at);
    }

    /**
     * Accessor: Kiểm tra thông báo có hiển thị được không
     */
    public function getCanDisplayAttribute()
    {
        return $this->is_published &&
               !$this->is_expired &&
               ($this->published_at === null || Carbon::now()->gte($this->published_at));
    }

    /**
     * Tăng view count
     */
    public function incrementViewCount()
    {
        $this->increment('views_count');
    }

    /**
     * Lấy danh sách type
     */
    public static function getTypeOptions()
    {
        return [
            self::TYPE_ACADEMIC => 'Học vụ',
            self::TYPE_FINANCE => 'Tài chính',
            self::TYPE_EVENT => 'Sự kiện',
            self::TYPE_REGULATION => 'Quy định',
            self::TYPE_OPPORTUNITY => 'Cơ hội',
            self::TYPE_EMERGENCY => 'Khẩn cấp'
        ];
    }

    /**
     * Lấy danh sách priority
     */
    public static function getPriorityOptions()
    {
        return [
            self::PRIORITY_LOW => 'Thấp',
            self::PRIORITY_MEDIUM => 'Trung bình',
            self::PRIORITY_HIGH => 'Cao',
            self::PRIORITY_URGENT => 'Khẩn cấp'
        ];
    }

    /**
     * Lấy danh sách target audience
     */
    public static function getTargetAudienceOptions()
    {
        return [
            'student' => 'Sinh viên',
            'teacher' => 'Giáo viên',
            'admin' => 'Quản trị viên',
            'all' => 'Tất cả'
        ];
    }
}
