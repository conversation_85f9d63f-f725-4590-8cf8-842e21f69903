<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Model;

class ServiceLog extends Model
{
    protected $table = 'service_logs';
    protected $fillable = ['user_id','type','note','full_name','ki_thu','study_status','cost','payment_status','note_report','updated_by','user_code','status','transfer_industry_id','term_id','term_name'];
    //
    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function service()
    {
        return $this->belongsTo('App\Models\Fu\Service', 'type');
    }

    public function statusDetail()
    {
        $status = config('status')->register_status;
        return (object) $status[$this->status];
    }

    public function paymentStatusDetail()
    {
        $payment_status = config('status')->payment_status;
        return (object) $payment_status[$this->payment_status];
    }

    public function serviceProcessLogs()
    {
        return $this->hasMany('App\Models\Fu\ServiceProcessLog', 'service_log_id');
    }

    public function serviceAttachment()
    {
        return $this->hasMany('App\Models\Fu\ServiceAttachment', 'service_log_id');
    }

    public function serviceRegister()
    {
        return $this->hasMany('App\Models\Fu\ServiceRegister', 'service_log_id');
    }

    public function service_register_retest()
    {
        return $this->hasMany('App\Models\Fu\ServiceRegister', 'service_log_id', 'id')->where('service_register.type', 23);
    }

    public function transferIndustry()
    {
        return $this->belongsTo('App\Models\Fu\TransferIndustry', 'transfer_industry_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo('App\User', 'user_id', 'user_login');
    }

    public function payment()
    {
        return $this->hasOne('App\Models\Fee\Transaction', 'service_log_id');
    }

    public function group_member() {
        return $this->hasMany('App\Models\Fu\GroupMember', 'member_login', 'user_id');
    }
}
