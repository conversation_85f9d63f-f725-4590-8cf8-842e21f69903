<?php

namespace App\Models\Fu;
use Illuminate\Database\Eloquent\Model;
use App\Models\Fu\Activity;
class ChangeScheduledDetail extends Model
{
    protected $table = 'change_scheduled_detail';
    protected $fillable = ['id', 'change_scheduled_order_id', 'activity_id', 'proposed_date', 'proposed_slot', 'proposed_teach', 'origin_class','origin_date', 'origin_slot', 'origin_room', 'origin_teacher', 'staff_arrange_date', 'staff_arrange_slot', 'staff_arrange_teacher', 'staff_user_login','staff_arrange_class', 'note', 'created_at', 'updated_at'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function activity()
    {
        $this->hasMany(Activity::class, 'id', 'activity_id');
    }
}
