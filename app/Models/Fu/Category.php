<?php

namespace App\Models\Fu;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Fu\Content;

class Category extends Model
{
    protected $table = 'categories';

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function content()
    {
        return $this->hasMany('App\Fu\Content', 'catid', 'id');
    }
}
