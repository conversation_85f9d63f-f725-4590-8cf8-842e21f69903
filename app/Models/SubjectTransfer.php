<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SubjectTransfer extends Model
{
    protected $table = 'subject_transfer';
    protected $fillable = ['subject_name','subject_code','skill_code','subject_id','type','status','period_subject_id','curriculum_id'];
    

    public function details()
    {
        return $this->hasMany('App\Models\SubjectTransferDetail', 'subject_transfer_id');
    }
}
