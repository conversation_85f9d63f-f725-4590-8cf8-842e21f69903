<?php

namespace App\Models\Iaps;

// use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GraduationRank extends Model
{
    // use HasFactory;
    const TYPE_10 = 0; // loại điểm 10
    const TYPE_4 = 1; // Loại điểm 4

    protected $table = 'graduation_rank';
    
    protected $fillable = [
        'id',
        'graduation_decision_id',
        'type',
        'name',
        'name_en',
        'min_point',
        'max_point',
        'name_gpa',
        'name_gpa_en',
        'min_gpa_point',
        'max_gpa_point',
        'ordering',
        'is_down',
        'created_at',
        'updated_at'
    ];
}
