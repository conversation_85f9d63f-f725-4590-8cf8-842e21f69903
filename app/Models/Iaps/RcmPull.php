<?php

namespace App\Models\Iaps;

use Illuminate\Database\Eloquent\Model;

class RcmPull extends Model
{
    // const LEGAL_ENTITY_UNIT = 0;
    // const LEGAL_ENTITY_UNIVERSITY = 1;

    // ADD COLUMN `he_dao_tao` tinyint NOT NULL COMMENT '0,1: <PERSON> Đẳng, 2: Trung Cấp, 3: Dự Bị cao Đẳng, 5: Sơ Cấp' AFTER `log`,
    // ADD COLUMN `rcm_name` varchar(255) NULL COMMENT 'Tên của chiến dịch' AFTER `rcm_id`;

    const HE_DAO_TAO_CAO_DANG = 1;
    const HE_DAO_TAO_TRUNG_CAP = 2;
    const HE_DAO_TAO_DU_BI_CAO_DANG = 3;
    const HE_DAO_TAO_SO_CAP = 5;

    const LIST_HE_DAO_TAO = [
        self::HE_DAO_TAO_CAO_DANG => 'Cao Đẳng',
        self::HE_DAO_TAO_TRUNG_CAP => 'Trung Cấp',
        self::HE_DAO_TAO_DU_BI_CAO_DANG => 'Dự Bị cao Đẳng',
        self::HE_DAO_TAO_SO_CAP => 'Sơ Cấp',
    ];

    protected $table = 'rcm_pull';
    protected $fillable = [
        'id',
        'rcm_id',
        'khoa',
        'campus_code',
        'status',
        'created_by',
        'note',
        'he_dao_tao',
        'created_at',
        'updated_at',
    ];
}

