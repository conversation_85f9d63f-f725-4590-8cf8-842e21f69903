<?php

namespace App\Models\Iaps;

// use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GraduationDecision extends Model
{
    const LEGAL_ENTITY_UNIT = 0;
    const LEGAL_ENTITY_UNIVERSITY = 1;
    const HE_DAO_TAO_CAO_DANG = 1;
    const HE_DAO_TAO_TRUNG_CAP = 2;
    const HE_DAO_TAO_DU_BI_CAO_DANG = 3;
    const HE_DAO_TAO_SO_CAP = 5;
    const TYPE_PROCESS_RANK_10 = 0;
    const TYPE_PROCESS_RANK_4 = 1;


    const LIST_HE_DAO_TAO = [
        self::HE_DAO_TAO_CAO_DANG => 'Cao Đẳng',
        self::HE_DAO_TAO_TRUNG_CAP => 'Trung Cấp',
        self::HE_DAO_TAO_DU_BI_CAO_DANG => 'Dự Bị cao Đẳng',
        self::HE_DAO_TAO_SO_CAP => 'Sơ Cấp',
    ];
    const LEGAL_ENTITY = [
        self::LEGAL_ENTITY_UNIT => 'Khối',
        self::LEGAL_ENTITY_UNIVERSITY => 'Trường'
    ];
    const TYPE_PROCESS_RANK = [
        self::TYPE_PROCESS_RANK_10 => 'Tính rank theo thang 10',
        self::TYPE_PROCESS_RANK_4 => 'Tính rank theo thang 4'
    ];

    // use HasFactory;
    protected $table = 'graduation_decision';
    
    protected $fillable = [
        'id',
        'Legal_entity', // Pháp nhân
        'he_dao_tao', // hệ đào tạo
        'name', // Tên QĐ
        'note', // ghi chú
        'type_process_rank', // Loại tính hạng tốt nghiệp (0: Tính Rank theo thang 10, 1: thang 4)
        'level_of_training',
        'template_10',
        'template_4',
        'footer',
        'created_at',
        'updated_at' ,
        'is_downgrade'
    ];
}
