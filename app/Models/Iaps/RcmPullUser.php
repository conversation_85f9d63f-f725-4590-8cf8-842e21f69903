<?php

namespace App\Models\Iaps;

use Illuminate\Database\Eloquent\Model;

class RcmPullUser extends Model
{
    const STATUS_NO_PULL = 0;
    const STATUS_PULLED = 1;
    const STATUS_DELETE = 2;

    protected $table = 'rcm_pull_user';
    protected $fillable = [
        'id',
        'rcm_pull_id',
        'user_code',
        'full_name',
        'people_id',
        'status',
        'log',
        'user_pull',
        'time_pull',
        'created_by',
        'created_by',
        'created_at',
        'updated_at',
    ];
}
