<?php

namespace App\Models\Iaps;

// use Illuminate\Database\Eloquent\Factories\HasFactory;

use App\Models\Fu\DecisionUser;
use App\Models\Fu\User;
use Illuminate\Database\Eloquent\Model;

class GraduationUserRank extends Model
{
    // use HasFactory;
    protected $table = 'graduation_user_rank';
    
    protected $fillable = [
        'id',
        'campaign_id',
        'user_code',
        'user_login',
        'type',
        'note',
        'rank_id',
        'rank_old_id',
        'reason_downgrade',
        'is_downgrade',
        'lock_sync',
        'created_at',
        'updated_at',
        'total_credit_relearn'
    ];

    public function user() {
        return $this->belongsTo(User::class, 'user_code', 'user_code');
    }


    public function decisionUser() {
        return $this->hasMany(DecisionUser::class, 'user_code', 'user_code');
    }

    public function graduationRank() {
        return $this->belongsTo(GraduationRank::class, 'rank_id', 'id');
    }

    public function graduationOldRank() {
        return $this->belongsTo(GraduationRank::class, 'rank_old_id', 'id');
    }
}
