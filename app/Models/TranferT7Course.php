<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TranferT7Course extends Model
{
    protected $table = 'tranfer_t7_course';
    protected $fillable = [
        "student_login",
        "student_code",
        "val",
        "grade",
        "psubject_name",
        "short_subject_name",
        "start_date",
        "end_date",
        "psubject_code",
        "grade_detail",
        "pterm_name",
        "final_result",
        "resit_result",
        "number_of_credit",
        "is_finish",
        "skill_code",
        "syllabus_name",
        "deleted",
    ];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
