<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class StudentWallet extends Model
{
    use HasFactory;

    protected $table = 'student_wallet';
    public $timestamps = false;

    protected $fillable = [
        'user_code',
        'balance',
        'total_deposit',
        'total_withdraw',
        'last_transaction_date',
        'status',
    ];

    protected $casts = [
        'balance' => 'decimal:2',
        'total_deposit' => 'decimal:2',
        'total_withdraw' => 'decimal:2',
        'last_transaction_date' => 'datetime',
    ];

    /**
     * Get the student that owns this wallet
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'user_code', 'user_code');
    }

    /**
     * Get wallet transactions
     */
    public function transactions()
    {
        return $this->hasMany(WalletTransaction::class, 'user_code', 'user_code')
                    ->orderBy('created_at', 'desc');
    }

    /**
     * Get recent transactions
     */
    public function recentTransactions($limit = 10)
    {
        return $this->transactions()->limit($limit);
    }

    /**
     * Add money to wallet
     */
    public function deposit($amount, $description = null, $reference = null)
    {
        $this->balance += $amount;
        $this->total_deposit += $amount;
        $this->last_transaction_date = now();
        $this->save();

        // Create transaction record
        WalletTransaction::create([
            'user_code' => $this->user_code,
            'type' => 'deposit',
            'amount' => $amount,
            'balance_after' => $this->balance,
            'description' => $description,
            'reference' => $reference,
            'created_at' => now(),
        ]);

        return $this;
    }

    /**
     * Withdraw money from wallet
     */
    public function withdraw($amount, $description = null, $reference = null)
    {
        if ($this->balance < $amount) {
            throw new \Exception('Insufficient balance');
        }

        $this->balance -= $amount;
        $this->total_withdraw += $amount;
        $this->last_transaction_date = now();
        $this->save();

        // Create transaction record
        WalletTransaction::create([
            'user_code' => $this->user_code,
            'type' => 'withdraw',
            'amount' => $amount,
            'balance_after' => $this->balance,
            'description' => $description,
            'reference' => $reference,
            'created_at' => now(),
        ]);

        return $this;
    }

    /**
     * Check if wallet has sufficient balance
     */
    public function hasSufficientBalance($amount)
    {
        return $this->balance >= $amount;
    }

    /**
     * Get formatted balance
     */
    public function getFormattedBalanceAttribute()
    {
        return number_format($this->balance, 0, ',', '.') . ' VNĐ';
    }

    /**
     * Get wallet status text
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            1 => 'Hoạt động',
            0 => 'Tạm khóa',
            -1 => 'Đã khóa',
        ];

        return $statusMap[$this->status] ?? 'Không xác định';
    }

    /**
     * Scope for active wallets
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }
}
