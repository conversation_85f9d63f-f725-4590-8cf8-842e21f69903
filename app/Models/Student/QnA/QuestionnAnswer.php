<?php

namespace App\Models\Student\QnA;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuestionnAnswer extends Model
{
    use HasFactory;
    protected $table = 'qna_question_answer';
    protected $fillable = ['id','is_display', 'question', 'answer', 'category_id','category_key', 'created_by', 'last_modifier'];
    public function category()
    {
        return $this->belongsTo('App\Models\Student\QnA\Category','key', 'category_key');
    }
}
