<?php

namespace App\Models\Student\QnA;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;
    
    protected $table = 'qna_categories';

    public $timestamps = false;
    protected $fillable = ['id', 'key', 'name'];
    protected $primaryKey = 'id';

    public function qnas()
    {
        return $this->hasMany('App\Models\Student\QnA\QuestionnAnswer', 'category_key', 'key');
    }
}
