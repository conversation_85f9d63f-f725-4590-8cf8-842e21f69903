<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChangeSubjectStudent extends Model
{
    use HasFactory;
    protected $table = "change_subject_student";
    protected $fillable = [
        'student_login',
        'old_subject',
        'new_subject'
    ];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
