<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Fu\User;

class Popup extends Model
{
    use HasFactory;
    
    protected $table = 'popups';

    protected $fillable = ['title', 'content', 'start_time', 'end_time','created_by', 'updated_by', 'status'];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
