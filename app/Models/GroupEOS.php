<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GroupEOS extends Model
{
    protected $table = 'group_eos';
    protected $fillable = [
        'id', 
        'group_id', 
        'activity_id', 
        'group_name', 
        'student_login', 
        'user_code', 
        'lastmodifier_login', 
        'date_eos', 
        'slot_eos', 
        'room_name', 
        'psubject_code', 
        'psubject_name',
        'term_id',
        'course_session',
    ];

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function slotDetail()
    {
        return $this->belongsTo('App\Models\Fu\Slot', 'slot', 'id');
    }
}
