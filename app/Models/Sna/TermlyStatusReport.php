<?php

namespace App\Models\Sna;

use Illuminate\Database\Eloquent\Model;

class TermlyStatusReport extends Model
{
    protected $table = 'termly_status_report';
    protected $fillable = ['term_no','student_login','student_code','term_id','status_id','second_status_id','curriculum_id','date_created','last_modified','locked','is_new'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
