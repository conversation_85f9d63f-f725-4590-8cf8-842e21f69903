<?php

namespace App\Models\Sna;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransferStudentSubject extends Model
{
    use HasFactory;
    protected $table = 'sna_tranfer_student_subject';
    protected $fillable = ['student_login','skill_code','grade','number_of_credit','course_start_date','course_end_date','subject_name','subject_code','no_attemp','note','real_no_attemp','campus_code','chuyen_co_so','status','is_start','has_sub','is_sub','applied','is_syllabus_update','special','alt_subject_list','is_lock_edit'];
}
