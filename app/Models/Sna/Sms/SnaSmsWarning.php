<?php

namespace App\Models\Sna\Sms;
use App\Models\Sms\Account;
use Illuminate\Database\Eloquent\Model;

class SnaSmsWarning extends Model
{
    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
    protected $table = 'sna_sms_warning';
    public $timestamps = false;
    public function account() {
        return $this->belongsTo(Account::class,'student_code', 'student_code')->orderBy('created_on', 'DESC');
    }
}

