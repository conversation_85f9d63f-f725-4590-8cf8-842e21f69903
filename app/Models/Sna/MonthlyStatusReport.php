<?php

namespace App\Models\Sna;

use Illuminate\Database\Eloquent\Model;

class MonthlyStatusReport extends Model
{
    protected $table = 'monthly_status_report';
    protected $fillable = ['term_no','student_login','student_code','month','year','status_id','second_status_id','curriculum_id','date_created','last_modified','locked','is_new','startday','endday'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
