<?php

namespace App\Models\Sna;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransferCampus extends Model
{
    use HasFactory;
    protected $table = 'sna_tranfer_campus';
    protected $fillable = ['user_middlename', 'user_givenname', 'user_DOB', 'user_email', 'user_address', 'user_telephone', 'cmt', 'ngaycap', 'noicap', 'created_date', 'brand_name', 'gender', 'grade', 'learn_start_day', 'curriculum_id', 'current_period', 'study_status', 'people_id', 'total_debt_subject', 'last_term_id', 'old_campus', 'new_campus', 'new_curriculum', 'note', 'chot', 'old_campus_code', 'new_campus_code', 'date_created','legal_entity','grade_create','kithu'];
}
