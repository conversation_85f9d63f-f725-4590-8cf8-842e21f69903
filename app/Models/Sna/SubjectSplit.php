<?php

namespace App\Models\Sna;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubjectSplit extends Model
{
    use HasFactory;
    protected $table = 'sna_subject_split';
    protected $fillable = ['subject_code','subject_name','subject_id','sub_subject_code','sub_subject_id','sub_subject_name'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
