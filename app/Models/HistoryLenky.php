<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HistoryLenky extends Model
{
    use HasFactory;
    protected $table = "history_lenky";
    protected $fillable = ['id', 'user_code', 'student_login', 'kythu', 'term_id', 'term_name', 'create_date'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
