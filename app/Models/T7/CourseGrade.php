<?php

namespace App\Models\T7;

use Illuminate\Database\Eloquent\Model;

class CourseGrade extends Model
{
    protected $table = 't7_course_grade';
    protected $fillable = ['id', 'course_id', 'groupid', 'login', 'val', 'creator_login', 'modifier_login', 'create_time', 'lastmodified_time', 'comment', 'grade_id', 'grade_weight', 'grade_group_id', 'grade_group_weight', 'grade_minimum_required', 'grade_group_minimum_required', 'syllabus_id', 'subject_id', 'is_used', 'subject_name', 'grade_name', 'grade_group_name', 'course_group_name', 'subject_code', 'term_id', 'term_name', 'is_final', 'is_resit', 'master_grade', 'group_val', 'temp', 'chksum', 'token', 'locked'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'lastmodified_time';

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }
}
