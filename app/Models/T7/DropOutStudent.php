<?php

namespace App\Models\T7;

use Illuminate\Database\Eloquent\Model;

class DropOutStudent extends Model
{
    protected $table = 't7_drop_out_student';
    protected $fillable = ['drop_out_id','student_login','interrupt_times','interrupt_status','reenroll_date'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }
}
