<?php

namespace App\Models\T7;

use Illuminate\Database\Eloquent\Model;

class AcademicDecisionStudents extends Model
{
    protected $table = 'academic_decision_students';
    protected $fillable = ['drop_out_id','student_login','interrupt_times','interrupt_status','reenroll_date'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }
}
