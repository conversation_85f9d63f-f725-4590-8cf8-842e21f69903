<?php

namespace App\Models\T7;

use Illuminate\Database\Eloquent\Model;

class Discipline extends Model
{
    protected $table = 't7_discipline';
    protected $fillable = ['decision_no','type','decision_name','signee','date_affected','term_id','file_name','note','modifier_login','date_created','date_of_decision'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function term()
    {
        return $this->belongsTo('App\Models\Fu\Term','term_id');
    }

    public function students()
    {
        return $this->hasMany('App\Models\T7\DisciplineStudent', 'discipline_id');
    }
}
