<?php

namespace App\Models\T7;

use Illuminate\Database\Eloquent\Model;
use App\Models\EnglishDetailLevel;

class CourseResult extends Model
{
    // punishment: 1 - vi pham, 2 - chuyen doi thi lai thanh hoc lai
    protected $table = 't7_course_result';
    protected $fillable = ['course_id', 'block', 'groupid', 'student_login', 'val', 'grade', 'create_time', 'creator_login', 'modified_time', 'modifier_login', 'subject_id', 'psubject_name', 'short_subject_name', 'start_date', 'end_date', 'pgroup_name', 'psubject_code', 'punishment', 'attendance', 'total_session', 'grade_detail', 'attendance_fail', 'attendance_absent', 'done_activity', 'not_done_activity', 'is_finish', 'subject_finish', 'complete_id', 'pterm_name', 'pcampus_name', 'term_id', 'pcampus_id', 'attendance_cutoff', 'source', 'note', 'approved_by', 'attendance_state', 'ctsv_note', 'attendance_detail', 'syllabus_id', 'minimum_required', 'done_session', 'teacher_login', 'temp', 'is_announced', 'lastupdated', 'total_exam', 'done_exam', 'total_grade', 'done_grade', 'final_date', 'final_result', 'resit_date', 'resit_result', 'final_taken', 'resit_taken', 'activity_id', 'skill_code', 'skill_code_update', 'study_status', 'chksum', 'taken_exam', 'number_of_credit', 'is_grade', 'lan_thu', 'transfer_sub', 'slot', 'loai_buoi_hoc', 'note_chuyen_diem', 'student_code'
                            ,'origin_campus_code','origin_course_result_id'
                            ];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'modified_time';
    const ABSENT = -1;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }
}
