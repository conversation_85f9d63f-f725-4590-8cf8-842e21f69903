<?php

namespace App\Models\T7;

use Illuminate\Database\Eloquent\Model;
class GradeGroup extends Model
{
    protected $table = 't7_grade_group';
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function grade() {
        return $this->hasMany('App\Models\T7\Grade', 'grade_group_id', 'id');
    }

    public function grades() {
        return $this->hasMany('App\Models\T7\Grade', 'grade_group_id', 'id');
    }
}
