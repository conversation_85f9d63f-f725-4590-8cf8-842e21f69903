<?php

namespace App\Models\T7;

use Illuminate\Database\Eloquent\Model;

class AcademicDecisionManagement extends Model
{
    protected $table = 'academic_decision_management';
    protected $fillable = ['so_quyet_dinh','nguoi_ky','drop_out_date','term_id','file_name','note','drop_type','drop_user','date_created','date_affected'];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        
        parent::__construct($attributes);
    }

    public function students()
    {
        return $this->hasMany('App\Models\T7\DropOutStudent', 'drop_out_id');
    }

    public function term()
    {
        return $this->belongsTo('App\Models\Fu\Term','term_id');
    }
}
