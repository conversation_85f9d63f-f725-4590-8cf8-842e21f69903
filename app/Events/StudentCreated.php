<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StudentCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $user_code;
    public $user_login;
    public $initial_balance;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($user_code, $user_login, $initial_balance = 0)
    {
        $this->user_code = $user_code;
        $this->user_login = $user_login;
        $this->initial_balance = $initial_balance;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
