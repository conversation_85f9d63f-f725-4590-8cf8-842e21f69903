<?php

namespace App\Events;

use App\Models\LQueue;
use App\Models\Fu\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class QueueCreate implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    public $user;
    public $lqueue;
    public $type;

    /**
     * Create a new event instance.
     *
     * @param User $user
     * @param LQueue $lqueue
     * @param $type
     */
    public function __construct(User $user, LQueue $lqueue, $type)
    {
        $this->user = $user;
        $this->lqueue = $lqueue;
        $this->type = $type;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('fee_statistic_' . $this->user->user_login);
    }
}
