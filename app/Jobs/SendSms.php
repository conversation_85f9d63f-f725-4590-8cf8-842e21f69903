<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Repositories\Admin\SmsRepository;
use Illuminate\Support\Facades\Log;

class SendSms implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public $sms_id;
    public $count_list;
    public $timeout = 7200;

    public function __construct($sms_id, $count_list)
    {
        $this->sms_id = $sms_id;
        $this->count_list = $count_list;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        SmsRepository::sendNow($this->sms_id, $this->count_list);
    }
}
