<?php

namespace App\Jobs;

use App\Repositories\Admin\EducateRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Request;

class ExportThieuNoMon implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    private $request;
    private $by_user_login = null;
    public $timeout = 7200;

    public function __construct($request, $user_login)
    {
        $this->request = $request;
        $this->by_user_login = $user_login;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        EducateRepository::thieuNoMon($this->request, $this->by_user_login);
    }

    public function failed()
    {
        if (Cache::has('thieu_no_mon')) {
            Cache::forget('thieu_no_mon');
        }
    }
}
