<?php

namespace App\Jobs;

use App\Repositories\Admin\EducateRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ExportGradebook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    private $data;
    private $by_user_login = null;
    public $timeout = 7200;

    public function __construct($data, $by_user_login = null)
    {
        $this->data = $data;
        $this->by_user_login = $by_user_login;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("---------- Start queue export grades book -----------");
        EducateRepository::exportGradebook($this->data, $this->by_user_login);
    }

    public function failed()
    {
        if (Cache::has('export_gradebook')) {
            Cache::forget('export_gradebook');
        } 
    }
}
