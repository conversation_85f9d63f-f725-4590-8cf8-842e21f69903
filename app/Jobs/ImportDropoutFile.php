<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Request;

class ImportDropoutFile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    private $request;
    private $by_user_login = null;
    private $status = "";
    private $date_affected = "";
    private $dropOutRepository;
    public $timeout = 7200;
    private $users = [];

    public function __construct($request, $status , $user_login, $date_affected, $dropOutRepository, $users)
    {
        $this->request = $request;
        $this->status = $status;
        $this->by_user_login = $user_login;
        $this->date_affected = $date_affected;
        $this->dropOutRepository = $dropOutRepository;
        $this->users = $users;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->dropOutRepository->updateUserStudy($this->request, $this->status, $this->date_affected, $this->users, $this->by_user_login);
    }

    public function failed()
    {
        if (Cache::has('dropdown_import_file')) {
            Cache::forget('dropdown_import_file');
        }
    }
}
