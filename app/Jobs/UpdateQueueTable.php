<?php


namespace App\Jobs;


use App\Events\QueueCreate;
use App\Models\LQueue;
use GuzzleHttp\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class UpdateQueueTable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $id;
    public $target;
    public $filename;
    public $type;
    public $user;

    public function __construct($user, $id, $target, $filename, $type)
    {
        $this->id = $id;
        $this->target = $target;
        $this->filename = $filename;
        $this->type = $type;
        $this->user = $user;
    }

    public function handle()
    {
        $queue = LQueue::find($this->id);
        $queue->target = $this->target;
        $queue->file_name = $this->filename;
        $queue->file_type = $this->type;
        $queue->status = 1;
        $queue->save();

        broadcast(new QueueCreate($this->user, $queue, 'update'));
    }
}
