<?php

namespace App\Listeners;

use App\Events\StudentCreated;
use App\Repositories\Admin\StudentWalletRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class CreateStudentWallet implements ShouldQueue
{
    use InteractsWithQueue;

    protected $walletRepository;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(StudentWalletRepository $walletRepository)
    {
        $this->walletRepository = $walletRepository;
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\StudentCreated  $event
     * @return void
     */
    public function handle(StudentCreated $event)
    {
        try {
            $result = $this->walletRepository->autoCreateWalletForNewStudent(
                $event->user_code,
                $event->user_login,
                $event->initial_balance
            );

            if ($result['success']) {
                Log::info('Auto wallet creation successful', [
                    'user_code' => $event->user_code,
                    'user_login' => $event->user_login,
                    'initial_balance' => $event->initial_balance
                ]);
            } else {
                Log::warning('Auto wallet creation failed', [
                    'user_code' => $event->user_code,
                    'user_login' => $event->user_login,
                    'error' => $result['message']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Auto wallet creation exception', [
                'user_code' => $event->user_code,
                'user_login' => $event->user_login,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \App\Events\StudentCreated  $event
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(StudentCreated $event, $exception)
    {
        Log::error('Auto wallet creation job failed', [
            'user_code' => $event->user_code,
            'user_login' => $event->user_login,
            'error' => $exception->getMessage()
        ]);
    }
}
