<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class LichThiTheoBoMonMultipleExport implements WithMultipleSheets
{
    use Exportable;

    protected $ds_lich_thi_theo_bo_mon;
    protected $excel_info;

    public function __construct($ds_lich_thi_theo_bo_mon, $excel_info)
    {
        $this->ds_lich_thi_theo_bo_mon = $ds_lich_thi_theo_bo_mon;
        $this->excel_info = $excel_info;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];

        for ($i = 0; $i < count($this->ds_lich_thi_theo_bo_mon); $i++) {
            $is_error = $this->ds_lich_thi_theo_bo_mon[$i]['is_error'];
            $is_ds_cam_thi = $this->ds_lich_thi_theo_bo_mon[$i]['is_ds_cam_thi'] ?? false;

            $group_name = "";
            $block = null;
            $members = [];
            $ds_error = [];

            if ($is_error) {
                $ds_error = $this->ds_lich_thi_theo_bo_mon[$i]['ds_error'];
            } else {
                $group_name = $this->ds_lich_thi_theo_bo_mon[$i]['group_name'];
                $block = $this->ds_lich_thi_theo_bo_mon[$i]['block'];
                $members = $this->ds_lich_thi_theo_bo_mon[$i]['members'];
            }

            $sheets[] = new LichThiTheoBoMonExport($ds_error, $this->excel_info, $group_name, $block, $members, $is_ds_cam_thi);
        }

        return $sheets;
    }
}
