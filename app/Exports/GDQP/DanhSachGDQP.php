<?php

namespace App\Exports\GDQP;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;

class DanhSachGDQP implements WithStyles, FromView
{
    private $data;
    public function __construct($data)
    {
        $this->data = $data;
    }

    public function view() :View
    {
        return view('excel.gdqp.danh_sach_gdqp', [
            'datas' => $this->data,
        ]);
    }

    public function styles(Worksheet $sheet)
    {
        $totalRow = count($this->data) + 1;
       
        $sheet->getPageSetup()->setFitToPage(true);
        $sheet->getPageSetup()->setFitToWidth(1);
        $sheet->getPageSetup()->setFitToHeight(0);

        $sheet->getColumnDimension('A')->setWidth(8);  
        $sheet->getColumnDimension('B')->setWidth(15);  
        $sheet->getColumnDimension('C')->setWidth(20);  
        $sheet->getColumnDimension('D')->setWidth(18);  
        $sheet->getColumnDimension('E')->setWidth(10);  
        $sheet->getColumnDimension('F')->setWidth(15);  

        $sheet->getStyle('A1:F1')->getFont()->setBold(true);

        $sheet->getStyle('A1:K' . $totalRow)->getFont()->setName('Times New Roman');

        $sheet->getStyle('A1:F' . $totalRow)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ])->getAlignment()->setWrapText(true);



    }
}
