<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class DanhSachThiEOSTheoBoMonMultipleExport implements WithMultipleSheets
{
    use Exportable;

    protected $ds_lop_eos;
    protected $excel_info;

    public function __construct($ds_lop_eos, $excel_info)
    {
        $this->ds_lop_eos = $ds_lop_eos;
        $this->excel_info = $excel_info;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];

        for ($i = 0; $i < count($this->ds_lop_eos); $i++) {
            $is_error = $this->ds_lop_eos[$i]['is_error'];

            $group_name = "";
            $ds_members_total = [];
            $ds_error = [];

            if ($is_error) {
                $ds_error = $this->ds_lop_eos[$i]['ds_error'];
            } else {
                $group_name = $this->ds_lop_eos[$i]['group_name'];
                $ds_members_total = $this->ds_lop_eos[$i]['ds_members_total'];
            }

            $sheets[] = new DanhSachThiEOSTheoBoMonExport($ds_error, $this->excel_info, $group_name, $ds_members_total);
        }

        return $sheets;
    }
}
