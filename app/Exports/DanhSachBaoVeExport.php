<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class DanhSachBaoVeExport implements WithMultipleSheets
{
    use Exportable;

    protected $ds_bao_ve;

    public function __construct($ds_bao_ve)
    {
        $this->ds_bao_ve = $ds_bao_ve;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];

        for ($i = 0; $i < count($this->ds_bao_ve); $i++) {
            $data = $this->ds_bao_ve[$i];
            $sheets[] = new DanhSachBaoVeItemExport($data);
        }

        return $sheets;
    }
}
