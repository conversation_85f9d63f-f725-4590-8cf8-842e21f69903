<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class DebtExport implements FromCollection, WithHeadings, WithMapping
{
    protected $debts;

    public function __construct($debts)
    {
        $this->debts = $debts;
    }

    public function collection()
    {
        return $this->debts;
    }

    public function headings(): array
    {
        return [
            'ID',
            'Mã SV',
            'Tài khoản',
            '<PERSON><PERSON> học',
            'Loại phí',
            '<PERSON><PERSON> tiền gốc',
            'Phần trăm miễn giảm (%)',
            '<PERSON>ố tiền miễn giảm',
            '<PERSON><PERSON> do miên giảm',
            'Số tiền phải trả',
            'Đã thanh toán',
            'Còn nợ',
            'Trạng thái',
            '<PERSON><PERSON> tả',
            'Người tạo',
            '<PERSON><PERSON><PERSON> tạo'
        ];
    }

    public function map($debt): array
    {
        return [
            $debt->id,
            $debt->user_code,
            $debt->user_login,
            $debt->term_name ?? 'N/A',
            $debt->fee_type_name ?? 'N/A',
            number_format($debt->original_amount ?? $debt->amount) . ' đ',
            $debt->discount_percentage ?? 0,
            number_format($debt->discount_amount ?? 0) . ' đ',
            $debt->discount_reason ?? '',
            number_format($debt->amount) . ' đ',
            number_format($debt->paid_amount) . ' đ',
            number_format($debt->amount - $debt->paid_amount) . ' đ',
            $this->getStatusText($debt->status),
            $debt->description ?? '',
            $debt->created_by ?? '',
            $debt->created_at ? $debt->created_at->format('d/m/Y H:i') : 'N/A'
        ];
    }

    private function getStatusText($status)
    {
        switch ($status) {
            case 0:
                return 'Chưa thanh toán';
            case 1:
                return 'Đã thanh toán';
            case 2:
                return 'Đã hủy';
            default:
                return 'Không xác định';
        }
    }
}
