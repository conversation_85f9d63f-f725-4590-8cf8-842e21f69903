<?php

namespace App\Exports;

use App\Models\Fu\Group;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ScheduleExamByTermExport implements FromCollection,WithHeadings,WithMapping
{
    protected $termId;
    protected $blockId;
    protected $subjectCode;
    public function __construct($termId, $blockId, $subjectCode)
    {
        $this->termId = $termId;
        $this->blockId = $blockId;
        $this->subjectCode = $subjectCode;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $datas = Group::select([
            // 'activity.day',
            DB::raw("DATE_FORMAT(activity.day, '%c/%e/%Y') AS day"),
            'activity.slot',
            'activity.room_name',
            'activity.leader_login',
            'list_group.psubject_code',
            'activity.course_slot',
            'list_group.group_name'
        ])
        ->join('activity', 'list_group.id', '=', 'activity.groupid')
        ->join('session_type', 'activity.session_type', '=', 'session_type.id')
        ->where('list_group.pterm_id', $this->termId)
        ->where('session_type.is_exam', 1);

        if ($this->blockId != -1 && $this->blockId != null) {
            $datas = $datas->where('list_group.block_id', $this->blockId);
        }

        if ($this->subjectCode != -1 && $this->subjectCode != null) {
            $datas = $datas->where('list_group.psubject_code', $this->subjectCode);
        }
        
        $datas = $datas->orderBy('list_group.id')->get();
        return $datas;
    }
    
    /**
     * Returns headers for report
     * @return array
     */
    public function headings(): array {
        return [
            'Ngày',
            'Ca học',
            'Tên phòng',
            'Giảng viên',
            'Mã môn',
            'Buổi học',
            'Tên lớp'
        ];
    }

    // public function prepareForValidation($data, $index)
    // {
    //     //Fix that Excel's numeric date (counting in days since 1900-01-01)
    //     $dayx = new \DateTime($data['day']);
    //     $data['day'] = \PhpOffice\PhpSpreadsheet\Shared\Date::PHPToExcel($dayx)->format('m/d/Y');
    //     //...
    // }
    
    public function map($course): array {
        return [
            $course->day,
            $course->slot,
            $course->room_name,
            $course->leader_login,
            $course->psubject_code,
            $course->course_slot,
            $course->group_name,
        ];
    }
}
