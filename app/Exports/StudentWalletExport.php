<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;

class StudentWalletExport implements FromQuery, WithHeadings, WithMapping, WithTitle
{
    protected $query;

    public function __construct($query)
    {
        $this->query = $query;
    }

    public function query()
    {
        return $this->query;
    }

    public function headings(): array
    {
        return [
            'STT',
            'Mã sinh viên',
            'Tài khoản',
            'Số dư ví (VNĐ)',
            'Trạng thái',
            '<PERSON><PERSON> do khóa',
            '<PERSON><PERSON><PERSON> tạo',
            '<PERSON><PERSON><PERSON> cập nhật'
        ];
    }

    public function map($wallet): array
    {
        static $index = 0;
        $index++;

        return [
            $index,
            $wallet->user_code,
            $wallet->user_login,
            number_format($wallet->balance, 0, ',', '.'),
            $wallet->is_locked ? 'Đã khóa' : 'Hoạt động',
            $wallet->lock_reason ?? '',
            $wallet->created_at->format('d/m/Y H:i:s'),
            $wallet->updated_at->format('d/m/Y H:i:s')
        ];
    }

    public function title(): string
    {
        return 'Danh sách ví sinh viên';
    }
}
