<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class FreeRelearnExport implements FromView
{
    private $data;
    public $type;

    public function __construct($data, $type)
    {
        $this->data = $data;
        $this->type = $type;
    }

    public function view(): View
    {
        if ($this->type == 'free_relearn') {
            return view('excel.free_relearn.export_free_relearn', [
                'data' => $this->data,
            ]);
        }else if($this->type == 'request_confirm'){
            return view('excel.free_relearn.export_request_conf_relearn', [
                'data' => $this->data,
            ]);
        }
    }
}
