<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class XepGiamThiMutipleExport implements WithMultipleSheets
{
    use Exportable;

    protected $ds_giam_thi;
    protected $excel_info;

    public function __construct($ds_giam_thi, $excel_info)
    {
        $this->ds_giam_thi = $ds_giam_thi;
        $this->excel_info = $excel_info;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];

        for ($i = 0; $i < count($this->ds_giam_thi); $i++) {
            $is_error = $this->ds_giam_thi[$i]['is_error'];

            $group_name = "";
            $ds_mon = [];
            $ds_error = [];

            if ($is_error) {
                $ds_error = $this->ds_giam_thi[$i]['ds_error'];
            } else {
                $group_name = $this->ds_giam_thi[$i]['group_name'];
                $ds_mon = $this->ds_giam_thi[$i]['ds_mon'];
            }

            $sheets[] = new XepGiamThiExport($ds_error, $this->excel_info, $group_name, $ds_mon);
        }

        return $sheets;
    }
}
