<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class AdmissionTemplateExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function array(): array
    {
        // Return sample data rows matching the headers
        return [
            [
                'Nguyễn',           // ho (required)
                'Văn',              // ten_dem (optional)
                'An',               // ten (required)
                '15/05/1995',       // ngay_sinh (required, format: dd/mm/yyyy)
                'Nam',              // gioi_tinh (required: Nam/Nữ)
                'TS2024001',        // ma_tuyen_sinh (optional)
                '<EMAIL>', // email (optional)
                '0123456789',       // so_dien_thoai (optional)
                '123 Đường ABC, Phường XYZ', // dia_chi (required)
                'Hà Nội',           // tinh_thanh_pho (required)
                'Hoàn Kiếm',        // quan_huyen (optional)
                'Phường Hàng Bạc',  // phuong_xa (optional)
                'cao đẳng',         // bac_dao_tao (required: cao đẳng, liên thông, trung cấp)
                'tin học ứng dụng', // chuong_trinh_dao_tao (required)
                'trung học phổ thông', // trinh_do_van_hoa (required: trung học cơ sở, trung học phổ thông)
                'Đợt 1 - 2024',     // thoi_gian_tuyen_sinh (required)
                'Đối tượng ưu tiên 1', // doi_tuong_uu_tien (optional)
                'Ghi chú thêm'      // ghi_chu (optional)
            ],
            [
                'Trần',
                'Thị',
                'Bình',
                '20/08/1996',
                'Nữ',
                'TS2024002',
                '<EMAIL>',
                '0987654321',
                '456 Đường DEF, Phường UVW',
                'Hồ Chí Minh',
                'Quận 1',
                'Phường Bến Nghé',
                'liên thông',
                'kế toán doanh nghiệp',
                'trung học cơ sở',
                'Đợt 2 - 2024',
                '',
                ''
            ]
        ];
    }

    public function headings(): array
    {
        return [
            'ho',                      // A - Required (Họ)
            'ten_dem',                 // B - Optional (Tên đệm)
            'ten',                     // C - Required (Tên)
            'ngay_sinh',               // D - Required (Ngày sinh YYYY-MM-DD)
            'gioi_tinh',               // E - Required (Giới tính Nam/Nữ)
            'ma_tuyen_sinh',           // F - Optional (Mã tuyển sinh)
            'email',                   // G - Optional (Email)
            'so_dien_thoai',           // H - Optional (Số điện thoại)
            'dia_chi',                 // I - Required (Địa chỉ)
            'tinh_thanh_pho',          // J - Required (Tỉnh/Thành phố)
            'quan_huyen',              // K - Optional (Quận/Huyện)
            'phuong_xa',               // L - Optional (Phường/Xã)
            'bac_dao_tao',             // M - Required (Bậc đào tạo)
            'chuong_trinh_dao_tao',    // N - Required (Chương trình đào tạo)
            'trinh_do_van_hoa',        // O - Required (Trình độ văn hóa)
            'thoi_gian_tuyen_sinh',    // P - Required (Thời gian tuyển sinh)
            'doi_tuong_uu_tien',       // Q - Optional (Đối tượng ưu tiên)
            'ghi_chu'                  // R - Optional (Ghi chú)
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Set title
        $sheet->setCellValue('A1', 'DANH SÁCH THÔNG TIN TUYỂN SINH');
        $sheet->mergeCells('A1:R1');

        // Style title
        $sheet->getStyle('A1')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 16,
                'color' => ['rgb' => 'FF0000'] // Red color
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
            ]
        ]);

        // Insert empty row
        $sheet->insertNewRowBefore(2, 1);

        // Add Vietnamese column names in row 2
        $vietnameseHeaders = [
            'Họ *',
            'Tên đệm',
            'Tên *',
            'Ngày sinh *',
            'Giới tính *',
            'Mã tuyển sinh',
            'Email',
            'Số điện thoại',
            'Địa chỉ *',
            'Tỉnh/Thành phố *',
            'Quận/Huyện',
            'Phường/Xã',
            'Bậc đào tạo *',
            'Chương trình đào tạo *',
            'Trình độ văn hóa *',
            'Thời gian tuyển sinh *',
            'Đối tượng ưu tiên',
            'Ghi chú'
        ];

        foreach ($vietnameseHeaders as $index => $header) {
            $column = chr(65 + $index); // A, B, C, etc.
            $sheet->setCellValue($column . '2', $header);
        }

        // Move field names to row 3
        $headers = $this->headings();
        foreach ($headers as $index => $header) {
            $column = chr(65 + $index); // A, B, C, etc.
            $sheet->setCellValue($column . '3', $header);
        }

        // Style Vietnamese headers (row 2)
        $sheet->getStyle('A2:R2')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'E6E6FA'] // Light purple background
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ]);

        // Style field name headers (row 3)
        $sheet->getStyle('A3:R3')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 10,
                'color' => ['rgb' => '0000FF'] // Blue for field names
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'F0F0F0'] // Light gray background
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ]);

        // Color required fields (red) and optional fields (black) in Vietnamese headers
        $requiredColumns = ['A', 'C', 'D', 'E', 'I', 'J', 'M', 'N', 'O', 'P']; // Required fields
        foreach ($requiredColumns as $col) {
            $sheet->getStyle($col . '2')->getFont()->setColor(new Color('FF0000')); // Red
        }

        // Move data to start from row 4
        $data = $this->array();
        foreach ($data as $rowIndex => $row) {
            $actualRow = $rowIndex + 4; // Start from row 4
            foreach ($row as $colIndex => $value) {
                $column = chr(65 + $colIndex);
                $sheet->setCellValue($column . $actualRow, $value);
            }
        }

        // Style data rows
        $lastRow = count($data) + 3;
        $sheet->getStyle('A4:R' . $lastRow)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ]);

        // Add instructions
        $instructionRow = $lastRow + 2;
        $sheet->setCellValue('A' . $instructionRow, 'HƯỚNG DẪN:');
        $sheet->getStyle('A' . $instructionRow)->getFont()->setBold(true);

        $instructions = [
            '• Các trường có dấu (*) là bắt buộc phải nhập',
            '• Ngày sinh có thể nhập theo các định dạng sau:',
            '  - dd/mm/yyyy (VD: 12/08/1998) - Khuyến khích',
            '  - dd-mm-yyyy (VD: 12-08-1998)',
            '  - yyyy-mm-dd (VD: 1998-08-12)',
            '  - dd.mm.yyyy (VD: 12.08.1998)',
            '• Giới tính chỉ nhập: Nam hoặc Nữ',
            '• Bậc đào tạo: cao đẳng, liên thông, trung cấp',
            '• Chương trình đào tạo: báo chí, công nghệ kỹ thuật điện điện tử, kế toán doanh nghiệp, tin học ứng dụng',
            '• Trình độ văn hóa: trung học cơ sở, trung học phổ thông',
            '• Thời gian tuyển sinh: ví dụ "Đợt 1 - 2024", "Đợt 2 - 2024"',
            '• Không được để trống các ô bắt buộc',
            '• Xóa các dòng mẫu này trước khi import'
        ];

        foreach ($instructions as $index => $instruction) {
            $row = $instructionRow + 1 + $index;
            $sheet->setCellValue('A' . $row, $instruction);
            $sheet->getStyle('A' . $row)->getFont()->setColor(new Color('0000FF')); // Blue
        }

        return [];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15,  // Họ
            'B' => 15,  // Tên đệm
            'C' => 15,  // Tên
            'D' => 15,  // Ngày sinh
            'E' => 12,  // Giới tính
            'F' => 15,  // Mã tuyển sinh
            'G' => 25,  // Email
            'H' => 15,  // Số điện thoại
            'I' => 30,  // Địa chỉ
            'J' => 20,  // Tỉnh/Thành phố
            'K' => 15,  // Quận/Huyện
            'L' => 15,  // Phường/Xã
            'M' => 15,  // Bậc đào tạo
            'N' => 25,  // Chương trình đào tạo
            'O' => 20,  // Trình độ văn hóa
            'P' => 20,  // Thời gian tuyển sinh
            'Q' => 20,  // Đối tượng ưu tiên
            'R' => 20,  // Ghi chú
        ];
    }

    public function title(): string
    {
        return 'Danh sách tuyển sinh';
    }
}
