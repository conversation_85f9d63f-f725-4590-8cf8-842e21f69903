<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\BeforeExport;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Illuminate\Contracts\Support\Responsable;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;

class ExportNewListStudentFailExam implements Responsable, FromView, WithTitle, ShouldAutoSize, WithEvents
{
    use Exportable, RegistersEventListeners;

    protected $type; // 1 là danh sách không đủ điều kiện, 2 là môn bình thường
    protected $key;
    protected $datas;
    protected $term; 
    protected $block;
    protected $course;
    public function __construct($datas, $term, $block, $course)
    {
        $this->type = 1;
        $this->datas = $datas;
        $this->term = $term;
        $this->block = $block;
        $this->course = $course;
    }

    public function view(): View
    {
        $listFails = array_merge([], ...array_column($this->datas, 'list_fail'));
        // dd($listFails);
        return view('excel.calendar.new.export_course_fail', [
            'type' => $this->type,
            'data' => $listFails,
            'block' => $this->block,
            'term' => $this->term,
            'course' => $this->course,
        ]);
    }

    public function title(): string
    {
        return "Danh sách cấm thi";
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            BeforeExport::class  => function (BeforeExport $event) {
                $event->writer->setCreator('dev');
            },
            AfterSheet::class    => function (AfterSheet $event) {
                $listFails = array_merge([], ...array_column($this->datas, 'list_fail'));
                $event->sheet
                    ->getPageSetup()
                    ->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_PORTRAIT)
                    ->setPaperSize(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::PAPERSIZE_A4);

                //create default style
                $default_font_style = [
                    'font' => ['name' => 'Times New Roman', 'size' => 11]
                ];

                $event->sheet->getDelegate()->getColumnDimension('J')->setWidth(60);

                $event->sheet->getStyle('J5:J' . (5 + count($listFails)))->getAlignment()->setWrapText(true);

                //style default
                $event->sheet->getParent()->getDefaultStyle()->applyFromArray($default_font_style);

                //style header
                $event->sheet->getStyle('A1:C1')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

                //style table
                $table_style_array = array(
                    'borders' => array(
                        'allBorders' => array(
                            'borderStyle' => Border::BORDER_THIN,
                        )
                    )
                );


                $cellTable = 'A5:K' . (5 + count($listFails));
                $event->sheet->getStyle($cellTable)->applyFromArray($table_style_array);

                //a4 page setup
                $event->sheet->getPageSetup()->setFitToWidth(1);
                $event->sheet->getPageSetup()->setFitToHeight(0);
            },
        ];
    }
}
