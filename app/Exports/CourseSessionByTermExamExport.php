<?php

namespace App\Exports;

use App\Models\Fu\Course;
use App\Models\Fu\User;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class CourseSessionByTermExamExport implements FromCollection,WithHeadings,WithMapping
{
    protected $termId;
    public function __construct($termId)
    {
        $this->termId = $termId;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return Course::select([
            'course.id',
            DB::raw('grade_syllabus.id as syllabus_id'),
            'grade_syllabus.subject_code',
            't7_syllabus_plan.course_session',
        ])
        ->join('grade_syllabus', 'grade_syllabus.id', '=', 'course.syllabus_id')
        ->join('t7_syllabus_plan', 'grade_syllabus.id', '=', 't7_syllabus_plan.syllabus_id')
        ->join('session_type', 't7_syllabus_plan.session_type', '=', 'session_type.id')
        ->where('course.term_id', $this->termId)
        ->where('session_type.is_exam', 1)
        ->orderBy('course.id')
        ->orderBy('t7_syllabus_plan.course_session')
        ->get();
    }
    /**
     * Returns headers for report
     * @return array
     */
    public function headings(): array {
        return [
            'Ngày',
            'Ca học',
            'Tên phòng',
            'Giảng viên',
            'Mã môn',
            'Buổi học',
            'Tên lớp'
        ];
    }
    
    public function map($course): array {
        return [
            '',
            '',
            '',
            '',
            $course->subject_code,
            $course->course_session,
            '',
        ];
    }
}
