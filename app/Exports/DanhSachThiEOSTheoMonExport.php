<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithEvents;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Illuminate\Contracts\Support\Responsable;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;

class DanhSachThiEOSTheoMonExport implements Responsable, FromView, WithTitle, ShouldAutoSize, WithEvents
{
    use Exportable, RegistersEventListeners;

    private $ds_members_total;
    private $excel_info;

    private $data = [];

    public function __construct($ds_members_total, $excel_info)
    {
        $this->ds_members_total = $ds_members_total;
        $this->excel_info = $excel_info;
    }

    public function view(): View
    {
        $this->data = [];

        $this->data = $this->ds_members_total;

        return view('excel.eos.export_ds_thi_eos', [
            'data' => $this->data,
            'excel_info' => $this->excel_info,
        ]);
    }

    public function title(): string
    {
        $group_name = $this->excel_info['group_name'];
        return $group_name ?? "";
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function (AfterSheet $event) {
                $event->sheet
                    ->getPageSetup()
                    ->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_PORTRAIT);

                //create default style
                $default_font_style = [
                    'font' => ['name' => 'Times New Roman', 'size' => 11]
                ];

                //style default
                $event->sheet->getParent()->getDefaultStyle()->applyFromArray($default_font_style);
                //style header
                $event->sheet->getStyle('A1:J1')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
                $event->sheet->getStyle('A1:J1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                $event->sheet->getStyle('A1:J1')->getFont()->setBold(true);
                //style table
                $table_style_array = array(
                    'borders' => array(
                        'allBorders' => array(
                            'borderStyle' => Border::BORDER_THIN,
                        )
                    )
                );

                $cellTable = 'A1:J' . (1 + count($this->data));
                $event->sheet->getStyle($cellTable)->applyFromArray($table_style_array);
            },
        ];
    }
}
