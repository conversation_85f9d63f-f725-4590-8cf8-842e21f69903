<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\BeforeExport;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Illuminate\Contracts\Support\Responsable;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Maatwebsite\Excel\Concerns\WithDrawings;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;

class ExportNewListStudentExam implements Responsable, FromView, WithTitle, ShouldAutoSize, WithEvents, WithDrawings
{
    use Exportable, RegistersEventListeners;

    protected $type; // 1 là thi ASM, 1 là EOS
    protected $key;
    protected $data;
    protected $term; 
    protected $block; 
    protected $course;
    public function __construct($key, $data, $term, $block, $course, $type = 1)
    {
        $this->type = $type;
        $this->key = $key;
        $this->data = $data;
        $this->term = $term;
        $this->block = $block;
        $this->course = $course;
    }

    public function view(): View
    {
        return view('excel.calendar.new.export_course', [
            'type' => $this->type,
            'key' => $this->key,
            'data' => $this->data,
            'term' => $this->term,
            'course' => $this->course,
        ]);
    }

    public function drawings()
    {
        $drawing = new Drawing();
        $drawing->setName('Logo');
        $drawing->setDescription('This is my logo');
        $drawing->setPath(public_path('images/logo.png'));
        $drawing->setHeight(60);
        $drawing->setCoordinates('B1');

        return $drawing;
    }

    public function title(): string
    {
        $group = $this->data['group'];
        return $group->group_name ?? "";
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {   
        return [
            BeforeExport::class  => function (BeforeExport $event) {
                $event->writer->setCreator('dev');
            },
            AfterSheet::class    => function (AfterSheet $event) {
                $event->sheet
                    ->getPageSetup()
                    ->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_PORTRAIT)
                    ->setPaperSize(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::PAPERSIZE_A4);

                $event->sheet
                    ->getSheetView()
                    ->setZoomScale(80);

                // $event->sheet
                //     ->getPageMargins()
                //     ->setTop(1.5)
                //     ->setRight(1.5)
                //     ->setLeft(2.0)
                //     ->setBottom(1.5);

                $event->sheet->getDelegate()->getColumnDimension('A')->setWidth(6);                     
                $event->sheet->getDelegate()->getColumnDimension('B')->setWidth(12);                     
                $event->sheet->getDelegate()->getColumnDimension('C')->setWidth(23);                     
                $event->sheet->getDelegate()->getColumnDimension('D')->setWidth(16);                     
                $event->sheet->getDelegate()->getColumnDimension('E')->setWidth(12);                     
                $event->sheet->getDelegate()->getColumnDimension('F')->setWidth(13);                     
                $event->sheet->getDelegate()->getColumnDimension('G')->setWidth(14);                     
                $event->sheet->getDelegate()->getColumnDimension('H')->setWidth(16);                     
                $event->sheet->getDelegate()->getColumnDimension('I')->setWidth(18);

                $event->sheet
                    ->getHeaderFooter()
                    ->setOddFooter('&L' . '07.03.03-BM/FPL/HDCV/FE 1/0' . '&R&P/&N');

                //create default style
                $default_font_style = [
                    'font' => ['name' => 'Times New Roman', 'size' => 11]
                ];

                //style default
                $event->sheet->getParent()->getDefaultStyle()->applyFromArray($default_font_style);
                //style header
                $event->sheet->getStyle('A8:I7')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
                //style table
                $table_style_array = array(
                    'borders' => array(
                        'allBorders' => array(
                            'borderStyle' => Border::BORDER_THIN,
                        )
                    )
                );

                $numMember = array_merge([], ...array_column($this->data['course'], 'members'));
                if ($this->data == null || count($numMember) <= 0) {
                    $cellTable = 'A8:I' . (8 + 8);
                } else {
                    $cellTable = 'A8:I' . (8 + count($numMember));
                }

                $event->sheet->getStyle($cellTable)->applyFromArray($table_style_array);

                //logo
                $event->sheet
                    ->getStyle('A1:C3')
                    ->getAlignment()
                    ->setWrapText(true)
                    ->setVertical(Alignment::VERTICAL_CENTER)
                    ->setHorizontal(Alignment::HORIZONTAL_CENTER);

                //danh sach sinh vien thi cuoi ky
                $event->sheet
                    ->getStyle('D1:I3')
                    ->getAlignment()
                    ->setWrapText(true)
                    ->setVertical(Alignment::VERTICAL_CENTER)
                    ->setHorizontal(Alignment::HORIZONTAL_CENTER);

                //phong thi - mon
                $event->sheet
                    ->getStyle('A5:I6')
                    ->getAlignment()
                    ->setWrapText(true)
                    ->setVertical(Alignment::VERTICAL_CENTER)
                    ->setHorizontal(Alignment::HORIZONTAL_CENTER);

                $event->sheet
                    ->getRowDimension('5')
                    ->setRowHeight(50, 'px');

                $event->sheet
                    ->getRowDimension('7')
                    ->setRowHeight(40, 'px');

                //a4 row height
                $startRow = 9;
                if ($this->data != null && count($numMember) > 0) {
                    $totalRow = $startRow + count($numMember);
                    for ($i = $startRow; $i < $totalRow; $i++) {
                        $event->sheet
                            ->getRowDimension($i)
                            ->setRowHeight(30, 'px');
                    }
                }

                //a4 page setup
                $event->sheet->getPageSetup()->setFitToWidth(1);
                $event->sheet->getPageSetup()->setFitToHeight(0);
            },
        ];
    }
}
