<?php

namespace App\Exports;

use App\Models\EnglishDetail;
use App\Models\EnglishDetailLevel;
use App\Models\Fee\Fee;
use App\Models\Fee\FeeDetail;
use App\Models\Fee\FeeMail;
use App\Models\Fee\Student;
use App\Models\Fu\Term;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class FeeExport implements FromView, ShouldAutoSize, ShouldQueue
{
    const PHI_KY_TOI = 1;
    const KY_0 = 0;
    const KY_7 = 7;
    const PHI_HOC_KY = 1;
    const PHI_TIENG_ANH = 2;
    const PHI_SACH = 3;
    const PHI_HOC_LAI = 4;
    private $data;
    private $status;
    private $term_id;

    public function setData($data)
    {
        $this->status = $data->status;
        $this->term_id = $data->term_id;
    }

    public function view(): View
    {
        $status = $this->status;
        $term_id = $this->term_id;
        $phi_hoc_ky = self::PHI_HOC_KY;
        $phi_tieng_anh = self::PHI_TIENG_ANH;
        $phi_sach = self::PHI_SACH;
        $phi_hoc_lai = self::PHI_HOC_LAI;
        $study_status = config('status')->trang_thai_hoc;
        $complete = 0;
        $not_complete = 0;
        $term = Term::where('id', $term_id)->first();
        $fees = Fee::select('user_code','ki_thu','user_code','brand_code','ki_thu','bo_sung','mien_giam','study_status','study_wallet')->when($status, function ($query, $status) {
            return $query->whereIn('study_status', $status ?? [1,3,10]);
        })->where('ki_thu', '>', 0)->get();

        foreach ($fees as $fee)
        {
            $english_description = '';
            $english = EnglishDetail::select('id')->where('user_code', $fee->user_code)->first();
            if ($english) {
                $last_english = EnglishDetailLevel::select('amount', 'note', 'payment_status')->where('english_id', $english->id)->orderBy('create_time','desc')->first();
                if ($last_english && $last_english->amount > 0) {
                    if ($last_english->note == "Tiếng anh học đi") {
                        $english_description = ($last_english->payment_status ? 'Hoàn thành':'Chưa hoàn thành');
                    }
                }
            }
            $ki_thu = $fee->ki_thu + 1;
            $fee_mail = FeeMail::select('tieng_anh', 'english_level', 'note')
                ->where('user_code', $fee->user_code)
                ->where('term_name', $term->term_name)
                ->where('brand_code', $fee->brand_code)
                ->where('ki_thu',$fee->ki_thu + 1)
                ->first();
            $bo_sung = $fee->bo_sung;
            $mien_giam = $fee->mien_giam;
            $phi_hoan_thanh = 0;
            $phi_chua_hoan_thanh = 0;
            $fee->study_status = (object)$study_status[$fee->study_status];

            $hoc_ky = 0;
            $tien_sach = 0;
            $tieng_anh = 0;
            $english_level = 0;
            $note = "";
            $fee->details = FeeDetail::select('status', 'type_fee', 'discount', 'amount')->where('ki_thu', $ki_thu)->whereIn('type_fee', [1,2,5])->where('fee_id', $fee->id)->get();
            if ($fee->details->count()) {
                foreach ($fee->details as $item) {
                    if ($item->status == 0) {
                        $phi_chua_hoan_thanh++;
                    } else {
                        $phi_hoan_thanh++;
                    }
                    if ($item->type_fee == 1) {
                        $hoc_ky = $item->discount ? (($item->amount - $item->discount)) : $item->amount;
                    }
                    if ($item->type_fee == 2) {
                        $tien_sach = $item->discount ? (($item->amount - $item->discount)) : $item->amount;
                    }
                }
            }
            if ($fee_mail) {
                $tieng_anh = $fee_mail->tieng_anh;
                $english_level = $fee_mail->english_level;
                $note = $fee_mail->note;
            }
            $fee->hoc_ky = $hoc_ky;
            $fee->tien_sach = $tien_sach;
            $fee->note = $note;
            $fee->tieng_anh = $tieng_anh;
            $fee->english_level = $english_level;
            $so_thu = ($hoc_ky + $tien_sach + $tieng_anh) - $fee->study_wallet + $bo_sung - $mien_giam;
            $fee_status = 0;
            if ($so_thu <= 0) {
                $complete += 1;
                $so_thu = 0;
                $fee_status = 1;
            } else {
                $not_complete += 1;
            }
            $fee->con_thieu = $so_thu;
            $fee->fee_status = $fee_status;
            $fee->hoan_thanh = $phi_hoan_thanh;
            $fee->chua_hoan_thanh = $phi_chua_hoan_thanh;
            $fee->english_description = $english_description;
        }

        return view('excel.fee', [
            'result' => [$fees, $complete, $not_complete],
        ]);
    }

    public function handle()
    {
        $this->user->notify(new ExportReady());
    }

}
