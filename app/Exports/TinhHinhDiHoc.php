<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithTitle;
use Illuminate\Contracts\View\View;

class TinhHinhDiHoc implements FromView, WithTitle
{
    /**
     * @return \Illuminate\Support\Collection
     */

    public $data;
    public function __construct($data)
    {
        $this->data = $data;
    }

    public function view(): View
    {
        return view('admin_v1.tinh_hinh_di_hoc.tinhhinhdihoc', [
            'data' => $this->data
        ]);
    }

    public function title(): string
    {
        return "Sheet1";
    }
}
