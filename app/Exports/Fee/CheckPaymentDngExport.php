<?php

namespace App\Exports\Fee;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;

class CheckPaymentDngExport implements FromView
{
    private $data;
    
    public function __construct($data)
    {
        $this->data = $data;
    }

    public function view(): View
    {
        return view('excel.fee.check_payment_dng', [
            'data' => $this->data,
        ]);
    }
}