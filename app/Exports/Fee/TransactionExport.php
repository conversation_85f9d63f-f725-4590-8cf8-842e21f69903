<?php

namespace App\Exports\Fee;

use App\Models\Fee\Transaction;
use App\Models\Fu\Service;
use App\Models\Fu\ServiceLog;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class TransactionExport implements FromView, ShouldAutoSize
{
    private $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function view(): View
    {
        $request = $this->data;
        $services = Service::all();
        $service = $request->service;
        $payment_status = $request->payment_status;
        $keyword = $request->keyword;
        $transactions = ServiceLog::with(['service:id,title,name','serviceRegister'])
            ->where('type', $service ?? $services->first()->id)
            ->when($payment_status, function ($query, $payment_status) {
                $query->where('payment_status', $payment_status == 1 ? 1:0);
            })
            ->when($keyword, function ($query, $keyword) {
                $query->where('user_id', 'like', "%$keyword%");
            })
            ->orderBy('payment_status')
            ->orderBy('created_at','desc')
            ->get();
        foreach ($transactions as $transaction) {
            $logs = Transaction::where('service_log_id', $transaction->id)->get();
            $transaction->log = $logs->implode('note', '<br>');
            $subject = '';
            if ($transaction->type == 2) {
                $subject = $transaction->serviceRegister->map(function($item, $key) {
                    return $item->subject_code;
                });
                $subject = $subject->implode(', ');
            }
            $transaction->temp_created_at = $transaction->created_at->format('d-m-Y');
            if ($service == 23) {
                $subject = $transaction->serviceRegister->map(function($item, $key) {
                    return $item->subject_name . '-' . $item->relearn_type;
                });
                $subject = $subject->implode('<br>');
            }
            $transaction->subject = $subject;
        }

        return view('excel.fee.transaction', [
            'result' => $transactions,
        ]);
    }
}
