<?php

namespace App\Exports\ChangeScheduled;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class ChangeScheduledExport implements FromView
{
    private $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function view(): View
    {
        return view('excel.changeScheduled.export_change_scheduled', [
            'data' => $this->data,
        ]);
    }
}
