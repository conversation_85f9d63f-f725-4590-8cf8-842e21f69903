<?php

namespace App\Exports;

use App\Models\Dra\CurriCulum;
use App\Models\Fu\User;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use Maatwebsite\Excel\Concerns\WithDrawings;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Style\Style;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;

class UsersExport extends De<PERSON>ultV<PERSON>ueB<PERSON> implements FromView, ShouldAutoSize, WithCustomValueBinder, WithColumnFormatting
{
    private $request;

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function view(): View
    {
        return $this->setQuery();
    }

    public function setQuery()
    {

        $study_id = $this->request->study_status;
        $role_id = $this->request->role_id;
        $curriculum_id = $this->request->curriculum;
        $obj = User::select('*')->when($study_id, function ($query, $study_id) {
            return $query->where('study_status', $study_id);
        })->when($role_id, function ($query, $role_id) {
            return $query->where('user_level', $role_id);
        })->when($curriculum_id, function ($query, $curriculum_id) {
            return $query->where('curriculum_id', $curriculum_id);
        });
        $obj = $obj->get();

        foreach ($obj as $item) {
            $item->chi_tiet = CurriCulum::find($item->curriculum_id);
        }

        return view("excel.users.users_nor", [
            'users' => $obj,
        ]);
    }

    public function columnFormats(): array
    {
        return [
            'F' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'H' => NumberFormat::FORMAT_TEXT,
        ];
    }
}
