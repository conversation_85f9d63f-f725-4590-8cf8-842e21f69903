<?php

namespace App\Exports;

use App\Models\Fu\Activity;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\BeforeExport;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Illuminate\Contracts\Support\Responsable;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;

class LichThiTheoBoMonExport implements Responsable, FromView, WithTitle, ShouldAutoSize, WithEvents
{
    use Exportable, RegistersEventListeners;

    private $fileName = "lich-thi.xls";

    private $ds_error;
    private $excel_info;

    private $block;
    private $group_name;
    private $members;
    private $is_ds_cam_thi;

    private $data = [];

    public function __construct($ds_error, $excel_info, $group_name, $block, $members, $is_ds_cam_thi)
    {
        $this->ds_error = $ds_error;
        $this->excel_info = $excel_info;

        $this->block = $block;
        $this->group_name = $group_name;
        $this->members = $members;
        $this->is_ds_cam_thi = $is_ds_cam_thi;
    }

    public function view(): View
    {
        $this->data = [];

        $this->data = $this->members;

        return view('excel.calendar.export_course', [
            'ds_error' => $this->ds_error,
            'is_ds_cam_thi' => $this->is_ds_cam_thi,
            'block' => $this->block,
            'data' => $this->data,
            'excel_info' => $this->excel_info
        ]);
    }

    public function title(): string
    {
        if ($this->ds_error != null && count($this->ds_error) > 0) {
            return "ERROR";
        } else {
            return $this->group_name ?? "";
        }
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        if ($this->ds_error != null && count($this->ds_error) > 0) {
            return [
                BeforeExport::class  => function (BeforeExport $event) {
                    $event->writer->setCreator('dont');
                },
                AfterSheet::class    => function (AfterSheet $event) {
                    $event->sheet
                        ->getPageSetup()
                        ->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_PORTRAIT)
                        ->setPaperSize(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::PAPERSIZE_A4);

                    //create default style
                    $default_font_style = [
                        'font' => ['name' => 'Times New Roman', 'size' => 11]
                    ];

                    //style default
                    $event->sheet->getParent()->getDefaultStyle()->applyFromArray($default_font_style);

                    //style header
                    $event->sheet->getStyle('A1:C1')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

                    //style table
                    $table_style_array = array(
                        'borders' => array(
                            'allBorders' => array(
                                'borderStyle' => Border::BORDER_THIN,
                            )
                        )
                    );

                    $cellTable = 'A5:K' . (5 + count($this->ds_error));
                    $event->sheet->getStyle($cellTable)->applyFromArray($table_style_array);
                },
            ];
        } else {
            return [
                BeforeExport::class  => function (BeforeExport $event) {
                    $event->writer->setCreator('dont');
                },
                AfterSheet::class    => function (AfterSheet $event) {
                    $event->sheet
                        ->getPageSetup()
                        ->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_PORTRAIT)
                        ->setPaperSize(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::PAPERSIZE_A4);

                    //create default style
                    $default_font_style = [
                        'font' => ['name' => 'Times New Roman', 'size' => 12]
                    ];

                    //style default
                    $event->sheet->getParent()->getDefaultStyle()->applyFromArray($default_font_style);
                    //style header
                    //A5:J5
                    $event->sheet->getStyle('A5:H5')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
                    //style table
                    $table_style_array = array(
                        'borders' => array(
                            'allBorders' => array(
                                'borderStyle' => Border::BORDER_THIN,
                            )
                        )
                    );

                    if ($this->data == null || count($this->data) <= 0) {
                        $cellTable = 'A5:K' . (5 + 8);
                    } else {
                        $cellTable = 'A5:K' . (5 + count($this->data));
                    }
                    $event->sheet->getStyle($cellTable)->applyFromArray($table_style_array);
                },
            ];
        }
    }
}
