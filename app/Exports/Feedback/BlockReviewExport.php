<?php

namespace App\Exports\Feedback;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class BlockReviewExport implements FromView
{
    private $data;

    public function __construct($data)
    {
        foreach ($data as &$item) {
            $item['note'] = $this->sanitize_html($item['note']);
            $item['evaluation_note'] = $this->sanitize_html($item['evaluation_note']);
        }
        $this->data = $data;
    }
    private function sanitize_html($content) {
        if (!$content) return '';
        $valid_characters = '/[^a-z0-9A-Z_[:space:]àáâãăđĩèéêìíỉịòóôõơởợớọỏốồổỗộớờởỡợùũủúụứừửữựÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềểễệếỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴÝỶỸỳỵỷỹý ,.?!\/{}[]`~\'@#$%^&*()_=\-+":;"<>]/u';
        return preg_replace($valid_characters, '', $content);
    }
    public function view(): View
    {
        return view('excel.feedback.export_1_3_block', [
            'data' => $this->data
        ]);
    }
}
