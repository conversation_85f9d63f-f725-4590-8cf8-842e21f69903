<?php

namespace App\Exports\Feedback;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class FeedbackExport implements FromView
{
    private $datas;

    public function __construct($datas, $infoReport)
    {
        $this->datas = $datas;
        $this->infoReport = $infoReport;
    }

    public function view(): View
    {
        return view('excel.feedback.feedback_student', [
            'datas' => $this->datas,
            'infoReport' => $this->infoReport,
        ]);
    }
}
