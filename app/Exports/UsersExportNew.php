<?php

namespace App\Exports;

use App\Models\Dra\CurriCulum;
use App\Models\Fu\User;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromView;
use Illuminate\Contracts\View\View;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class UsersExportNew extends DefaultValueBinder implements FromView
{
    private $request;

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function view(): View
    {
        return $this->setQuery();
    }

    public function setQuery()
    {

        $study_id = $this->request->get('study_status', null);
        $role_id = $this->request->get('role_id', null);
        $curriculum_id = $this->request->get('curriculum', null);
        $obj = DB::table('user')
        ->select([
            'user.id',
            'user.user_code',
            'user.old_user_code',
            'user.user_login',
            DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ", user.user_givenname) as full_name'),
            'user.user_DOB',
            'user.user_email',
            'user.dantoc',
            'user.cmt',
            'user.ngaycap',
            'user.noicap',
            'user.user_address',
            'user.user_telephone',
            'user.study_status',
            'user.study_status_code',
            'user.gender',
            'user.kithu',
            'user.Legal_entity',
            'user.grade_create',
            'curriculum.khoa',
            'curriculum.nganh',
            'curriculum.brand_code',
            'curriculum.chuyen_nganh',
            'curriculum.nganh_in_bang'
        ])
        ->leftJoin('curriculum', 'curriculum.id', 'user.curriculum_id')
        ->where('user.user_level', $role_id);
        
        if ($role_id > 0 && $role_id != null) {
            if ($role_id == 3) {
                $obj = $obj->when(($study_id != null && $study_id > 0), function ($query, $study_id) {
                    return $query->where('user.study_status', $study_id);
                })
                ->when(($curriculum_id != '-1' && $curriculum_id != null), function ($query, $curriculum_id) {
                    return $query->where('user.curriculum_id', $curriculum_id);
                });
            }
        }

        $obj = $obj->orderBy('user.id', 'desc')->get();
        return view("excel.users.users", [
            'users' => $obj
        ]);
    }
}
