<?php

namespace App\Exports\Edu;

use App\Exports\Graduation\BangDiemDoiSoat;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class ImportDiem implements WithMultipleSheets
{
    private $course_id;

    public function __construct($course_id)
    {
        $this->course_id = $course_id;
    }

    public function sheets(): array
    {
        $course_id = $this->course_id;
        $sheets = [];
        $sheets[] = new ImportDiemSheet1($course_id);
//        $sheets[] = new ImportDiemSheet2();

        return $sheets;
    }
}
