<?php

namespace App\Exports\EosExam;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class ExportNewMultipleListStudentExam implements WithMultipleSheets
{
    use Exportable;

    protected $datas;
    protected $term;
    protected $block;
    protected $course;
    protected $camThi;
    protected $type; // 1 là thi ASM, 1 là EOS

    public function __construct($datas, $term, $block_search, $course, $camThi, $type = 1)
    {
        $this->datas = $datas;
        $this->term = $term;
        $this->block = $block_search;
        $this->course = $course;
        $this->camThi = $camThi;
        $this->type = $type;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];
        $sheets[] = new ExportNewListStudentFailExam($this->datas, $this->term, $this->block, $this->course);
        if ($this->camThi != 1) {
            foreach ($this->datas as $key => $data) {
                // dd($this->datas);
                foreach ($data['course'] as $k => $v) {
                    if (isset($v['members'])) {
                        if (count($v['members']) == 0) {
                            continue;
                        }
                        $sheets[] = new ExportNewListStudentExam($key, $data, $this->term, $this->block, $this->course, $k);
                    }
                }
            }
        }

        return $sheets;
    }
}
