<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\BeforeExport;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Illuminate\Contracts\Support\Responsable;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;


class LichThiTheoMonExport implements Responsable, FromView, WithDrawings, WithTitle, ShouldAutoSize, WithEvents
{
    use Exportable, RegistersEventListeners;

    private $fileName = "lich-thi.xls";

    private $is_total;
    private $slot_assignment;
    private $ds_members_total;
    private $excel_info;

    private $data = [];

    public function __construct($is_total, $slot_assignment, $ds_members_total, $excel_info)
    {
        $this->is_total = $is_total;
        $this->slot_assignment = $slot_assignment;
        $this->ds_members_total = $ds_members_total;
        $this->excel_info = $excel_info;
    }

    public function view(): View
    {
        $this->data = [];

        if ($this->is_total) {
            $this->data = $this->ds_members_total;
        } else {
            foreach ($this->ds_members_total as $member) {
                // if ($member->date_graduate == $this->slot_assignment['day']) {
                //     array_push($this->data, $member);
                // }
                // if ($member->slot_graduate->day == $this->slot_assignment['day']) {
                //     array_push($this->data, $member);
                // }
                if ($member->is_danger == 0 && $member->slot_graduate != null && $member->slot_graduate->day == $this->slot_assignment['day'] && $member->slot_graduate->slot == $this->slot_assignment['slot']) {
                    array_push($this->data, $member);
                }
            }
        }

        return view('excel.calendar.export_group', [
            'is_total' => $this->is_total,
            'data' => $this->data,
            'slot_assignment' => $this->slot_assignment,
            'excel_info' => $this->excel_info,
        ]);
    }

    public function drawings()
    {
        $drawing = new Drawing();
        $drawing->setName('Logo');
        $drawing->setDescription('This is my logo');
        $drawing->setPath(public_path('images/logo.png'));
        $drawing->setHeight(60);
        $drawing->setCoordinates('B1');

        return $drawing;
    }

    public function title(): string
    {
        if ($this->is_total) {
            $group = $this->excel_info['group'];
            return $group->group_name ?? "";
        } else {
            // return $this->slot_assignment['day'];
            // $slot_date = str_replace('/', '-', $this->slot_assignment['day']);
            // return date("Y.m.d", strtotime($slot_date)) ?? "";
            $slot_date = str_replace('/', '-', $this->slot_assignment['day']);
            $course_slot = $this->slot_assignment['slot'];
            $slot_date_format = date("Y-m-d", strtotime($slot_date)) ?? "";

            return $slot_date_format . "-slot-" . $course_slot;
        }
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        if ($this->is_total) {
            return [
                BeforeExport::class  => function (BeforeExport $event) {
                    $event->writer->setCreator('dont');
                },
                AfterSheet::class    => function (AfterSheet $event) {
                    $event->sheet
                        ->getPageSetup()
                        ->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_PORTRAIT)
                        ->setPaperSize(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::PAPERSIZE_A4);

                    //create default style
                    $default_font_style = [
                        'font' => ['name' => 'Times New Roman', 'size' => 11]
                    ];

                    //style default
                    $event->sheet->getParent()->getDefaultStyle()->applyFromArray($default_font_style);

                    //style header
                    $event->sheet->getStyle('A1:C1')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

                    //style table
                    $table_style_array = array(
                        'borders' => array(
                            'allBorders' => array(
                                'borderStyle' => Border::BORDER_THIN,
                            )
                        )
                    );

                    $cellTable = 'A5:K' . (5 + count($this->ds_members_total));
                    $event->sheet->getStyle($cellTable)->applyFromArray($table_style_array);
                },
            ];
        } else {
            return [
                BeforeExport::class  => function (BeforeExport $event) {
                    $event->writer->setCreator('dont');
                },
                AfterSheet::class    => function (AfterSheet $event) {
                    $event->sheet
                        ->getPageSetup()
                        ->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_PORTRAIT)
                        ->setPaperSize(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::PAPERSIZE_A4);

                    $event->sheet
                        ->getSheetView()
                        ->setZoomScale(80);

                    // $event->sheet
                    //     ->getPageMargins()
                    //     ->setTop(1.5)
                    //     ->setRight(1.5)
                    //     ->setLeft(2.0)
                    //     ->setBottom(1.5);

                    $event->sheet
                        ->getHeaderFooter()
                        ->setOddFooter('&L' . '07.03.03-BM/FPL/HDCV/FE 1/0' . '&R&P/&N');

                    //create default style
                    $default_font_style = [
                        'font' => ['name' => 'Times New Roman', 'size' => 11]
                    ];

                    //style default
                    $event->sheet->getParent()->getDefaultStyle()->applyFromArray($default_font_style);
                    //style header
                    $event->sheet->getStyle('A7:H7')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
                    //style table
                    $table_style_array = array(
                        'borders' => array(
                            'allBorders' => array(
                                'borderStyle' => Border::BORDER_THIN,
                            )
                        )
                    );

                    if ($this->data == null || count($this->data) <= 0) {
                        $cellTable = 'A8:H' . (8 + 8);
                    } else {
                        $cellTable = 'A8:H' . (8 + count($this->data));
                    }

                    $event->sheet->getStyle($cellTable)->applyFromArray($table_style_array);

                    //logo
                    $event->sheet
                        ->getStyle('A1:C3')
                        ->getAlignment()
                        ->setWrapText(true)
                        ->setVertical(Alignment::VERTICAL_CENTER)
                        ->setHorizontal(Alignment::HORIZONTAL_CENTER);

                    //danh sach sinh vien thi cuoi ky
                    $event->sheet
                        ->getStyle('D1:H3')
                        ->getAlignment()
                        ->setWrapText(true)
                        ->setVertical(Alignment::VERTICAL_CENTER)
                        ->setHorizontal(Alignment::HORIZONTAL_CENTER);

                    //phong thi - mon
                    $event->sheet
                        ->getStyle('A5:H6')
                        ->getAlignment()
                        ->setWrapText(true)
                        ->setVertical(Alignment::VERTICAL_CENTER)
                        ->setHorizontal(Alignment::HORIZONTAL_CENTER);

                    $event->sheet
                        ->getRowDimension('5')
                        ->setRowHeight(40, 'px');

                    $event->sheet
                        ->getRowDimension('7')
                        ->setRowHeight(40, 'px');

                    //a4 row height
                    $startRow = 9;
                    if ($this->data != null && count($this->data) > 0) {
                        $totalRow = $startRow + count($this->data);
                        for ($i = $startRow; $i < $totalRow; $i++) {
                            $event->sheet
                                ->getRowDimension($i)
                                ->setRowHeight(30, 'px');
                        }
                    }

                    //a4 page setup
                    $event->sheet->getPageSetup()->setFitToWidth(1);
                    $event->sheet->getPageSetup()->setFitToHeight(0);
                },
            ];
        }
    }
}
