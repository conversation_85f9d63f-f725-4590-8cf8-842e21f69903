<?php

namespace App\Exports\Activity;

use App\Models\Fu\Activity;
use App\Models\T7\Grade;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class DanhSachThi implements WithMultipleSheets
{
    private $date;
    public function __construct($date)
    {
        $this->date = $date;
    }

    public function sheets(): array
    {
        $sheets = [];
        $date = $this->date;
        $result_main = Activity::with(['group','slotDetail'])
            ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
            ->where('session_type.is_exam', 1)
            ->where('activity.day', $date)
            ->orderBy('activity.day')
            ->orderBy('activity.slot')
            ->get();
        foreach ($result_main as $item_main) {
            // $course_slot = $item_main->course_slot;
            // if ($course_slot == 250) { // trường hợp thi gi<PERSON><PERSON> kỳ thì hiện danh sách cả lớp
                $members = DB::select("Select * from user t1,t7_course_result t2 where t1.user_login=t2.student_login and t2.groupid in ($item_main->groupid) order by t1.user_code asc");
            // } else {
                // $members = DB::select("Select * from user t1,t7_course_result t2 where t1.user_login=t2.student_login and t2.groupid in ($item_main->groupid) and t2.val=0 order by t1.user_code asc");
            // }
//             if (($course_slot >= 150 && $course_slot < 200) || $session_type == 9) {
                // $grades = Grade::where('syllabus_id', $item_main->group->syllabus_id)->where('is_final_exam', 1)->where('grade_type','<',2)->get();
//             } else if (($course_slot > 200 || $session_type == 10) && $course_slot != 250) {
//                 $grades = Grade::where('syllabus_id', $item_main->group->syllabus_id)->where('is_final_exam', 1)->get();
// //                $old_point = DB::select("Select *, t1.val as grade_val from t7_course_grade t1, t7_course_result t2 where t1.groupid=t2.groupid and t1.login=t2.student_login and t2.val=0 and t2.groupid in ($item_main->groupid) and t1.is_final=1 and t1.is_resit!=1");
//             }
            $grades = Grade::where('syllabus_id', $item_main->group->syllabus_id)->where('is_final_exam', 1)->get();
            $item_main->grades = $grades;
            $item_main->members = $members;
            $item_main->cam_thi = DB::select("Select * from user t1,t7_course_result t2 where t1.user_login=t2.student_login and t2.groupid in($item_main->groupid) and t2.val=-1 order by t1.user_code asc");;
        }
        foreach ($result_main as $item) {
            $sheets[] = new DanhSachThiItem($item);
        }

        return $sheets;
    }
}
