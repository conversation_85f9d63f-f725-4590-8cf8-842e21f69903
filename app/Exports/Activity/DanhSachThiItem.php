<?php

namespace App\Exports\Activity;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class DanhSachThiItem implements FromView, WithTitle, WithStyles, ShouldAutoSize
{
    private $item;
    const COL = ['A','B','C','D','E','F','G','H','I','J','K','L'];
    public function __construct($item)
    {
        $this->item = $item;
    }

    public function view() :View
    {

        return view('excel.activity.danh_sach_thi', [
            'main_item' => $this->item,
        ]);
    }

    public function title(): string
    {
        return $this->item->group_name;
    }

    public function styles(Worksheet $sheet)
    {
        $max = $sheet->getHighestRowAndColumn();
        $ddk_col = $this->item->grades->count() + 6;
        $ddk_row = count($this->item->members) + 5;
        $ddk_name_col = self::COL[$ddk_col - 1];

        $sheet->getStyle('A1')->getFont()->setSize(15)->setBold('boldest');
        $sheet->getStyle('A1')->getAlignment()->setHorizontal('center');
        $sheet->getStyle('A2')->getFont()->setSize(13)->setBold('boldest');
        $sheet->getStyle('A4:' . $ddk_name_col . '4')->getFont()->setBold('boldest');
        $sheet->getStyle('A5:' . $ddk_name_col . '5')->getFont()->setBold('boldest');
        $sheet->getStyle('A2')->getAlignment()->setHorizontal('center');
        $sheet->getStyle('C3')->getAlignment()->setHorizontal('center');
        $sheet->getStyle('F3')->getAlignment()->setHorizontal('center');
        $sheet->getStyle('A1:' . $ddk_name_col . $ddk_row)->getBorders()->getAllBorders()->setBorderStyle('thin');

        $sheet->getStyle('A' . ($ddk_row + 2))->getFont()->setSize(15)->setBold('boldest');
        $sheet->getStyle('A' . ($ddk_row + 2))->getAlignment()->setHorizontal('center');
        $sheet->getStyle('A' . ($ddk_row + 3))->getFont()->setSize(13)->setBold('boldest');
        $sheet->getStyle('A' . ($ddk_row + 4) . ':' . $max['column'] . ($ddk_row + 4))->getFont()->setBold('boldest');
        $sheet->getStyle('A' . ($ddk_row + 3))->getAlignment()->setHorizontal('center');
        $sheet->getStyle('A' . ($ddk_row + 2) . ':' . $max['column'] . $max['row'])->getBorders()->getAllBorders()->setBorderStyle('thin');


    }
}
