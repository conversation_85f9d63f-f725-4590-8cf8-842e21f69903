<?php

namespace App\Exports\Activity;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithTitle;

class ScheduleTemplateExport implements FromView, WithTitle
{
    private $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function view(): View
    {
        return view('excel.activity.template_import_group_schedule', ['data' => $this->data]);
    }

    public function title(): string
    {
        return 'Template Import Group Schedule';
    }
}
