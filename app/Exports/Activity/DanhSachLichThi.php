<?php

namespace App\Exports\Activity;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;

class DanhSachLichThi implements WithStyles, FromView
{
    private $data;
    public function __construct($data)
    {
        $this->data = $data;
    }

    public function view() :View
    {
        return view('excel.activity.danh_sach_lich_thi', [
            'days' => $this->data,
        ]);
    }

    public function styles(Worksheet $sheet)
    {
        $totalRow = array_sum(array_map('count', $this->data));

        $sheet->getPageSetup()->setFitToPage(true);
        $sheet->getPageSetup()->setFitToWidth(1);
        $sheet->getPageSetup()->setFitToHeight(0);

        $sheet->getColumnDimension('A')->setWidth(12);  
        $sheet->getColumnDimension('B')->setWidth(6);  
        $sheet->getColumnDimension('C')->setWidth(15);  
        $sheet->getColumnDimension('D')->setWidth(30);  
        $sheet->getColumnDimension('E')->setWidth(10);  
        $sheet->getColumnDimension('F')->setWidth(15);  
        $sheet->getColumnDimension('G')->setWidth(25);
        $sheet->getColumnDimension('H')->setWidth(20);
        $sheet->getColumnDimension('I')->setWidth(25);
        $sheet->getColumnDimension('J')->setWidth(25);
        $sheet->getColumnDimension('K')->setWidth(25);
        // $sheet->getRowDimension('1')->setRowHeight(50, 'px');

        $sheet->getStyle('A1:K' . $totalRow)->getFont()->setName('Times New Roman');
        $sheet->setCellValue('A1', '');
        $sheet
            ->getHeaderFooter()
            ->setOddFooter('&L' . '07.03.03-BM/FPL/HDCV/FE 1/0' . '&R&P/&N');

        $sheet->getStyle('A1:K' . $totalRow)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_NONE,
                    // 'color' => ['argb' => 'FFFFFF'],
                ],
            ],
        ])->getAlignment()->setWrapText(true);



    }
}
