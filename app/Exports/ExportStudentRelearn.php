<?php

namespace App\Exports;

use App\Models\Dra\CurriCulum;
use App\Models\Fu\ServiceLog;
use App\Models\Fu\User;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromView;
use Illuminate\Contracts\View\View;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class ExportStudentRelearn extends DefaultValueBinder implements FromView
{
    private $request;

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function view(): View
    {
        return $this->setQuery();
    }

    public function setQuery()
    {
        $obj = ServiceLog::select([
            'service_logs.user_id',
            'service_logs.user_code',
            'service_register.subject_code',
        ])
        ->join('service_register', 'service_register.service_log_id', '=', 'service_logs.id')
        ->where('subject_code', $this->request->subject_code)
        ->where('service_logs.payment_status', 1)
        ->whereIn('service_logs.status', [0, 1])
        ->get();

        return view("excel.relearn_online.relearn.relearn", [
            'orders' => $obj
        ]);
    }
}
