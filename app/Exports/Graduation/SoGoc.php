<?php

namespace App\Exports\Graduation;
use App\Models\Crm\People;
use App\Models\Dra\CurriCulum;
use App\Models\Fu\Graduation;
use App\Models\Fu\GraduationCampaign;
use App\Models\Fu\GraduationCampaignUser;
use App\Models\Fu\Subject;
use App\Models\T7\AcademicDecisionManagement;
use App\Models\Transcript;
use App\Models\TranscriptDetail;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class SoGoc implements FromView, ShouldAutoSize, WithColumnFormatting
{
    private $campaign_id;
    public function __construct($campaign_id)
    {
        $this->campaign_id = $campaign_id;
    }

    public function columnFormats(): array
    {
        return [
            'G' => NumberFormat::FORMAT_TEXT,
        ];
    }
    public function view() : View
    {
        $campaign = GraduationCampaign::find($this->campaign_id);
        $dropout = AcademicDecisionManagement::findOrFail($campaign->dropout_id);
        $result = $this->getGraduations($this->campaign_id);
        $result = $this->xuLyInBangDiem($result, $dropout);

        return view('excel.graduation.so_goc', [
            'data' => $result,
            'campaign' => $campaign
        ]);
    }

    public function getGraduations($campaign_id, $curriculum_list = [])
    {
        return GraduationCampaignUser::where('campaign_id', $campaign_id)->when($curriculum_list, function ($query, $curriculum_list) {
            $query->whereIn('curriculum_id', $curriculum_list);
        })->get();
    }

    public function xuLyInBangDiem($result, $dropout)
    {
        foreach ($result as $item) {
            $item->name_en = str_slug(' ', $item->full_name);
            $item->nam_tot_nghiep = $item->created_at->format('Y');
            $item->ngay_cap = Carbon::createFromDate($dropout->dateaffected)->format('d/m/Y');
            $item->so_quyet_dinh = $dropout->so_quyet_dinh;
            $bang_diem = Transcript::where('user_login', $item->user_login)->first();
            $bang_diem->details = TranscriptDetail::where('transcript_id', $bang_diem->id)->where('num_of_credit', '!=', 0)->where('type', '!=', 3)->get();
            $item->chi_tiet = CurriCulum::findOrFail($bang_diem->curriculum_id);
            if ($item->type == 1) {
                $item->i = 18;
            } else {
                $item->i = 16;
                if(count($bang_diem->details) >= 34) {
                    $item->i = floor(count($bang_diem->details) / 2);
                }
            }

            foreach ($bang_diem->details as $subject) {
                $chi_tiet_mon = null;
                if ($subject->type == 1) {
                    $chi_tiet_mon = Subject::where('subject_code', $subject->subject_code_replace)->first();
                } else {
                    $chi_tiet_mon = Subject::find($subject->subject_id);
                }

                $subject->chi_tiet_mon = $chi_tiet_mon;

            }

            $item->bang_diem = $bang_diem;
            $item->dob = Carbon::createFromDate($item->dob);
            $dob_month = $item->dob->format('m');
            if ($dob_month > 2 && $dob_month < 10) {
                $dob_month = str_replace('0', '', $dob_month);
            }
            
            $item->dob_month = $dob_month;
            $item->dob_day = $item->dob->format('d');
            $item->dob_year = $item->dob->format('Y');
            $item->dob_trans_vi = "$item->dob_day/$item->dob_month/$item->dob_year";
        }

        return $result;
    }
}
