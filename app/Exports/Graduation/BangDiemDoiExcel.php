<?php

namespace App\Exports\Graduation;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromView;

use App\Models\Fu\User;
use App\Models\Fu\GraduationCampaign;
use App\Models\Fu\GraduationCampaignUser;
use App\Models\Transcript;
use App\Models\TranscriptDetail;


class BangDiemDoiExcel implements FromView
{
    private $campaign_id;

    public function __construct($campaign_id)
    {
        $this->campaign_id = $campaign_id;
    }

    public function view() : View
    {
        $data = $this->exportBangDiemDoiSoat();
        return view('excel.graduation.bang_diem_doi_excel', [
            'res' => $data,
        ]);
    }

    public function exportBangDiemDoiSoat()
    { 
        $graduationCampaign = GraduationCampaign::find($this->campaign_id);
        if (!$graduationCampaign) {
            return [];
        }
        
        $listStudent = GraduationCampaignUser::where('campaign_id', $this->campaign_id)
        ->orderBy('user_code')
        ->get();

        if (count($listStudent) == 0) {
            return back();
        }

        $res = [];
        $listUserCode = $listStudent->map(function ($v, $k) {
            return $v->user_code;
        })->toArray();

        if (count($listUserCode) > 500) {
            DB::getPdo()->setAttribute(\PDO::ATTR_EMULATE_PREPARES, true);
        }

        $listDataUser = User::select([
            'user.id',
            'user.user_code',
            'user.user_login',
            DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ", user.user_givenname) as full_name_student'),
            'curriculum.khoa',
            'curriculum.nganh',
            'curriculum.brand_code',
            'curriculum.chuyen_nganh',
            'curriculum.nganh_in_bang'
        ])
        ->join('curriculum', 'user.curriculum_id', 'curriculum.id')
        ->whereIn('user_code', $listUserCode)
        ->get();

        $listdataByUser = call_user_func(function () use ($listDataUser) {
            $res = [];
            foreach ($listDataUser as $key => $value) {
                $res[$value->user_code] = $value->toArray();
            }

            return $res;
        });

        $listdataLockByUser = call_user_func(function () use ($listStudent) {
            $res = [];
            foreach ($listStudent as $key => $value) {
                $res[$value->user_code] = [
                    'brand_code' => $value->brand_code,
                    'nganh_in_bang' => $value->nganh_in_bang,
                    'khoa_nhap_hoc' => $value->khoa_nhap_hoc,
                    'khoa_thuc_hoc' => $value->khoa_thuc_hoc,
                    'nganh_in_bang_en' => $value->nganh_in_bang_en,
                    'nganh' => $value->nganh,
                    'chuyen_nganh' => $value->chuyen_nganh,
                    'loai_tot_nghiep' => $value->loai_tot_nghiep,
                    'loai_tot_nghiep_en' => $value->loai_tot_nghiep_en,
                ];
            }

            return $res;
        });

        foreach ($listStudent as $key => $student) {
            $user_login = $student->user_login;
            $transcript = Transcript::where('user_login', $user_login)
            ->first();
            if (!$transcript) {
                continue;
            }
            $transcript->details = TranscriptDetail::where('transcript_id', $transcript->id)
            ->orderBy('ki_thu', 'asc')
            ->get()->toArray();

            $transcript->currentData = $listdataByUser[$student->user_code];
            $transcript->lockData = $listdataLockByUser[$student->user_code];
            $res[] = $transcript->toArray();
        }

        // dd($res);
        return $res;
    }
}
