<?php


namespace App\Exports\Graduation;


use App\Models\Fu\Decision;
use App\Models\Fu\GraduationCampaign;
use App\Models\Fu\GraduationCampaignUser;
use App\Models\Fu\User;
use App\Models\T7\Discipline;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class DuDieuKienTotNghiep implements FromView, ShouldAutoSize
{
    private $type;
    private $campaign_id;

    public function __construct($campaign_id = [], $type = 1)
    {
        $this->type = $type;
        $this->campaign_id = $campaign_id;
    }

    public function view() : View
    {
        $result = $this->exportTotNghiep();

        // Lấy đợt tốt nghiệp
        $campaign = GraduationCampaign::where('id', $this->campaign_id)->first();
        DB::getPdo()->setAttribute(\PDO::ATTR_EMULATE_PREPARES, true);
        $listUserLogin = $result->pluck('user_login')->toArray();
        $listUserCode = $result->pluck('user_code')->toArray();

        $union = Decision::select([
            'user.user_login',
            'decision.name as decision_name',
            'decision.decision_num as decision_no',
            'decision.sign_day as date_affected',
            'type',
            'decision.signer as signee',
            'decision_user.note as reason',
            'decision.note'
        ])
        ->join('decision_user', 'decision.id', '=', 'decision_user.decision_id')
        ->join('user', 'user.user_code', 'decision_user.user_code')
        ->where(function ($query) use ($listUserLogin, $listUserCode) {
            $query->whereIn('decision_user.user_code', $listUserLogin)
                ->orWhereIn('decision_user.user_code', $listUserCode);
        })
        ->whereIn('decision.type', [
            // Decision::TYPE_KHEN_THUONG,
            Decision::TYPE_KY_LUAT,
            23
        ]);
        
        $listData = Discipline::select(
            'user.user_login',
            // 'decision_user.user_login',
            't7_discipline.decision_name', 
            't7_discipline.decision_no',
            't7_discipline.date_affected', 
            // 't7_discipline.type', 
            DB::raw('(
                CASE t7_discipline.type
                    WHEN 1 THEN 25
                    WHEN 2 THEN 26
                    ELSE NULL
                END
            ) AS type'),
            't7_discipline.signee', 
            't7_discipline_student.reason', 
            't7_discipline.note'
        )
        ->join('t7_discipline_student', 't7_discipline.id', '=', 't7_discipline_student.discipline_id')
        ->join('user', function($q) {
            $q->on('user.user_code', 't7_discipline_student.student_login')
            ->orOn('user.user_login', 't7_discipline_student.student_login');
        })
        ->where('t7_discipline.type', 2)
        ->whereIn('t7_discipline_student.student_login', $listUserLogin)
        ->orWhereIn('t7_discipline_student.student_login', $listUserCode)
        ->union($union)
        ->get();


        // lấy danh sách khen thưởng kỷ luật
        $listUserDecision = call_user_func(function() use ($listData) {
            $res = [];
            foreach ($listData as $key => $value) {
                $res[$value->user_login][$value->type] = $value->toArray();
            }

            return $res;
        });

        // Lấy danh sách số bằng
        $listNumberDegree = User::whereIn('user.user_login', $listUserLogin)
        ->pluck('so_bang', 'user_login')
        ->toArray();

        $listCurriculum = User::select([
            'user.user_login',
            'curriculum.name',
            DB::raw('SUM(period_subject.number_of_credit) AS total_number')
        ])
        ->leftJoin('curriculum', 'curriculum.id', 'user.curriculum_id')
        ->leftJoin('period_subject', 'curriculum.id', 'period_subject.curriculum_id')
        ->whereIn('user.user_login', $listUserLogin)
        ->groupBy('user.user_login')
        ->get();

        // lấy danh sách thông tin khung theo user 
        $listUserDraCurriculum = call_user_func(function() use ($listCurriculum) {
            $res = [];
            foreach ($listCurriculum as $key => $value) {
                $res[$value->user_login] = [
                    'name' => $value->name,
                    'total_number' => $value->total_number,
                ];
            }

            return $res;
        });


        return view('excel.graduation.tot_nghiep', [
            'result' => $result,
            'listUserDecision' => $listUserDecision,
            'listNumberDegree' => $listNumberDegree,
            'listUserDraCurriculum' => $listUserDraCurriculum,
        ]);
    }

    public function exportTotNghiep()
    {
        $result = $this->getGraduations();

        return $result;
    }

    public function getGraduations($curriculum_list = [])
    {
        $campaign_id = $this->campaign_id;
        if ($this->type == 1) {
            $result = GraduationCampaignUser::with('campaign')->whereIn('campaign_id', $campaign_id)->get();
        } else {
            $id = GraduationCampaign::where('term_name', $campaign_id)->get();
            $result = GraduationCampaignUser::with('campaign')->whereIn('campaign_id', $id->pluck('id'))->get();
        }

        return $result;
    }
}
