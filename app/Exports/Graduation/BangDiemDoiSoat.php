<?php

namespace App\Exports\Graduation;

use App\Models\Crm\People;
use App\Models\Crm\Rcm;
use App\Models\Dra\CurriCulum;
use App\Models\Dra\PeriodSubject;
use App\Models\Dra\StudentSubject;
use App\Models\Fu\Graduation;
use App\Models\Fu\GraduationCampaign;
use App\Models\Fu\GraduationCampaignUser;
use App\Models\KhoaNhapHoc;
use App\Models\T7\CourseResult;
use App\Models\Transcript;
use App\Models\TranscriptDetail;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithTitle;

class BangDiemDoiSoat implements FromView, ShouldAutoSize, WithTitle
{
    private $campaign_id;
    private $brand_code;
    private $nganh;

    public function __construct($campaign_id, $brand_code, $nganh)
    {
        $this->campaign_id = $campaign_id;
        $this->brand_code = $brand_code;
        $this->nganh = $nganh;
    }

    public function view() : View
    {
        $data = $this->exportBangDiemDoiSoat();

        return view('excel.graduation.bang_diem_doi_soat', [
            'students' => $data['students'],
            'list' => $data['list'],
            'mon_hoc_trong_khung' => $data['mon_hoc_trong_khung'],
        ]);
    }

    public function exportBangDiemDoiSoat()
    {
        $campaign_id = $this->campaign_id;
        $array_curriculum = [];
        $list = [];
        $list_curriculum = CurriCulum::select('id')->where('nganh_in_bang', $this->nganh)->get();
        foreach ($list_curriculum as $item)
        {
            $array_curriculum[] = $item->id;
        }

        $students = $this->getGraduations($campaign_id, $array_curriculum);
        $mon_co_diem = $students[3];
        $mon_hoc_trong_khung = $students[2];
        foreach($mon_hoc_trong_khung as $item) {
            if (!isset($mon_co_diem[$item['subject_code']])) {
                unset($mon_hoc_trong_khung[$item['subject_code']]);
            }
        }

        return [
            'students' => $students[0],
            'list' => $list,
            'mon_hoc_trong_khung' => $mon_hoc_trong_khung,
        ];
    }

    public function getGraduations($campaign_id, $curriculum_list = [])
    {
        $mon_hoc_trong_khung = [];
        $subject_pro = [
            'PRO106','PRO109','PRO110','PRO115','PRO116','PRO117','PRO118','PRO119','PRO120','PRO121','PRO122',
            'PR0105', 'PRO130', 'PRO128', 'PRO123', 'PRO126', 'PRO134', 'PRO135', 'PRO136', 'PRO137'

        ];
        $subjects = PeriodSubject::whereIn('curriculum_id', $curriculum_list)->get();
        foreach ($subjects as $item) {
            $mon_hoc_trong_khung[$item->subject_code] = [
                'subject_name' => $item->subject_name,
                'subject_code' => $item->subject_code,
                'skill_code' => $item->skill_code,
            ];
        }

        $mon_co_diem = [];
        $user_login_array = [];
        $result = GraduationCampaignUser::where('campaign_id', $campaign_id)->whereIn('curriculum_id', $curriculum_list)->orderBy('user_code')->get();
        $graduation = GraduationCampaign::find($campaign_id);
        try {
            foreach ($result as $key => $item) {
                $item->dob_text = Carbon::createFromDate($item->dob)->format('d/m/Y');
                // $bang_diem = Transcript::with('details')->where('user_login', $item->user_login)->first();
                $bang_diem = Transcript::where('user_login', $item->user_login)->first();
                $bang_diem->details = TranscriptDetail::where('transcript_id', $bang_diem->id)->get();
                $temp = [];
                foreach ($bang_diem->details as $mon) {
                    if (!isset($mon_hoc_trong_khung[$mon->subject_code_pass ?? $mon->subject_code])) {
                        $mon_hoc_trong_khung[$mon->subject_code_pass ?? $mon->subject_code] = [
                            'subject_name' => ($mon->subject_name_pass ?? $mon->subject_name),
                            'subject_code' => ($mon->subject_code_pass ?? $mon->subject_code),
                            'skill_code' => $item->skill_code,
                        ];
                    }

                    $mon_co_diem[$mon->subject_code_pass ?? $mon->subject_code] = 1;
                    if (in_array($mon->subject_code_pass, $subject_pro)) {
                        $mon->type = 2;
                    }
                    
                    $temp[$mon->subject_code_pass ?? $mon->subject_code] = [
                        'point' => $mon->point,
                        'type' => $mon->type,
                    ];
                    $item->diem_mon = $temp;
                }
            }
        } catch (\Exception $e) {
            dd($e);
        }

        return [$result,$user_login_array,$mon_hoc_trong_khung,$mon_co_diem];
    }

    public function title(): string
    {
        return $this->brand_code;
    }
}
