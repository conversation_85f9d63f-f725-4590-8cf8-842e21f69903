<?php


namespace App\Exports\Graduation;


use App\Models\Fu\GraduationCampaignUser;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use App\Models\Dra\CurriCulum;


class BangDiemDoiSoatAll implements WithMultipleSheets
{
    private $campaign_id;

    public function __construct($campaign_id = null)
    {
        $this->campaign_id = $campaign_id;
    }
    public function sheets(): array
    {
        $sheets = [];
        // $nganh = GraduationCampaignUser::select('nganh_in_bang','brand_code')->where('campaign_id', $this->campaign_id)->orderBy('user_code')->distinct()->get();
        $nganh = GraduationCampaignUser::select('nganh_in_bang','brand_code')->where('campaign_id', $this->campaign_id)->orderBy('user_code')->orderBy('curriculum_id')->distinct()->get();

        

        $arrCheck = [];
        foreach ($nganh as $item) {
            if (in_array($item->nganh_in_bang, $arrCheck)) {
                continue;
            }

            $arrCheck[] = $item->nganh_in_bang;
            $sheets[] = new BangDiemDoiSoat($this->campaign_id, $item->brand_code, $item->nganh_in_bang);
        }

        return $sheets;
    }
}
