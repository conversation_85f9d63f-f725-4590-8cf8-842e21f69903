<?php

namespace App\Exports\GradeBook;

use Illuminate\Contracts\View\View;
use App\Models\T7\CourseGrade as T7CourseGrade;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class GradeBookTermExportBySubject implements FromView, ShouldAutoSize, WithTitle, WithStyles
{
    private $datas;
    private $term;
    
    public function __construct($datas, $term)
    {
        $this->datas = $datas;
        $this->term = $term;
    }

    public function view(): View
    {
        $view_name = 'excel.gradebook.gradebookByTermSubject';
        return view($view_name, [
            'datas' => $this->datas,
            'term' => $this->term,
        ]);
    }

    public function title(): string
    {
        return "Điểm tổng kết theo môn";
    }

    public function styles(Worksheet $sheet)
    {
        $cellTable = 'A2:G' . (4 + count($this->datas));
        $sheet->getStyle($cellTable)->applyFromArray(
            [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        // 'color' => ['argb' => 'FFFFFF'],
                    ],
                ],
            ]
        )->getAlignment()->setWrapText(true);
    }


}