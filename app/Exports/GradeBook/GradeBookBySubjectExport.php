<?php

namespace App\Exports\GradeBook;

use Illuminate\Contracts\View\View;
use App\Models\T7\CourseGrade as T7CourseGrade;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithTitle;

class GradeBookBySubjectExport implements FromView, ShouldAutoSize, WithTitle
{
    private $groups;
    private $subject_code;
    public function __construct($groups, $subject_code)
    {
        $this->groups = $groups;
        $this->subject_code = $subject_code;
    }

    public function view(): View
    {
        $view_name = 'excel.gradebook.term_export';
        return view($view_name, [
            'groups' => $this->groups,
        ]);
    }

    public function title(): string
    {
        return $this->subject_code;
    }
}
