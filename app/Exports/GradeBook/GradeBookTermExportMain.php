<?php

namespace App\Exports\GradeBook;

use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use App\Exports\GradeBook\GradeBookTermExport;
use App\Exports\GradeBook\GradeBookTermExportBySubject;

class GradeBookTermExportMain implements WithMultipleSheets
{
    private $datas;
    private $datas2;
    private $term;
    
    public function __construct($datas, $datas2, $term)
    {
        $this->datas = $datas;
        $this->datas2 = $datas2;
        $this->term = $term;
    }

    public function sheets(): array
    {
        $sheets = [];
        $sheets[] = new GradeBookTermExport($this->datas, $this->term);
        $sheets[] = new GradeBookTermExportBySubject($this->datas2, $this->term);
        
        return $sheets;
    }
}
