<?php

namespace App\Exports\GradeBook;

use Illuminate\Contracts\View\View;
use App\Models\T7\CourseGrade as T7CourseGrade;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\WithStyles;
class GradeBookTermExport implements FromView, ShouldAutoSize, WithDrawings, WithTitle, WithStyles
{
    private $datas;
    private $term;
    
    public function __construct($datas, $term)
    {
        $this->datas = $datas;
        $this->term = $term;
    }

    public function view(): View
    {
        $view_name = 'excel.gradebook.gradebookByTerm';
        return view($view_name, [
            'datas' => $this->datas,
            'term' => $this->term,
        ]);
    }

    public function styles(Worksheet $sheet)
    {
        $cellTable = 'A4:F' . (4 + count($this->datas));
        $sheet->getStyle($cellTable)->applyFromArray(
           [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                    ],
                ],
           ])->getAlignment()->setWrapText(true);
    }
    
    public function drawings()
    {
        $drawing = new Drawing();
        $drawing->setName('logo');
        $drawing->setPath(public_path('images/logo.png'));
        $drawing->setHeight(40);
        $drawing->setCoordinates('A1');
        $drawing->setOffsetX(5);
        $drawing->setOffsetY(5);

        return $drawing;
    }

    public function title(): string
    {
        return "Bảng điểm học kỳ";
    }

}
