<?php

namespace App\Exports\GradeBook;

use Illuminate\Contracts\View\View;
use App\Models\T7\CourseGrade as T7CourseGrade;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class GradeBookExport implements WithMultipleSheets
{
    private $list_gradebook_group_by_subject;
    
    public function __construct($list_gradebook_group_by_subject)
    {
        $this->list_gradebook_group_by_subject = $list_gradebook_group_by_subject;
    }


    public function sheets(): array
    {
        $sheets = [];

        foreach ($this->list_gradebook_group_by_subject as $subject_code => $groups) {
            $sheets[] = new GradeBookBySubjectExport($groups, $subject_code);
        }

        return $sheets;
    }
}
