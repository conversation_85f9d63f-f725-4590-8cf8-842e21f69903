<?php

namespace App\Exports\Diploma;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;

class ListDiploma implements WithStyles, FromView
{
    private $data;
    public function __construct($data)
    {
        $this->data = $data;
    }

    public function view() :View
    {
        return view('excel.diploma.list_diploma', [
            'datas' => $this->data,
        ]);
    }

    public function styles(Worksheet $sheet)
    {
        $totalRow = count($this->data) + 1;
       
        $sheet->getPageSetup()->setFitToPage(true);
        $sheet->getPageSetup()->setFitToWidth(1);
        $sheet->getPageSetup()->setFitToHeight(0);

        $sheet->getColumnDimension('A')->setWidth(15);  
        $sheet->getColumnDimension('B')->setWidth(25);  
        $sheet->getColumnDimension('C')->setWidth(15);  

        $sheet->getStyle('A1:C1')->getFont()->setBold(true);

        $sheet->getStyle('A1:C' . $totalRow)->getFont()->setName('Times New Roman');

        $sheet->getStyle('A1:C' . $totalRow)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ])->getAlignment()->setWrapText(true);
    }
}
