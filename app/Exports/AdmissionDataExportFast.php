<?php

namespace App\Exports;

use App\Models\Admission;
use Maat<PERSON>bsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class AdmissionDataExportFast implements FromArray, WithStyles, WithColumnWidths, WithTitle, ShouldAutoSize, WithEvents
{
    protected $filters;

    public function __construct($filters = [])
    {
        $this->filters = $filters;
    }

    public function array(): array
    {
        // Get data efficiently with chunking for better memory usage
        $query = Admission::query()->orderBy('created_at', 'desc');

        // Apply filters
        if (!empty($this->filters['search'])) {
            $query->search($this->filters['search']);
        }

        if (isset($this->filters['status']) && $this->filters['status'] !== '') {
            $query->byStatus($this->filters['status']);
        }

        if (!empty($this->filters['admission_period'])) {
            $query->byAdmissionPeriod($this->filters['admission_period']);
        }

        if (!empty($this->filters['training_level'])) {
            $query->where('training_level', $this->filters['training_level']);
        }

        if (!empty($this->filters['training_program'])) {
            $query->where('training_program', $this->filters['training_program']);
        }

        if (!empty($this->filters['date_from'])) {
            $query->whereDate('created_at', '>=', $this->filters['date_from']);
        }

        if (!empty($this->filters['date_to'])) {
            $query->whereDate('created_at', '<=', $this->filters['date_to']);
        }

        // Use chunk for better memory management
        $admissions = $query->get();

        // Build export data
        $exportData = [];
        
        // Title row
        $exportData[] = [
            'DANH SÁCH TUYỂN SINH',
            '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''
        ];
        
        // Empty row for spacing
        $exportData[] = [
            '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''
        ];
        
        // Headers
        $exportData[] = [
            'STT',
            'Họ',
            'Tên đệm',
            'Tên',
            'Ngày sinh',
            'Giới tính',
            'Mã tuyển sinh',
            'Email',
            'Số điện thoại',
            'Địa chỉ',
            'Tỉnh/Thành phố',
            'Quận/Huyện',
            'Phường/Xã',
            'Bậc đào tạo',
            'Chương trình đào tạo',
            'Trình độ văn hóa',
            'Thời gian tuyển sinh',
            'Đối tượng ưu tiên',
            'Ghi chú',
            'Trạng thái',
            'Người tạo',
            'Ngày tạo',
            'Người duyệt',
            'Ngày duyệt',
            'Lý do từ chối'
        ];

        // Data rows
        foreach ($admissions as $index => $admission) {
            $exportData[] = [
                $index + 1,
                $admission->user_surname ?? '',
                $admission->user_middlename ?? '',
                $admission->user_givenname ?? '',
                $admission->user_birthday ? $admission->user_birthday->format('d/m/Y') : '',
                $this->getGenderLabel($admission->user_gender),
                $admission->user_code ?? '',
                $admission->user_email ?? '',
                $admission->user_telephone ?? '',
                $admission->user_address ?? '',
                $admission->user_province ?? '',
                $admission->user_district ?? '',
                $admission->user_ward ?? '',
                $admission->training_level ?? '',
                $admission->training_program ?? '',
                $admission->cultural_level ?? '',
                $this->getAdmissionPeriod($admission->created_at),
                $admission->priority_object ?? '',
                $admission->note ?? '',
                $this->getStatusLabel($admission->status),
                $admission->created_by ?? '',
                $admission->created_at ? $admission->created_at->format('d/m/Y H:i') : '',
                $admission->approved_by ?? '',
                $admission->approved_at ? $admission->approved_at->format('d/m/Y H:i') : '',
                $admission->rejection_note ?? ''
            ];
        }

        return $exportData;
    }

    public function styles(Worksheet $sheet)
    {
        // Get the last row with data
        $lastRow = $sheet->getHighestRow();

        return [
            // Title row styling
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 16,
                    'color' => ['rgb' => '000000']
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ]
            ],
            // Header row styling (now row 3)
            3 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                    'color' => ['rgb' => 'FFFFFF']
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4']
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                    'wrapText' => true
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000']
                    ]
                ]
            ],
            // Data rows styling (starting from row 4 now)
            "A4:Y{$lastRow}" => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'CCCCCC']
                    ]
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                    'wrapText' => true
                ]
            ]
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 8,   // STT
            'B' => 15,  // Họ
            'C' => 15,  // Tên đệm
            'D' => 15,  // Tên
            'E' => 12,  // Ngày sinh
            'F' => 10,  // Giới tính
            'G' => 15,  // Mã tuyển sinh
            'H' => 25,  // Email
            'I' => 15,  // Số điện thoại
            'J' => 30,  // Địa chỉ
            'K' => 20,  // Tỉnh/Thành phố
            'L' => 15,  // Quận/Huyện
            'M' => 15,  // Phường/Xã
            'N' => 15,  // Bậc đào tạo
            'O' => 25,  // Chương trình đào tạo
            'P' => 20,  // Trình độ văn hóa
            'Q' => 20,  // Thời gian tuyển sinh
            'R' => 20,  // Đối tượng ưu tiên
            'S' => 25,  // Ghi chú
            'T' => 12,  // Trạng thái
            'U' => 20,  // Người tạo
            'V' => 15,  // Ngày tạo
            'W' => 20,  // Người duyệt
            'X' => 15,  // Ngày duyệt
            'Y' => 25,  // Lý do từ chối
        ];
    }

    public function title(): string
    {
        return 'DANH SÁCH THÔNG TIN TUYỂN SINH';
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                // Merge cells A1:Y1 for title
                $event->sheet->getDelegate()->mergeCells('A1:Y1');

                // Set row height for title
                $event->sheet->getDelegate()->getRowDimension('1')->setRowHeight(30);
            },
        ];
    }

    /**
     * Get gender label
     */
    private function getGenderLabel($gender)
    {
        return $gender === 1 ? 'Nam' : 'Nữ';
    }

    /**
     * Get status label
     */
    private function getStatusLabel($status)
    {
        switch ($status) {
            case 0:
                return 'Chờ duyệt';
            case 1:
                return 'Đã duyệt';
            case 2:
                return 'Từ chối';
            default:
                return 'Không xác định';
        }
    }

    /**
     * Get admission period based on created date
     */
    private function getAdmissionPeriod($createdAt)
    {
        if (!$createdAt) return 'Chưa xác định';

        $date = $createdAt instanceof \Carbon\Carbon ? $createdAt : \Carbon\Carbon::parse($createdAt);
        $year = $date->year;
        $month = $date->month;

        // Determine admission period based on month
        if ($month >= 1 && $month <= 3) {
            return "Đợt 1/{$year}";
        } else if ($month >= 4 && $month <= 6) {
            return "Đợt 2/{$year}";
        } else if ($month >= 7 && $month <= 9) {
            return "Đợt 3/{$year}";
        } else {
            return "Đợt 4/{$year}";
        }
    }
}
