<?php

namespace App\Exports;

use App\Exports\LichThiTheoMonExport;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class LichThiTheoMonMultipleExport implements WithMultipleSheets
{
    use Exportable;

    // protected $ds_slot_assignment;
    // protected $ds_members_total;
    protected $ds_lich_thi_theo_lop_mon;
    protected $excel_info;

    public function __construct($ds_lich_thi_theo_lop_mon, $excel_info)
    {
        $this->ds_lich_thi_theo_lop_mon = $ds_lich_thi_theo_lop_mon;
        $this->excel_info = $excel_info;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];

        // for ($i = 0; $i < count($this->ds_slot_assignment); $i++) {
        //     $slot_assignment = $this->ds_slot_assignment[$i];
        //     $sheets[] = new LichThiTheoMonExport($slot_assignment, $this->ds_members_total, $this->excel_info);
        // }

        for ($i = 0; $i < count($this->ds_lich_thi_theo_lop_mon); $i++) {
            $item = $this->ds_lich_thi_theo_lop_mon[$i];

            $is_total = $item['is_total'];
            $ds_slot_assignment = $item['ds_slot_assignment'];
            $ds_members_total = $item['ds_members_total'];

            if ($is_total) {
                $sheets[] = new LichThiTheoMonExport($is_total, null, $ds_members_total, $this->excel_info);
            } else {
                for ($i = 0; $i < count($ds_slot_assignment); $i++) {
                    $slot_assignment = $ds_slot_assignment[$i];
                    $sheets[] = new LichThiTheoMonExport($is_total, $slot_assignment, $ds_members_total, $this->excel_info);
                }
            }
        }

        return $sheets;
    }
}
