<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithEvents;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Illuminate\Contracts\Support\Responsable;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;

class LichThiEOSMutipleExport implements Responsable, FromView, WithTitle, ShouldAutoSize, WithEvents
{
    use Exportable, RegistersEventListeners;

    private $ds_thi_eos;
    private $excel_info;

    private $data = [];

    public function __construct($ds_thi_eos, $excel_info)
    {
        $this->ds_thi_eos = $ds_thi_eos;
        $this->excel_info = $excel_info;
    }

    public function view(): View
    {
        $this->data = [];

        $this->data = $this->ds_thi_eos;

        return view('excel.eos.export_lich_thi_eos', [
            'data' => $this->data,
            'excel_info' => $this->excel_info,
        ]);
    }

    public function title(): string
    {
        return "Sheet1";
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function (AfterSheet $event) {
                $event->sheet
                    ->getPageSetup()
                    ->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_PORTRAIT);

                //create default style
                $default_font_style = [
                    'font' => ['name' => 'Times New Roman', 'size' => 11]
                ];

                //style default
                $event->sheet->getParent()->getDefaultStyle()->applyFromArray($default_font_style);
                //style header
                $event->sheet->getStyle('A1:F1')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
                $event->sheet->getStyle('A1:F1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                $event->sheet->getStyle('A1:F1')->getFont()->setBold(true);
                //style table
                $table_style_array = array(
                    'borders' => array(
                        'allBorders' => array(
                            'borderStyle' => Border::BORDER_THIN,
                        )
                    )
                );

                $cellTable = 'A1:F' . (1 + count($this->data));
                $event->sheet->getStyle($cellTable)->applyFromArray($table_style_array);
            },
        ];
    }
}
