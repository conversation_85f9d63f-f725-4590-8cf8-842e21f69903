<?php

namespace App\Exports\RelearnOnline;

use App\Models\RelearnOnlineCalendar;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;

class ExportCalendar implements FromView
{
    private $skill_code;

    public function __construct($skill_code)
    {
        $this->skill_code = $skill_code;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function view(): View
    {
        $skill_code = $this->skill_code;
        $calendar = RelearnOnlineCalendar::with(['calendarUser' => function ($query) {
            $query->with('relearn');
        }])->when($skill_code, function ($query, $skill_code) {
            $query->where('skill_code', $skill_code);
        })->get();

        return view('excel.relearn_online.export_calendar', [
            'data' => $calendar,
        ]);
    }
}
