<?php

namespace App\Exports\RelearnOnline;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class DanhSachKhongDuDieuKienThiLai implements FromView, ShouldAutoSize
{
    private $data;
    private $grades;

    public function __construct($data, $grades)
    {
        $this->data = $data;
        $this->grades = $grades;
    }

    public function view(): View
    {
        foreach($this->data as $item) {
            $ptl_text = [];
            if (count($item->phai_thi_lai) > 0) {
                foreach ($item->phai_thi_lai as $ptl) {
                    $ptl_text[] = $ptl['type'];
                }
            }
            $item->phai_thi_lai_text = implode(' ,', $ptl_text);
        }
        return view('excel.relearn_online.danh_sach_khong_du_dieu_kien', [
            'relearns' => $this->data,
            'grades' => $this->grades,
        ]);
    }
}
