<?php

namespace App\Exports\OnlineService;

use App\Exports\ExportBHYTService;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\Exportable;

class MultipleExportBHYTService implements WithMultipleSheets
{
    use Exportable;
    private $data;
    
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * @return array
     */

    public function sheets(): array
    {
        $sheets = [];

        $sheets[] = new ExportBHYTService($this->data, 'excel.export_BHYT_service.export_student_bhyt');
        $sheets[] = new ExportBHYTService($this->data, 'excel.export_BHYT_service.export_student_change_info');
        $sheets[] = new ExportBHYTService($this->data, 'excel.export_BHYT_service.export_bhyt_student_member');

        return $sheets;
    }
}
