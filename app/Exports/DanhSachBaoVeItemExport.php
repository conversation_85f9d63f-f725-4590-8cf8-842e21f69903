<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Illuminate\Contracts\Support\Responsable;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;


class DanhSachBaoVeItemExport implements Responsable, FromView, WithDrawings, WithTitle, ShouldAutoSize, WithEvents
{
    use Exportable, RegistersEventListeners;

    private $file_name = "lich-bao-ve.xls";

    private $data = [];

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function title(): string
    {
        $group = $this->data['group'];
        return $group->psubject_code . '_' . $group->group_name;
    }

    public function drawings()
    {
        $drawing = new Drawing();
        $drawing->setName('Logo');
        $drawing->setDescription('This is my logo');
        $drawing->setPath(public_path('images/logo.png'));
        $drawing->setHeight(60);
        $drawing->setCoordinates('A1');

        return $drawing;
    }

    public function view(): View
    {
        $is_check = $this->data['error'];
        $message = $this->data['message'];
        $date_assignment = $this->data['date_assignment'];
        $group = $this->data['group'];

        $current_activity = null;
        $members = [];

        if ($is_check != null && $is_check == 1) {
            $current_activity = $this->data['current_activity'][0];
            $members = $this->data['members'];
        }

        return view('excel.calendar.export_activity', [
            'is_check' => $is_check,
            'message' => $message,
            'date_assignment' => $date_assignment,
            'group' => $group,
            'current_activity' => $current_activity,
            'members' => $members,
        ]);
    }

}
