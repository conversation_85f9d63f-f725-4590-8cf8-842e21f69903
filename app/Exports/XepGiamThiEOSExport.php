<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\BeforeExport;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Illuminate\Contracts\Support\Responsable;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;

class XepGiamThiEOSExport implements Responsable, FromView, WithTitle, ShouldAutoSize, WithEvents
{
    use Exportable, RegistersEventListeners;

    private $fileName = "xep-giam-thi.xls";

    private $ds_error;
    private $excel_info;

    private $group_name;
    private $ds_mon;

    private $data = [];

    public function __construct($ds_error, $excel_info, $group_name, $ds_mon)
    {
        $this->ds_error = $ds_error;
        $this->excel_info = $excel_info;

        $this->group_name = $group_name;
        $this->ds_mon = $ds_mon;
    }

    public function view(): View
    {
        $this->data = [];

        $this->data = $this->ds_mon;

        return view('excel.eos.export_giam_thi', [
            'ds_error' => $this->ds_error,
            'data' => $this->data,
            'excel_info' => $this->excel_info
        ]);
    }

    public function title(): string
    {
        if ($this->ds_error != null && count($this->ds_error) > 0) {
            return "ERROR";
        } else {
            // return $this->group_name ?? "";
            return "Sheet1";
        }
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        if ($this->ds_error != null && count($this->ds_error) > 0) {
            return [
                BeforeExport::class  => function (BeforeExport $event) {
                    $event->writer->setCreator('dev');
                },
                AfterSheet::class    => function (AfterSheet $event) {
                    $event->sheet
                        ->getPageSetup()
                        ->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_PORTRAIT);

                    //create default style
                    $default_font_style = [
                        'font' => ['name' => 'Times New Roman', 'size' => 11]
                    ];

                    //style default
                    $event->sheet->getParent()->getDefaultStyle()->applyFromArray($default_font_style);

                    //style header
                    $event->sheet->getStyle('A1:H1')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
                },
            ];
        } else {
            return [
                BeforeExport::class  => function (BeforeExport $event) {
                    $event->writer->setCreator('dev');
                },
                AfterSheet::class    => function (AfterSheet $event) {
                    $event->sheet
                        ->getPageSetup()
                        ->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_PORTRAIT);

                    //create default style
                    $default_font_style = [
                        'font' => ['name' => 'Times New Roman', 'size' => 11]
                    ];

                    //style default
                    $event->sheet->getParent()->getDefaultStyle()->applyFromArray($default_font_style);
                    //style header
                    $event->sheet->getStyle('A1:I1')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
                    //style table
                    $table_style_array = array(
                        'borders' => array(
                            'allBorders' => array(
                                'borderStyle' => Border::BORDER_THIN,
                            )
                        )
                    );

                    if ($this->data == null || count($this->data) <= 0) {
                        $cellTable = 'A1:H' . (1 + 3);
                    } else {
                        $cellTable = 'A1:H' . (1 + count($this->data));
                    }
                    $event->sheet->getStyle($cellTable)->applyFromArray($table_style_array);
                },
            ];
        }
    }
}
