<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class WalletImportTemplate implements WithMultipleSheets
{
    protected $type;

    public function __construct($type = 'create')
    {
        $this->type = $type;
    }

    public function sheets(): array
    {
        if ($this->type === 'create') {
            return [
                'Tạo ví' => new WalletCreateTemplate(),
                'Hướng dẫn' => new WalletCreateInstructions(),
            ];
        } else {
            return [
                'Nạp tiền' => new WalletDepositTemplate(),
                'Hướng dẫn' => new WalletDepositInstructions(),
            ];
        }
    }
}

class WalletCreateTemplate implements FromArray, WithHeadings, WithTitle, WithStyles
{
    public function array(): array
    {
        return [
            ['SV001', 'student001'],
            ['SV002', 'student002'],
            ['SV003', 'student003'],
        ];
    }

    public function headings(): array
    {
        return [
            'ma_sinh_vien',
            'tai_khoan'
        ];
    }

    public function title(): string
    {
        return 'Tạo ví';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']], 'fill' => ['fillType' => 'solid', 'color' => ['rgb' => '4472C4']]],
            'A:B' => ['alignment' => ['horizontal' => 'center']],
        ];
    }
}

class WalletCreateInstructions implements FromArray, WithHeadings, WithTitle, WithStyles
{
    public function array(): array
    {
        return [
            ['ma_sinh_vien', 'Mã sinh viên', 'Bắt buộc, tối đa 20 ký tự', 'SV001, HE123456'],
            ['tai_khoan', 'Tài khoản đăng nhập', 'Bắt buộc, tối đa 50 ký tự', 'student001, nguyenvana'],
            ['', '', '', ''],
            ['VALIDATION:', '', '', ''],
            ['1. Mã sinh viên và tài khoản phải tồn tại trong hệ thống', '', '', ''],
            ['2. Mã sinh viên và tài khoản phải thuộc cùng 1 người', '', '', ''],
            ['3. Tài khoản phải có user_level = 3 (sinh viên)', '', '', ''],
            ['4. Sinh viên chưa có ví trong hệ thống', '', '', ''],
            ['', '', '', ''],
            ['LƯU Ý:', '', '', ''],
            ['1. File phải có định dạng .xlsx hoặc .xls', '', '', ''],
            ['2. Không được để trống cột ma_sinh_vien và tai_khoan', '', '', ''],
            ['3. Ví được tạo với số dư = 0', '', '', ''],
            ['4. Nạp tiền vào ví sử dụng chức năng import riêng', '', '', ''],
        ];
    }

    public function headings(): array
    {
        return [
            'Tên cột',
            'Mô tả',
            'Quy tắc',
            'Ví dụ'
        ];
    }

    public function title(): string
    {
        return 'Hướng dẫn';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']], 'fill' => ['fillType' => 'solid', 'color' => ['rgb' => '70AD47']]],
            5 => ['font' => ['bold' => true, 'color' => ['rgb' => 'C5504B']]],
            'A:D' => ['alignment' => ['horizontal' => 'left']],
        ];
    }
}

class WalletDepositTemplate implements FromArray, WithHeadings, WithTitle, WithStyles
{
    public function array(): array
    {
        return [
            ['SV001', 1000000, 'Nạp tiền học phí kỳ 1', 'bank_transfer'],
            ['SV002', 500000, 'Nạp tiền học lại môn Toán', 'cash'],
            ['SV003', 200000, 'Nạp tiền phụ thu', 'card'],
            ['HE123456', 1500000, 'Nạp tiền học phí', 'bank_transfer'],
            ['SE789012', 300000, 'Nạp tiền thi lại', 'cash'],
        ];
    }

    public function headings(): array
    {
        return [
            'ma_sinh_vien',
            'so_tien',
            'mo_ta',
            'phuong_thuc'
        ];
    }

    public function title(): string
    {
        return 'Nạp tiền';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']], 'fill' => ['fillType' => 'solid', 'color' => ['rgb' => 'E67E22']]],
            'A:D' => ['alignment' => ['horizontal' => 'center']],
        ];
    }
}

class WalletDepositInstructions implements FromArray, WithHeadings, WithTitle, WithStyles
{
    public function array(): array
    {
        return [
            ['ma_sinh_vien', 'Mã sinh viên', 'Bắt buộc, tối đa 20 ký tự', 'SV001, HE123456'],
            ['so_tien', 'Số tiền nạp (VNĐ)', 'Bắt buộc, >= 1000', '1000000, 500000'],
            ['mo_ta', 'Mô tả giao dịch', 'Không bắt buộc, tối đa 255 ký tự', 'Nạp học phí kỳ 1'],
            ['phuong_thuc', 'Phương thức thanh toán', 'Không bắt buộc, mặc định: bank_transfer', 'cash, bank_transfer, card, momo'],
            ['', '', '', ''],
            ['VALIDATION:', '', '', ''],
            ['1. Sinh viên phải đã có ví trong hệ thống', '', '', ''],
            ['2. Ví không được bị khóa', '', '', ''],
            ['3. Số tiền tối thiểu là 1,000 VNĐ', '', '', ''],
            ['', '', '', ''],
            ['LƯU Ý:', '', '', ''],
            ['1. File phải có định dạng .xlsx hoặc .xls', '', '', ''],
            ['2. Không được để trống cột ma_sinh_vien và so_tien', '', '', ''],
            ['3. Tiền sẽ được nạp vào ví duy nhất của sinh viên', '', '', ''],
        ];
    }

    public function headings(): array
    {
        return [
            'Tên cột',
            'Mô tả',
            'Quy tắc',
            'Ví dụ'
        ];
    }

    public function title(): string
    {
        return 'Hướng dẫn';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']], 'fill' => ['fillType' => 'solid', 'color' => ['rgb' => '70AD47']]],
            6 => ['font' => ['bold' => true, 'color' => ['rgb' => '2E75B6']]],
            11 => ['font' => ['bold' => true, 'color' => ['rgb' => 'C5504B']]],
            'A:D' => ['alignment' => ['horizontal' => 'left']],
        ];
    }
}
