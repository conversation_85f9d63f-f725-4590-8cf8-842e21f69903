<?php

namespace App\Services;

use App\Models\Fee\StudentDebt;
use App\Models\Fee\DebtPayment;
use App\Models\Fee\StudentWallet;
use App\Exceptions\PaymentException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class PaymentService
{
    /**
     * Process debt payment for API requests
     *
     * @param StudentDebt $debt
     * @param array $paymentData
     * @return array
     * @throws PaymentException
     */
    public function processDebtPayment(StudentDebt $debt, array $paymentData)
    {
        try {
            DB::beginTransaction();

            // Validate debt status
            if ($debt->status === StudentDebt::STATUS_PAID) {
                throw new PaymentException('Debt has already been paid');
            }

            if ($debt->status === StudentDebt::STATUS_CANCELLED) {
                throw new PaymentException('Cannot pay cancelled debt');
            }

            // Validate payment amount
            $amount = floatval($paymentData['amount'] ?? $debt->remaining_amount);
            $remainingAmount = $debt->amount - $debt->paid_amount;

            if ($amount <= 0) {
                throw new PaymentException('Payment amount must be greater than 0');
            }

            if ($amount > $remainingAmount) {
                throw new PaymentException('Payment amount cannot exceed remaining debt amount');
            }

            // Process payment based on type
            $paymentType = $paymentData['payment_type'] ?? 'direct';

            if ($paymentType === 'wallet') {
                $this->processWalletPayment($debt, $amount, $paymentData);
            } else {
                $this->processDirectPayment($debt, $amount, $paymentData);
            }

            DB::commit();

            Log::info('Debt payment processed successfully', [
                'debt_id' => $debt->id,
                'amount' => $amount,
                'payment_type' => $paymentType,
                'user_code' => $debt->user_code
            ]);

            return [
                'debt' => $debt->fresh(),
                'message' => 'Payment processed successfully'
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Payment processing failed', [
                'debt_id' => $debt->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new PaymentException('Payment processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Process wallet payment
     *
     * @param StudentDebt $debt
     * @param float $amount
     * @param array $paymentData
     * @return void
     * @throws PaymentException
     */
    protected function processWalletPayment(StudentDebt $debt, float $amount, array $paymentData)
    {
        // Get wallet
        $wallet = StudentWallet::where('user_code', $debt->user_code)->first();

        if (!$wallet) {
            throw new PaymentException('Student wallet not found');
        }

        if ($wallet->is_locked) {
            throw new PaymentException('Wallet is locked: ' . ($wallet->lock_reason ?? 'Unknown reason'));
        }

        if ($wallet->balance < $amount) {
            throw new PaymentException('Insufficient wallet balance');
        }

        // Deduct from wallet
        $wallet->balance -= $amount;
        $wallet->save();

        // Create payment record
        $payment = DebtPayment::create([
            'debt_id' => $debt->id,
            'user_code' => $debt->user_code,
            'user_login' => $debt->user_login,
            'amount' => $amount,
            'payment_method' => 'wallet',
            'transaction_id' => $paymentData['transaction_id'] ?? null,
            'invoice_id' => $paymentData['invoice_id'] ?? null,
            'note' => $paymentData['note'] ?? 'Payment from wallet',
            'created_by' => Auth::user()->user_login ?? 'system'
        ]);

        // Update debt
        $this->updateDebtAfterPayment($debt, $amount);

        // Log wallet activity
        $debt->logPayment($amount, 'wallet', [
            'wallet_balance_before' => $wallet->balance + $amount,
            'wallet_balance_after' => $wallet->balance
        ]);
    }

    /**
     * Process direct payment
     *
     * @param StudentDebt $debt
     * @param float $amount
     * @param array $paymentData
     * @return void
     */
    protected function processDirectPayment(StudentDebt $debt, float $amount, array $paymentData)
    {
        // Create payment record
        $payment = DebtPayment::create([
            'debt_id' => $debt->id,
            'user_code' => $debt->user_code,
            'user_login' => $debt->user_login,
            'amount' => $amount,
            'payment_method' => $paymentData['payment_method'] ?? 'cash',
            'transaction_id' => $paymentData['transaction_id'] ?? null,
            'invoice_id' => $paymentData['invoice_id'] ?? null,
            'note' => $paymentData['note'] ?? null,
            'created_by' => Auth::user()->user_login ?? 'system'
        ]);

        // Update debt
        $this->updateDebtAfterPayment($debt, $amount);

        // Log payment activity
        $debt->logPayment($amount, $paymentData['payment_method'] ?? 'cash');
    }

    /**
     * Update debt after payment
     *
     * @param StudentDebt $debt
     * @param float $amount
     * @return void
     */
    protected function updateDebtAfterPayment(StudentDebt $debt, float $amount)
    {
        // Update paid amount
        $newPaidAmount = $debt->paid_amount + $amount;

        // Determine new status
        $newStatus = StudentDebt::STATUS_PARTIAL_PAID;
        if ($newPaidAmount >= $debt->amount) {
            $newStatus = StudentDebt::STATUS_PAID;
            // Ensure paid amount doesn't exceed debt amount
            $newPaidAmount = $debt->amount;
        }

        // Update debt
        $debt->update([
            'paid_amount' => $newPaidAmount,
            'status' => $newStatus,
            'updated_at' => now()
        ]);
    }

    /**
     * Validate payment data
     *
     * @param array $paymentData
     * @return bool
     * @throws PaymentException
     */
    public function validatePaymentData(array $paymentData)
    {
        if (isset($paymentData['amount']) && $paymentData['amount'] <= 0) {
            throw new PaymentException('Payment amount must be greater than 0');
        }

        return true;
    }

    /**
     * Get payment history for a debt
     *
     * @param int $debtId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPaymentHistory($debtId)
    {
        return DebtPayment::where('debt_id', $debtId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Calculate total paid amount for a debt
     *
     * @param int $debtId
     * @return float
     */
    public function getTotalPaidAmount($debtId)
    {
        return DebtPayment::where('debt_id', $debtId)
            ->sum('amount');
    }

    /**
     * Check if debt is fully paid
     *
     * @param StudentDebt $debt
     * @return bool
     */
    public function isDebtFullyPaid(StudentDebt $debt)
    {
        $totalPaid = $this->getTotalPaidAmount($debt->id);
        return $totalPaid >= $debt->amount;
    }

    /**
     * Get remaining amount for a debt
     *
     * @param StudentDebt $debt
     * @return float
     */
    public function getRemainingAmount(StudentDebt $debt)
    {
        return max(0, $debt->amount - $debt->paid_amount);
    }
}
