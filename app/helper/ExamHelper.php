<?php

namespace App\helper;

use App\Models\Fu\Activity;
use App\Models\Fu\Attendance;
use App\Models\Fu\Block;

use App\Models\Fu\Course;
use App\Models\Fu\Group;
use App\Models\Fu\Term;
use App\Models\GroupEOS;
use App\Models\GroupGraduate;
use App\Models\T7\CourseGrade;
use App\Models\T7\CourseResult;
use App\Models\T7\GradeGroup;
use App\Models\T7\SyllabusPlan;


use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ExamHelper {


    /**
     * 
     * Xuất danh sách thi
     * 
     * <AUTHOR>
     * @since 27/11/2023
     * @todo Cập nhập danh sách sinh viên 
     * @param Integer $termId Id kỳ
     * @param Integer $courseId Id khóa học
     * @param Integer $listGroupId danh sách id lớp cần đồng bộ
     * @return String $message
     */
    static public function SyncExamAssignment($termId, $courseId, $listGroupId = []) {
        DB::getPdo()->setAttribute(\PDO::ATTR_EMULATE_PREPARES, true);
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', '-1');
        // Cập nhập danh sách thi
        try {
            // Log::error("Dev: " . time());
            $groupErr = null;
            $termId = $termId;
            $blockId = null;
            $listGroupIdBlock = [];

            if ( $termId == null || $termId <= 0) {
                return array('danger', 'Vui lòng chọn môn hoặc lớp môn cần in danh sách thi!');
            }

            $term = Term::where('id', $termId)->first();
            if ($term == null) {
                return array('danger', 'Không tìm thấy kỳ cần in danh sách thi!');
            }

            $blocks = Block::where('term_id', $termId)->get();

            // Lấy khóa học
            $course = Course::find($courseId);

            // kiểm tra có lớp chưa được up lên chưa 
            $checkGroupCalendar = Group::select([
                "list_group.id",
                "list_group.body_id",
                "list_group.group_name",
                "list_group.pterm_name",
                DB::raw("COUNT(group_member.member_login) as number_student"),
                DB::raw("(
                    SELECT COUNT(group_graduate.student_login)
                    FROM group_graduate 
                    WHERE `group_graduate`.`group_id` = `list_group`.`id`
                ) as number_student_exam"),  
            ])
            ->join('group_member', 'list_group.id', '=', 'group_member.groupid')
            ->where('list_group.pterm_id', $termId)
            ->where('list_group.body_id', $courseId)
            ->where('list_group.is_virtual', 0)
            ->when($listGroupId, function ($q, $listGroupId) {
                $q->whereIn('list_group.id', $listGroupId);
            })
            ->groupBy('list_group.id')
            ->havingRaw('number_student > number_student_exam')
            ->get();

            if (count($checkGroupCalendar) > 0) {
                $listGroupIdBlock = array_merge($listGroupIdBlock, $checkGroupCalendar->pluck('id')->toArray());
                $checkGroupCalendar = $checkGroupCalendar->pluck('group_name')->toArray();
                $strReport = count($checkGroupCalendar) . ' lớp môn ' . $course->psubject_code . ' chưa xếp hết lịch thi cho sinh viên, vui lòng kiểm tra lại các lớp: ' . implode(', ', $checkGroupCalendar);
                Log::channel('exam_schedule')->error(['msg' => $strReport]);
                // return array('danger', $strReport);
            }

            // Lấy danh sách lớp
            $groups = Group::where('body_id', $course->id)
            ->with([
                'groupMembers' => function($q) {
                    $q->select([
                        'group_member.id', 
                        'group_member.groupid', 
                        'group_member.member_login', 
                        'group_member.user_code',
                        'user.user_code',
                        DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ", user.user_givenname) as full_name')
                    ])
                    ->join('user', 'user.user_login', 'group_member.member_login')
                    ->get();
                }])
            ->where('pterm_id', $termId)
            ->where('list_group.is_virtual', 0)
            ->when($listGroupId, function ($q, $listGroupId) {
                $q->whereIn('list_group.id', $listGroupId);
            })
            ->whereNotIn('list_group.id', $listGroupIdBlock)
            ->get();

            if (count($groups) == 0) {
                return array('danger', 'Không tìm thấy lớp nào!');
            }

            // Nếu pass qua quét từng lớp 1 
            // Lấy ra số buổi học + đầu điểm
            
            // Kiểm tra xem có các buổi bảo vệ không ?
            $checkCouseSlotDef = SyllabusPlan::where('syllabus_id', $course->syllabus_id)
                ->where('session_type', '=', 11)
                // ->whereIn('session_type', [11, 9])
                ->orderBy('course_session')
                ->pluck('course_session');
                // ->get();
            if ($checkCouseSlotDef == null || count($checkCouseSlotDef) != 3) {
                return array('danger', 'Không tìm thấy 3 buổi bảo vệ của môn hoặc lớp môn trong syllabus');
            }
            
            // Danh sách buổi bảo vệ
            $listActivitiesDef = Activity::select([
                'id',
                'groupid',
                'slot', 
                'day', 
                'is_online', 
                'room_name', 
                'url_room_online',
                'course_slot',
                'session_type', 
            ])
                ->whereIn('groupid', $groups->pluck('id')->toArray())
                ->whereIn('course_slot', $checkCouseSlotDef)
                ->get();
        
            // Danh sách buổi học
            $listActivities = Activity::whereIn('groupid', $groups->pluck('id')->toArray())
                ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
                ->where('session_type.is_exam', 0)
                ->orderBy('course_slot')
                ->get();

            // Lấy danh sách các nhóm đầu điểm cần tính toán
            $listGradeGroupCheck = GradeGroup::whereRaw("syllabus_id = ? AND Id NOT IN (
                SELECT grade_group_id 
                FROM t7_grade WHERE syllabus_id = ?
                AND (
                    is_final_exam = 1	
                    OR t7_grade.bonus_type = 1
                )
            )", [$course->syllabus_id, $course->syllabus_id])->with(['grades'])->get();
                

            // duyệt lớp
            $res = [];
            foreach ($groups as $key => $group) {
                // if ($key == 6) break;
                $members = $group->groupMembers;
                // lấy danh sách buổi học
                $listActivityByGroup = $listActivities->where('groupid', $group->id);
                $listActivityByGroupId = $listActivities->pluck('id')->toArray();
                $listActivitiesDefByGroup = $listActivitiesDef->where('groupid', $group->id)->sortBy('course_slot');
                // if (count($listActivitiesDefByGroup) != 3) {
                //     return array('danger', "Không tìm thấy 3 buổi bảo vệ của lớp $group->group_name($group->id)");
                // }
                
                $res[$group->id]['course'] = array_values($listActivitiesDefByGroup->toArray());
                // $res[$group->id]['group'] = $group;
                $res[$group->id]['group_id'] = $group->id;
                $res[$group->id]['list_no_course'] = [];

                // lịch sử học của cả lớp
            
                // lấy danh sách điểm theo lớp
                $listStudentGrades = CourseGrade::select([
                    'id',
                    'val', 
                    'grade_id',
                    'groupid',
                    'login',
                    'grade_group_id'
                ])
                // ->whereIn('groupid', $groups->pluck('id')->toArray())
                ->where('groupid', $group->id)
                ->orderBy('grade_id')
                ->get();
                
                $listAllAttendance = Attendance::select([
                    'user_login',
                    DB::raw('count(attendance.id) as total_att')
                ])
                // ->whereIn('groupid', $groups->pluck('id')->toArray())
                ->whereIn('activity_id', $listActivityByGroupId)
                ->where('groupid', $group->id)
                ->groupBy('user_login')
                ->pluck('total_att', 'user_login')->toArray();
                $listWarning = [];
                foreach ($members as $member) {
                    $listMsgFail = [];
                    $memberStatus = 0;
                    $courseResult = CourseResult::select([
                        'id',
                        'student_login',
                        'groupid',
                        'val'
                    ])->where('student_login', $member->member_login)
                    ->where('groupid', $group->id)
                    ->first();
                    
                    if (!$courseResult) {
                        $listWarning[$group->id][] = ('Vui lòng kiểm tra lại "Lịch sử học" sinh viên ' . $member->member_login . " lớp $group->group_name($group->id)!");
                        continue;
                    }

                    $member->group_name = $group->group_name;
                    if ($courseResult->val == -1) {
                        $member->status = 1;
                        $member->reason_fail = "Cấm thi do trượt điểm danh";
                        $res[$group->id]['list_fail'][] = $member->toArray();
                        continue;
                    }

                    // kiểm tra sinh viên đầy đủ buổi điểm danh chưa
                    if (!isset($listAllAttendance[$member->member_login])) {
                        $listWarning[$group->id][] = ('Vui lòng điểm danh tất cả các buổi học cho sinh viên ' . $member->member_login . " lớp $group->group_name($group->id)!");
                        continue;
                    }
                    
                    $studentAttendance = $listAllAttendance[$member->member_login];
                    if (count($listActivityByGroup) != $studentAttendance) {
                        $listWarning[$group->id][] = ('Vui lòng điểm danh tất cả các buổi học cho sinh viên ' . $member->member_login . " lớp $group->group_name($group->id)!");
                        continue;
                    } 

                    // Duyệt nhóm đầu điểm
                    foreach ($listGradeGroupCheck as $GradeGroup) {
                        //lấy ds nhóm đầu điểm của nhóm đầu điểm
                        $listGradeByGroup = $GradeGroup->grades;
                        //lấy tất cả điểm quá trình sinh viên đạt được
                        $listStudentGrade = $listStudentGrades
                        // ->where('groupid', $group->id)
                        ->where('login', $member->member_login)
                        ->where('grade_group_id', $GradeGroup->id);
                        
                        $listStudentGradeArr = $listStudentGrade->pluck('val', 'grade_id');
                        $groupGradeTotalPoint = 0;
                        // Kiểm tra nhóm điểm
                        foreach ($listGradeByGroup as $item) {
                            if (isset($listStudentGradeArr[$item->id])) {
                                if ($listStudentGradeArr[$item->id] < $item->minimum_required) {
                                    $listMsgFail[] = "Đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]: " . $listStudentGradeArr[$item->id] . " < " . $item->minimum_required;
                                }
    
                                //tính điểm theo nhóm cho sv
                                if ($GradeGroup->weight > 0) {
                                    $qt_detail_score = ($item->weight / $GradeGroup->weight) * $listStudentGradeArr[$item->id];
                                    $groupGradeTotalPoint += $qt_detail_score;
                                }
                            } else {
                                $listMsgFail[] = "Thiếu đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]";
                            }
                        }
    
                        // Làm tròn nhóm đầu điểm để xét điều kiện
                        $groupGradeTotalPointCheck = round($groupGradeTotalPoint, 1);
                        if ($groupGradeTotalPointCheck < $GradeGroup->minimum_required) {
                            $listMsgFail[] = "Nhóm điểm [$GradeGroup->grade_group_name]: " . $groupGradeTotalPointCheck . " < " . $GradeGroup->minimum_required . " - Chưa làm tròn: " . $groupGradeTotalPointCheck;
                        }
                    }
                    
                    // kiểm tra xem 
                    if (count($listMsgFail) > 0) {
                        $member->status = 2;
                        $member->reason_fail = "Cấm thi do điểm thành phần: " . implode(", ", $listMsgFail);
                        $res[$group->id]['list_fail'][] = $member->toArray();
                        continue;
                    } else {
                        $member->reason_fail = "";
                    }

                    $member->status = $memberStatus;
                }

                if (count($listWarning) > 0) Log::channel('exam_schedule')->warning($listWarning);
                unset($listActivityByGroup);
                unset($listActivitiesDefByGroup);
                unset($listStudentGrades);

                foreach ($res as $key => $groupCheck) {
                    $groupErr = $groupCheck['group_id'];
                    GroupGraduate::where('group_id', $groupCheck['group_id'])
                        ->where('status', '!=', 0)
                        ->update([
                            'status' => 0
                        ]);
                    GroupGraduate::where('group_id', $groupCheck['group_id'])
                        ->where('course_session', 1)
                        ->update([
                            'activity_id' => $res[$groupCheck['group_id']]['course'][0]['id'] ?? ""
                        ]);
                    GroupGraduate::where('group_id', $groupCheck['group_id'])
                        ->where('course_session', 2)
                        ->update([
                            'activity_id' => $res[$groupCheck['group_id']]['course'][1]['id'] ?? ""
                        ]);
                    GroupGraduate::where('group_id', $groupCheck['group_id'])
                        ->where('course_session', 3)
                        ->update([
                            'activity_id' => $res[$groupCheck['group_id']]['course'][2]['id'] ?? ""
                        ]);

                    if (count(['list_fail']) == 0) {
                        continue;
                    }
                    
                    // Cập nhập từng sinh viên
                    foreach ($groupCheck['list_fail'] ?? [] as $member) {
                        GroupGraduate::where('group_id', $member['groupid'])
                            ->where('student_login', $member['member_login'])
                            ->update([
                                'status' => $member['status'],
                                'reason_fail' => $member['reason_fail'],
                            ]);
                    }
                }
            }

            return true;
        } catch (Exception $th) {
            Log::error("---------dev check: Lỗi đồng bộ danh sách thi ------------");
            Log::error([$termId, $courseId, $groupErr, $listGroupId]);
            Log::error($th);
            return array('danger', 'Có lỗi xảy ra. vui lòng thử lại');
        }
    }

    /**
     * 
     * Xuất danh sách thi eos
     * 
     * @since 27/11/2023
     * @todo Cập nhập danh sách sinh viên eos
     * @param Integer $termId Id kỳ
     * @param Integer $courseId Id khóa học
     * @param Integer $listGroupId danh sách id lớp cần đồng bộ
     * @return String $message
     */
    static public function SyncExamEos($termId, $courseId, $listGroupId = []) {
        DB::getPdo()->setAttribute(\PDO::ATTR_EMULATE_PREPARES, true);
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', '-1');
        // Cập nhập danh sách thi
        DB::beginTransaction();
        try {
            // Log::error("Dev: " . time());
            $groupErr = null;
            $termId = $termId;
            $blockId = null;
            $listGroupIdBlock = [];

            if ( $termId == null || $termId <= 0) {
                return array('danger', 'Vui lòng chọn môn hoặc lớp môn cần in danh sách thi!');
            }

            $term = Term::where('id', $termId)->first();
            if ($term == null) {
                return array('danger', 'Không tìm thấy kỳ cần in danh sách thi!');
            }

            $blocks = Block::where('term_id', $termId)->get();

            // Lấy khóa học
            $course = Course::find($courseId);

            // kiểm tra có lớp chưa được up lên chưa 
            $checkGroupCalendar = Group::select([
                "list_group.id",
                "list_group.body_id",
                "list_group.group_name",
                "list_group.pterm_name",
                DB::raw("COUNT(group_member.member_login) as number_student"),
                DB::raw("(
                    SELECT COUNT(group_eos.student_login)
                    FROM group_eos 
                    WHERE `group_eos`.`group_id` = `list_group`.`id`
                ) as number_student_exam"),  
            ])
            ->join('group_member', 'list_group.id', '=', 'group_member.groupid')
            ->where('list_group.pterm_id', $termId)
            ->where('list_group.body_id', $courseId)
            ->where('list_group.is_virtual', 0)
            ->when($listGroupId, function ($q, $listGroupId) {
                $q->whereIn('list_group.id', $listGroupId);
            })
            ->groupBy('list_group.id')
            ->havingRaw('number_student > number_student_exam')
            ->get();
            if (count($checkGroupCalendar) > 0) {
                $checkGroupCalendar = $checkGroupCalendar->pluck('group_name')->toArray();
                $strReport = count($checkGroupCalendar) . ' lớp môn ' . $course->psubject_code . ' chưa xếp hết lịch thi cho sinh viên, vui lòng kiểm tra lại các lớp: ' . implode(', ', $checkGroupCalendar);
                Log::channel('exam_schedule')->error(['msg' => $strReport]);
            }

            // Lấy danh sách lớp
            $groups = Group::where('body_id', $course->id)
            ->with([
                'groupMembers' => function($q) {
                    $q->select([
                        'group_member.id', 
                        'group_member.groupid', 
                        'group_member.member_login', 
                        'group_member.user_code',
                        'user.user_code',
                        DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ", user.user_givenname) as full_name')
                    ])
                    ->join('user', 'user.user_login', 'group_member.member_login')
                    ->get();
                }])
            ->where('pterm_id', $termId)
            ->where('list_group.is_virtual', 0)
            ->when($listGroupId, function ($q, $listGroupId) {
                $q->whereIn('list_group.id', $listGroupId);
            })
            ->whereNotIn('list_group.id', $listGroupIdBlock)
            ->get();

            if (count($groups) == 0) {
                return array('danger', 'Không tìm thấy lớp nào!');
            }

            // Nếu pass qua quét từng lớp 1 
            // Lấy ra số buổi học + đầu điểm
            
            // Kiểm tra xem có các buổi bảo vệ không ?
            $checkCouseSlotDef = SyllabusPlan::where('syllabus_id', $course->syllabus_id)
                ->where('session_type', '=', 9)
                // ->whereIn('session_type', [11, 9])
                ->orderBy('course_session')
                ->pluck('course_session');
                // ->get();
            if ($checkCouseSlotDef == null || count($checkCouseSlotDef) != 3) {
                return array('danger', 'Không tìm thấy 3 buổi bảo vệ của môn hoặc lớp môn trong syllabus');
            }
            
            // Danh sách buổi bảo vệ
            $listActivitiesDef = Activity::select([
                'id',
                'groupid',
                'slot', 
                'day', 
                'is_online', 
                'room_name', 
                'url_room_online',
                'course_slot',
                'session_type', 
            ])
                ->whereIn('groupid', $groups->pluck('id')->toArray())
                ->whereIn('course_slot', $checkCouseSlotDef)
                ->get();
        
            // Danh sách buổi học
            $listActivities = Activity::whereIn('groupid', $groups->pluck('id')->toArray())
                ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
                ->where('session_type.is_exam', 0)
                ->orderBy('course_slot')
                ->get();

            // Lấy danh sách các nhóm đầu điểm cần tính toán
            $listGradeGroupCheck = GradeGroup::whereRaw("syllabus_id = ? AND Id NOT IN (
                SELECT grade_group_id 
                FROM t7_grade WHERE syllabus_id = ?
                AND (
                    is_final_exam = 1	
                    OR t7_grade.bonus_type = 1
                )
            )", [$course->syllabus_id, $course->syllabus_id])->with(['grades'])->get();
                

            // duyệt lớp
            $res = [];
            foreach ($groups as $key => $group) {
                // if ($key == 6) break;
                $members = $group->groupMembers;
                // lấy danh sách buổi học
                $listActivityByGroup = $listActivities->where('groupid', $group->id);
                $listActivityByGroupId = $listActivities->pluck('id')->toArray();
                $listActivitiesDefByGroup = $listActivitiesDef->where('groupid', $group->id)->sortBy('course_slot');
                // if (count($listActivitiesDefByGroup) != 3) {
                //     return array('danger', "Không tìm thấy 3 buổi bảo vệ của lớp $group->group_name($group->id)");
                // }
                
                $res[$group->id]['course'] = array_values($listActivitiesDefByGroup->toArray());
                // $res[$group->id]['group'] = $group;
                $res[$group->id]['group_id'] = $group->id;
                $res[$group->id]['list_no_course'] = [];

                
            
                // lấy danh sách điểm theo lớp
                $listStudentGrades = CourseGrade::select([
                    'id',
                    'val', 
                    'grade_id',
                    'groupid',
                    'login',
                    'grade_group_id'
                ])
                // ->whereIn('groupid', $groups->pluck('id')->toArray())
                ->where('groupid', $group->id)
                ->orderBy('grade_id')
                ->get();
                
                $listAllAttendance = Attendance::select([
                    'user_login',
                    DB::raw('count(attendance.id) as total_att')
                ])
                // ->whereIn('groupid', $groups->pluck('id')->toArray())
                ->whereIn('activity_id', $listActivityByGroupId)
                ->where('groupid', $group->id)
                ->groupBy('user_login')
                ->pluck('total_att', 'user_login')->toArray();

                $listWarning = [];
                foreach ($members as $member) {
                    $listMsgFail = [];
                    $memberStatus = 0;
                    // $courseResult = $courseResults->where('student_login', $member->member_login)->first();
                    $courseResult = CourseResult::select([
                        'id',
                        'student_login',
                        'groupid',
                        'val'
                    ])->where('student_login', $member->member_login)
                    ->where('groupid', $group->id)
                    ->first();
                    
                    if (!$courseResult) {
                        $listWarning[$group->id][] = ('Không thể xuất file excel. Vui lòng kiểm tra lại "Lịch sử học" sinh viên ' . $member->member_login . " lớp $group->group_name($group->id)!");
                        continue;
                    }

                    $member->group_name = $group->group_name;
                    if ($courseResult->val == -1) {
                        $member->status = 1;
                        $member->reason_fail = "Cấm thi do trượt điểm danh";
                        $res[$group->id]['list_fail'][] = $member->toArray();
                        continue;
                    }

                    // kiểm tra sinh viên đầy đủ buổi điểm danh chưa
                    if (!isset($listAllAttendance[$member->member_login])) {
                        $listWarning[$group->id][] = ('Không thể xuất file excel. Vui lòng điểm danh tất cả các buổi học cho sinh viên ' . $member->member_login . " lớp $group->group_name($group->id)!");
                        continue;
                    }
                    
                    $studentAttendance = $listAllAttendance[$member->member_login];
                    if (count($listActivityByGroup) != $studentAttendance) {
                        $listWarning[$group->id][] = ('Không thể xuất file excel. Vui lòng điểm danh tất cả các buổi học cho sinh viên ' . $member->member_login . " lớp $group->group_name($group->id)!");
                        continue;
                    } 

                    // Duyệt nhóm đầu điểm
                    foreach ($listGradeGroupCheck as $GradeGroup) {
                        //lấy ds nhóm đầu điểm của nhóm đầu điểm
                        $listGradeByGroup = $GradeGroup->grades;
                        //lấy tất cả điểm quá trình sinh viên đạt được
                        $listStudentGrade = $listStudentGrades
                        // ->where('groupid', $group->id)
                        ->where('login', $member->member_login)
                        ->where('grade_group_id', $GradeGroup->id);
                        
                        $listStudentGradeArr = $listStudentGrade->pluck('val', 'grade_id');
                        $groupGradeTotalPoint = 0;
                        // Kiểm tra nhóm điểm
                        foreach ($listGradeByGroup as $item) {
                            if (isset($listStudentGradeArr[$item->id])) {
                                if ($listStudentGradeArr[$item->id] < $item->minimum_required) {
                                    $listMsgFail[] = "Đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]: " . $listStudentGradeArr[$item->id] . " < " . $item->minimum_required;
                                }
    
                                //tính điểm theo nhóm cho sv
                                if ($GradeGroup->weight > 0) {
                                    $qt_detail_score = ($item->weight / $GradeGroup->weight) * $listStudentGradeArr[$item->id];
                                    $groupGradeTotalPoint += $qt_detail_score;
                                }
                            } else {
                                $listMsgFail[] = "Thiếu đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]";
                            }
                        }
    
                        // Làm tròn nhóm đầu điểm để xét điều kiện
                        $groupGradeTotalPointCheck = round($groupGradeTotalPoint, 1);
                        if ($groupGradeTotalPointCheck < $GradeGroup->minimum_required) {
                            $listMsgFail[] = "Nhóm điểm [$GradeGroup->grade_group_name]: " . $groupGradeTotalPointCheck . " < " . $GradeGroup->minimum_required . " - Chưa làm tròn: " . $groupGradeTotalPointCheck;
                        }
                    }
                    
                    // kiểm tra xem 
                    if (count($listMsgFail) > 0) {
                        $member->status = 2;
                        $member->reason_fail = "Cấm thi do điểm thành phần: " . implode(", ", $listMsgFail);
                        $res[$group->id]['list_fail'][] = $member->toArray();
                        continue;
                    } else {
                        $member->reason_fail = "";
                    }

                    $member->status = $memberStatus;
                }

                if (count($listWarning) > 0) Log::channel('exam_schedule')->warning($listWarning);
                unset($listActivityByGroup);
                unset($listActivitiesDefByGroup);
                unset($listStudentGrades);

                foreach ($res as $key => $groupCheck) {
                    $groupErr = $groupCheck['group_id'];
                    GroupEOS::where('group_id', $groupCheck['group_id'])
                        ->where('status', '!=', 0)
                        ->update([
                            'status' => 0
                        ]);
                    GroupEOS::where('group_id', $groupCheck['group_id'])
                        ->where('course_session', 1)
                        ->update([
                            'activity_id' => $res[$groupCheck['group_id']]['course'][0]['id'] ?? ""
                        ]);
                    GroupEOS::where('group_id', $groupCheck['group_id'])
                        ->where('course_session', 2)
                        ->update([
                            'activity_id' => $res[$groupCheck['group_id']]['course'][1]['id'] ?? ""
                        ]);
                    GroupEOS::where('group_id', $groupCheck['group_id'])
                        ->where('course_session', 3)
                        ->update([
                            'activity_id' => $res[$groupCheck['group_id']]['course'][2]['id'] ?? ""
                        ]);

                    if (count(['list_fail']) == 0) {
                        continue;
                    }
                    
                    // Cập nhập từng sinh viên
                    foreach ($groupCheck['list_fail'] ?? [] as $member) {
                        GroupEOS::where('group_id', $member['groupid'])
                            ->where('student_login', $member['member_login'])
                            ->update([
                                'status' => $member['status'],
                                'reason_fail' => $member['reason_fail'],
                            ]);
                    }
                }
            }
            
            DB::commit();
            return true;
        } catch (Exception $th) {
            DB::rollBack();
            Log::error("---------dev check: Lỗi đồng bộ danh sách thi ------------");
            Log::error([$termId, $courseId, $groupErr, $listGroupId]);
            Log::error($th);
            return array('danger', 'Có lỗi xảy ra. vui lòng thử lại');
        }
    }
}