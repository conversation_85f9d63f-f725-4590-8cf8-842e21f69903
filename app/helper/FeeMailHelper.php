<?php

namespace App\helper;

use App\Models\Fee\Fee;
use App\Models\Fee\FeeLog;
use App\Models\Fee\Transaction;
use App\Models\Fee\FeeMail;
use App\Models\Fee\FeeDetail;
use App\Models\Fu\Term;
use App\Models\EnglishDetail;
use App\Models\EnglishDetailLevel;
use App\Models\Fee\FeeMailLog;
use App\Models\Fee\Plan;
use App\Models\T7\CourseResult;
use App\Models\TranferT7Course;
use App\Models\Fu\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class FeeMailHelper {
    /**
     * Xuất dữ liệu phí theo dữ liệu truyền vào
     * <AUTHOR>
     * @since 05/08/2022
     * @version 1.0
     * @param Fee $fee Object thông tin phí của sinh viên
     * @param String $capusCode Mã cơ sở
     * @param Term $termCheck Mã cơ sở
     */
    public static function getFeeByOption($termId, $listInitData)
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', -1);
        $listStatusEnglish = [
            -4 => 'Thi lại',
            -3 => 'Chưa đạt',
            -2 => 'Đang học',
            -1 => 'Trượt điểm danh',
            0 => 'Chưa học',
            1 => 'Đạt',
            2 => 'Miễn Giảm'
        ];

        $reposts = [];
        $termCheck = Term::find($termId);
        if (!$termCheck) {
            $termCheck = Term::orderBy('id', 'DESC')->first();
        }
        
        if ($termCheck->is_locked == 1) {
            $reposts[] = [
                'status' => false,
                'msg' => "Kì học đã bị khóa"
            ];

            return $reposts;
        }
        
        $termGetData = Term::where('id', '<', $termCheck->id)->orderBy('ordering', 'DESC')->first();
        // Khởi tạo dữ liệu
        $dataJsonArr = [];
        foreach ($listInitData as $keyInit => $dataInit) {
            if ($keyInit == 0) {
                continue;
            } elseif ($dataInit[0] == null || $dataInit[1] == null) {
                $reposts[] = [
                    'status' => false,
                    'msg' => "Dòng $keyInit với dữ liệu là ($dataInit[0]) - ($dataInit[1])  không hợp lệ"
                ];
                continue;
            }


            $userInit = User::select([
                'user.user_code',
                'curriculum.brand_code',
            ])->join('curriculum', 'curriculum.id', '=', 'user.curriculum_id')
            ->where('user_code', $dataInit[0])
            ->first();
            $dataJsonArr[] = [
                'student_code' => $dataInit[0],
                'id' => $termCheck->id,
                'name' => $termCheck->term_name,
                'major_id' => 1,
                'code' => $userInit->brand_code,
                'status' => 'DH',
                'semester' => $dataInit[1],
            ];

        }

        $feeCount = count($dataJsonArr);
        $listUser = [];

        // echo "\nTotal student: $feeCount\n";
        $limit = 500;
        $numFee = ceil($feeCount / $limit);
        $listMajorTravel = [
            'HDDL',
            'HDDL-T',
            'HDDL01',
            'QTNH-T',
            'QTNH01',
            'QTNH',
            'QTKS',
            'QTKS01',
            'QTKS-T',
        ];

        // Danh sách sinh viên đăng ký vượt kỳ học
        $listPassSemester = [];
        $listPassSemester = FeeMail::select('user_code')
        ->where('term_id',  $termGetData->id)
        ->where('vuot_ky', 1)
        ->pluck('user_code')
        ->toArray();

        $feePlans = Plan::with('details:fee_plan_id,ki_thu,type_fee,amount')
        ->select('id', 'curriculum_id', 'brand_code')
        ->get()
        ->toArray();
        
        for ($i = 0; $i < $numFee * $limit; $i += $limit) {
            // dump($i);
            $dataProcess1 = array_slice($dataJsonArr, $i, $limit);
            $arrUserLogin = array_column($dataProcess1, 'student_code');
            $dataProcess = [];
            foreach ($dataProcess1 as $kx => $vx) {
                $dataProcess[$vx['student_code']] = $vx;
            }

            // echo "[". time()  ."] load $i:" . round(($i / $feeCount) * 100 , 2) . "% \n";
            $fees = Fee::select('user_code', 'user_login', 'brand_code', 'study_wallet', 'relearn_wallet', 'promotion_wallet', 'ki_thu', 'id')
                ->whereIn('user_code', $arrUserLogin)
                ->where('study_status', '!=', 8)
                ;
            
            if (count($listUser) > 0) {
                $fees = $fees->whereIn('user_code', $listUser);
            }
            
            $fees = $fees
                // ->skip($i)
                // ->limit($limit)
                ->get();

            foreach ($fees as $fee) {
                $userData = array_filter($dataProcess1, function ($a) use ($fee) {
                    return $a['student_code'] ==  $fee->user_code; 
                });

                $userData = array_values($userData);
                if (count($userData) == 0) {
                    $reposts[] = [
                        'status' => false,
                        'msg' => "Không thấy bản ghi phí $fee->user_code"
                    ];
                    continue;
                }

                $userData = $userData[0];
                $tieng_anh = 0;
                $englishStatus = null;
                $type_fee = [1,2,5];
                $version = 0;
                $english_level = 0;
                $note = null;
    
                $checkFeeMail = FeeMail::where('user_login', $fee->user_login)
                ->where('term_id', $termCheck->id)
                ->count();
                // nếu đã có mail
                if ($checkFeeMail > 0) {
                    $reposts[] = [
                        'status' => false,
                        'msg' => "Đã phát sinh mail cho sinh viên $fee->user_code"
                    ];
                    continue;
                }

                $user = User::select([
                        'id', 
                        'user_code', 
                        'user_login', 
                        'study_status', 
                        'kithu', 
                        'brand_code',
                        'curriculum_id'
                    ])
                    ->where('user_code', $fee->user_code)
                    ->first();
                if (!$user) {
                    $reposts[] = [
                        'status' => false,
                        'msg' => "Không tồn tại sinh viên $fee->user_code"
                    ];
                    echo "$fee->user_code \n";
                    continue;
                }
    
                if ($user->study_status == 8 || $user->study_status == 4) {
                    continue;
                }

                $ki_thu = [$userData['semester'] + 1];
                if (in_array($user->user_code, $listPassSemester)) {
                    $ki_thu[] = $userData['semester'] + 2;
                }
                
                $lastEngCheck = CourseResult::select([
                    't7_course_result.id', 
                    't7_course_result.pterm_name', 
                    't7_course_result.psubject_code', 
                    't7_course_result.skill_code', 
                    't7_course_result.term_id'
                ])->join('list_group', 'list_group.id', '=', 't7_course_result.groupid')
                ->where('student_login', $user->user_login)
                ->where('list_group.is_virtual', 0)
                ->whereIn('t7_course_result.skill_code', ['ENT111', 'ENT121', 'ENT211', 'ENT221'])
                ->orderBy('t7_course_result.term_id', 'DESC')
                ->orderBy('t7_course_result.id', 'DESC')
                ->first();

                if (!$lastEngCheck) {
                    $lastEngCheck = TranferT7Course::select([
                        'tranfer_t7_course.id', 
                        'tranfer_t7_course.pterm_name', 
                        'tranfer_t7_course.psubject_code', 
                        'tranfer_t7_course.skill_code', 
                        DB::raw('term.id as term_id')
                    ])->join('term', 'term.term_name', '=', 'tranfer_t7_course.pterm_name')
                    ->where('student_login', $user->user_login)
                    ->whereIn('tranfer_t7_course.skill_code', ['ENT111', 'ENT121', 'ENT211', 'ENT221'])
                    ->orderBy('term.id', 'DESC')
                    ->orderBy('tranfer_t7_course.id', 'DESC')
                    ->first();
                }

                $english = EnglishDetail::select('id')->where('user_code', $fee->user_code)->first();
                $last_english = null;
                if ($english) {
                    $last_english = EnglishDetailLevel::select('level', 'status', 'version', 'note', 'skill_code')
                    ->where('english_id', $english->id);
                    
                    $last_english = $last_english->orderBy('level', 'DESC')
                    ->orderBy('id', 'DESC')
                    ->first();
                    
                    if ($last_english) {
                        $tieng_anh = ($last_english->level != 4 ? ($last_english->status == 1 ? 2600000 : 500000) : 0);
                        $englishStatus = $last_english->status;
                        $version = $last_english->version + $version;
                        $note = $note . $last_english->note;
                        $english_level = $last_english->level;
                    }
                }
    
                $fdetail = FeeDetail::select([
                        'type_fee',
                        'amount',
                        'status'
                    ])
                    ->whereIn('ki_thu', $ki_thu)
                    ->whereIn('type_fee', $type_fee)
                    ->where('fee_id', $fee->id)
                    ->get();
                $feeNeedCheck = array_values(array_filter($feePlans, function ($a) use ($fee) {
                    return ($a['brand_code'] == $fee->brand_code);
                }));
                    
                $totalFee = [
                    'hoc_ky' => 0,
                    'tien_sach' => 0,
                    'tieng_anh' => null
                ];

                if ($ki_thu[0] > 1) {
                    foreach ($fdetail as $key => $value) {
                        if ($value->type_fee == 1) {
                            $totalFee['hoc_ky'] += $value->amount;
                        } elseif ($value->type_fee == 2) {
                            $totalFee['tien_sach'] += $value->amount;
                        }
                    }
                    
                    $checkPricedEng = Transaction::select('amount')
                    ->where('type', 'HP')
                    ->where('user_code', $fee->user_code)
                    // ->where(function ($query) {
                    //     $query->where('note', 'like','%Tiếng anh học đi%');
                    //     $query->orWhere('note', 'like','%Trượt điểm danh Học lại%');
                    // })
                    ->where('type_extension', 'Like', 'Tiếng anh|%')
                    ->where('term_name', $termCheck->term_name)->first();
                    
                    // "Tiếng anh học đi"
                    if (isset($checkPricedEng)) {
                        $totalFee['tieng_anh'] = $checkPricedEng->amount;
                    }
        
                    if (!isset($feeNeedCheck[0])) {
                        echo "Cần check $fee->user_code \n";
                        continue;
                    }

                    $feeNeedCheck = $feeNeedCheck[0];
                    if ($fee->details->count()) {
                        $listFeeDetail = $feeNeedCheck['details'];
                        $listFeeDetail = array_values(array_filter($listFeeDetail, function ($a) use ($user, $ki_thu) {
                            // return ($a['ki_thu'] == ($user->kithu + 1));
                            return in_array($a['ki_thu'], $ki_thu);
                        }));
                
                        $hoc_ky = 0;
                        $tien_sach = 0;
                        // phí sách ngành hẹp
                        foreach ($listFeeDetail as $item) {
                            if ($item['type_fee'] == 1) {
                                if (in_array($user->user_code, ['PF15011', 'PF15013', 'PF15005', 'PF15019', 'PF15035', 'PF15032', 'PF15050', 'PF15028', 'PF15030', 'PF15051', 'PF15001', 'PF15029', 'PF15098', 'PF15086', 'PF15094', 'PF15115', 'PF15102', 'PF15055', 'PF15149', 'PF15142', 'PF15166', 'PF15056'])) {
                                    $hoc_ky += $user->brand_code == 'MA' ? 3400000 : 3600000;
                                } else {
                                    $hoc_ky += $item['amount'];
                                }
                            }
        
                            if ($item['type_fee'] == 2) {
                                $tien_sach += $item['amount'];
                            }
                        }
        
                        $totalFee = [
                            'hoc_ky' => $hoc_ky,
                            'tien_sach' => $tien_sach,
                            'tieng_anh' => $tieng_anh
                        ];
                    }
        
                    // phí tiếng anh
                    $fee_end = 0;
                    if ($english_level < 4) {
                        $fee_end = 2600000;
                        if ($last_english == null || $englishStatus == '-1') {
                            $fee_end = 2600000;
                        }
                        
                        if (in_array($fee->brand_code, $listMajorTravel)) {
                            if ($english_level < 2) {
                                $fee_end = 0;
                                if (in_array($englishStatus, [-1, -3, -2, -4])) {
                                    $fee_end = 2600000;
                                }
        
                                if ($last_english == null) { 
                                    $fee_end = 2600000;
                                }
                            } else {
                                $fee_end = 0;
                            }
                        } else {
                            if ($englishStatus == 2) {
                                if ($english_level < 4) {
                                    $fee_end = 2600000;
                                    if ($english_level == 2 && in_array($fee->brand_code, $listMajorTravel)) {
                                        $fee_end = 0;
                                    } 
                                }
                            }
                        }
        
                        if (in_array($user->user_code, ['PF15011', 'PF15013', 'PF15005', 'PF15019', 'PF15035', 'PF15032', 'PF15050', 'PF15028', 'PF15030', 'PF15051', 'PF15001', 'PF15029', 'PF15098', 'PF15086', 'PF15094', 'PF15115', 'PF15102', 'PF15055', 'PF15149', 'PF15142', 'PF15166', 'PF15056'])) {
                            // $hoc_ky += $user->user_code == 'MA' ? 3400000 : 3600000; 
                            if ($englishStatus != '1') {
                                $fee_end = 2600000;
                            } else {
                                $fee_end = 0;
                            }
                        }
        
                    } else {
                        $fee_end = 0;
                    }
        
                    if (!$checkPricedEng) {
                        $totalFee['tieng_anh'] = $fee_end;
                    }
                    
                    // Miễn giảm 2 lv đầu
                    if (!$lastEngCheck) {
                        $totalFee['tieng_anh'] = 2600000;
                    }

                    if ($englishStatus == 2 && !in_array($fee->brand_code, $listMajorTravel)) {
                        if ($english_level < 4) {
                            $totalFee['tieng_anh'] = 2600000;
                        }
                    }
                } else {
                    $lastEngCheck = null;
                    $totalFee = [
                        'hoc_ky' => 0,
                        'tien_sach' => 0,
                        'tieng_anh' => null
                    ];
                }

                $feeNeed = ($totalFee['hoc_ky'] + $totalFee['tien_sach'] + $totalFee['tieng_anh']) - $fee->study_wallet;
                $feeNeed = $feeNeed < 0 ? 0 : $feeNeed;
                FeeMail::create([
                    'user_login' => $user->user_login,
                    'user_code' => $fee->user_code,
                    'term_id' => $termCheck->id,
                    'term_name' => $termCheck->term_name,
                    'brand_code' => $fee->brand_code,
                    'ki_thu' => ($ki_thu[1] ?? $ki_thu[0]),
                    'amount' => $feeNeed,
                    'hoc_ky' => $totalFee['hoc_ky'],
                    'tien_sach' => $totalFee['tien_sach'],
                    'tieng_anh' => ($totalFee['tieng_anh'] ?? 0),
                    'version' => 0,
                    'status' => 0,
                    'status_fee' => 0,
                    'english_level' => $english_level,
                    'note' => '',
                    'study_status' => $user->study_status,
                    'type_student' => FeeMail::TYPE_STUDENT_NHTL
                ]);
        
                $reposts[] = [
                    'status' => true,
                    'msg' => "Tạo thành công bản dự thu học phí cho sinh viên $fee->user_code Ngày $fee->brand_code KỲ thứ " . ($ki_thu[1] ?? $ki_thu[0])
                ];
            }
        }

        return $reposts;
    }
}