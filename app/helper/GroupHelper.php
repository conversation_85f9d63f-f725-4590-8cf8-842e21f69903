<?php

namespace App\helper;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;


use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use App\Models\Fu\Room;
use App\Models\Fu\Activity;

class GroupHelper {

    protected $otherCondition = [
        'SKI1014'   =>  ['max' => 9, 'min' => 9],
        'ACC105'   => ['max' => -10, 'min' => 0],
        'AUT102'   => ['max' => -10, 'min' => 0],
        'AUT103'   => ['max' => -10, 'min' => 0],
        'AUT104'   => ['max' => -10, 'min' => 0],
        'AUT105'   => ['max' => -10, 'min' => 0],
        'AUT106'   => ['max' => -10, 'min' => 0],
        'AUT107'   => ['max' => -10, 'min' => 0],
        'AUT108'   => ['max' => -10, 'min' => 0],
        'AUT109'   => ['max' => -10, 'min' => 0],
        'AUT110'   => ['max' => -10, 'min' => 0],
        'AUT206'   => ['max' => -10, 'min' => 0],
        'EHO102'   => ['max' => -10, 'min' => 0],
        'EHO202'   => ['max' => -10, 'min' => 0],
        'ENT1125'   => ['max' => -10, 'min' => 0],
        'ENT1126'   => ['max' => -10, 'min' => 0],
        'ENT1225'   => ['max' => -10, 'min' => 0],
        'ENT2125'   => ['max' => -10, 'min' => 0],
        'ENT2225'   => ['max' => -10, 'min' => 0],
        'ETO101'   => ['max' => -10, 'min' => 0],
        'ETO201'   => ['max' => -10, 'min' => 0],
        'HIS101'   => ['max' => -10, 'min' => 0],
        'HIS102'   => ['max' => -10, 'min' => 0],
        'HOS1011'   => ['max' => -10, 'min' => 0],
        'HOS1021'   => ['max' => -10, 'min' => 0],
        'HOS1031'   => ['max' => -10, 'min' => 0],
        'HOS1041'   => ['max' => -10, 'min' => 0],
        'HOS105'   => ['max' => -10, 'min' => 0],
        'HOS2011'   => ['max' => -10, 'min' => 0],
        'HOS2021'   => ['max' => -10, 'min' => 0],
        'HOS305'   => ['max' => -10, 'min' => 0],
        'HOS401'   => ['max' => -10, 'min' => 0],
        'HOS402'   => ['max' => -10, 'min' => 0],
        'HOS4031'   => ['max' => -10, 'min' => 0],
        'INE101'   => ['max' => -10, 'min' => 0],
        'INE102'   => ['max' => -10, 'min' => 0],
        'INE202'   => ['max' => -10, 'min' => 0],
        'INE203'   => ['max' => -10, 'min' => 0],
        'INE214'   => ['max' => -10, 'min' => 0],
        'MEC105'   => ['max' => -10, 'min' => 0],
        'MEC111'   => ['max' => -10, 'min' => 0],
        'MEC114'   => ['max' => -10, 'min' => 0],
        'MEC119'   => ['max' => -10, 'min' => 0],
        'MEC201'   => ['max' => -10, 'min' => 0],
        'MEC2021'   => ['max' => -10, 'min' => 0],
        'PRO1051'   => ['max' => -10, 'min' => 0],
        'PRO1081'   => ['max' => -10, 'min' => 0],
        'PRO125'   => ['max' => -10, 'min' => 0],
        'PRO127'   => ['max' => -10, 'min' => 0],
        'PRO1291'   => ['max' => -10, 'min' => 0],
        'PRO132'   => ['max' => -10, 'min' => 0],
        'PSY1011'   => ['max' => -10, 'min' => 0],
        'TOU1011'   => ['max' => -10, 'min' => 0],
        'TOU1021'   => ['max' => -10, 'min' => 0],
        'TOU106'   => ['max' => -10, 'min' => 0],
        'TOU2013'   => ['max' => -10, 'min' => 0],
        'TOU2021'   => ['max' => -10, 'min' => 0],
        'TOU2031'   => ['max' => -10, 'min' => 0],
        'TOU2041'   => ['max' => -10, 'min' => 0],
        'TOU3011'   => ['max' => -10, 'min' => 0],
        'TOU302'   => ['max' => -10, 'min' => 0],
        'VIE1016'   => ['max' => 60, 'min' => 55],
        'VIE1026'   => ['max' => 60, 'min' => 55],
    ];

    /**
     * quét phí tiếng anh
     * 
     * <AUTHOR>
     * @since 07/09/2022
     * @version 1.0
     * @param Int $groupId id của lớp
     */
    static public function getInfoGroup($groupId)
    {
        $nowStr = now()->format('d-m-Y');
        if (!Cache::has("$nowStr-get-info-group-$groupId")) {
            $group = Group::select([
                'id',
                'group_name',
                'psubject_code',
                'slot',
                'room_id',
                'start_date',
                DB::raw("DATE_FORMAT(start_date, '%d / %m / %Y') as format_start_date")
            ])
            ->where('id', $groupId)
            ->first();
    
            // Cập nhập một số thông tin cơ bản
            $slotData = collect(DB::connection()
                ->select("SELECT 
                    slot, 
                    room_id, 
                    leader_login, 
                    COUNT(id) 
                FROM activity 
                LEFT JOIN session_type ON activity.session_type = session_type.id
                WHERE groupid = $group->id
                    AND slot > 0
                    AND session_type.is_exam = 0
                GROUP BY slot
                ORDER BY COUNT(id) DESC"))->first();
                
            if ($slotData != null) {
                // kiểm tra
                if ($group->slot == 0) {
                    $group->slot = $slotData->slot ?? 0;
                }
                
                // Kiểm tra phòng
                if ($group->room_id != $slotData->room_id) {
                    $group->room_id = $slotData->room_id ?? $group->room_id;
                }
                
                // kiểm tra giảng viên
                if ($group->teacher == '' || $group->teacher == null) {
                    $group->teacher = $slotData->leader_login ?? $group->teacher;
                }
    
                $group->save();
            }
            
            // lấy danh sách ngày học
            $activityData = collect(DB::connection()
                ->select("SELECT 
                    SUM( CASE WHEN (activity.DAY <= CURRENT_DATE) THEN 1 END) AS 'total_leared', 
                    COUNT(*) as 'total'
                FROM activity 
                LEFT JOIN session_type ON activity.session_type = session_type.id
                WHERE groupid = '$group->id'
                    AND session_type.is_exam = 0"))->first();
            
            // lấy thông tin phòng
            $room = Room::find($slotData->room_id, [        
                'id',
                'area_id',
                'room_name',
            ]);

            $dataGroup = [
                'id' => $group->id,
                'group_name' => $group->group_name,
                'psubject_code' => $group->psubject_code,
                'slot' => $group->slot,
                'room_id' => $group->room_id,
                'room_name' => $room->room_name,
                'start_date' => $group->start_date,
                'format_start_date' => $group->format_start_date,
                'total_leared' => $activityData->total_leared ?? 0,
                'total_activity' => $activityData->total,
            ];
            
            Cache::put("$nowStr-get-info-group-$groupId", $dataGroup, 86400);
        }
        
        $res = Cache::get("$nowStr-get-info-group-$groupId");
        $total = GroupMember::where('groupid', $groupId)->count();
        $res['total_nember'] = $total;
        // lấy thông tin lớp cần có 
        return $res;
    }

    /**
     * Hàm thêm cache khi đổi lớp
     * 
     * <AUTHOR>
     * @since 07/09/2022
     * @version 1.0
     * @param Array[Int] $listGroupId Mã cơ sở
     * @param Boolean oneArray gộp toàn bộ vào 1 mảng hay không
     */
    static public function getListActivity($listGroupId = [], $oneArray = false)
    {
        $res = (new Activity)->newCollection();
        foreach ($listGroupId as $groupId) {
            if (!Cache::has("activity-check-$groupId")) {
                $listObjActiviesCheck = Activity::query()
                ->select('id', 'groupid', 'day', 'psubject_code', 'psubject_name', 'groupid', 'room_name', 'room_id', 'group_name', 'slot')
                ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
                ->where('slot', '>', 0)
                ->where('groupid', $groupId)
                ->where('session_type.is_exam', 0)
                ->orderBy('course_slot')
                ->get();
                
                Cache::put("activity-check-$groupId", $listObjActiviesCheck, 86400);
            }

            $listObjActiviesCheck = Cache::get("activity-check-$groupId");

            if ($oneArray == true) {
                $res = $res->merge($listObjActiviesCheck);
            } else {
                $res->push($listObjActiviesCheck);
            }
        }
        
        return $res;
    }

    /**
     * 
     * Khởi tạo dữ liệu
     * 
     * <AUTHOR>
     * @param Term $term Dữ liệu kỳ cần xử lý
     * @param Integer $currentGroupid ID lớp hiện tại của sinh viên
     * @param User $user Dữ liệu sinh viên
     * @param String $subjectCode mã môn cần join
     * @param Group $groupNeedJoin lớp cần join
     */
    static public function getListClassJoin($term, $currentGroupid, $user, $subjectCode, $groupNeedJoin)
    {
        // Lấy danh sách lớp theo kỳ hiện tại (bỏ môn cần của lớp cần join)
        $listCurrentGroup = Group::select('list_group.id')
        ->leftJoin('group_member', 'group_member.groupid', '=', 'list_group.id')
        ->where('pterm_id', $term->id)
        ->where('group_member.member_login', $user->user_login)
        ->where('list_group.psubject_code', '!=', $subjectCode)
        ->where('list_group.is_virtual', 0)
        ->get();
        
            
        // Lấy lịch của lớp cần join
        if (!Cache::has("activity-check-$groupNeedJoin->id")) {
            $listObjActiviesNeedJoin = Activity::query()
            ->select('id', 'groupid', 'day', 'psubject_code', 'psubject_name', 'groupid', 'room_name', 'room_id', 'group_name', 'slot')
            ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
            ->where('groupid', $groupNeedJoin->id)
            ->where('session_type.is_exam', 0)
            ->where('slot', '>', 0)
            ->orderBy('course_slot')
            ->get();
            
            Cache::put("activity-check-$groupNeedJoin->id", $listObjActiviesNeedJoin, 86400);
        }

        $listObjActiviesNeedJoin = Cache::get("activity-check-$groupNeedJoin->id");

        // Lấy lịch của hiện tại của sinh viên
        $listObjActiviesCurrent = GroupHelper::getListActivity($listCurrentGroup->pluck('id'), true);
        if (count($listObjActiviesCurrent) == 0) {
            return [
                'status' => true,
                'current_group_id' => $currentGroupid,
                'group_check' => "$groupNeedJoin->group_name ($groupNeedJoin->id)",
                'msg' => "Có thể chuyển vào lớp $groupNeedJoin->group_name ($groupNeedJoin->id)",
            ];
        }

        $listActiviesCompare = call_user_func(function () use ($listObjActiviesCurrent) {
            $res = [];
            foreach ($listObjActiviesCurrent as $value) {
                $res[$value->day . '-' . $value->slot] = [
                    'groupid' => $value->groupid,
                    'group_name' => $value->group_name,
                    'psubject_code' => $value->psubject_code,
                ];
            }

            return $res;
        });
        
        // check trùng lịch học
        foreach ($listObjActiviesNeedJoin as $key => $value) {
            if (isset($listActiviesCompare[$value->day . '-' . $value->slot])) {
                $classFail = $listActiviesCompare[$value->day . '-' . $value->slot];
                $msg = "Trùng lịch học với Lớp <b>". $classFail['group_name'] ." (" . $classFail['psubject_code'] . ")</b> Ngày <b>$value->day</b> ca <b>$value->slot</b>";
                return [
                    'status' => false,
                    'current_group_id' => $currentGroupid,
                    'group_check' => "$groupNeedJoin->group_name ($groupNeedJoin->id)",
                    'msg' => $msg,
                ];
            }
        }

        return [
            'status' => true,
            'current_group_id' => $currentGroupid,
            'group_check' => "$groupNeedJoin->group_name ($groupNeedJoin->id)",
            'msg' => "Có thể chuyển vào lớp $groupNeedJoin->group_name ($groupNeedJoin->id)",
        ];
    }
}