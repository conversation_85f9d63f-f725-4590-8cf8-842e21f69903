<?php

namespace App\helper;

use Illuminate\Support\Facades\DB;

use App\Models\Fu\Term;
use App\Models\Fu\Activity;
use App\Models\Fu\Feedback;
use App\Models\Fu\FeedbackDetail;
use Illuminate\Support\Facades\Log;

/**
    /* "Hệ thống tự động mở lấy GPA cho tất cả các lớp theo nguyên tắc:
    /* - Với môn học trong 1 Block:
    /* + Bắt đầu mở từ tuần thứ 4 của Block.
    /* + <PERSON>éo dài 11 ngày (từ ngày thứ 22 - 33 của Block).
    /* - Với môn học cả kỳ:
    /* + Bắt đầu mở từ tuần thứ 6 của Kỳ.
    /* + <PERSON>éo dài 11 ngày (từ ngày thứ 43 - 54 của Kỳ)."
 */
class FeedbackHelper {
    
    // danh sách code môn tiếng anh
    protected $listSkillCodeEnglish = [
        'ENT111', 'ENT121', 'ENT211', 'ENT221'
    ];
    
    /**
     * <AUTHOR>
     * l<PERSON>y danh sách các lớp chưa tạo feedback học xuyên 2 block ( tiếng anh )
     */
    public function createFeedbackEnglish($term, $userOpen = 'dev')
    {
        $strNow = now()->format('Y-m-d');
        $dataCreate = Activity::select([
            DB::raw('COUNT( activity.id ) AS done'),
            'activity.groupid',
            'activity.leader_login AS teacher' 
        ])
        ->leftJoin('list_group', 'activity.groupid', '=', 'list_group.id')
        ->whereRaw('list_group.id IN ( SELECT id FROM list_group WHERE pterm_id = ? )', [$term->id])
        ->whereRaw('list_group.id NOT IN ( SELECT groupid FROM feedback WHERE groupID IN (SELECT id FROM list_group WHERE pterm_id = ? ) )' , [$term->id])
        ->where('activity.done', 1)
        ->where('list_group.is_virtual', 0)
        ->groupBy('list_group.id')
        ->orderBy('done', 'DESC')
        ->get();

        // Tạo feedback
        DB::beginTransaction();
        try {
            foreach ($dataCreate as $value) {
                // kiểm tra xem có feedback chưa
                $checkIsset = Feedback::where('groupid', $value->groupid)
                ->count();

                if ($checkIsset == 0) {
                    $newFeedback = new Feedback();
                    $newFeedback->groupID = $value->groupid;
                    $newFeedback->open = 1;
                    $newFeedback->hit = 0;
                    $newFeedback->GPA = 0;
                    $newFeedback->opener_login = $userOpen;
                    $newFeedback->day = $strNow;
                    $newFeedback->open_day = $strNow;
                    $newFeedback->planer_login = $userOpen;
                    $newFeedback->teacher_login = $value->teacher;
                    $newFeedback->save();
                }

                echo "$value->groupid \n";
            }

            DB::commit();
        } catch (\Exception $ex) {
            DB::rollback();
        }
    }

    
    /**
     * <AUTHOR>
     * Tạo feedback cho các lớp 1 block
     */
    public function createFeedback($block, $userOpen = 'dev')
    {
        $strNow = now()->format('Y-m-d');
        $dataCreate = Activity::select([
            DB::raw('COUNT( activity.id ) AS done'),
            'activity.groupid',
            'activity.leader_login AS teacher' 
        ])
        ->leftJoin('list_group', 'activity.groupid', '=', 'list_group.id')
        ->whereRaw('list_group.id IN ( SELECT id FROM list_group WHERE pterm_id = ? )', [$block->term_id])
        ->whereRaw('list_group.id NOT IN ( SELECT groupid FROM feedback WHERE groupid IN (SELECT id FROM list_group WHERE pterm_id = ? ) )', [$block->term_id])
        ->where('activity.done', 1)
        ->where('list_group.is_virtual', 0)
        ->groupBy('list_group.id')
        ->orderBy('done', 'DESC')
        ->get();

        // Tạo feedback
        DB::beginTransaction();
        try {
            foreach ($dataCreate as $value) {
                // kiểm tra xem có feedback chưa
                $checkIsset = Feedback::where('groupid', $value->groupid)
                ->count();

                if ($checkIsset == 0) {
                    $newFeedback = new Feedback();
                    $newFeedback->groupID = $value->groupid;
                    $newFeedback->open = 1;
                    $newFeedback->hit = 0;
                    $newFeedback->GPA = 0;
                    $newFeedback->opener_login = $userOpen;
                    $newFeedback->day = $strNow;
                    $newFeedback->open_day = $strNow;
                    $newFeedback->planer_login = $userOpen;
                    $newFeedback->teacher_login = $value->teacher;
                    $newFeedback->save();
                }

                echo "$value->groupid \n";
            }

            DB::commit();
        } catch (\Exception $ex) {
            DB::rollback();
        }
    }

    
    /**
     * <AUTHOR>
     * Tạo feedback theo id lớp
     */
    public function createFeedbackByGroupId($term, $groupIds, $userOpen = 'dev')
    {
        $strNow = now()->format('Y-m-d');
        $dataCreate = Activity::select([
            DB::raw('COUNT( activity.id ) AS done'),
            'activity.groupid',
            'activity.leader_login AS teacher' 
        ])
        ->leftJoin('list_group', 'activity.groupid', '=', 'list_group.id')
        ->whereIn('list_group.id', $groupIds)
        ->whereRaw('list_group.id NOT IN ( SELECT groupid FROM feedback WHERE groupID IN (SELECT id FROM list_group WHERE pterm_id = ? ) )' , [$term->id])
        ->where('list_group.is_virtual', 0)
        ->groupBy('list_group.id')
        ->orderBy('done', 'DESC')
        ->get();

        // Tạo feedback
        DB::beginTransaction();
        try {
            foreach ($dataCreate as $value) {
                // kiểm tra xem có feedback chưa
                $checkIsset = Feedback::where('groupid', $value->groupid)
                ->count();

                if ($checkIsset == 0) {
                    $newFeedback = new Feedback();
                    $newFeedback->groupID = $value->groupid;
                    $newFeedback->open = 1;
                    $newFeedback->hit = 0;
                    $newFeedback->GPA = 0;
                    $newFeedback->opener_login = $userOpen;
                    $newFeedback->day = $strNow;
                    $newFeedback->open_day = $strNow;
                    $newFeedback->planer_login = $userOpen;
                    $newFeedback->teacher_login = $value->teacher;
                    $newFeedback->save();
                }

                echo "$value->groupid \n";
            }

            DB::commit();
        } catch (\Exception $ex) {
            DB::rollback();
        }
    }

    
    /**
     * <AUTHOR>
     * Tự động đóng các feedback đã đủ yêu cầu
     * @param Object $term Object thông tin block
     * @param String $capusCode Mã cơ sở
     */
    public function closeFeedback($block)
    {
        $strNow = now()->format('Y-m-d');
        // Khóa toàn bộ feedback sau ngày cuối cùng check
        if ($block->max_day_check == $strNow) {
            // Feedback::update([
            //     'open' => 0
            // ])
            // ->leftJoin('list_group', 'feedback.groupID', '=', 'list_group.id')
            // ->whereRaw('list_group.id IN ( SELECT id FROM list_group WHERE pterm_id = ? )', [$block->term_id])
            // ->whereNotIn('list_group.skill_code', $this->listSubjectDontCreateFeedback)
            // ->where('feedback.open', 1);
            // return;
        }

        // khóa những của môn thường có GPA lớn hơn 0.6
        $dataClose = Feedback::select([
            'open' => 0
        ])
        ->leftJoin('list_group', 'feedback.groupID', '=', 'list_group.id')
        ->leftJoin('group_member', 'feedback.groupID', '=', 'group_member.groupid')
        ->whereRaw('list_group.id IN ( SELECT id FROM list_group WHERE pterm_id = ? )', [$block->term_id])
        ->where('feedback.open', 1)
        ->where('list_group.is_virtual', 0)
        ->groupBy('list_group.id')
        ->havingRaw('(feedback.hit / COUNT(group_member.id)) >= 0.6');
    }


    /**
     * <AUTHOR>
     * Tự động đóng các feedback đã đủ yêu cầu ( tiếng anh )
     * @param Object $term Object thông tin Kỳ
     * @param String $capusCode Mã cơ sở
     */
    public function closeFeedbackEnglish($term)
    {
        $strNow = now()->format('Y-m-d');
        // Khóa toàn bộ feedback sau ngày cuối cùng check
        if ($term->max_day_check == $strNow) {
            Feedback::update([
                'open' => 0
            ])
            ->leftJoin('list_group', 'feedback.groupID', '=', 'list_group.id')
            ->whereRaw('list_group.id IN ( SELECT id FROM list_group WHERE pterm_id = ? )', [$term->id])
            ->whereIn('list_group.skill_code', $this->listSkillCodeEnglish)
            ->where('feedback.open', 1);
            return;
        }
        
        // khóa những của môn tiếng anh có GPA lớn hơn 0.6
        $dataClose = Feedback::select([
            'open' => 0
        ])
        ->leftJoin('list_group', 'feedback.groupID', '=', 'list_group.id')
        ->leftJoin('group_member', 'feedback.groupID', '=', 'group_member.groupid')
        ->whereRaw('list_group.id IN ( SELECT id FROM list_group WHERE pterm_id = ? )', [$term->id])
        ->whereIn('list_group.skill_code', $this->listSkillCodeEnglish)
        ->where('feedback.open', 1)
        ->where('list_group.is_virtual', 0)
        ->groupBy('list_group.id')
        ->havingRaw('(feedback.hit / COUNT(group_member.id)) >= 0.6');
    }


    /**
     * <AUTHOR>
     * Cập nhật thông tin feedback
     * @param Int $termId ID của kỳ
     */
    public function updateFeedback($termId = null)
    {
        try {
            // Lấy thông tin kỳ hiện tại
            $currentTerm = $termId 
            ? Term::find($termId) 
            : Term::whereRaw('startday <= CURRENT_DATE')
                ->whereRaw('endday >= CURRENT_DATE')
                ->first();

            if (!$currentTerm) {
                Log::error('func updateFeedback: Không tìm thấy kỳ hiện tại.');
                return 0; 
            }

            // Lấy danh sách feedback cần cập nhật
            $feedbacks = DB::table('feedback')
            ->leftJoinSub(
                DB::table('feedback_detail')
                    ->selectRaw('feedback.id AS id, COUNT(feedback_detail.feedbackID) AS hit, feedback.groupID, AVG((Q1 + Q2 + Q3 + Q4 + Q5) / 5) AS avg')
                    ->leftJoin('feedback', 'feedback_detail.feedbackID', '=', 'feedback.id')
                    ->groupBy('feedbackID')
                    ->havingRaw('feedback.groupID IN (SELECT id FROM list_group WHERE list_group.pterm_id = ?)', [$currentTerm->id]),
                'cstbl',
                'cstbl.id',
                '=',
                'feedback.id'
            )
            ->select('feedback.id', 'cstbl.avg', 'cstbl.hit')
            ->get();

            // Cập nhật GPA và hit cho từng feedback
            foreach ($feedbacks as $feedback) {
            DB::table('feedback')
                ->where('id', $feedback->id)
                ->update([
                    'GPA' => $feedback->avg,
                    'hit' => $feedback->hit
                ]);
            }
            DB::statement("
                WITH duplicate_ids AS (
                    SELECT MIN(id) AS keep_id 
                    FROM feedback_detail 
                    GROUP BY student_login, feedbackID 
                    HAVING COUNT(*) > 1
                )
                DELETE FROM feedback_detail 
                WHERE id NOT IN (SELECT keep_id FROM duplicate_ids)
            ");
            return 1;
        } catch (\Throwable $th) {
            Log::error($th);
            return 0;
        }
        
    }

}