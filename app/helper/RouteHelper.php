<?php

namespace App\helper;

use App\Models\RelearnOnline;
use App\Models\Fee\Fee;

class RouteHelper
{
    static function checkTypeFee($level, $data, $last_english, $english) {
        $type = 0;
        ${'level_' . $level} = $data;
        if (${'level_' . $level} == 1) {
            $type = 2;
        } else if (${'level_' . $level} == -1 || ${'level_' . $level} == -3) {
            $type = 4;
        } else if (${'level_' . $level} == -4) {
            $type = 5;
        } else if (${'level_' . $level} == 0) {
            if ($last_english->val == 1) {
                $type = 2;
            } elseif ($last_english->val == -1) {
                $type = 4;
            }
            if ($last_english->val == 0) {
                $type = 4;
            }
            $relearn_online = RelearnOnline::where('user_code', $english->user_code)->whereNull('term_name_pass')->where('skill_code', $last_english->skill_code)->count();
            if ($relearn_online > 0)
            {
                $type = 5;
            }
        }
        return $type;
    }

    static function createFee($level, $type, $data, $discount, $english, $level_data) {
        $amount = 0;
        $promotion = 0;
        if ($type == 2 && $level_data != 0) {
            $level += 1;
        }
        if ($level == 1) {
            $subject_code = 'ENT11';
            $subject_name = 'Tiếng anh 1.1';
        } else if ($level == 2) {
            $subject_code = 'ENT12';
            $subject_name = 'Tiếng anh 1.2';
        } else if ($level == 3) {
            $subject_code = 'ENT21';
            $subject_name = 'Tiếng anh 2.1';
        } else if ($level == 4) {
            $subject_code = 'ENT22';
            $subject_name = 'Tiếng anh 2.2';
        } else {
            $subject_code = $level;
            $subject_name = $level;
        }
        if ($type == 2) {
            $amount = 2600000;
            $promotion = 520000;
        } elseif ($type == 4) {
            if ($discount) {
                $amount = 1300000;
                $promotion = 0;
            } else {
                $amount = 2600000;
                $promotion = 520000;
            }
        } elseif ($type == 5) {
            try {
            $relearn_online = RelearnOnline::where('user_code', $data->user_code)->where('skill_code', $english->skill_code)->count();
            } catch (Exception $e) {
                dd($data);
            }
            if ($relearn_online > 0) {
                $amount = 250000 * $relearn_online;
                $promotion = 0;
            } else {
                $amount = 0;
            }
        }
    
        if ($amount > 0) {
            echo "user_login: $data->user_code, $subject_code - $subject_name, amount: $amount, promotion: $promotion";
            $check = Fee::where('user_code', $data->user_code)->whereIn('type_fee', [2,4,5])->where('term_id', 34)->first();
            if (!$check) {
                Fee::insert([
                    'user_login' => $data->user_login,
                    'user_code' => $data->user_code,
                    'curriculum_id' => $data->curriculum_id,
                    'brand_code' => $data->brand_code,
                    'amount' => $amount,
                    'type_fee' => $type,
                    'chot' => '0',
                    'ky' => $data->ky_hien_tai + 1,
                    'term_id' => '34',
                    'subject_code' => $subject_code,
                    'subject_name' => $subject_name,
                ]);
                echo "insert";
                echo "<br>";
            } else {
                $check->type_fee = $type;
                $check->amount = $amount;
                $check->save();
                echo "update";
                echo "<br>";
            }
    //        if ($data->user_code == 'PH09431') {
    //            echo $amount;
    //            echo "<br>";
    //            echo $type;
    //            dd(1);
    //        }
        }
    }
}
