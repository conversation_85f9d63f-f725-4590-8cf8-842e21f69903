<?php
namespace App\TechAPI;

use App\Models\Sms\TelecoNumber;
use App\TechAPI\Contracts\BaseSmsInterface;
use TechAPI\Auth\ClientCredentials;
use TechAPI\Auth\AccessToken;
use TechAPI\Exception as TechException;
use TechAPI\Api\SendBrandnameOtp;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
require_once __DIR__.'/bootstrap.php';

class SmsBase implements BaseSmsInterface {
    private  $authenticated;
    public static $campus = [];
    protected $campusCode;
    protected $brandName;


    /**
     * authenticate
     *
     * @param  mixed $client_id
     * @param  mixed $sceret_code
     * @param  mixed $scope
     * @return \TechAPI\Auth\ClientCredentials
     */
    public static function authenticate(string $client_id, string $sceret_code, array $scope)
    {
        if ($authenticated = getTechAuthorization($client_id, $sceret_code, $scope)) {
            return $authenticated;
        }
        return false;
    }

    /**
     * g<PERSON>i tin nhắn theo brandname, scope = [send_brandname_otp] mặc định theo sdk
     *
     * @param  array $messages ['phone' => '', 'content' => '']
     * @param  string $campus_code 
     * @param  mixed $authenticated = (null default null)
     * @return mixed ['success' => [], 'fail' => []]
     */
    public static function sendBrandNameOtp(array $messages, string $campus_code, ClientCredentials $authenticated = null) {
        try {
            if ($authenticated == null) {
                $client_id = SmsBase::$campus[$campus_code]['id'];
                $sceret = SmsBase::$campus[$campus_code]['secret'];
                $scope = [
                    'send_brandname_otp'
                ];
                $authenticated = SmsBase::authenticate($client_id, $sceret, $scope);
            }
            $brand = SmsBase::$campus[$campus_code]['brand_name'];
            Log::channel('sms')->info('BrandName : ' . $brand);
            foreach ($messages as $key => $message) {
                $messageInstance = [
                    'Phone' => $message['phone'],
                    'BrandName' => $brand,
                    'Message' => $message['content']
                ];

                $messageInstance = new SendBrandnameOtp($messageInstance);
                try {
                    $arrResponse = $authenticated->execute($messageInstance);
                    if (!empty($arrResponse['error'])) {
                        AccessToken::getInstance()->clear();
                        throw new TechException($arrResponse['error_description'], $arrResponse['error']);
                    }
                    $count['success'][] = $message;
                } catch (\Exception $ex) {
                    $count['fail'][] = [
                        $ex->getCode(),
                        $ex->getMessage(),
                        $message
                    ];
                }
                usleep(200000);
            }
            return $count;
        } catch (\Exception $ex) {
            Log::channel('sms')->error("--------------Start Log Sms Function sendBrandNameOtp()-------------");
            Log::channel('sms')->error("Params: " . json_encode([
                "message" => $messages,
            ]));
            Log::channel('sms')->error($ex);
            Log::channel('sms')->error("--------------End Log Sms Function sendBrandNameOtp()-------------");
        }
    }

    /**
     * get mã nhà mạng (viettel, vina, mobifone, vietnamobile, ... )
     *
     * @param  string $phone_number
     * @param  mixed TelecoNumber $list_telecom = (null default null)
     * @return string $telecom_code - mã nhà mạng (return undefined if not found)
     */
    public static function getTelcoCode($phone_number, $list_telecom = null){
        try {
            if ($list_telecom == null) {
                $list_telecom = TelecoNumber::all();
            }
            foreach ($list_telecom as $telecom) {
                if (substr($phone_number, 0, 3) == $telecom->number || substr($phone_number, 0, 4) == $telecom->number) {
                    return $telecom->code;
                }
            }
            return 'undifined';
        } catch (\Throwable $th) {
            Log::channel('sms_base')->error("--------------Start Log Sms Function sendBrandNameOtp()-------------");
            Log::channel('sms_base')->error($th);
            Log::channel('sms_base')->error("--------------End Log Sms Function sendBrandNameOtp()-------------");
            return 'undifined';
        }

    }
}
