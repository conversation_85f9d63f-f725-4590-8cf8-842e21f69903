<?php

namespace App\Console;

use App\Http\Controllers\Admin\TranscriptController;

use App\Models\Fu\Term;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // Gửi tin nhắn hẹn giờ
        // $currentTerm = Term::whereRaw('startday <= CURRENT_DATE')->whereRaw('endday >= CURRENT_DATE')->first();
        // $schedule->command("schedule-exam:sync-eos $currentTerm->id")->everyFourHours();
        // $schedule->command("schedule-exam:sync-assignment $currentTerm->id")->everyFourHours();

        // Chạy quét bảng fee_request cập nhật trạng thái thanh toán dịch vụ trực tuyến (status = 31)
        // $schedule->command("scan-fee-request-status")->everyMinute();
        // Chạy file FUGE
        // $schedule->command("command:process-data-fuge")->daily();
        // tự động điểm danh
        $schedule->command("attendance:auto-attendance-online")->daily();
        // Cập nhập bảng điểm
        $schedule->command("transcript:update")->dailyAt('00:00');

        // $schedule->command("command:resend-confirm-attendance 0")->dailyAt('09:30');

        // $schedule->command("command:resend-confirm-attendance 0")->dailyAt('15:30');

        // $schedule->command("update-data-warning-sms")->dailyAt('00:00');
        $schedule->command('cleanup:old-files')->daily();
        // Tự động tạo feedback + đóng feedback
        $schedule->command("command:feedback-create")->daily();

        $schedule->command("command:feedback-close")->daily();
        // Xóa log của telescope
        $schedule->command('telescope:prune --hours=1140')->daily();

        // $schedule->command("gradebook-export")->weeklyOn(5, '02:00');
        $schedule->command('fee:update-period-student-debt')->everyThirtyMinutes();

        // $schedule->command("command:send-sms")->everyFiveMinutes();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
