<?php

namespace App\Console\Commands;

use App\Http\Controllers\Admin\SystemController;
use App\Models\Fu\User;
use Illuminate\Console\Command;

class UpdateFeeDetail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fee:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Khởi tạo mới và cập nhật dữ liệu phí';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        
        try {
            $countUser = User::select('user.*')
            ->leftJoin('fee_mails', 'user.user_login', '=', 'fee_mails.user_login')
            ->where('fee_mails.term_id', 51)
            ->where('user.user_level', 3)
            ->count();
            $num = ceil($countUser / 1000);
            $this->info("Thực hiện $num lần");
            $per = 0;
            for ($i = 0;$i < $num * 1000;$i+=1000) {
                $per++;
                SystemController::createOrUpdateFee($i, 1000);
                $this->info("Hoàn tất lần $per, skip $i limit 1000");
            }
        } catch (\Exception $e) {
            $this->error('Lỗi english:update');
        }
    }
}
