<?php

namespace App\Console\Commands;

use App\Models\Fu\Term;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AutoUpdateAttendance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:auto-attendance-online';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tự động điểm danh cho sinh viên các buổi online';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Command $this->signature is running...");
        try {
            /* ============ Tự động điểm danh các buổi online ============ */
            // lấy kỳ hiện tại
            $currentTerm = Term::whereRaw('startday <= CURRENT_DATE')
            ->whereRaw('endday >= CURRENT_DATE')
            ->first();

            // Cập nhập điểm danh cho buổi học online
            DB::select("UPDATE `activity` 
            JOIN list_group on list_group.id = activity.groupid
            SET activity.`done` = 1 
            WHERE
                CURRENT_DATE >= activity.`day` 
                AND activity.session_type = 18 
                AND activity.slot > 6
                AND list_group.pterm_id = ?
                AND list_group.is_virtual = 0", [$currentTerm->id]);
            $this->info("Thực hiện điểm danh tự động xong");
        } catch (\Exception $e) {
            $this->error('Lỗi transcript:update');
        }
    }
}
