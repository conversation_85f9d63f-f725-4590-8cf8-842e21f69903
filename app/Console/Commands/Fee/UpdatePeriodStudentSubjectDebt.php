<?php

namespace App\Console\Commands\Fee;

use App\Models\Fee\TuitionPeriodStudent;
use App\Models\Fee\TuitionPeriodSubject;
use Illuminate\Support\Facades\Log;
use App\Models\Fee\TuitionPeriod;
use Illuminate\Console\Command;
use App\Models\Fee\StudentDebt;
use App\Models\Fu\ActionLog;
use Carbon\Carbon;

class UpdatePeriodStudentSubjectDebt extends Command
{
    protected $processed = [];
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tuition-period:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Xử lý đồng bộ trạng thái thanh toán sang đợt thu học phí của học sinh';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */

    public function handle()
    {
        $this->info('Bắt đầu xử lý đồng bộ trạng thái thanh toán sang đợt thu học phí.');
        Log::channel('debt')->info('Bắt đầu xử lý đồng bộ trạng thái thanh toán sang đợt thu học phí.');

        $now = Carbon::now();
        // Lấy danh sách các đợt thu học phí đang trong quá trình thu
        $periods = TuitionPeriod::where('status', TuitionPeriod::PERIOD_STATUS_IN_PROGRESS)
        ->where(function($qurey) use ($now){
            $qurey->where('start_date', '<', $now)->where('end_date', '>', $now);
        })->get();
        
        // Lấy danh sách sinh viên theo đợt đang thu chưa hoàn thiện tất cả công nợ
        $students = TuitionPeriodStudent::whereIn('tuition_period_id', $periods->pluck('id'))
            ->where('status', TuitionPeriodStudent::STUDENT_DEBT_STATUS_PENDING)
            ->get();
        
        // Lấy danh sách môn học của sinh viên theo đợt thu học phí
        $studentSubjectDebts = TuitionPeriodSubject::whereIn('tuition_period_id', $periods->pluck('id'))
            ->where('status', TuitionPeriodSubject::SUBJECT_DEBT_STATUS_PENDING)
            ->whereIn('student_code', $students->pluck('student_code'))
            ->get();
        
        // Đối chiếu sang bảng công nợ để check trạng thái thanh toán của sinh viên
        $studentDebtPaidIds = StudentDebt::whereIn('tuition_period_subject_id', $studentSubjectDebts->pluck('id'))
            ->where('status', StudentDebt::STATUS_PAID)
            ->pluck('tuition_period_subject_id')->toArray();
        
        foreach ($studentDebtPaidIds as $key => $id) {
            // Lấy thông tin môn học có công nợ đã thanh toán
            $studentSubjectDebt = TuitionPeriodSubject::find($id);
            if ($studentSubjectDebt->status === 0) {
                // Cap nhật trạng thái môn học đã thanh toán
                $studentSubjectDebt->status = TuitionPeriodSubject::SUBJECT_DEBT_STATUS_PAID;
                // Ghi log hành động
                $note = "Cập nhật trạng thái thu học phí môn học {$studentSubjectDebt->subject_name} (theo đợt thu học phí $studentSubjectDebt->tuition_period_id) của học sinh {$studentSubjectDebt->student_code} sang đã thanh toán.";
                ActionLog::create([
                    'object'        => 'tuition_period',
                    'auth'          => 'system',
                    'action'        => 'update',
                    'description'   => $note,
                    'object_id'     => $studentSubjectDebt->tuition_period_id,
                    'data_changed'  => "",
                    'ip'            => "",
                ]);
                
                $studentSubjectDebt->save();
                $this->processed[] = $studentSubjectDebt->id;
            }
        }
        Log::channel('debt')->info([
            'processed' => $this->processed,
            'total_processed' => count($this->processed),
        ]);

        foreach ($students as $key => $student) {
            $pendingDebt = TuitionPeriodSubject::where('student_code', $student->student_code)
                ->where('status', TuitionPeriodSubject::SUBJECT_DEBT_STATUS_PENDING)
                ->where('tuition_period_id', $student->tuition_period_id)
                ->count();
            if ($pendingDebt === 0) {
                // Cập nhật trạng thái công nợ của sinh viên sang đã hoàn thành thanh toán
                $student->status = TuitionPeriodStudent::STUDENT_DEBT_STATUS_PAID;
                // Ghi log hành động
                $note = "Cập nhật trạng thái thu học phí của học sinh {$student->student_code} (theo đợt thu học phí $student->tuition_period_id) sang đã hoàn thành đợt thu học phí.";
                ActionLog::create([
                    'object'        => 'tuition_period',
                    'auth'          => 'system',
                    'action'        => 'update',
                    'description'   => $note,
                    'object_id'     => $student->tuition_period_id,
                    'data_changed'  => "",
                    'ip'            => "",
                ]);
                $student->save();
            }
        }

        Log::channel('debt')->info('Kết thúc xử lý đồng bộ trạng thái thanh toán sang đợt thu học phí.');
        $this->info('Xử lý đồng bộ trạng thái thanh toán sang đợt thu học phí hoàn tất.');
        return 0;
    }
}
