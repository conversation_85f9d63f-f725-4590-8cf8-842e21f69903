<?php

namespace App\Console\Commands\Fee;

use App\Models\Fee\TuitionPeriodStudent;
use App\Models\Fee\TuitionPeriodSubject;
use Illuminate\Support\Facades\Log;
use App\Models\Fee\TuitionPeriod;
use Illuminate\Console\Command;
use App\Models\Fee\StudentDebt;
use App\Models\Fu\ActionLog;
use Carbon\Carbon;
use Illuminate\Notifications\Action;
use Illuminate\Support\Facades\DB;

class ScanTuitionPeriod extends Command
{
    protected $processed = [];
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tuition-period:scan';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Quét đợt thu học phí';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */

    public function handle()
    {
        $this->info('Bắt đầu quét đợt thu học phí');
        Log::channel('debt')->info('Bắt đầu quét đợt thu học phí');

        $now = Carbon::now();
        // Lấy danh sách các đợt thu học phí đang trong quá trình thu
        $periods = TuitionPeriod::where('end_date', '<', $now)->where('status', TuitionPeriod::PERIOD_STATUS_IN_PROGRESS)->get();

        if (count($periods) > 0) {
            try {
                DB::beginTransaction();

                TuitionPeriodSubject::whereIn('tuition_period_id', $periods->pluck('id'))
                    ->where('status', TuitionPeriodSubject::SUBJECT_DEBT_STATUS_PENDING)
                    ->update([
                        'status' => TuitionPeriodSubject::SUBJECT_DEBT_STATUS_CANCEL,
                    ]);

                TuitionPeriod::whereIn('id', $periods->pluck('id'))
                    ->update([
                        'status' => TuitionPeriod::PERIOD_STATUS_CLOSE,
                        'updated_at' => $now,
                    ]);

                foreach ($periods as $period) {
                    // Ghi log hành động
                    ActionLog::create([
                        'object'        => 'tuition_period',
                        'auth'          => 'system',
                        'action'        => 'update',
                        'description'   => "Đóng đợt thu học phí $period->name (ID: $period->id)",
                        'object_id'     => $period->id,
                        'data_changed'  => "",
                        'ip'            => "",
                    ]);
                }
                    
                DB::commit();
            } catch (\Throwable $th) {
                DB::rollBack();
                Log::channel('debt')->error($th);
            }
        }
        Log::channel('debt')->info('Kết thúc quét đợt thu học phí');
        $this->info('Xử lý quét đợt thu học phí hoàn tất.');
        return 0;
    }
}
