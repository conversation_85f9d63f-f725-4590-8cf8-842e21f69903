<?php

namespace App\Console\Commands;

use App\Http\Controllers\Admin\SystemController;
use App\Models\Fee\Fee;
use App\User;
use Illuminate\Console\Command;

class SendFeeMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fee:mail {campus_code} {term_name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gửi email học phí';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $campus_code = $this->argument('campus_code');
        $term_name = $this->argument('term_name');
        $this->info($term_name);
        try {
            $countUser = Fee::on($campus_code)
                ->whereIn('study_status', [1,3,10])
                ->where('ki_thu', "!=", 0)
                ->count();
                // dd($countUser);
            $num = ceil($countUser / 1000);
            $this->info("Thực hiện $num lần");
            $per = 0;
            for ($i = 0;$i < $num * 1000;$i+=1000) {
                $per++;
                SystemController::sendFeeMail($campus_code, $term_name, $i, 1000);
                $this->info("Hoàn tất lần $per, skip $i limit 1000");
            }
        } catch (\Exception $e) {
            dd($e);
            $this->error('Lỗi english:update');
        }
    }
}
