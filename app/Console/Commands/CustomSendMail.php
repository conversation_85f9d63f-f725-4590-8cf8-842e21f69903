<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Mail\UpdateWallet;
use App\Models\Fee\Fee;
use App\Models\Fu\User;
use Carbon\Carbon;

class CustomSendMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */

    protected $signature = 'command:send-custom-mail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gửi mail theo custom mail';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $listUserCode = array_map('str_getcsv', file('app/Console/Commands/demofile.csv'));
        $listUserCode = array_merge(...$listUserCode);

        $max = count($listUserCode);
        $check = true;
        dump("số lượng mail: $max");
        for ($i=0; $i < $max; $i = $i + 100) { 
            $timeNow = new Carbon();
            $end = $i + 100;
            $end = $end < $max ? $end : $max;
            $dataProcess = [];
            $dataProcess = array_slice($listUserCode, $i, 100);
            $listUser = User::whereIn('user_code', $dataProcess)
                ->get();    
            foreach ($listUser as $key => $value) {
                // dump("Đã xử lý  " .$value->user_login . "");
                try {
                    Mail::to($value->user_login . "")->send(new \App\Mail\CustomMail());
                // Mail::to($value->user_login . "")->queue(new \App\Mail\CustomMail());
                    // echo "done $value->user_login \n";
                } catch (\Exception $ex) {
                    echo "lỗi $value->user_login";
                    dd($ex);
                }
            }

            dump("[" . $timeNow->format('Y-m-d H:i:s') . "] Đã xử lý danh sách sv từ $i đến " . (count($dataProcess) + $i));
            Log::debug("command:send-custom-mail [" . $timeNow->format('Y-m-d H:i:s') . "] Đã xử lý danh sách sv từ $i đến " . (count($dataProcess) + $i));
            if ($check == false) break;
        }
    }
}
