<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Fee\StudentWallet;
use App\Models\Fu\User;

class FixWalletUserLogin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wallet:fix-user-login';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix missing user_login in student wallets';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting to fix missing user_login in student wallets...');

        // Tìm tất cả ví thiếu user_login
        $walletsWithoutLogin = StudentWallet::where(function($query) {
            $query->whereNull('user_login')
                  ->orWhere('user_login', '');
        })->get();

        $this->info("Found {$walletsWithoutLogin->count()} wallets without user_login");

        $fixed = 0;
        $failed = 0;

        foreach ($walletsWithoutLogin as $wallet) {
            $user = User::where('user_code', $wallet->user_code)->first();
            
            if ($user) {
                $wallet->user_login = $user->user_login;
                $wallet->save();
                $this->line("✓ Fixed wallet for {$wallet->user_code} -> {$user->user_login}");
                $fixed++;
            } else {
                $this->error("✗ No user found for user_code: {$wallet->user_code}");
                $failed++;
            }
        }

        $this->info("\nSummary:");
        $this->info("Fixed: {$fixed} wallets");
        if ($failed > 0) {
            $this->warn("Failed: {$failed} wallets");
        }

        return Command::SUCCESS;
    }
}
