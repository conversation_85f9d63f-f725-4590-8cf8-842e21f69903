<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

use App\Models\Fu\Feedback;
use App\Models\Fu\Block;
use App\Models\Fu\Term;
use App\Models\Fu\Group;
use App\Models\Fu\Activity;
use App\Models\Fu\Subject;
use App\Utils\ResponseBuilder;
use Illuminate\Support\Facades\Log;

class ProcessFeedback extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'feedback:open';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // init data
        $currentTerm = Term::whereRaw('startday <= CURRENT_DATE')
        ->whereRaw('endday >= CURRENT_DATE')
        ->first();
        
        $currentBlock = Block::whereRaw('start_day <= CURRENT_DATE')
        ->whereRaw('end_day >= CURRENT_DATE')
        ->first();

        // dd($currentTerm->toArray(), $currentBlock->toArray());

        // $this->OpenFeedbackBlock($currentTerm, $currentBlock);
        $this->OpenFeedbackTerm($currentTerm);
        $this->syncCurrentFeedback();
        $this->closeFeedback();
        return Command::SUCCESS;
    }

    public function syncCurrentFeedback()
    {
        // ĐỒng bộ feedback của kỳ
    }

    // mở feedback theo block với những môn học 2 block
    public function OpenFeedbackBlock($currentTerm, $currentBlock)
    {
        $blockCheck = null;
        $ignoreStr = null;

        $blockCheck = Block::select([        
            'id',
            'start_day',
            'end_day',
            DB::raw('DATE_ADD( start_day, INTERVAL 20 DAY ) AS min_day_check'),
            DB::raw('DATE_ADD( start_day, INTERVAL 33 DAY ) AS max_day_check')
        ])
        ->where('id', $currentBlock->id)
        ->where('term_id', $currentTerm->id)
        ->first();

        $ignoreSkillcode = ['vie101', 'vie102', 'vie103', 'ENT111', 'ENT121', 'ENT211', 'ENT221', 'VIE104'];
        $listGroupIgnore = Subject::select([        
            'id',
            'subject_name',
            'skill_code'
        ])
        ->where('subject_name', 'Like', '%Dự án tốt nghiệp%')
        ->orWhere('subject_name', 'Like', '%Thực tập tốt nghiệp%')
        ->get();

        $ignoreSkillcode = array_merge($ignoreSkillcode, ($listGroupIgnore->pluck('skill_code')->toArray() ?? []));
        if(count($ignoreSkillcode) > 0) {
            $ignoreStr = 'AND skill_code NOT IN (\'' . implode('\',\'', $ignoreSkillcode) . '\')';
        }
        
        $dataCheck = DB::select("SELECT
            groupid,
            leader_login,
            min( `day` ) AS min,
            max( `day` ) AS max 
        FROM
            activity
            LEFT JOIN session_type ON activity.session_type = session_type.id
        WHERE
            groupid IN ( 
                SELECT id 
                FROM list_group 
                WHERE term_id = ? 
                AND id NOT IN  (SELECT groupid FROM feedback) 
                $ignoreStr 
            ) 
            AND session_type.is_exam = 0
            GROUP BY groupid 
            HAVING max >= ? 
            AND min <= ?", [$currentTerm->id, $blockCheck->min_day_check, $blockCheck->max_day_check]);

        $strNow = now()->format('Y-m-d');
        $currentUser = auth()->user();
        
        DB::beginTransaction();
        try {
            foreach ($dataCheck as $key => $value) {
                $checkIsset = Feedback::where('groupid', $value->groupid)->count();
                if ($checkIsset == 0) {
                    $newFeedback = new Feedback();
                    $newFeedback->groupID = $value->groupid;
                    $newFeedback->open = 1;
                    $newFeedback->hit = 0;
                    $newFeedback->GPA = 0;
                    $newFeedback->opener_login = $currentUser->user_login;
                    $newFeedback->day = $strNow;
                    $newFeedback->open_day = $strNow;
                    $newFeedback->planer_login = $currentUser->user_login;
                    $newFeedback->teacher_login = $value->leader_login;
                    $newFeedback->save();
                }
            }

            DB::commit();
            return ResponseBuilder::Success([], 'Tạo feedback thành công');
        } catch (\Exception $ex) {
            Log::error($ex);
            DB::rollback();
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng báo lại cán bộ IT');
        }
    }

    // Mở feedback theo kỳ với những môn học 2 block
    public function OpenFeedbackTerm($currentTerm)
    {
        $blockCheck = null;
        $ignoreStr = null;
        $currentTerm = Term::select([        
                'id',
                'startday',
                'endday',
                DB::raw('DATE_ADD( startday, INTERVAL 43 DAY ) AS min_day_check'),
                DB::raw('DATE_ADD( startday, INTERVAL 254 DAY ) AS max_day_check')
            ])
            ->whereRaw('startday <= CURRENT_DATE')
            ->whereRaw('endday >= CURRENT_DATE')
            ->first();

        $CheckSkillCode = ['ENT111', 'ENT121', 'ENT211', 'ENT221'];
        $listGroupCheck = Group::where('is_virtual', 0)
            ->select('id')
            ->where('pterm_id', $currentTerm->id)
            ->whereIn('skill_code', $CheckSkillCode)
            ->get();

        if(count($listGroupCheck) > 0) {
            $ignoreStr = 'AND skill_code IN (\'' . implode('\',\'', $CheckSkillCode) . '\')';
        }
        
        $dataCheck = DB::select("SELECT
            groupid,
            leader_login,
            min( `day` ) AS min,
            max( `day` ) AS max 
        FROM
            activity
            LEFT JOIN session_type ON activity.session_type = session_type.id
        WHERE
            groupid IN ( 
                SELECT id 
                FROM list_group 
                WHERE term_id = ? 
                AND id NOT IN  (SELECT groupid FROM feedback) 
                $ignoreStr 
            ) 
            AND session_type.is_exam = 0
            GROUP BY groupid 
            HAVING 
            -- max >= ? AND 
            min <= ?", [
                $currentTerm->id, 
                // $currentTerm->min_day_check, 
                $currentTerm->max_day_check
            ]);

        $strNow = now()->format('Y-m-d');
        $currentUser = auth()->user();
        
        dd($dataCheck, [$currentTerm->id, $currentTerm->min_day_check, $currentTerm->max_day_check]);
        dd("SELECT
            groupid,
            leader_login,
            min( `day` ) AS min,
            max( `day` ) AS max 
        FROM
            activity
            LEFT JOIN session_type ON activity.session_type = session_type.id
        WHERE
            groupid IN ( 
                SELECT id 
                FROM list_group 
                WHERE term_id = ? 
                AND id NOT IN  (SELECT groupid FROM feedback) 
                $ignoreStr 
            ) 
            AND session_type.is_exam = 0
            GROUP BY groupid 
            HAVING 
            -- max >= ? AND 
            min <= ?", [
                $currentTerm->id, 
                // $currentTerm->min_day_check, 
                $currentTerm->max_day_check
            ]);
        DB::beginTransaction();
        try {
            foreach ($dataCheck as $key => $value) {
                $checkIsset = Feedback::where('groupid', $value->groupid)->count();
                if ($checkIsset == 0) {
                    $newFeedback = new Feedback();
                    $newFeedback->groupID = $value->groupid;
                    $newFeedback->open = 1;
                    $newFeedback->hit = 0;
                    $newFeedback->GPA = 0;
                    $newFeedback->opener_login = $currentUser->user_login;
                    $newFeedback->day = $strNow;
                    $newFeedback->open_day = $strNow;
                    $newFeedback->planer_login = $currentUser->user_login;
                    $newFeedback->teacher_login = $value->leader_login;
                    $newFeedback->save();
                }
            }

            DB::commit();
            return ResponseBuilder::Success([], 'Tạo feedback thành công');
        } catch (\Exception $ex) {
            DB::rollback();
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng báo lại cán bộ IT');
        }
    }

    // Đóng feedbacj
    public function closeFeedback()
    {
        // với các môn thường

        
        // với các môn liên block
    }
}
