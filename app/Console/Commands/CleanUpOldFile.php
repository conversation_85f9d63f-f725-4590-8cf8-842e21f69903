<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CleanUpOldFile extends Command
{
    protected $signature = 'cleanup:old-files';
    protected $description = 'Xóa các file cũ trong storage/app/documents/';

    public function handle()
    {
        $files = Storage::files('documents');
        $now = now();

        foreach ($files as $file) {
            $lastModified = Storage::lastModified($file);
            if ($now->diffInDays($lastModified) > 5) {
                Storage::delete($file);
                $this->info("Đã xóa: $file");
            }
        }
    }
}
