<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdateWallet extends Mailable
{
    use Queueable, SerializesModels;
    private $data;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        try {
            $data = $this->data;
            $subject = "Số dư ví " . $data->wallet_name . " thay đổi";
    
            return $this->view('emails.fee.update_wallet')
                ->with([
                    'data' => $data,
                ])
                ->subject($subject);
        } catch (\Throwable $th) {
            Log::channel('mail')->error('err: ' . $th->getFile() . " - " . $th->getLine() . ' - ' . $th->getMessage());
        }
    }
}
