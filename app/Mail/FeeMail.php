<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class FeeMail extends Mailable 
{
    use Queueable, SerializesModels;
    private $data;
    private $fee;
    private $type;
    private $detailFee;
    private $full_name;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($data, $fee, $full_name, $detailFee = null, $type = 0)
    {
        $this->data = $data;
        $this->fee = $fee;
        $this->type = $type;
        $this->detailFee = $detailFee;
        $this->full_name = $full_name;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        // if ($this->type == 1) {
        //     $subject = "THÔNG BÁO: V/V ĐÓNG HỌC PHÍ CHUYỂN NGÀNH TẠI CYBER UNI HỌC KỲ FALL 2022";
        // } else {
        //     $subject = "THÔNG BÁO: V/V ĐÓNG HỌC PHÍ TẠI CYBER UNI HỌC KỲ FALL 2022 (NHẮC NHỞ)";
        // }
        // return $this->from('<EMAIL>','Cyber Uni')->subject($subject)->view($this->type == 1 ? 'emails.fee.hoc_phi_chuyen_nganh':'emails.fee.hoc_phi', [
        //     'data' => $this->data,
        //     'fee' => $this->fee,
        //     'full_name' => $this->full_name,
        //     'fee_detail' => $this->detailFee,
        // ]);
        $termName = strtoupper($this->data->term_name ?? "");
        $subject = "THÔNG BÁO: V/V ĐÓNG HỌC PHÍ TẠI CYBER UNI HỌC KỲ $termName";
        return $this->from('<EMAIL>','Cyber Uni')
            ->subject($subject)
            ->view('emails.fee.new_hoc_phi', [
            'data' => $this->data,
            'fee' => $this->fee,
            'full_name' => $this->full_name,
            'fee_detail' => $this->detailFee,
            'term_name' => $termName,
        ]);
    }
}
