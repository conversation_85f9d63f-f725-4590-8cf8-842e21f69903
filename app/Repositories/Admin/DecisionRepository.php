<?php


namespace App\Repositories\Admin;


use App\Imports\DropOutImport;
use App\Models\Fu\ActionLog;
use App\Models\Fu\Term;
use App\Models\Fu\DecisionUser;
use App\Models\Fu\Decision;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Log;
use App\Models\Fu\DisciplineExamRegulation;
use Carbon\Carbon;
use Illuminate\Support\Facades\Response;

class DecisionRepository extends BaseRepository
{
    const HTKL = [
        "BUOC_THOI_HOC" => [
            'code' => "BUOC_THOI_HOC",
            'name' => "Buộc thôi học",
            'ha_loai' => true
        ],

        "DINH_CHI" => [
            'code' => 'DINH_CHI',
            'name' => 'Đình chỉ',
            'ha_loai' => true
        ],

        "CANH_CAO" => [
            'code' => "CANH_CAO",
            'name' => 'Cảnh cáo',
            'ha_loai' => true
        ],
    ];
    public function getModel()
    {
        return Decision::class;
    }

    public function index()
    {
        $terms = Term::orderBy('id', 'desc')->get();
        $listStudyStatus = config('status')->trang_thai_hoc;
        return view(env('THEME_ADMIN') . '.decision.index', [
            'terms' => $terms,
            'decision_types' => Decision::TYPES,
            'listStudyStatus' => $listStudyStatus
        ]);
    }

    public function createFrom()
    {
        $terms = Term::orderBy('id', 'desc')->get();
        return view(env('THEME_ADMIN') . '.decision.create', [
            'terms' => $terms,
            'decision_types' => Decision::TYPES,
        ]);
    }

    public function edit($id, $request)
    {
        $decision = Decision::find($id);
        if (!$decision) {
            return redirect()->route('admin.decision.index');
        }
        $terms = Term::orderBy('id', 'desc')->get();
        return view(env('THEME_ADMIN') . '.decision.edit', [
            'terms' => $terms,
            'decision_types' => Decision::TYPES,
            'decision' => $decision
        ]);
    }

    public function store($request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required',
            'so_quyet_dinh' => 'required|alpha_num',
            'nguoi_ky' => 'required',
            'drop_out_date' => 'required',
            'quyet_dinh_tu' => 'required',
            'users' => 'required',
            'decision_type' => 'required',
        ], [
            'file.required' => 'Bạn chưa tải file quyết định lên',
            'so_quyet_dinh.required' => 'Số quyết định không được để trống',
            'nguoi_ky.required' => 'Người ký không được để trống',
            'drop_out_date.required' => 'Ngày ký không được để trống',
            'quyet_dinh_tu.required' => 'Quyết định từ không được để trống',
            'so_quyet_dinh.regex' => 'Số quyết định chỉ được nhập số',
            'users.required' => 'File danh sách sinh viên không được để trống',
            'decision_type.required' => 'Loại quyết định không được để trống',
        ]);

        if ($validator->fails()) {
            return redirect(url()->previous())
                ->withErrors($validator)
                ->withInput();
        }

        if (!$users = $this->importDataFromFile($request->users)) {
            return $this->redirectWithStatus('danger', 'Không có sinh viên nào để import hoặc file tải lên bị lỗi', url()->previous());
        }

        DB::beginTransaction();
        $bug = null;
        $termDetail = Term::find($request->term_id);
        $fileDecision = $this->uploadFileDecisionPdf($request->file('file'), $request->so_quyet_dinh, $termDetail->term_name);
        // $typeDecision = Decision::getTypeDecision($status['code']);
        try {
            $dataDecision = Decision::where('term_id', $request->term_id)
                ->where('type', $request->decision_type)
                ->where('decision_num', $request->so_quyet_dinh)
                ->first();

            $checkInsert = false;
            if (!$dataDecision) {
                $dataDecision = Decision::create([
                    'name' => $request->file('file')->getClientOriginalName(),
                    'decision_num' => $request->so_quyet_dinh,
                    'term_id' => $request->term_id,
                    'from' => $request->quyet_dinh_tu,
                    'type' => $request->decision_type,
                    'signer' => $request->nguoi_ky,
                    'sign_day' => $request->drop_out_date,
                    'effective_time' => $request->date_affected,
                    'file' => $fileDecision,
                    'file_status' => 1,
                    'note' => ($request->note ?? ""),
                    'created_by' => (auth()->user()->user_login ?? null),
                ]);
            } else {
                $dataDecision::where('term_id', $request->term_id)
                    ->where('type', $request->decision_type)
                    ->where('decision_num', $request->so_quyet_dinh)
                    ->update([
                        'from' => $request->quyet_dinh_tu,
                        'signer' => $request->nguoi_ky,
                        'sign_day' => $request->drop_out_date,
                        'effective_time' => $request->date_affected,
                        'note' => ($request->note ?? ""),
                    ]);
            }
            $IdsListImportFromExamined = [];
            foreach ($users as $item) {
                $bug = $item;
                $userUpdate = $item[0] ?? $item;
                $IdsListImportFromExamined[] = $item[3] ?? '';
                if ($userUpdate == null) {
                    continue;
                } elseif (is_array($userUpdate) && $userUpdate[0] == null) {
                    continue;
                }

                $checkInsert = true;
                /* dev Thêm phần quản lý quyết định theo mã sinh viên */
                $decisionUser = DecisionUser::where('decision_id', $dataDecision->id)
                    ->where('user_code', strtoupper($userUpdate))
                    ->first();

                if (!$decisionUser) {
                    DecisionUser::create([
                        'decision_id' => $dataDecision->id,
                        'user_code' => strtoupper($userUpdate),
                    ]);
                }
            }
            $IdsListImportFromExamined = array_unique($IdsListImportFromExamined);


            if ($checkInsert == false) {
                DB::rollBack();
                return $this->redirectWithStatus('danger', 'Kiểm tra lại danh sách sinh viên', url()->previous());
            }
            $this->updateDisciplineExam($IdsListImportFromExamined, $request->term_id, $request->decision_type, $request->so_quyet_dinh);
            DB::commit();
            return $this->redirectWithStatus('success', "Tạo mới quyết định thành công", route('admin.decision.edit', ['id' => $dataDecision->id]));
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::error('DecisionRepository - Store: ' . $th->getFile() . ' - ' . $th->getLine() . ' - ' . $th->getMessage());
            return redirect(url()->previous())->withErrors("Dữ liệu không đúng định dạng, huỷ bỏ quá trình import!")->withInput();
        }
    }

    public function updateDisciplineExam($idList, $term_id, $decision_type, $so_quyet_dinh)
    {
        try {
            $decision = Decision::where('term_id', $term_id)
                ->where('type', $decision_type)
                ->where('decision_num', $so_quyet_dinh)
                ->first();

            DisciplineExamRegulation::whereIn('id', $idList)->update([
                'decision_no' => $decision->id
            ]);
            return true;
        } catch (\Exception $exception) {
            Log::debug($exception);
            return false;
        }
    }

    public function update($id, $request)
    {
        $validator = Validator::make($request->all(), [
            'so_quyet_dinh' => 'required|alpha_num',
            'nguoi_ky' => 'required',
            'drop_out_date' => 'required',
            'quyet_dinh_tu' => 'required',
            'decision_type' => 'required',
        ], [
            'file.required' => 'Bạn chưa tải file quyết định lên',
            'so_quyet_dinh.required' => 'Số quyết định không được để trống',
            'nguoi_ky.required' => 'Người ký không được để trống',
            'drop_out_date.required' => 'Ngày ký không được để trống',
            'quyet_dinh_tu.required' => 'Quyết định từ không được để trống',
            'so_quyet_dinh.regex' => 'Số quyết định chỉ được nhập số',
            'users.required' => 'File danh sách sinh viên không được để trống',
            'decision_type.required' => 'Loại quyết định không được để trống',
        ]);

        if ($validator->fails()) {
            return redirect(url()->previous())
                ->withErrors($validator)
                ->withInput();
        }

        $users = [];
        $fileDecision = null;
        $termDetail = Term::find($request->term_id);
        if (isset($request->users) && !$users = $this->importDataFromFile($request->users)) {
            return $this->redirectWithStatus('danger', 'Không có sinh viên nào để import hoặc file tải lên bị lỗi', url()->previous());
        }

        if (isset($request->file)) {
            $fileDecision = $this->uploadFileDecisionPdf($request->file('file'), $request->so_quyet_dinh, $termDetail->term_name);
        }

        $dataDecision = Decision::find($id);
        if (!$dataDecision) {
            return back();
        }

        DB::beginTransaction();
        $bug = null;
        try {
            $logData = [];
            $listUserUpdate = [];
            $oldData = $dataDecision->toArray();
            $newData = [
                'name' => $request->name,
                'term_id' => $request->term_id,
                'from' => $request->quyet_dinh_tu,
                'decision_num' => $request->so_quyet_dinh,
                'signer' => $request->nguoi_ky,
                'sign_day' => $request->drop_out_date,
                'effective_time' => $request->date_affected,
                'type' => $request->decision_type,
                'note' => ($request->note ?? ""),
            ];

            $updateData = call_user_func(function () use (&$logData, $fileDecision, $oldData, $newData) {
                $res = [];
                $oldLog = [];
                foreach ($newData as $key => $value) {
                    if (trim($newData[$key]) != trim($oldData[$key])) {
                        $oldLog[$key] = $oldData[$key];
                        $res[$key] = $newData[$key];
                    }
                }

                if ($fileDecision != null) {
                    $res['file'] = $fileDecision;
                }

                $logData = [
                    'auth' => auth()->user()->user_login,
                    'updated_at' => Carbon::now()->toDateTimeString(),
                    'old_data' => $oldLog,
                    'data_update' => $res
                ];

                return $res;
            });
            $IdsListImportFromExamined = [];

            foreach ($users as $item) {
                $bug = $item;
                /* dev Thêm phần quản lý quyết định theo mã sinh viên */
                $decisionUser = DecisionUser::where('decision_id', $dataDecision->id)
                    ->where('user_code', strtoupper($item[0] ?? $item))
                    ->first();
                $IdsListImportFromExamined[] = $item[3] ?? '';

                if (!$decisionUser) {
                    $listUserUpdate[] = strtoupper($item[0] ?? $item);
                    DecisionUser::create([
                        'decision_id' => $dataDecision->id,
                        'user_code' => strtoupper($item[0] ?? $item),
                    ]);
                }
            }
            $IdsListImportFromExamined = array_unique($IdsListImportFromExamined);

            if (count($listUserUpdate) > 0) {
                $logData['data_update']['user_add'] = $listUserUpdate;
            }

            $logUpdate = [];
            if ($dataDecision->log != null) {
                $logUpdate = json_decode($dataDecision->log, 1);
            }

            $logUpdate[] = $logData;
            $updateData['log'] = json_encode($logUpdate);
            $decision = Decision::find($id)->update($updateData);
            $this->updateDisciplineExam($IdsListImportFromExamined, $dataDecision->term_id, $dataDecision->type, $dataDecision->decision_num);
            $terms = Term::orderBy('id', 'desc')->get();

            DB::commit();
            return $this->redirectWithStatus('success', "Cập nhập quyết định thành công", route('admin.decision.edit', [
                'id' => $dataDecision->id,             
                'terms' => $terms,
                'decision_types' => Decision::TYPES,
                'decision' => $decision
            ]));
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::debug($exception);
            return $this->redirectWithStatus('danger', 'Dữ liệu không đúng định dạng, huỷ bỏ quá trình import', url()->previous());
        }
    }

    /* dev Thêm phần quản lý quyết định theo mã sinh viên */
    public function removeUser($request)
    {
        $formReturn = [
            "status" => 1,
            "code" => 200,
            "message" => "Rút sinh viên ra khỏi quyết định thành công!",
            "data" => null
        ];
        $student_code = $request->user_code ?? null;
        // Tìm theo 2 cái trái tránh trường hợp cố tình phang Id khác vào
        $decisionUser = DecisionUser::where('id', $request->id)->where('user_code', $student_code)->first();
        if (!$decisionUser) {
            $formReturn['code'] = 400;
            $formReturn['message'] = "Sinh viên không tồn tại trong quyết đinh";
            return response()->json($formReturn, $formReturn['code']);
        }

        $dataDecision = $decisionUser->decision;
        DB::beginTransaction();
        $bug = null;
        try {
            $logData = [
                'auth' => auth()->user()->user_login,
                'updated_at' => Carbon::now()->toDateTimeString(),
                'old_data' => [],
                'data_update' => [
                    'user_remove' => [strtoupper($student_code)]
                ]
            ];

            $logUpdate = [];
            if ($dataDecision->log != null) {
                $logUpdate = json_decode($dataDecision->log, 1);
            }

            $logUpdate[] = $logData;
            $dataDecision->log = json_encode($logUpdate);
            $dataDecision->save();
            $decisionUser->delete();

            $this->systemLog('update decision', 'remove student decision', "Rút sinh viên $student_code khỏi quyết định số  $dataDecision->from" . " " . $dataDecision->decision_num . "($dataDecision->id)", $student_code, 0, 0, '', '', 'remove student decision');
            DB::commit();
            return response()->json($formReturn, $formReturn['code']);
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error($exception);
            $formReturn['code'] = 400;
            $formReturn['message'] = $exception->getMessage();
            return response()->json($formReturn, $formReturn['code']);
        }
    }

    public function exportData($request)
    {
        ini_set('max_execution_time', -1);
        header('Content-Type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename="export_' . time() . '.csv";');

        $header = [
            1 => "Mã sinh viên"
        ];

        $decisionUser = DecisionUser::where('decision_id', $request->id)->get();
        $f = fopen('php://output', 'w');
        fputcsv($f, $header);
        foreach ($decisionUser as $key => $value) {
            fputcsv($f, [$value->user_code]);
        }
    }

    public function loadSelect2Decision($request)
    {
        $list = Decision::query();
        $term_id = $request->get('term_id', null);
        $decision_type = $request->get('decision_type', null);
        if ($term_id != null && $term_id != -1) {
            $list = $list->where('term_id', $term_id);
        }

        if ($decision_type != null && $decision_type != -1) {
            $list = $list->where('type', $decision_type);
        }

        $listType = Decision::TYPES;
        $list = $list->get();
        $res[] = [
            'id' => -1,
            'text' => "Tất cả các quyết định",
        ];
        foreach ($list as $key => $value) {
            $res[] = [
                'id' => $value->id,
                'text' => ($listType[$value->type]['code'] . " - " . $value->decision_num . " - " . $value->name),
            ];
        }

        return response()->json($res, 200);
    }

    public function uploadFileDecisionPdf($file, $no, $termName)
    {
        $fileName = $termName . '-' . $no . '-' . now()->format('d_m_y_h_i_s') . '.' . $file->getClientOriginalExtension();
        $this->uploadFile(('decision'), $file, $fileName);

        return $fileName;
    }

    public function importDataFromFile($file)
    {
        $data = Excel::toArray(new DropOutImport(), $file);
        if (count($data[0]) <= 1) {
            return false;
        }

        unset($data[0][0]);
        return $data[0];
    }

    /**
     * Xuất danh sách tất cả sinh viên quyết định theo kỳ
     * 
     * 
     */
    public function exportDecision($request) {
        try {
            $term_id = $request->term_id;
            $study_status = $request->get('study_status', []);
            $time = time();
            $listDecisionType = Decision::TYPES;
            
            $queryDecisionUsers = DecisionUser::select([
                'user.user_code',
                DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ", user.user_givenname) as full_name'),
                'term.term_name',
                'decision.type',
                'decision.name',
                'decision_user.note',
                'decision_user.hinh_thuc_ky_luat',
                'decision_user.decision_description',
                ])
                ->join('decision', 'decision.id', '=', 'decision_user.decision_id')
                ->join('user', 'user.user_code', '=', 'decision_user.user_code')
                ->join('term', 'term.id', '=', 'decision.term_id');

            if ($term_id != null && $term_id != -1) {
                $queryDecisionUsers->where('decision.term_id', $term_id);
            }
                
            if(count($study_status) > 0 && !in_array(-1, $study_status)) {
                $queryDecisionUsers->whereIn('study_status', $study_status);
            }

            $queryDecisionUsers = $queryDecisionUsers->get();
            $header = ['MSSV', 'Họ và tên', 'Kỳ', 'Loại QĐ', 'Tên QĐ', 'Ghi chú', 'Hình thức kỷ luật', 'Nội dung kỷ luật'];
            
            $headers = array(
                "Content-type" => "text/csv",
                "Content-Disposition" => "attachment; filename=exportListDecision-{$time}.csv",
                "Pragma" => "no-cache",
                "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                "Expires" => "0"
            );
                
            $callback = function () use ($queryDecisionUsers, $header, $listDecisionType) {
                $file = fopen('php://output', 'w');
                fwrite($file, chr(0xEF) . chr(0xBB) . chr(0xBF));
                fputcsv($file, $header);
    
                foreach ($queryDecisionUsers as $key => $line) {
                    
                    fputcsv($file, [
                        $line->user_code,
                        $line->full_name,
                        $line->term_name,
                        $listDecisionType[$line->type]['name'] ?? '',
                        $line->name,
                        $line->note,
                        $line->hinh_thuc_ky_luat,
                        $line->decision_description,
                    ]);
                }
                fclose($file);
            };
    
            return Response::stream($callback, 200, $headers)->send();
        } catch (\Throwable $th) {
            Log::error($th);
        }
    }

    public function destroy($request)
    {
        $formReturn = [
            "status" => 1,
            "code" => 200,
            "message" => "Xóa quyết định thành công!",
            "data" => null
        ];
        $decision = Decision::find($request->id);
        if (!$decision) {
            $formReturn['code'] = 400;
            $formReturn['message'] = "Quyết định không tồn tại";
            return response()->json($formReturn, $formReturn['code']);
        }
        $auth = auth()->user();
        if ($auth->user_level != 1 || $auth->user_login != $decision->created_by) {
            $formReturn['code'] = 400;
            $formReturn['message'] = "Ban không có quyền xóa quyết định này";
            return response()->json($formReturn, $formReturn['code']); 
        } 
        DB::beginTransaction();
        try {
            $description = "Xóa quyết định số  $decision->from" . " " . $decision->decision_num . "($decision->id)";
            $decision->delete();
            $this->actionLog('decision', $auth->user_login, 'delete', $description, $request->id, "");
            DB::commit();
            return response()->json($formReturn, $formReturn['code']);
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error($exception);
            $formReturn['code'] = 400;
            $formReturn['message'] = $exception->getMessage();
            return response()->json($formReturn, $formReturn['code']);
        }
    }
}
