<?php

namespace App\Repositories\Admin;

use App\Models\Brand;
use App\Models\Fu\ActionLog;
use App\Repositories\BaseRepository;
use App\Utils\ResponseBuilder;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class MajorRepository extends BaseRepository
{
    public function getModel()
    {
        return Brand::class;
    }

    public function list(Request $request) {
        try {
            $major_filter = Arr::get($request->all(), 'major');
            $list_brand = Brand::select([
                'major as nganh',
                'major_code as ma_nganh',
            ])
            ->where('brand.major_code', '!=', "")
            ->where('brand.specialized_code', '!=', "")
            ->groupBy('ma_nganh')->orderBy('id')->get()->toArray();
            
            $list_specialized = Brand::select([
                'major as nganh',
                'major_code as ma_nganh',
                'brand.name as chuyen_nganh',
                DB::raw('CONCAT(brand.major_code, "-",brand.specialized_code) AS ma_chuyen_nganh')
            ])
            ->where('brand.major_code', '!=', "")
            ->where('brand.specialized_code', '!=', "")
            ->groupBy('ma_chuyen_nganh')->orderBy('brand.name')->get()->toArray();
            
            $list_sub_specialized = Brand::select([
                'brand.id',
                'brand.code',
                'brand.major as nganh',
                'brand.major_code as ma_nganh',
                'brand.name as chuyen_nganh',
                DB::raw('CONCAT(brand.major_code, "-", brand.specialized_code) AS ma_chuyen_nganh'),
                'brand.description AS chuyen_nganh_hep',
                DB::raw('(
                    CASE 
                        WHEN (brand.sub_specialized_code IS NOT NULL) THEN CONCAT(brand.major_code, "-",brand.specialized_code, "-", brand.sub_specialized_code)
                        ELSE CONCAT(brand.major_code, "-",brand.specialized_code)
                    END) AS ma_chuyen_nganh_hep'
                ),
            ])
            ->where('brand.major_code', '!=', "")
            ->where('brand.specialized_code', '!=', "")
            ->when($major_filter, function ($query) use ($major_filter) {
                return $query->where('brand.major_code', $major_filter);
            })
            ->orderBy('brand.major_code')->get()->toArray();

            return ResponseBuilder::Success([
                'list_brand' => $list_brand, 
                'list_specialized' => $list_specialized, 
                'list_sub_specialized' => $list_sub_specialized
            ]);
        } catch (\Throwable $th) {
            Log::error("getNewestBrandList at line : " . $th->getLine() . "\n" . $th->getMessage());
            return ResponseBuilder::Fail('Lỗi lấy danh sách ngành', null, 500);
        }
    }

    public function createMajor(Request $request) {
        try {
            $rules = [
                'major' => 'required|string|max:10',
                'major_name' => 'required|string|max:255',
                'specialized' => 'required|string|max:10',
                'specialized_name' => 'required|string|max:255',
                'sub_specialized' => 'required|string|max:10',
                'sub_specialized_name' => 'required|string|max:255',
            ];

            $messages = [
                'major.required' => 'Mã ngành không được để trống',
                'major_name.required' => 'Tên ngành không được để trống',
                'specialized.required' => 'Mã chuyên ngành không được để trống',
                'specialized_name.required' => 'Tên chuyên ngành không được để trống',
                'sub_specialized.required' => 'Mã chuyên ngành hẹp không được để trống',
                'sub_specialized_name.required' => 'Tên chuyên ngành hẹp không được để trống',
                
                'major.string' => 'Mã ngành phải là chuỗi',
                'major_name.string' => 'Tên ngành phải là chuỗi',
                'specialized.string' => 'Mã chuyên ngành phải là chuỗi',
                'specialized_name.string' => 'Tên chuyên ngành phải là chuỗi',
                'sub_specialized.string' => 'Mã chuyên ngành hẹp phải là chuỗi',
                'sub_specialized_name.string' => 'Tên chuyên ngành hẹp phải là chuỗi',

                'major.max' => 'Mã ngành không được vượt quá 2 ký tự',
                'major_name.max' => 'Tên ngành không được vượt quá 255 ký tự',
                'specialized.max' => 'Mã chuyên ngành không được vượt quá 2 ký tự',
                'specialized_name.max' => 'Tên chuyên ngành không được vượt quá 255 ký tự',
                'sub_specialized.max' => 'Mã chuyên ngành hẹp không được vượt quá 2 ký tự',
                'sub_specialized_name.max' => 'Tên chuyên ngành hẹp không được vượt quá 255 ký tự',
            ];

            $validator = Validator::make($request->all(), $rules, $messages);

            if ($validator->fails()) {
                // Chuyển đổi lỗi thành một chuỗi đơn giản
                $errorMessages = [];
                foreach ($validator->errors()->all() as $error) {
                    $errorMessages[] = $error;
                }
                $errorString = implode("\n", $errorMessages);
                return ResponseBuilder::Fail($errorString, null, 422);
            }

            $major = $request->input('major_name');
            $major_code = $request->input('major');
            $name = $request->input('specialized_name');
            $specialized_code = $request->input('specialized');  
            $sub_specialized_code = $request->input('sub_specialized');
            $description = $request->input('sub_specialized_name');
            $code = $major_code . $specialized_code . $sub_specialized_code;

            $checkMajor = Brand::where('major_code', $major_code)
                ->where('specialized_code', $specialized_code)
                ->where('sub_specialized_code', $sub_specialized_code)
                ->exists();

            if ($checkMajor) {
                return ResponseBuilder::Fail('Ngành đã tồn tại', null, 400);
            }

            $major = Brand::create([
                'code' => $code,
                'major' => $major,
                'major_code' => $major_code,
                'name' => $name,
                'specialized_code' => $specialized_code,
                'description' => $description,
                'sub_specialized_code' => $sub_specialized_code,
            ]);

            ActionLog::create([
                'object' => 'brand',
                'auth' => auth()->user()->user_login,
                'action' => 'create',
                'description' => "Tài khoản " . auth()->user()->user_login . " tạo mới ngành học $code",
                'object_id' => ($parentUser->id ?? ""),
                'data_changed' => json_encode([
                    'code' => $code,
                    'nganh' => $major,
                    'ma_nganh' => $major_code,
                    'chuyen_nganh' => $name,
                    'ma_chuyen_nganh' => $specialized_code,
                    'chuyen_nganh_hep' => $description,
                    'ma_chuyen_nganh_hep' => $sub_specialized_code,
                ]),
                'ip' => $request->getClientIp(),
            ]);
            
            return ResponseBuilder::Success($major);
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::Fail('Lỗi tạo mới ngành', null, 500);
        }
    }
    
    public function updateMajor(Request $request, $id)
    {
        try {

            $rules = [
                'id' => 'required',
                'code' => 'required|string|max:10',
                'nganh' => 'required|string|max:255',
                'ma_nganh' => 'required|string|max:10',
                'chuyen_nganh' => 'required|string|max:255',
                'ma_chuyen_nganh' => 'required|string|max:10',
                'chuyen_nganh_hep' => 'required|string|max:255',
                'ma_chuyen_nganh_hep' => 'required|string|max:10',
            ];

            $messages = [
                'id.required' => 'ID không được để trống',
                'code.required' => 'Mã ngành không được để trống',
                'nganh.required' => 'Tên ngành không được để trống',
                'ma_nganh.required' => 'Mã ngành không được để trống',
                'chuyen_nganh.required' => 'Mã chuyên ngành không được để trống',
                'ma_chuyen_nganh.required' => 'Mã chuyên ngành không được để trống',
                'chuyen_nganh_hep.required' => 'Mã chuyên ngành hẹp không được để trống',
                'ma_chuyen_nganh_hep.required' => 'Mã chuyên ngành hẹp không được để trống',

                'code.string' => 'Mã ngành phải là chuỗi',
                'nganh.string' => 'Tên ngành phải là chuỗi',
                'ma_nganh.string' => 'Mã ngành phải là chuỗi',
                'chuyen_nganh.string' => 'Mã chuyên ngành phải là chuỗi',
                'ma_chuyen_nganh.string' => 'Mã chuyên ngành phải là chuỗi',
                'chuyen_nganh_hep.string' => 'Mã chuyên ngành hẹp phải là chuỗi',
                'ma_chuyen_nganh_hep.string' => 'Mã chuyên ngành hẹp phải là chuỗi',

                'code.max' => 'Mã ngành không được vượt quá 10 ký tự',
                'nganh.max' => 'Tên ngành không được vượt quá 255 ký tự',
                'ma_nganh.max' => 'Mã ngành không được vượt quá 10 ký tự',
                'chuyen_nganh.max' => 'Mã chuyên ngành không được vượt quá 10 ký tự',
                'ma_chuyen_nganh.max' => 'Mã chuyên ngành không được vượt quá 10 ký tự',
                'chuyen_nganh_hep.max' => 'Mã chuyên ngành hẹp không được vượt quá 10 ký tự',
                'ma_chuyen_nganh_hep.max' => 'Mã chuyên ngành hẹp không được vượt quá 10 ký tự',
            ];

            $validator = Validator::make($request->all(), $rules, $messages);
            
            if ($validator->fails()) {
                // Chuyển đổi lỗi thành một chuỗi đơn giản
                $errorMessages = [];
                foreach ($validator->errors()->all() as $error) {
                    $errorMessages[] = $error;
                }
                $errorString = implode("\n", $errorMessages);
                return ResponseBuilder::Fail($errorString, null, 422);
            }

            $id = $request->input('id');
            $code = $request->input('code');
            $nganh = $request->input('nganh');
            $ma_nganh = $request->input('ma_nganh');
            $chuyen_nganh = $request->input('chuyen_nganh');
            $ma_chuyen_nganh = $request->input('ma_chuyen_nganh');
            $chuyen_nganh_hep = $request->input('chuyen_nganh_hep');
            $ma_chuyen_nganh_hep = $request->input('ma_chuyen_nganh_hep');

            $major = Brand::findOrFail($id);

            // Kiểm tra xem có ngành nào khác với ID khác nhưng cùng mã không
            $checkMajor = Brand::where('major_code', $ma_nganh)
                ->where('specialized_code', $ma_chuyen_nganh)
                ->where('sub_specialized_code', $ma_chuyen_nganh_hep)
                ->where('id', '!=', $id)
                ->exists();

            if ($checkMajor) {
                return ResponseBuilder::Fail('Ngành đã tồn tại với mã này', null, 400);
            }

            // Lưu dữ liệu cũ trước khi cập nhật cho việc ghi log
            $oldData = [
                'code' => $major->code,
                'nganh' => $major->major,
                'ma_nganh' => $major->major_code,
                'chuyen_nganh' => $major->name,
                'ma_chuyen_nganh' => $major->specialized_code,
                'chuyen_nganh_hep' => $major->description,
                'ma_chuyen_nganh_hep' => $major->sub_specialized_code,
            ];

            // Cập nhật dữ liệu
            $major->code = $code;
            $major->major = $nganh;
            $major->major_code = $ma_nganh;
            $major->name = $chuyen_nganh;
            $major->specialized_code = $ma_chuyen_nganh;
            $major->description = $chuyen_nganh_hep;
            $major->sub_specialized_code = $ma_chuyen_nganh_hep;
            $major->save();

            // Tạo log hành động
            ActionLog::create([
                'object' => 'brand',
                'auth' => auth()->user()->user_login,
                'action' => 'update',
                'description' => "Tài khoản " . auth()->user()->user_login . " cập nhật ngành học $code",
                'object_id' => $major->id,
                'data_changed' => json_encode([
                    'old' => $oldData,
                    'new' => [
                        'code' => $code,
                        'nganh' => $nganh,
                        'ma_nganh' => $ma_nganh,
                        'chuyen_nganh' => $chuyen_nganh,
                        'ma_chuyen_nganh' => $ma_chuyen_nganh,
                        'chuyen_nganh_hep' => $chuyen_nganh_hep,
                        'ma_chuyen_nganh_hep' => $ma_chuyen_nganh_hep,
                    ]
                ]),
                'ip' => $request->getClientIp(),
            ]);
            
            return ResponseBuilder::Success($major);
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::Fail('Lỗi cập nhật ngành', null, 500);
        }
    }
    
    public function delete($id)
    {
        try {
            $major = Brand::findOrFail($id);
            $major->delete();
            ActionLog::create([
                'object' => 'brand',
                'auth' => auth()->user()->user_login,
                'action' => 'delete',
                'description' => "Tài khoản " . auth()->user()->user_login . " xóa ngành học $major->code",
                'object_id' => $major->id,
                'data_changed' => json_encode([
                    'code' => $major->code,
                    'nganh' => $major->major,
                    'ma_nganh' => $major->major_code,
                    'chuyen_nganh' => $major->name,
                    'ma_chuyen_nganh' => $major->specialized_code,
                    'chuyen_nganh_hep' => $major->description,
                    'ma_chuyen_nganh_hep' => $major->sub_specialized_code,
                ]),
                'ip' => request()->getClientIp(),
            ]);
            return ResponseBuilder::Success($major);
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::Fail('Thất bại! Có lỗi xảy ra', null, 500);
        }   
    }
    
}
