<?php


namespace App\Repositories\Admin;

use App\Exports\GDQP\DanhSachGDQP;
use App\Imports\GradeGDQPImport;
use App\Models\Fu\Term;
use App\Models\GradeGdqp;
use App\Repositories\BaseRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class GDQPRepository extends BaseRepository
{
    public function getModel()
    {
        return GradeGdqp::class;
    }

    public function index(Request $request)
    {
        $terms = Term::orderBy('id', 'desc')->get();
        $status = config('status')->trang_thai_hoc;

        return view(env('THEME_ADMIN'). '.GDQP.index', [
            'terms' => $terms,
            'status' => $status
        ]);
    }

    public function import(Request $request) {
        $request->validate([
            'file' => 'required'//|mimes:xlsx,xls,csv'
        ], [
            'file.required' => "File import không được bỏ trống",
            // 'file.mimes' => "Không đúng định dạng file (xlsx, xls, csv)"
        ]);
        $file = $request->file('file');
        DB::beginTransaction();
        try {
            $import = new GradeGDQPImport($request->user());
            Excel::import($import, $request->file('file'));
            if (count($import->getValidate()) > 0) {
                $message = "";
                foreach ($import->getValidate() as $inValid) {
                    $message .= $inValid . " , ";
                }

                return redirect()->back()->with('status', [
                    'type' => 'danger',
                    'messages' => $message
                ]);
            }

            return redirect()->back()->with('status', [
                'type' => 'success',
                'messages' => "Import thành công"
            ]);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
     

    public function export(Request $request)
    {
        $input = $request->all();

        $dataQuery = GradeGdqp::query();

        if (!empty($input['term_name'])) {
            $dataQuery = $dataQuery->where('term_name', $input['term_name']);
        }

        if (!empty($input['study_status'])) {
            $dataQuery = $dataQuery->where('study_status', $input['study_status']);
        }

        $datas = $dataQuery->get();
        $status = config('status')->trang_thai_hoc;
        foreach ($datas as $key => $data) {

            $datas[$key]['study_status'] =  $status[$data->study_status]['value'];
            $datas[$key]['status'] = $data->status == 1 ? 'Pass' : ($data->status == 2 ? 'Fail' : '');
        }
        return Excel::download(new DanhSachGDQP($datas), 'Danh_sach_gdqp_'. time() .'.xlsx');
    }

   
}
