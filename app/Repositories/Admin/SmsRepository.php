<?php

namespace App\Repositories\Admin;

use App\Repositories\BaseRepository;
use Illuminate\Http\Request;
use App\Models\Fu\User;
use Illuminate\Support\Facades\Auth;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx as ReaderXlxs;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Models\Sms\StudentSmsLog;
use App\Models\Sms\Account;
use App\Models\Sms\StudentSms;
use App\Models\Sms\StudentSmsList;
use App\Models\Sms\StudentSmsFormat;
use App\Models\Sms\StudentSmsCategory;
use App\Models\Sms\TelecoNumber;
use Carbon\Carbon;
use App\Models\Sms\StudentSmsPermission;
use App\Models\Sms\StudentSmsRole;
use App\TechAPI\SmsBase;
use Illuminate\Support\Facades\Log;
use App\Models\Fu\Term;
use App\Jobs\SendSms;
use App\Models\Sna\Sms\SnaSmsWarning;
Use App\Models\Dra\T1Role;
use App\Models\Dra\T1UserRole;

class SmsRepository extends BaseRepository
{
    public function getModel()
    {
        return User::class;
    }

    public function getPermissionSms()
    {
        $user_login = Auth::user()->user_login;
        $query_permission = StudentSmsPermission::where('student_sms_permission.user_login',$user_login)->leftJoin('student_sms_role', 'student_sms_permission.role_id', '=','student_sms_role.id')
        ->select('student_sms_role.sms_role_code')->get()->toArray();
        $permission = array_map(function($permission){
            return $permission['sms_role_code'];
        },$query_permission);
        return response(['status'=> 'success' ,'permission' => $permission, "user_login" => $user_login], 200);
    }

    public function getListRole()
    {
        $arrIdRole = [78, 79, 80, 81];
        $listRole = T1Role::select(['id','role_name'])->whereIn('id', $arrIdRole)->get();
        return response(['listRole'=> $listRole], 200);
    }
    public function index()
    {
        return $this->view('sms_by_template.index');
    }

    public function viewFormatSms()
    {
        return $this->view('sms_by_template.format_sms');

    }

    public function getSmsCategory()
    {
        return StudentSmsCategory::where('id', '!=', '4')->get();
    }

    public function getSmsFormat()
    {
        $format_list = StudentSmsFormat::whereNotIn('id', [10,12,14,16])->get()->groupBy('category_id');
        return response(['status'=> 'success' ,'format_list' => $format_list], 200);
    }

    public function getFormatList(Request $request)
    {
        $format_list = StudentSmsFormat::query();
        $format_list->leftJoin('student_sms_category','student_sms_format.category_id','=','student_sms_category.id');
        if(isset($request->sms_category)){
            $format_list->where('student_sms_format.category_id',$request->sms_category);
        }
        $format_list->select('student_sms_format.id', 'student_sms_category.category_name', 'student_sms_format.sms_content', 'student_sms_format.description');
        $data = $format_list->get();
        return json_encode($data);
    }

    public function getListTelco()
    {
        $list_telco = TelecoNumber::get();
        return json_encode($list_telco);
    }

    public function getDataSmsList($request)
    {
        $number_paginate = 8;

        $student_sms_list =  StudentSmsList::query();

        if(isset($request->sms_status)){
            $student_sms_list->where('student_sms_list.status_list',$request->sms_status);
        }
        if(isset($request->sms_category)){
            $student_sms_list->where('student_sms_list.category_id',$request->sms_category);
        }
        if(isset($request->sms_format)){
            $student_sms_list->where('student_sms_list.format_id',$request->sms_format);
        }
        if(isset($request->update_by)){
            $student_sms_list->where('student_sms_list.update_by',$request->update_by);
        }
        if(isset($request->role)){
            $student_sms_list->where('student_sms_list.role_id',$request->role);
        }
        if (isset($request->datetime_from)) {
            if ($request->datetime_from != 0 && $request->datetime_from != null && $request->datetime_from != "") {
                $student_sms_list->whereDate('student_sms_list.created_at', '>=', $request->datetime_from);
            }
        }
        if (isset($request->datetime_to)) {
            if ($request->datetime_to != 0 && $request->datetime_to != null && $request->datetime_to != "") {
                $student_sms_list->whereDate('student_sms_list.created_at', '<=', $request->datetime_to);
            }
        }
        $student_sms_list->select([
            'student_sms_list.id as id',
            'student_sms_list.list_name',
            'student_sms_category.id as category_id',
            'student_sms_category.category_name',
            'student_sms_category.number_review',
            'student_sms_format.sms_content',
            DB::raw('(CASE
                WHEN student_sms_list.role_id = "78" THEN "Phòng TC&QLDT"
                WHEN student_sms_list.role_id = "79" THEN "Phòng CTSV"
                WHEN student_sms_list.role_id = "80" THEN "Phòng DVSV"
                WHEN student_sms_list.role_id = "81" THEN "Phòng hành chính/kế toán"
                WHEN student_sms_list.role_id = "53" THEN "Phòng Khảo thí"
                ELSE "" END) AS role_name'),
            DB::raw('(CASE
                WHEN student_sms_list.status_list = "0" THEN "Chờ duyệt"
                WHEN student_sms_list.status_list = "1" THEN "Đã duyệt"
                WHEN student_sms_list.status_list = "2" THEN "Đã gửi"
                WHEN student_sms_list.status_list = "3" THEN "Đang gửi"
                ELSE "Từ chối" END) AS status'),
            'student_sms_list.review',
            'student_sms_list.status_list',
            'student_sms_list.description',
            'student_sms_list.created_by',
            'student_sms_list.updated_by',
            'student_sms_list.send_time',
            'student_sms_list.created_at',
            'student_sms_list.updated_at',
            'count_sms_list as quantity'
        ]);

        $student_sms_list->leftJoin('student_sms_category', 'student_sms_list.category_id','=', 'student_sms_category.id')
        ->leftJoin('student_sms_format', 'student_sms_list.format_id', '=', 'student_sms_format.id')
        ->orderBy('student_sms_list.id','DESC');

        $data =  $student_sms_list->paginate($number_paginate);
        return response(['status'=> 'success' ,'student_sms_list' => $data], 200);
        
    }

    public function getSmsListDetail(Request $request)
    {
        $number_paginate = 10;
        $data_sms_list = StudentSms::where('list_id',$request->list_id)
        ->select([
            '*',
            DB::raw('(CASE
            WHEN status = "0" THEN "Chờ gửi"
            WHEN status = "1" THEN "Đã gửi"
            WHEN status = "-1" THEN "Đã huỷ"
            WHEN status = "-2" THEN "Gửi lỗi"
            END) AS status_sms'),
        ]);
        if(isset($request->search_data['status_sms_sended']) && $request->search_data['status_sms_sended'] != null){
            $data_sms_list->where("status", $request->search_data['status_sms_sended']);
        }
        if(isset($request->search_data['input_search']) && $request->search_data['input_search'] != null){
            $data_sms_list->where($request->search_data['search_option'], $request->search_data['input_search']);
        }
        $data =  $data_sms_list->paginate($number_paginate);
        return response(['status'=> 'success' ,'sms_list_detail' => $data], 200);
    }

    public function createFormat(Request $request)
    {
        try{
            $check_trung = StudentSmsFormat::where('category_id',$request->sms_category)->select('sms_content')
            ->where('sms_content','=',$request->sms_prototype)->get();
            if(count($check_trung) !== 0 ){
                return response(['status' => true, 'notification'=> 'Tạo mẫu thất bại! Bản mẫu tin nhắn đã có sẵn'], 200);
            }else{
                $item = [
                    'category_id' => $request->sms_category,
                    'sms_content' => $request->sms_prototype,
                    'description' => $request->description,
                ];
                $format_id = StudentSmsFormat::insertGetId($item);
                // Tạo log
                $this->createLogSms('create', 'create_format_sms_'.$format_id);
                $newFormatSms = StudentSmsFormat::where('student_sms_format.id',$format_id)->leftJoin('student_sms_category','student_sms_format.category_id','=','student_sms_category.id')
                ->select('student_sms_category.category_name', 'student_sms_format.sms_content', 'student_sms_format.description')->first();
                return response(['status' => true, 'notification'=>'Tạo mẫu tin nhắn mới thành công!', 'newFormatSms'=>$newFormatSms], 200);
            }
        }
        catch(\Exception $e){
            error_log($e);
            Log::channel('sms')->error($e);
            throw new \Exception($e);
        }
    }

    public function importDataSms(Request $request)
    {
        try {
            DB::beginTransaction();
            $now = Carbon::now('Asia/Ho_Chi_Minh');
            $list_id = $this->createListSms($request,$now);
            // Tạo log
            $this->createLogSms('create', 'create_list_sms_'.$list_id);

            $this->createStudentSms($request->dataInput, $list_id, $now);
            DB::commit();
            Log::channel('sms')->info('Import list sms Id = ' . $list_id);
            return response(['status'  => true, 'notification'=> 'Tạo danh sách tin nhắn thành công!'], 200);
        } catch (\Throwable $e) {
            error_log($e);
            DB::rollBack();
            Log::channel('sms')->error($e);
            return response(['status' => $e], 500);
        }
    }

    public function fillDataStudent($request)
    {
        $arr_student = [];
        foreach (array_chunk($request->array_student_code, 500) as $arr ){
            $list_data_student = [];
            $list_data_student = Account::whereIn('student_code', $arr)->where('is_active', '1')
            ->where('owner_type', $request->owner_type)
            ->leftJoin('teleco_number', DB::raw('SUBSTRING(account.phone, 1, 3)'), '=', 'teleco_number.number')
            ->with(['user' => function($query){
                $query->select('user.user_code',
                DB::raw("CONCAT(TRIM(user.user_surname),' ', TRIM(user.user_middlename),' ',TRIM(user.user_givenname)) AS student_name"));
            }])
            ->select(
                // 'account.student_name',
                'account.student_code',
                'account.phone',
                'teleco_number.code as telco'
            )->orderBy('account.created_on', 'DESC')->get()->groupBy('student_code');
            foreach ($list_data_student as $key => $value) {
                $arr_student[$key] = $value[0];
                $arr_student[$key]['student_name'] = $value[0]['user']['student_name'] ?? "";
            }
        }
        return $arr_student;
    }    

    public function createListSms($formData,$now)
    {
        try {
            $list_data = [
                'list_name' => $formData->list_name,
                'category_id' => $formData->sms_category,
                'format_id' => $formData->sms_format['id'] ?? null,
                'description' => $formData->description,
                'created_by' => Auth::user()->user_login,
                'created_at' => $now,
                'updated_at' => $now,
                'count_sms_list' => count($formData->dataInput)
            ];
            $list_id = StudentSmsList::insertGetId($list_data);
            return $list_id;
        } catch (\Throwable $e) {
            error_log($e);
            Log::channel('sms')->error($e);
            throw new \Exception($e);
        }
    }

    public function createStudentSms($dataSms, $list_id, $now)
    {
        $dataImport = [];
        for($i = 0; $i< count($dataSms); $i++){
            $dataImport[] = [
                'student_name'=> $dataSms[$i]['student_fullname'] ?? null,
                'student_code'=> $dataSms[$i]['student_code'] ?? null,
                'student_phone'=> $dataSms[$i]['student_telephone'] ?? null,
                'telco'=> $dataSms[$i]['telco'] ?? null,
                'sms_content'=> $this->convert_sms_content($dataSms[$i]['sms_content']) ?? null,
                'list_id' => $list_id,
                'sna_warning_id' => $dataSms[$i]['warning_id'] ?? null,
                'group_id' => $dataSms[$i]['group_id'] ?? null,
                'attendance_absent' => $dataSms[$i]['attendance_absent'] ?? null,
                'created_at' => $now,
                'updated_at' => $now
            ];
        }
        foreach (array_chunk($dataImport,200) as $data)
        {
            StudentSms::insert($data);
        }
    }

    public function convert_sms_content($str) {
		$str = preg_replace("/(à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ)/", 'a', $str);
		$str = preg_replace("/(è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ)/", 'e', $str);
		$str = preg_replace("/(ì|í|ị|ỉ|ĩ)/", 'i', $str);
		$str = preg_replace("/(ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ)/", 'o', $str);
		$str = preg_replace("/(ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ)/", 'u', $str);
		$str = preg_replace("/(ỳ|ý|ỵ|ỷ|ỹ)/", 'y', $str);
		$str = preg_replace("/(đ)/", 'd', $str);
		$str = preg_replace("/(À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ)/", 'A', $str);
		$str = preg_replace("/(È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ)/", 'E', $str);
		$str = preg_replace("/(Ì|Í|Ị|Ỉ|Ĩ)/", 'I', $str);
		$str = preg_replace("/(Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ)/", 'O', $str);
		$str = preg_replace("/(Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ)/", 'U', $str);
		$str = preg_replace("/(Ỳ|Ý|Ỵ|Ỷ|Ỹ)/", 'Y', $str);
		$str = preg_replace("/(Đ)/", 'D', $str);
		// $str = preg_replace("/(\“|\”|\‘|\’|\,|\!|\&|\;|\@|\#|\%|\~|\`|\=|\_|\'|\]|\[|\}|\{|\)|\(|\+|\^)/", '-', $str);
		// $str = preg_replace("/( )/", '-', $str);
		return $str;
	}

    public function setSendTime(Request $request)
    {
        try {
            $this->createLogSms('update', 'set_a_timer_list_'.$request->id);
            StudentSmsList::where('id',$request->id)->update([
                'send_time' => $request->send_time,
            ]);
            return response(['status' => 'success', 'notification'=> 'Cài đặt thời gian thành công!'],200);
        } catch (\Throwable $e) {
            error_log($e);
            Log::channel('sms')->error($e);
            throw new \Exception($e);
        }
    }

    public static function sendNow($sms_id, $count_list)
    {
        try { 
            $sms = StudentSms::where('id',$sms_id)->first();
            if($count_list == 1){
                StudentSmsList::where('id',$sms['list_id'])->update([
                    'status_list' => 2,
                ]);
            }
            if($sms['status'] == 0){
                if($sms['student_phone'] != null && $sms['student_phone'] !="" && $sms['sms_content'] != null && $sms['sms_content'] != "" ){
                    $arr_sms = [];
                    $valid_data = [
                        'phone' => $sms['student_phone'],
                        'content' => $sms['sms_content']
                    ];
                    array_push($arr_sms, $valid_data);
                    
                    $client_id = SmsBase::$campus['id'];
                    $sceret = SmsBase::$campus['secret'];
                    $scope = [
                        'send_brandname_otp'
                    ];
                    $authenticated = SmsBase::authenticate($client_id, $sceret, $scope);
                    $result = SmsBase::sendBrandNameOtp($arr_sms, $authenticated);
                    Log::channel('sms')->info($result);
                    if(isset($result['success']) && count($result['success']) > 0){
                        // Cập nhật trạng thái tin nhắn gửi thành công
                        StudentSms::where('id', $sms_id)->update(['status' => 1]); 
                        if($sms['sna_warning_id'] != null){
                            // Cập nhật trạng thái đã thông báo
                            SnaSmsWarning::where('student_code', $sms['student_code'])
                            ->where('group_id', $sms['group_id'])
                            ->where('attendance_absent', $sms['attendance_absent'])
                            ->update(['notified' => 1]);
                        }
                    }
                    // Cập nhật mã lỗi nếu có
                    if(isset($result['fail']) && count($result['fail']) > 0){
                        foreach ($result['fail'] as $data){
                            StudentSms::where('id', $sms_id)->update([
                                'error_log' => $data[0],
                                'status' => -2
                            ]);
                        };
                    }
                }else{
                    StudentSms::where('id', $sms_id)->update([
                        'status' => -2
                    ]);
                    Log::channel('sms')->info(["sms_id"=>$sms_id, "status"=> "Tin nhắn không hợp lệ"]);
                }

            }
            
        } catch (\Throwable $th) {
            Log::channel('sms')->error($th);
            StudentSms::where('id', $sms_id)->update([
                'status' => -2
            ]);
        }
    }
    public function reviewListSms($id)
    {
        try {
            $this->createLogSms('review', 'review_list_sms_'.$id);
            StudentSmsList::where('id',$id)->update([
                'review' => 1,
            ]);
            return response(['status' => true, 'notification'=>'Đã duyệt lần 1'],200);
        } catch (\Throwable $e) {
            error_log($e);
            Log::channel('sms')->error($e);
            throw new \Exception($e);
        }
    }
    public function acceptListSms(Request $request)
    {
        try {
            $this->createLogSms('accept', 'accept_list_sms_'.$request->id);
            StudentSmsList::where('id',$request->id)->update([
                'review' => 2,
                'status_list' => 1,
                'updated_by'=> Auth::user()->user_login,
            ]);
            return response(['status' => true, 'notification'=>'Đã chuyển trạng thái danh sách sang "Đã duyệt!"'],200);
        } catch (\Throwable $e) {
            error_log($e);
            Log::channel('sms')->error($e);
            throw new \Exception($e);
        }
    }

    public function cancelListSms(Request $request)
    {
        try {
            $this->createLogSms('cancel', 'cancel_list_sms_'.$request->id);
            StudentSmsList::where('id',$request->id)->update([
                'status_list' => -1,
            ]);
            StudentSms::where('list_id',$request->id)->update([
                'status' => -1,
            ]);
            return response(['status' => true, 'notification'=>'Đã chuyển trạng thái danh sách sang "Huỷ"!'],200);
        } catch (\Throwable $e) {
            error_log($e);
            Log::channel('sms')->error($e);
            throw new \Exception($e);
        }
    }
    public function saveFormatWarning(Request $request)
    {
        try{
            $this->createLogSms('update', 'update_format_sms_warning'.$request->id);
            StudentSmsFormat::where('id', $request->id)->update(['sms_content' => $request->sms_content]);
            return response(["status"=>"success"],200);
        } catch (\Throwable $th) {
            Log::channel('sms')->error($th->getLine() . " : " . $th->getMessage());
            return response(['status' => 'error', 'error' => $th], 500);
        }
    }
    public function sendSms($list_id)
    {
        $now = Carbon::now('Asia/Ho_Chi_Minh');
        $checkMulti = 0;
        $author = auth()->user()->user_login ?? "System";
        if (Cache::has('anti-request-sendSms' . $author)) {
            $checkMulti = Cache::get('anti-request-sendSms' . $author);
            if (time() - $checkMulti < 15) {
                Log::channel('sms')->error(" Tài khoản: " . $author . " Đang spam hệ thống");
                return response(['status' => [
                        'type' => 'danger', 
                        'messages' => 'Bạn đang cố gắng spam vào hệ thống, Tài khoản của bạn sẽ được note lại vào gửi về phía IT chờ xử lý'
                    ]
                ]);
            }
        } else {
            $checkMulti = time();
            Cache::put('anti-request-sendSms' . $author, time(), 90000);
        }
        $this->createLogSms('send', 'send_sms_list'.$list_id);
        try{
            $sms_list = StudentSmsList::where('id',$list_id)->first();
            if($sms_list->status_list == 1){
                StudentSmsList::where('id',$list_id)->update([
                    'send_time' => $now,
                    'status_list'=> 3,
                ]);
                Log::channel('sms')->info([
                    "Send List SMS"=> $list_id,
                    "send_by" => $author,
                ]);
                $smsList = StudentSms::where('list_id',$list_id)->select('id')->where('status', 0)->get()->toArray();
                foreach ($smsList as $key=>$sms){
                    $count_list= count($smsList) - $key;
                    dispatch((new SendSms($sms['id'], $count_list))->onConnection('sms-queue')->onQueue('sms-queue'));
                }
                return response(['status'  => 'success', 'notification'=> 'Gửi thành công!'], 200);
            }else{
                return response(['status'  => 'fail', 'notification'=> 'Danh sách tin nhắn không phù hợp để gửi!'], 200);
            }
        } catch (\Throwable $th) {
            Log::channel('sms')->error('--------- Send SMS --------');
            Log::channel('sms')->error($th->getFile() . ' - ' . $th->getLine() . ' - ' . $th->getMessage());
            Log::channel('sms')->error('---------------------------');
            return response(['status' => 'error', 'error' => $th], 500);
        }
    }
    public function sendSmsTimer($list_id)
    {
        Log::channel('sms')->info(["SendSmsTimer()"=>["list_id" => $list_id]]);
        try{
            $this->sendSms($list_id);
        } catch (\Throwable $th) {
            Log::channel('sms')->error($th->getFile() . ' : ' . $th->getLine() . " : " . $th->getMessage());
            return response(['status' => 'error', 'error' => $th], 500);
        }
        return response(['status'  => 'success', 'notification'=> 'Gửi thành công'], 200);
    }
    public function editSmsList($list_id, $sms_id)
    {
        try{
            $smsList = StudentSmsList::where('id', $list_id)->firstOrFail();
            if($smsList->status_list == 0 && $smsList->review == 0){
                DB::beginTransaction();
                StudentSms::where('id', $sms_id)->delete();
                StudentSmsList::where('id', $list_id)->decrement('count_sms_list',1);
                return response(['status'  => "success", 'notification'=> 'Tin nhắn đã được loại bỏ khỏi danh sách!'], 200);
                DB::commit();
            }else{
                return response(['status'  => "error", 'notification'=> 'Thất bại! Danh sách tin nhắn không tồn tại!'], 200);
            }
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::channel('sms')->error($th->getFile() . ' : ' . $th->getLine() . " : " . $th->getMessage());
            return response(['status' => 'error', 'error' => $th], 500);
        }
        return response(['status'  => "success", 'notification'=> 'Tin nhắn đã được loại bỏ khỏi danh sách!'], 200);
    }

    public function createLogSms($action, $action_detail)
    {
        try {
            $author = Auth::user()->user_login ?? "";
            $dateTime = Carbon::now()->toDateTimeString();
            $currentTerm = Term::whereRaw('startday <= CURRENT_DATE')->whereRaw('endday >= CURRENT_DATE')->first();
            StudentSmsLog::create([
                'relation_login' => $author,
                'action' => $action,
                'action_detail' => $action_detail,
                'term_id' => $currentTerm['id'],
                'created_at' => $dateTime
            ]);
        } catch (\Throwable $th) {
            Log::channel('sms')->error($th->getFile() . ' : ' . $th->getLine() . " : " . $th->getMessage());
            return response(['status' => 'error', 'error' => $th], 500);
        }
    }
    
    public function exportExcelSms($list_id)
    {
        try {
            $list_sms = StudentSms::select([
                'id', 
                'student_name',
                'student_code',
                'student_phone', 
                'telco',
                'sms_content',
                'group_id', 
                'attendance_absent',
                DB::raw('(CASE
                WHEN status = "0" THEN "Chờ gửi"
                WHEN status = "1" THEN "Đã gửi"
                WHEN status = "-1" THEN "Đã huỷ"
                WHEN status = "-2" THEN "Gửi lỗi"
                END) AS status_sms'),
                'created_at',
            ])->where('list_id', $list_id)->orderBy('student_code', 'ASC')->get()->toArray();
            return response(['status' => 'success', 'list_sms' => $list_sms], 200);
        } catch (\Throwable $th) {
            Log::channel('sms')->error($th->getLine() . " : " . $th->getMessage());
            return response(['status' => 'error', 'error' => $th], 500);
        }
    }

    public function reSendSms($list_id = null)
    {
        $now = Carbon::now('Asia/Ho_Chi_Minh');
        $this->createLogSms('resend', 'resend_sms_list'.$list_id);

        try{
            $sms_list = StudentSmsList::where('id',$list_id)->first();
            if($sms_list->status_list == 3){
                Log::channel('sms')->info([
                    "Send List SMS"=> $list_id,
                    "send_by" => auth()->user()->user_login,
                ]);
                $smsList = StudentSms::where('list_id',$list_id)->select('id')->where('status', 0)->get()->toArray();
                foreach ($smsList as $key=>$sms){
                    $count_list= count($smsList) - $key;
                    dispatch((new SendSms($sms['id'], $count_list))->onConnection('sms-queue')->onQueue('sms-queue'));
                }
                return response(['status'  => 'success', 'notification'=> 'Gửi thành công!'], 200);
            }else{
                return response(['status'  => 'fail', 'notification'=> 'Danh sách tin nhắn không phù hợp để gửi!'], 200);
            }
        } catch (\Throwable $th) {
            Log::channel('sms')->error($th->getLine() . " : " . $th->getMessage());
            return response(['status' => 'error', 'error' => $th], 500);
        }
    }
}
