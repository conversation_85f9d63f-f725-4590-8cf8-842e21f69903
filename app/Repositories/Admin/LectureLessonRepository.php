<?php

namespace App\Repositories\Admin;

use App\Http\Lib;
use Carbon\Carbon;
use App\Models\SessionType;
use App\Models\Fu\User;
use App\Models\Fu\Activity;
use App\Models\Fu\Subject;
use App\Models\Fu\Attendance;
use App\Models\Fu\Department;
use Illuminate\Support\Facades\Storage;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportLectureLesson;

class LectureLessonRepository extends BaseRepository
{

    public function getModel()
    {
        return Activity::class;
    }

    public function indexLectureLesson()
    {
        return $this->view('lecture.lesson');
    }

    public function getListLectureLesson($request)
    {
        try {
            $exportExcel = isset($request->exportExcel) ? $request->exportExcel : null;
            $subject_type = $request->subject_type;
            $subject_code = $request->subject_code;
            $type_lesson = $request->type_lesson;
            $department = $request->department;
            $start_date = $request->start_date;
            $end_date = $request->end_date;
            $teacher = $request->teacher;

            // tìm kiếm những môn học Dự án tốt nghiệp
            $graduation_project = Subject::where('subject_code', 'like', 'PRO2%')->get('subject_code');
            $subject_graduation_project = [];
            // gán dữ liệu cho mảng môn học Dự án tốt nghiệp
            foreach ($graduation_project as $project) {
                $subject_graduation_project[] = $project->subject_code;
            }

            // get data lesson
            $list_lecture_lesson = Activity::query()
                // ->where([ ['activity.day', '>=', $start_date], ['activity.day', '<=', $end_date] ])
                ->where([['activity.done', '=', 1], ['activity.session_type', '!=', 18]])
                ->whereNotIn('activity.session_type', [9, 10, 11, 19])
                ->with(['attendance' => function ($query) {
                    // group cho dữ liệu ko quá nhiều, vì chỉ để check != empty
                    $query->groupBy('attendance.activity_id');
                }])
                ->join('list_group', 'list_group.id', '=', 'activity.groupid')
                ->where('list_group.is_virtual', '!=', 1)
                ->join('subject', 'subject.subject_code', '=', 'activity.psubject_code')
                ->whereNotIn('subject.subject_code', $subject_graduation_project);

            // filter data lesson
            if (isset($subject_type) && $subject_type != null) {
                $list_lecture_lesson->where('subject.subject_type', 'like', '%' . $subject_type . '%');
            }
            if (isset($subject_code) && $subject_code != null && $subject_code != 'null') {
                $list_lecture_lesson->where('activity.psubject_code', $subject_code);
            }
            if (isset($type_lesson) && $type_lesson != null) {
                $list_lecture_lesson->where('activity.session_type', $type_lesson);
            }
            if (isset($department) && $department != null) {
                $list_lecture_lesson->where('subject.department_id', $department);
            }
            if (isset($teacher) && $teacher != null && $teacher != 'null') {
                $list_lecture_lesson->where('activity.leader_login', $teacher);
            }
            if (isset($start_date) && $start_date != null) {
                $list_lecture_lesson->where('activity.day', '>=', $start_date);
            }
            if (isset($end_date) && $end_date != null) {
                $list_lecture_lesson->where('activity.day', '<=', $end_date);
            }
            // get data option, nếu exportExcel !- null thì sẽ ko get data vì sẽ kéo dài quá trình
            if ($exportExcel == null) {
                $list_department_lesson = Department::query()
                    ->select(['id', 'department_name'])
                    ->get();

                $list_teacher_lesson = User::query()
                    ->where('user_level', '=', 2)
                    ->select('user_login')
                    ->get();

                $subjectTypeMap = [
                    'Traditional' => 'Học truyền thống',
                    'Integrated'  => 'Học tích hợp',
                    'Online'      => 'Học trực tuyến',
                    'Blended'     => 'Học kết hợp',
                ];
                $subjectTypeKeys = array_keys($subjectTypeMap);
                $list_subject_type_lesson = Subject::query()
                    ->distinct()
                    ->select('subject_type')
                    ->whereIn('subject_type', $subjectTypeKeys)
                    ->get()
                    ->map(function ($item) use ($subjectTypeMap) {
                        return (object)[
                            'subject_type' => $item->subject_type,
                            'subject_type_translated' => $subjectTypeMap[$item->subject_type] ?? $item->subject_type,
                        ];
                    })
                    ->values();

                $list_subject_code = Subject::query()
                    ->whereNotIn('subject_code', $subject_graduation_project)
                    ->select(['subject_code', 'subject_name'])
                    ->get();

                $list_type_lesson = SessionType::query()
                    ->whereNotIn('id', [9, 10, 11, 19])
                    ->select(['session_type', 'id'])
                    ->get();
            }

            $list_lecture_lesson->select([
                'activity.id',
                'activity.leader_login',
                'activity.slot',
                'activity.day',
                'activity.session_description',
                'activity.session_type',
                'list_group.group_name',
                'list_group.id as group_id',
                'subject.subject_code',
                'subject.subject_type',
            ]);

            // export excel
            if (isset($exportExcel) && $exportExcel == 'exportExcel') {
                $list_lecture_lesson = $list_lecture_lesson->get();
                $export = new ExportLectureLesson($list_lecture_lesson);
                return Excel::download($export, 'thong_ke_gio_giang_theo_buoi_hoc.xlsx');
            }
            $list_lecture_lesson = $list_lecture_lesson->paginate(10);

            return response()->json([
                'list_type_lesson' => $list_type_lesson,
                'list_subject_code' => $list_subject_code,
                'list_lecture_lesson' => $list_lecture_lesson,
                'list_teacher_lesson' => $list_teacher_lesson,
                'list_department_lesson' => $list_department_lesson,
                'list_subject_type_lesson' => $list_subject_type_lesson,
            ]);
        } catch (\Throwable $th) {
            Log::error($th);
            return response("Err", 500);
        }
    }
}
