<?php

namespace App\Repositories\Admin;

use App\Exports\ExportExcelDSSVGenerate;
use App\Http\Lib;
use App\Models\Report\File;
use App\Models\Report\3_PartiesConfirmation;
use App\Models\Report\3_PartiesConfirmation_Detail;
use Carbon\Carbon;
use App\Repositories\BaseRepository;
use App\Models\Report\RP\ListStudent as RPListStudent;
use App\Models\Report\FZ\ListStudent as FZListStudent;
use App\Models\Report\FZ\Terms;
use App\Models\Report\Job;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class Report3PartyRepository extends BaseRepository
{

    public function getModel()
    {
        return RPListStudent::class;
    }

    /**
     * gọi api từ bên ap_report và mã hóa
     */
    public function updateListStudentReport() {
        if(Job::where('queue', self::FILE_TYPE_LIST_STUDENT_REALTIME)->count()){
            return response()->json([
                'status' => 400,
                'message' => 'There is a pull data job is running. Please wait for a while.'
            ]);
        } else {
            $api_url = env('AP_REPORT_URL');
            $endpoint = $api_url . '/api/report/update';
            try {
                $response = \Illuminate\Support\Facades\Http::post($endpoint,[
                    "user_login" => auth()->user()->user_login,
                ]);
                return $response;
            } catch (\Throwable $th) {
                Log::error('--error at updateListStudentReport--');
                Log::error($th->getMessage());
                Log::error('--updateListStudentReport--');
                return response([], 500);
            }
        }
    }

    /**
     * lấy danh sách chốt 3 bên
     */
    public function getFu3PartiesConfirmation($request) {
        try {
            $api_url = env('AP_REPORT_URL');
            $endpoint = $api_url . '/api/getFu3PartiesConfirmation';
            $response = \Illuminate\Support\Facades\Http::get($endpoint,[
                "connection" => 'mysql',
                "page" => $request->page,
                "term_id" => $request->term_id
            ]);

            return $response;
        } catch (\Throwable $e) {
            Log::error('--error at getFu3PartiesConfirmation--');
            Log::error($e);
            Log::error('--getFu3PartiesConfirmation--');
            return response([], 500);
        }
    }

    /**
     * lấy danh sách chi tiết chốt 3 bên
     * @param  mixed $id {id của quyết định chốt 3 bên}
     * @return mixed $response
     */
    public function getFu3PartiesConfirmationDetail($id) {
        try {
            $api_url = env('AP_REPORT_URL');
            $endpoint = $api_url . '/api/getFu3PartiesConfirmationDetail';
            $response = \Illuminate\Support\Facades\Http::get($endpoint,[
                "connection" => 'mysql',
                "id" => $id,
            ]);

            return $response;
        } catch (\Throwable $e) {
            Log::error('--error at getFu3PartiesConfirmationDetail--');
            Log::error($e);
            Log::error('--getFu3PartiesConfirmationDetail--');
            return response([], 500);
        }
    }

    public function downloadFileListStudentReport($request) {
        return Storage::disk('bi-files')->download($request->file_url);
    }

    /**
     * thay đổi xác nhận đã chốt 3 bên chưa
     * @param  mixed $type_room {loại phòng chốt}
     * @param  mixed $id {id của quyết định chốt 3 bên}
     * @return mixed $response
     */
    public function changeConfirmType($type_room ,$id) {
        try {
            $api_url = env('AP_REPORT_URL');
            $endpoint = $api_url . '/api/changeConfirmType';
            $response = \Illuminate\Support\Facades\Http::post($endpoint,[
                "user_login" => auth()->user()->user_login,
                "connection" => 'mysql',
                "id" => $id,
                "type_room" => $type_room
            ]);

            return $response;
        } catch (\Throwable $e) {
            Log::error('--error at getFu3PartiesConfirmationDetail--');
            Log::error($e);
            Log::error('--getFu3PartiesConfirmationDetail--');
            return response([], 500);
        }
    }

    /**
     * upload file theo file_dir custom
     *
     * @param  mixed $request {file, dir}
     * @return mixed {file_name, error}
     */
    public function uploadFileToSource($request)
    {
        try {
            $api_url = env('AP_REPORT_URL');
            $endpoint = $api_url . '/api/uploadFileToSource';
            $response = \Illuminate\Support\Facades\Http::attach(
                'file',
                file_get_contents($request->file('file')->getPathname()),
                $request->file('file')->getClientOriginalName()
            )->post($endpoint, [
                "user_login" => auth()->user()->user_login,
                "connection" => 'mysql',
                "id" => $request->id,
                "type_room" => $request->type_file_id,
            ]);

            return $response;
        } catch (\Throwable $th) {
            DB::connection('mysql')->rollBack();
            $th = $th->getMessage();
            Log::error(`uploadFileToSource Repository error: ${$th}`);
            return response([
                'error' => true,
                'file_name' => null,
                'message' => 'Upload file error'
            ], 200);
        }
    }

    public function getInfoLog($request) {
        try {
            $api_url = env('AP_REPORT_URL');
            $endpoint = $api_url . '/api/getInfoLog';
            $response1 = \Illuminate\Support\Facades\Http::get($endpoint,[
                "connection" => 'mysql',
                "action_type" => $request->action_type,
                "type_room" => 1,
                '3_parties_confirmation_id' => $request->3_parties_confirmation_id
            ]);
            $response2 = \Illuminate\Support\Facades\Http::get($endpoint,[
                "connection" => 'mysql',
                "action_type" => $request->action_type,
                "type_room" => 2,
                '3_parties_confirmation_id' => $request->3_parties_confirmation_id
            ]);
            $response3 = \Illuminate\Support\Facades\Http::get($endpoint,[
                "connection" => 'mysql',
                "action_type" => $request->action_type,
                "type_room" => 3,
                '3_parties_confirmation_id' => $request->3_parties_confirmation_id
            ]);
            $log = [];
            $log[] = $response1->json()['log'] == null ? null : $response1->json()['log']['user_login'];
            $log[] = $response2->json()['log'] == null ? null : $response2->json()['log']['user_login'];
            $log[] = $response3->json()['log'] == null ? null : $response3->json()['log']['user_login'];
            return response()->json([
                'log' => $log,
            ]);
        } catch (\Throwable $th) {
            Log::error('getInfoLog Repository error: ' . $th->getMessage() . ' --- Line: ' . $th->getLine() . ' --- File: ' . $th->getFile());
        }
    }
}
