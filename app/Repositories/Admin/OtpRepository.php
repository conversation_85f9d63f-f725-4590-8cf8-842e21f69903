<?php


namespace App\Repositories\Admin;

use App\Models\Sms\Account;
use App\Models\Sna\Sms\Otp;
use App\Repositories\BaseRepository;
use Illuminate\Http\Request;
use App\TechAPI\SmsBase;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
class OtpRepository
{
    public function sendOtp(Request $request) {
        try {
            if (Cache::has("otp_{$request->user_login}")) {
                return [
                    'code' => 200,
                    'content' => 'already sent'
                ];
            } else {
                $this->otpGenerate($request->user_login, $request->phone);
                $result = SmsBase::sendBrandNameOtp([
                    [
                        "phone" => $request->phone,
                        "content" => Cache::get("otp_{$request->user_login}")['otp']
                    ]
                ], $request->campus);
                if (count($result['success']) > 0) {
                    try {
                        $telecom_code = SmsBase::getTelcoCode($request->phone);
                        Otp::on($request->campus)->create([
                            'user_login' => $request->user_login,
                            'phone_number' => $request->phone,
                            'otp' => Cache::get("otp_{$request->user_login}")['otp'],
                            'telco' => $telecom_code
                        ]);
                    } catch (\Throwable $ex) {
                        Log::channel('otp_sms')->error("--------------Start Log Sms Function sendOtp()-------------");
                        Log::channel('otp_sms')->error("Request Params: " . json_encode($request->all()));
                        Log::channel('otp_sms')->error($ex);
                        Log::channel('otp_sms')->error("--------------End Log Sms Function sendOtp()-------------");
                    }
                    return [
                        'code' => 200,
                        'content' => 'Sent otp successfully'
                    ];
                }else{
                    return [
                        'code' => 500,
                        'content' => 'internal server error - cant send otp'
                    ];
                }
            }
        } catch (\Throwable $ex) {
            Log::channel('otp_sms')->error("--------------Start Log Sms Function sendOtp()-------------");
            Log::channel('otp_sms')->error("Request Params: " . json_encode($request->all()));
            Log::channel('otp_sms')->error($ex);
            Log::channel('otp_sms')->error("--------------End Log Sms Function sendOtp()-------------");
            return [
                'code' => 500,
                'content' => 'internal server error - cant send otp'
            ];
        }
    }

    public function validOtp(Request $request) {
        try {
            $user = Account::where([
                'student_login' => $request->user_login,
                'owner_type' => 0
            ])->first();
            if ($user) {
                $failedOtpCount = $user->failed_otp;
                if (Cache::has("otp_{$request->user_login}")) {
                    if (Cache::get("otp_{$request->user_login}")['otp'] == $request->otp) {
                        $phone = Cache::get("otp_{$request->user_login}")['phone'];
                        Cache::forget("otp_{$request->user_login}");
                        $user->failed_otp = 0;
                        $user->save();
                        return [
                            'code' => 200,
                            'message' => 'success',
                            'phone' => $phone
                        ];
                    } else {
                        $user->failed_otp += 1;
                        $failedOtpCount = $user->failed_otp;
                    }
                } else {
                    $user->failed_otp += 1;
                    $failedOtpCount = $user->failed_otp;
                }

                if ($user->failed_otp >= 5) {
                    Cache::forget("otp_{$request->user_login}");
                    Account::where([
                        'student_login' => $request->user_login,
                        'owner_type' => 0
                    ])->update([
                        'is_active' => 0,
                        'failed_otp' => 0
                    ]);
                }
                $user->save();
                return [
                    "code" => 409,
                    "message" => "OTP not exist",
                    "failed_otp" => $failedOtpCount
                ];
            } else {
                return  [
                    "code" => 401,
                    "message" => "User not found"
                ];
            }
        } catch (\Throwable $ex) {
            Log::channel('otp_sms')->error("--------------Start Log Sms Function validOtp()-------------");
            Log::channel('otp_sms')->error("Request Params: " . json_encode($request->all()));
            Log::channel('otp_sms')->error($ex);
            Log::channel('otp_sms')->error("--------------End Log Sms Function validOtp()-------------");
        }
    }

    private function otpGenerate($user_login, $phone) {
        try {
            $otp = rand(100000, 999999);
            if (Cache::has("otp_{$user_login}")) {
                Cache::forget("otp_{$user_login}");
            }
            Cache::put("otp_{$user_login}", [
                "otp" => $otp,
                "phone" => $phone
            ]
            , 90);
        }
        catch (\Throwable $ex) {
            throw new Exception($ex);
            Log::channel('otp_sms')->error("--------------Start Log Sms Function otpGenerate()-------------");
            Log::channel('otp_sms')->error("Params: " . json_encode([
                'user_login' => $user_login,
                'phone' => $phone
            ]));
            Log::channel('otp_sms')->error($ex);
            Log::channel('otp_sms')->error("--------------End Log Sms Function sendOtp()-------------");
        }
    }
}
