<?php


namespace App\Repositories\Admin;


use App\Http\Lib;
use App\Models\Fu\Course;
use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use App\Models\Fu\Subject;
use App\Models\Fu\Term;
use App\Models\Fu\Activity;
use App\Models\T7\CourseGrade;
use App\Models\T7\CourseResult;
use App\Models\T7\Grade;
use App\Models\T7\GradeGroup;
use App\Models\T7\GradeSyllabus;
use App\Models\T7\SyllabusPlan;
use App\Repositories\BaseRepository;
use App\Models\Fu\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Redirect;

class FUGERepository extends BaseRepository
{
    const DAT = 1;
    const KHONG_DAT = 0;
    const TRUOT_DIEM_DANH = -1;
    const CO_DI_THI = 1;
    const CO_DI_THI_2 = 2;
    const KHONG_DI_THI = 0;

    public function getModel()
    {
        return User::class;
    }

    public function exportXMLInfoPoint(Request $request)
    {
        // get base data
        $termName = $request->term_name;
        $teacherName = $request->teacher_name;
        $base = "<?xml version=\"1.0\" encoding=\"utf-8\"?><TeacherGrade xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">";
        $base .= "<Version>1.0</Version><Semester>$termName</Semester><Login>$teacherName</Login><Password></Password>";

        // init data
        $term = Term::where('id', $termName)->where('is_locked', 0)->first();
        $teacher = User::where('user_login', $teacherName)->where('user_level', 2)->first();
        $listGroupId = Activity::select('groupid')
            ->where('leader_login', $teacher->user_login)
            ->where('term_id', $term->id)
            ->groupBy('groupid')
            ->get()
            ->toArray();

        $listGroupId = array_column($listGroupId, 'groupid');
        $groups = Group::where('pterm_id', $term->id)
            ->whereIn('id', $listGroupId)
            ->where('is_virtual', 0)
            ->get();

        $base .= "<SubjectClassGrades>";
        foreach ($groups as $key => $group) {
            $base .= "<SubjectClassGrade>";
            $base .= '<Subject>' . ($group->psubject_code ?? '') . '</Subject>';
            $base .= '<Class>' . ($group->group_name ?? '') . '</Class>';

            $listMember = $group->groupMembers;
            $subject = Subject::find($group->psubject_id);
            $course = Course::where('term_id', $term->id)
                ->where('subject_id', $subject->id)
                ->first();
            $courseGrade = CourseGrade::where('course_id', $course->id)
                ->where('groupid', $group->id)
                ->orderBy('groupid')
                ->orderBy('login')
                ->orderBy('grade_id')
                ->get();
            $groupGrades = Grade::where('syllabus_id', $group->syllabus_id)
                ->orderBy('id')
                ->get();

            $base .= "<Students>";
            foreach ($listMember as $k => $v) {
                $base .= '<Student>';
                $listGradeMember = $courseGrade->filter(function ($item) use ($v) {
                    return ($item->login == $v->member_login);
                });

                $base .= '<Roll>' . ($v->user->user_code ?? '') . '</Roll>';
                $base .= '<Name>' . ($v->user->fullname ?? '') . '</Name>';
                $base .= '<Grades>';
                foreach ($listGradeMember as $grade) {
                    $base .= '<GradeComponent>';
                    $base .= "<Component>$grade->grade_name</Component>";
                    $base .= "<Grade>$grade->val</Grade>";
                    $base .= '</GradeComponent>';
                }

                $base .= '</Grades>';
                $base .= '</Student>';
            }

            $base .= '</Students>';
            $base .= '<Components>';
            foreach ($groupGrades as $grade) {
                $base .= "<string>$grade->grade_name</string>";
            }

            $base .= '</Components></SubjectClassGrade>';
        }

        $base .= '</SubjectClassGrades>';
        $base .= '</TeacherGrade>';


        // if ( isset($request->fg_file) &&  $request->fg_file == 1) {
        return response()->streamDownload(function () use ($base) {
            $characters = str_split($base);
            $binary = [];
            foreach ($characters as $character) {
                $data = unpack('H*', $character);
                // $binary[] = base_convert($data[1], 16, 2);
                $binary[] = ($data[1]);
            }

            echo implode(' ', $binary);
            // }, $teacher->user_login . '-' . $term->term_name . '-' . now()->format('d_m_y_h_i_s') . '.fg');
        }, $teacher->user_login . '-' . $term->term_name . '.fg');
        // }

        return response()->streamDownload(function () use ($base) {
            echo $base;
            // }, $teacher->user_login . '-' . $term->term_name . '-' . now()->format('d_m_y_h_i_s') . '.fg');
        }, $teacher->user_login . '-' . $term->term_name . '.fg');
    }

    public function indexGradeFUGE(Request $request)
    {
        return $this->view('edu.index-fuge', [
            'reports' => [],
            'terms' => Term::where('is_locked', 0)->orderBy('id', 'DESC')->get(),
            'teachers' => User::where('user_level', 2)->orderBy('id', 'DESC')->get(),
        ]);
    }

    public function importGradeFUGE(Request $request)
    {
        ini_set('max_execution_time', -1);
        $userUpdate = Auth::user();
        $dataFile2 = $request->list_file;
        $exists = Storage::disk('fuge')->has($request->term_name . "/cache.json");
        if (!$exists) {
            $fileCache = [];
        } else {
            $fileCache = json_decode(Storage::disk('fuge')->get($request->term_name . "/cache.json"), true);
        }


        if (isset($request->list_file) && count($request->list_file) > 0) {
            $reports = [];
            foreach ($request->list_file as $key => $file) {
                $inforFile = explode('-', $file->getClientOriginalName());
                if (sizeof($inforFile) != 3) {
                    $reports[] = 'File ' . $file->getClientOriginalName() . ' tên không hợp lệ!';
                    continue;
                } else {
                    $reports[] = 'File ' . $file->getClientOriginalName() . ' Đã được lưu đang tiến hành xử lý';
                }

                // $inforFile[2] = now()->format('d_m_y_h_i_s') . '.fg';
                $newName = implode('-', $inforFile);
                $fileCache[] = [
                    'process' => 0,
                    'time' => now(),
                    'auth' => $userUpdate->user_login,
                    'block' => $userUpdate->block_name,
                    'file_name_old' => $file->getClientOriginalName(),
                    'file_name_new' => $newName,
                ];
                Storage::disk('fuge')->putFileAs(
                    $request->term_name . "/" . $userUpdate->user_login,
                    $file,
                    $newName
                );
            }

            Storage::disk('fuge')->put($request->term_name . "/cache.json", json_encode($contents));
            return Redirect::route('edu.fuge.index')->with([
                'reports2' => $reports
            ]);
        }

        if (!isset($request->file)) {
            return Redirect::route('edu.fuge.index')->with([
                'reports2' => ['Không tồn tại file điểm!']
            ]);
        }

        $dataFile = $request->file->getContent();
        $reports = $this->importGrade($dataFile, $request->block_name);
        dd($reports);
        // Kiểm tra sự tồn tại của file
        return Redirect::route('edu.fuge.index')->with([
            'reports2' => $reports
        ]);
    }

    public function importGrade($dataFile, $blockCheck = null)
    {
        $dataFile = implode(' ', explode("\r\n", $dataFile));
        $dataFileArr = explode(' ', $dataFile);
        $dataAfterConvert = array_map(function ($a) {
            try {
                return pack("H*", $a);
            } catch (\Exception $th) {
                dd($a);
            }
        }, $dataFileArr);

        $xmlFile = implode('', $dataAfterConvert);
        $xmlFileData = simplexml_load_string($xmlFile);
        $json = json_encode($xmlFileData);
        $xmlFileData = json_decode($json, TRUE);

        // init data
        $termName = $xmlFileData['Semester'];
        $teacherLogin = $xmlFileData['Login'];
        $subjectClassGrades = $xmlFileData['SubjectClassGrades'];

        // find data form Database
        $term = Term::where('term_name', $termName)->orWhere('id', $termName)->first();
        if (!$term) {
            dd("Kỳ thứ không tồn tại");
        }

        $teacher = User::where('user_login', $teacherLogin)->where('user_level', 2)->first();
        if (!$teacher) {
            dd("giảng viên không tồn tại");
        }

        $listSubjectProcess = [];
        foreach ($subjectClassGrades as $subjectClassGrade) {
            foreach ($subjectClassGrade as $value) {
                if (!isset($listSubjectProcess[$value['Subject']])) {
                    $listSubjectProcess[$value['Subject']] = [
                        'subject_code' => $value['Subject'],
                        'class' => [],
                    ];
                }

                $classAdd = [
                    'class_name' => $value['Class'],
                    'students' => ($value['Students']['Student'] ?? [])
                ];

                $listSubjectProcess[$value['Subject']]['class'][] = $classAdd;
            }
        }

        $listSubjectProcess = array_values($listSubjectProcess);
        DB::beginTransaction();
        try {
            $reports = [];
            foreach ($listSubjectProcess as $key => $value) {
                $dataCheck = 1;
                $dataProcess = call_user_func(function () use ($value, $term) {
                    $gradesCheck = [];
                    $res = [];
                    $course_id = null;
                    foreach ($value['class'] as $group) {
                        $groupObject = Group::where('group_name', $group['class_name'])
                            ->where('list_group.is_virtual', 0)
                            ->where('psubject_code', $value['subject_code'])
                            ->where('pterm_id', $term->id)
                            ->first();

                        $course_id = $groupObject->body_id;
                        if (!isset($gradesCheck[$groupObject->syllabus_id])) {
                            // $gradesCheck[$groupObject->syllabus_id] = Grade::where('syllabus_id', $groupObject->syllabus_id)->orderBy('grade_name')->get();
                            $gradesCheck[$groupObject->syllabus_id] = Grade::select('t7_grade.id', 't7_grade.campus_id', 't7_grade.grade_name', 't7_grade.grade_group_id', 't7_grade.syllabus_id', 't7_grade.syllabus_name', 't7_grade.subject_id', 't7_grade.subject_name', 't7_grade_group.grade_group_name')
                                ->join('t7_grade_group', 't7_grade_group.id', '=', 't7_grade.grade_group_id')
                                ->where('t7_grade.syllabus_id', $groupObject->syllabus_id)
                                ->orderBy('grade_name')
                                ->get();
                        }

                        foreach ($group['students'] ?? [] as $student) {
                            $dataAdd = [
                                $student['Roll'],
                                $group['class_name']
                            ];

                            foreach ($gradesCheck[$groupObject->syllabus_id] as $grade) {
                                $dataAdd[] = call_user_func(function () use ($grade, $student) {
                                    $res = null;
                                    $nameSave = $grade->grade_group_name;
                                    $nameSave = str_replace('&', '-', $nameSave);
                                    $gradeNameSave = str_replace('&', '-', $grade->grade_name);
                                    $checkName = [
                                        "$gradeNameSave",
                                        "[$grade->grade_group_id]$gradeNameSave",
                                        "[$nameSave]$gradeNameSave",
                                        "[ $nameSave ] $gradeNameSave",
                                        "[$grade->grade_group_name]$gradeNameSave",
                                        "[ $grade->grade_group_name ] $gradeNameSave"
                                    ];

                                    foreach ($student['Grades']['GradeComponent'] ?? [] as $value) {
                                        // $checkName = explode(']', $value['Component']);
                                        // $checkName = trim(end($checkName));
                                        // if ($checkName == $gradeNameSave) {
                                        if (in_array($value['Component'], $checkName)) {
                                            $res = is_numeric($value['Grade']) ? (float)$value['Grade'] : null;
                                            break;
                                        }
                                    }

                                    return $res;
                                });
                            }

                            $dataAdd[] = $student['Comment'] ?? null;
                            $res[] = $dataAdd;
                        }
                    }

                    return [
                        'course_id' => $course_id,
                        'res' => $res
                    ];
                });

                $reports = array_merge($reports, $this->importFUGE($dataProcess, $term, $teacher, $value));
                // $reports = array_merge($reports, ["dev $key hihi", "dev $key hihi"]);
            }

            DB::rollback();
            return $reports;
        } catch (\Exception $exception) {
            DB::rollback();
            dd($exception);
        }
    }

    public function importFUGE($data, $term, $teacher, $dataUpdate)
    {
        $reports = [];
        $group_member_array = [];
        $grade_data = [];
        $grade_id_data = [];
        $course_id = $data['course_id'];
        $whoIs = Auth::user()->user_login;
        // $term = Lib::getTermDetails();
        $term = $term->toArray();
        $term['term_id'] = $term['id'];

        $users = $data['res'];
        $course = Course::find($course_id);
        $grades = Grade::where('syllabus_id', $course->syllabus_id)->orderBy('grade_name')->get();
        $grade_length = $grades->count();
        $group_grades = GradeGroup::where('syllabus_id', $course->syllabus_id)->get();
        $groups = Group::where('list_group.is_virtual', 0)->where('body_id', $course->id)->get();
        $grade_bonus = $grades->where('bonus_type', 1)->pluck('max_point', 'id');
        $bonus_i = [];
        foreach ($grades as $grade) {
            $grade_id_data[] = $grade->id;
            if ($grade->bonus_type) {
                $bonus_i[count($grade_id_data) - 1] = $grade->max_point;
            }
        }

        $group_array_name = $groups->pluck('group_name', 'id');
        $group_array_id = $groups->pluck('id', 'id');
        $group_members = GroupMember::whereIn('groupid', $group_array_id)->get();

        foreach ($group_members as $member) {
            $member->load('user');
            $member->user_code = $member->user->user_code;
            $group_name = $groups->firstWhere('id', $member->groupid);
            $group_member_array[$group_name->group_name][$member->user_code] = $member->member_login;
        }

        $group_grade_id_text = $group_grades->implode('id', ',');
        foreach ($users as $key => $item) {
            $current_key = "Dòng $key:";
            if (isset($item[0]) && isset($item[1])) {
                $user_code = $item[0];
                $group_id = $item[1];

                if (!$group_array_name->contains($group_id)) {
                    $reports[] = "Lớp $item[1] - $item[0] Mã lớp không thuộc khoá học này";
                    continue;
                }
                if (!isset($group_member_array[$group_id][$user_code])) {
                    $reports[] = "Lớp $item[1] - $item[0] Sinh viên không thuộc khoá học này";
                    continue;
                }
                for ($i = 0; $i < $grade_length; $i++) {
                    $point = $item[$i + 2];
                    if ($point === null) {
                        continue;
                    }
                    if (isset($bonus_i[$i])) {
                        if ($point < 0 || $point > $bonus_i[$i]) {
                            $reports[] = "Lớp $item[1] - $item[0] Điểm thưởng không nằm trong phạm vi 0-" . $bonus_i[$i];
                            continue;
                        }
                    } else {
                        if ($point < 0 || $point > 10) {
                            $reports[] = "Lớp $item[1] - $item[0] Điểm số không nằm trong phạm vi 0-10";
                            continue;
                        }
                    }
                    $grade_data[$group_id][$group_member_array[$group_id][$user_code]][$grade_id_data[$i]] = $point;
                }
            }
        }

        if (count($grade_data)) {
            foreach ($grade_data as $key => $data) {
                $group_id = $groups->firstWhere('group_name', $key)->id;
                // kiểm tra các lớp thi lại
                $groupCheck = $group = Group::where('is_virtual', 0)
                    ->where('id', $group_id)
                    ->count();
                if ($groupCheck == 0) {
                    continue;
                }

                $groupObj = $groups->firstWhere('group_name', $key);
                $syllabus_grade_info = $this->gradeInformationFUGE($group_grade_id_text, $groupObj->id);
                $this->createOrUpdateCourseGradeFUGE($syllabus_grade_info, $groupObj, $whoIs, $course_id, $term, $data);
                $this->syncDataSaveGradePointFUGE($group_id);
            }
        }

        $reports[] = "Môn " . $dataUpdate['subject_code'] . " quá trình import hoàn tất";
        return $reports;
    }

    /**
     *
     */

    public function createOrUpdateCourseGradeFUGE($syllabus_grade_info, $groupObj, $whoIs, $course_id, $term, $grades = [])
    {
        $reports = [];
        $grade_lock = [];
        foreach ($grades as $user_login => $grade) {
            foreach ($grade as $grade_id => $point) {
                if ($point == "" || $point == null) {
                    continue;
                }

                $has_point = CourseGrade::where('groupid', $groupObj->id)->where('grade_id', $grade_id)->where('login', $user_login)->first();
                if ($point !== null && trim($point) != '') {
                    $grade_lock[$grade_id] = $grade_id;
                    if ($has_point) {
                        $old_point = $has_point->val;
                        if ($groupObj->is_lock == 1) {
                            $reports[] = "Lớp $groupObj->group_name ($groupObj->id) Giáo viên cố ý thay đổi điểm " . ($syllabus_grade_info[$grade_id]['grade_name'] ?? "") . " của sinh viên $user_login từ $old_point => $point khi lớp đã khóa!";

                            continue;
                        }

                        if ($has_point->locked == 1) {
                            if ($old_point != $point) {
                                $reports[] = "Lớp $groupObj->group_name ($groupObj->id) Giáo viên cố ý thay đổi điểm " . ($syllabus_grade_info[$grade_id]['grade_name'] ?? "") . " của sinh viên $user_login từ $old_point => $point khi điểm đã khóa!";
                            }
                            continue;
                        }

                        $has_point->modifier_login = $whoIs->user_login ?? $whoIs;
                        $has_point->token = $syllabus_grade_info[$grade_id]['token'];
                        $has_point->val = $point;
                        $has_point->locked = 1;
                        $has_point->grade_minimum_required = $syllabus_grade_info[$grade_id]['minimum_required'];
                        $has_point->grade_weight = $syllabus_grade_info[$grade_id]['weight'];
                        $has_point->grade_name = $syllabus_grade_info[$grade_id]['grade_name'];
                        $has_point->save();
                        if ($old_point != $point) {
                            $this->systemLog('group', 'update', "update grade: " . $syllabus_grade_info[$grade_id]['grade_name'] . " for student $user_login with value: $point, old value: $old_point", $user_login, $groupObj->id, 0, '', '', 'grade');
                            $reports[] = "update grade: " . $syllabus_grade_info[$grade_id]['grade_name'] . " for student $user_login with value: $point, old value: $old_point";
                        }
                    } else {
                        CourseGrade::create([
                            'course_id' => $course_id,
                            'grade_id' => $grade_id,
                            'grade_minimum_required' => $syllabus_grade_info[$grade_id]['minimum_required'],
                            'grade_weight' => $syllabus_grade_info[$grade_id]['weight'],
                            'grade_name' => $syllabus_grade_info[$grade_id]['grade_name'],
                            'groupid' => $groupObj->id,
                            'login' => $user_login,
                            'val' => $point,
                            'comment' => '',
                            'creator_login' => $whoIs->user_login ?? $whoIs,
                            'modifier_login' => $whoIs->user_login ?? $whoIs,
                            'grade_group_id' => $syllabus_grade_info[$grade_id]['grade_group_id'],
                            'grade_group_name' => $syllabus_grade_info[$grade_id]['grade_group_name'],
                            'grade_group_weight' => $syllabus_grade_info[$grade_id]['grade_group_weight'],
                            'grade_group_minimum_required' => $syllabus_grade_info[$grade_id]['grade_group_minimum_required'],
                            'syllabus_id' => $syllabus_grade_info[$grade_id]['syllabus_id'],
                            'subject_id' => $syllabus_grade_info[$grade_id]['subject_id'],
                            'is_used' => 0,
                            'subject_name' => $syllabus_grade_info[$grade_id]['subject_name'],
                            'course_group_name' => '',
                            'subject_code' => '',
                            'term_id' => $term['term_id'],
                            'term_name' => $term['term_name'],
                            'is_final' => $syllabus_grade_info[$grade_id]['is_final_exam'],
                            'is_resit' => $syllabus_grade_info[$grade_id]['is_resit'],
                            'master_grade' => $syllabus_grade_info[$grade_id]['master_grade'],
                            'token' => $syllabus_grade_info[$grade_id]['token'],
                            'group_val' => 0,
                            'temp' => 0,
                            'locked' => 1,
                        ]);
                        $this->systemLog('group', 'insert', "insert grade: " . $syllabus_grade_info[$grade_id]['grade_name'] . " for student $user_login with value: $point", $user_login, $groupObj->id, 0, '', '', 'grade');
                    };
                } else {
                    if ($has_point) {
                        $old_point = $has_point->val;
                        $has_point->delete();
                        $reports[] = "delete grade: " . $syllabus_grade_info[$grade_id]['grade_name'] . " for student $user_login with value: $old_point";
                        $this->systemLog('group', 'delete', "delete grade: " . $syllabus_grade_info[$grade_id]['grade_name'] . " for student $user_login with value: $old_point", $user_login, $groupObj->id, 0, '', '', 'grade');
                    }
                }
            }
        }

        return $reports;
        //        $this->gradeLockProcess($group_id, $grade_lock);
    }


    public function syncDataSaveGradePointFUGE($group_id)
    {
        echo "$group_id<br>";
        $member_detail = [];
        $temp_grade_group = [];
        $temp_grade = [];
        $diem_thuong_id = [];
        $status_group = 0;
        $tong_dau_diem_thi = 0;
        $group = Group::where('list_group.is_virtual', 0)->with('groupMembers')->find($group_id);
        $syllabus = GradeSyllabus::findOrFail($group->syllabus_id);
        $syllabus_minimum = $syllabus->minimum_required;
        $checkUserdRemote2 = Term::where('id', $group->pterm_id)
            ->where('startday', '>', '2022-05-01')
            ->count();
        $arrSessionType = [8, 9, 10, 11, 18];
        if ($checkUserdRemote2 > 0) {
            $arrSessionType = [8, 9, 10, 11, 18, 21];
        }

        $syllabus_plan = SyllabusPlan::select('id')
            ->leftJoin('session_type', 't7_syllabus_plan.session_type', '=', 'session_type.id')
            ->where('t7_syllabus_plan.syllabus_id', $group->syllabus_id)
            ->where('session_type.is_exam', 0)
            ->count();
        $grade_groups = GradeGroup::where('syllabus_id', $group->syllabus_id)->where('subject_id', $group->psubject_id)->get();
        $grades_table = Grade::where('syllabus_id', $group->syllabus_id)->get();
        $tong_dau_diem = $grades_table->count();
        $tong_dau_diem_khong_co_diem_thuong = $grades_table->where('bonus_type', 0)->where('master_grade', 0)->count();
        $id_diem_thuong = $grades_table->where('bonus_type', 1)->pluck('id');
        $subject = Subject::findOrFail($group->psubject_id);
        foreach ($grade_groups as $grade_group) {
            CourseGrade::where('grade_group_id', $grade_group->id)->where('groupid', $group_id)->update([
                'grade_group_weight' => $grade_group->weight,
                'grade_group_minimum_required' => $grade_group->minimum_required,
                'grade_group_name' => $grade_group->grade_group_name,
            ]);
            $temp_grade_group[$grade_group->id] = [
                'grade_name' => $grade_group->grade_group_name,
                'point' => 0,
                'total_point' => 0,
                'weight' => $grade_group->weight,
                'number_grade' => $grade_group->number_grade,
                'minimum_required' => $grade_group->minimum_required,
            ];
        }

        foreach ($grades_table as $grade) {
            CourseGrade::where('grade_id', $grade->id)->where('groupid', $group_id)->update([
                'grade_weight' => $grade->weight,
                'grade_minimum_required' => $grade->minimum_required,
                'grade_name' => $grade->grade_name,
            ]);
            if ($grade->is_final_exam && $grade->master_grade == 0) {
                $tong_dau_diem_thi += 1;
            }

            $temp_grade[$grade->id] = [
                'bonus_type' => $grade->bonus_type,
            ];
            if ($grade->bonus_type) {
                $diem_thuong_id[$grade->id] = $grade->id;
            }
        }

        $course_grades = CourseGrade::where('groupid', $group->id)->orderBy('master_grade')->get();
        $tong_diem_hoan_thanh_cua_lop = $course_grades->whereNotIn('grade_id', $id_diem_thuong)->groupBy('grade_id')->count();
        foreach ($course_grades as $course_grade) {
            $member_detail[$course_grade->login][] = [
                'grade_name' => $course_grade->grade_name,
                'grade_id' => $course_grade->grade_id,
                'grade_group_id' => $course_grade->grade_group_id,
                'point' => $course_grade->val,
                'master_grade' => $course_grade->master_grade,
                'weight' => $course_grade->grade_weight,
                'minimum_required' => $course_grade->grade_minimum_required,
                'is_final' => $course_grade->is_final,
                'is_resit' => $course_grade->is_resit,
                'grade_group_weight' => $course_grade->grade_group_weight,
            ];
        }

        foreach ($group->groupMembers as $member) {
            $temp = $temp_grade_group;
            $user_login = $member->member_login;
            $total_point = 0;
            $bonus_point = 0;
            $status_subject = 0;
            $grade_pass = 1;
            $exam_finish = 0;
            $exam_finish_grade = [];
            $tong_diem_hoan_thanh = 0;
            $tong_diem_thuong_hoan_thanh = 0;
            $grade_array = [];
            $di_thi = self::KHONG_DI_THI;
            $gradeGroupIdOfFinalScore = null;
            $gradeIdOfFinalScore = null;
            $flagCheckHaveResit = false;
            $course_result = $this->createOrUpdateCourseResult($group, $member->member_login, $subject);
            if (isset($member_detail[$member->member_login])) {
                $current_status_subject = $course_result->val;
                $grades = $member_detail[$member->member_login];
                $temp_grade_detail = [];
                $final_total_point = 0;
                foreach ($grades as $grade) {
                    if (!isset($diem_thuong_id[$grade['grade_id']])) {
                        $tong_diem_hoan_thanh += 1;
                    } else {
                        $tong_diem_thuong_hoan_thanh += 1;
                    }

                    $master_grade = $grade['master_grade'];
                    $grade_array[] = $grade['grade_name'] . ':' . $grade['weight'] . ':' . $grade['point'] . ':' . $grade['grade_id'];
                    if ($master_grade == 0) {
                        if ($temp_grade[$grade['grade_id']]['bonus_type'] == 1) {
                            $bonus_point += $grade['point'];
                        } else {
                            $temp_grade_detail[$grade['grade_group_id']][$grade['grade_id']] = ($grade['point'] * $grade['weight']);
                            if (!isset($temp[$grade['grade_group_id']]['point'])) {
                                $temp[$grade['grade_group_id']]['point'] = ($grade['point'] * $grade['weight']);
                            } else {
                                $temp[$grade['grade_group_id']]['point'] += ($grade['point'] * $grade['weight']);
                            }
                        }
                    } else {
                        $grade_pass = 1;
                        CourseGrade::where('groupid', $group->id)->where('login', $user_login)->where('grade_id', $master_grade)->update([
                            'is_used' => -1
                        ]);
                        $temp_grade_detail[$grade['grade_group_id']][$master_grade] = ($grade['point'] * $grade['weight']);
                        $temp[$grade['grade_group_id']]['point'] = ($grade['point'] * $grade['weight']);
                    }

                    if (!isset($temp[$grade['grade_group_id']]['total_point'])) {
                        $temp[$grade['grade_group_id']]['total_point'] = ($grade['point']);
                    } else {
                        $temp[$grade['grade_group_id']]['total_point'] += ($grade['point']);
                    }

                    // check + diem final 1 hay final 2nd vao tong
                    if ($grade['is_final'] === 1) {

                        if ($grade['is_resit'] < 1) {
                            $final_total_point = ($grade['point']);
                            $gradeGroupIdOfFinalScore = $grade['grade_group_id'];
                            $gradeIdOfFinalScore = $grade['grade_id'];
                        } else {

                            if ($grade['point'] !== null) {
                                $temp[$grade['grade_group_id']]['total_point'] -= $final_total_point;
                                $flagCheckHaveResit = true;
                            }
                        }
                    }

                    if ($grade['minimum_required'] != 0 && $grade['minimum_required'] > $grade['point']) {
                        $grade_pass = 0;
                    }

                    if ($grade['is_final'] && $grade['point']) {
                        $di_thi = self::CO_DI_THI;
                        $status_group = 1;
                    }

                    if ($grade['is_resit'] == 1) {
                        $di_thi = self::CO_DI_THI_2;
                    }

                    if ($grade['is_final'] && $grade['is_resit'] == 1) {
                        $status_group = 2;
                    }

                    if ($grade['is_final'] && $grade['is_resit'] == 0) {
                        $exam_finish += 1;
                        $exam_finish_grade[$grade['grade_id']] = $grade['grade_id'];
                    }
                }
                foreach ($temp_grade_detail as $key => $sub_item) {

                    if (isset($temp[$key]['point'])) {
                        // gán cờ để kiểm tra nếu ko có điểm 2nd thì sẽ + điểm đầu vào
                        if ($key === $gradeGroupIdOfFinalScore && !$flagCheckHaveResit) {
                            $temp[$key]['point'] = array_sum($sub_item);
                        }
                    }
                }

                foreach ($temp as $key => $item) {
                    if ($item['weight'] == 0) {
                        $point = 0;
                    } else {
                        $point = $item['point'] / $item['weight'];
                    }

                    // $point = round($point, 1);
                    $pointCheck = round($point, 1);
                    CourseGrade::where('login', $user_login)->where('groupid', $group->id)->where('grade_group_id', $key)->update([
                        'group_val' => $point,
                    ]);
                    $total_point += ($point * $item['weight']) / 100;
                    if ($item['minimum_required'] != 0 && $item['minimum_required'] > $pointCheck) {
                        $grade_pass = 0;
                    }
                }

                $total_point = $total_point + $bonus_point;
                if ($total_point > 10) {
                    $total_point = 10;
                }

                if ($total_point) {
                    $total_point = round($total_point, 1);
                }

                $grade_array = implode('$', $grade_array);

                if ($grade_pass && $total_point >= $syllabus_minimum) {
                    $status_subject = self::DAT;
                } else {
                    $status_subject = self::KHONG_DAT;
                }

                if ($current_status_subject < self::KHONG_DAT) {
                    $di_thi = self::CO_DI_THI;
                }

                if ($course_result->attendance_absent == '') {
                    $course_result->attendance_absent = 0;
                }

                if ($exam_finish < $tong_dau_diem_thi && $status_subject >= 0) {
                    $status_subject = self::KHONG_DAT;
                }

                if (count($exam_finish_grade) < $tong_dau_diem_thi && $status_subject > 0) {
                    $status_subject = self::KHONG_DAT;
                    $di_thi = self::KHONG_DI_THI;
                }

                if ($tong_diem_hoan_thanh_cua_lop < $tong_dau_diem_khong_co_diem_thuong && $status_subject > 0) {
                    $status_subject = self::KHONG_DAT;
                    $di_thi = self::KHONG_DI_THI;
                }

                if ($tong_diem_hoan_thanh_cua_lop == $tong_dau_diem_khong_co_diem_thuong && $tong_diem_hoan_thanh > 0) {
                    $di_thi = self::CO_DI_THI_2;
                }
            }

            if ($di_thi == 2) {
                $status_group = 1;
            }

            $attendanceAbsent = $course_result->attendance_absent == null ? 0 : $course_result->attendance_absent;
            $status_subject = ($syllabus_plan ? ($attendanceAbsent * 100 / $syllabus_plan) : 0) > (100 - $syllabus->attendance_cutoff) ? self::TRUOT_DIEM_DANH : $status_subject;
            $course_result->grade = $total_point;
            $course_result->grade_detail = $grade_array;
            $course_result->is_finish = $di_thi;
            $course_result->val = $status_subject;
            $course_result->attendance_cutoff = $syllabus->attendance_cutoff;
            $course_result->minimum_required = $syllabus->minimum_required;
            $course_result->psubject_name = $group->psubject_name;
            $course_result->psubject_code = $group->psubject_code;
            $course_result->subject_id = $group->psubject_id;
            $course_result->start_date = $group->start_date;
            $course_result->end_date = $group->end_date;
            //            $course_result->skill_code = $group->skill_code;
            $course_result->total_session = $syllabus_plan;
            $course_result->total_exam = $tong_dau_diem_thi;
            $course_result->done_exam = count($exam_finish_grade);
            $course_result->taken_exam = $exam_finish;
            $course_result->total_grade = $tong_dau_diem;
            $course_result->done_grade = $tong_diem_hoan_thanh_cua_lop + $id_diem_thuong->count();
            $course_result->save();
        }

        if ($group->type == 1) {
            $group->finished = $status_group;
            $group->save();
        }
    }

    public function createOrUpdateCourseResult($group, $user_login, $subject)
    {
        $result = CourseResult::where('student_login', $user_login)->where('groupid', $group->id)->first();
        if (!$result) {
            $result = new CourseResult();
            $result->student_login = $user_login;
            $result->groupid = $group->id;
        }
        $result->number_of_credit = $subject->num_of_credit;
        $result->skill_code = $subject->skill_code;
        $result->course_id = $group->body_id;
        $result->source = 2;
        $result->subject_id = $group->psubject_id;
        $result->term_id = $group->pterm_id;
        $result->pterm_name = $group->pterm_name;
        $result->pgroup_name = $group->group_name;
        $result->syllabus_id = $group->syllabus_id;
        $result->start_date = $group->start_date;
        $result->end_date = $group->end_date;
        $result->save();

        return $result;
    }

    public function gradeInformationFUGE($group_grade_id_text, $group_id = 0)
    {
        $syllabus_grade_info = [];
        $syllabus_grade_group_info = [];
        $group_grade_id = explode(',', $group_grade_id_text);
        // $syllabus_grade_groups = GradeGroup::whereIn('id', $group_grade_id)->get();
        $syllabus_grade_groups = $this->getListGradeGroup($group_grade_id);
        foreach ($syllabus_grade_groups as $syllabus_grade_group) {
            $syllabus_grade_group_info[$syllabus_grade_group->id] = [
                'id' => $syllabus_grade_group->id,
                'weight' => $syllabus_grade_group->weight,
                'minimum_required' => $syllabus_grade_group->minimum_required,
                'grade_group_name' => $syllabus_grade_group->grade_group_name,
                'syllabus_id' => $syllabus_grade_group->syllabus_id,
                'subject_id' => $syllabus_grade_group->subject_id,
                'subject_name' => $syllabus_grade_group->subject_name,
            ];
        }

        $syllabus_grades = Grade::whereIn('grade_group_id', $group_grade_id)->get();
        $syllabus_grades2 = $this->getListGradeByGrade($group_grade_id, true);
        foreach ($syllabus_grades as $syllabus_grade) {
            $array_type = [0, 1, 2];
            if ($syllabus_grade->grade_type == 2) {
                $is_resit = 1;
            } else {
                $is_resit = 0;
            }

            $syllabus_grade_info[$syllabus_grade->id] = [
                'weight' => $syllabus_grade->weight,
                'is_final_exam' => $syllabus_grade->is_final_exam,
                'master_grade' => $syllabus_grade->master_grade,
                'grade_name' => $syllabus_grade->grade_name,
                'syllabus_name' => $syllabus_grade->syllabus_name,
                'minimum_required' => $syllabus_grade->minimum_required,
                'bonus_type' => $syllabus_grade->bonus_type,
                'grade_group_id' => $syllabus_grade_group_info[$syllabus_grade->grade_group_id]['id'],
                'grade_group_weight' => $syllabus_grade_group_info[$syllabus_grade->grade_group_id]['weight'],
                'grade_group_minimum_required' => $syllabus_grade_group_info[$syllabus_grade->grade_group_id]['minimum_required'],
                'grade_group_name' => $syllabus_grade_group_info[$syllabus_grade->grade_group_id]['grade_group_name'],
                'syllabus_id' => $syllabus_grade_group_info[$syllabus_grade->grade_group_id]['syllabus_id'],
                'subject_id' => $syllabus_grade_group_info[$syllabus_grade->grade_group_id]['subject_id'],
                'subject_name' => $syllabus_grade_group_info[$syllabus_grade->grade_group_id]['subject_name'],
                'is_resit' => $is_resit,
                'token' => substr(md5($group_id . $syllabus_grade->id), 0, 13),
            ];
        }

        return $syllabus_grade_info;
    }

    /**
     * Hàm thêm cache khi đầu điểm
     */
    public function getListGradeByGrade($groupGradeId, $oneArray = false)
    {
        // GradeGroup::whereIn('id', $group_grade_id)->get();
        // $res = Collection::make(new Activity);
        $res = (new Grade)->newCollection();
        foreach ($groupGradeId as $key => $gradeGroupId) {
            if (!Cache::has("grade-by-grade-group-first-$gradeGroupId")) {
                $listGradeCheck = Grade::query()
                ->where('grade_group_id', $gradeGroupId)
                ->get();

                Cache::put("grade-by-grade-group-first-$gradeGroupId", $listGradeCheck, (1200 + (rand() % 200)));
            }

            $listGradeCheck = Cache::get("grade-by-grade-group-first-$gradeGroupId");
            if ($oneArray == true) {
                $res = $res->merge($listGradeCheck);
            } else {
                $res->push($listGradeCheck);
            }
        }

        return $res;
    }

    /**
     * Hàm thêm cache khi nhóm đầu điểm
     */
    public function getListGradeGroup($groupGradeId, $oneArray = false)
    {
        // GradeGroup::whereIn('id', $group_grade_id)->get();
        // $res = Collection::make(new Activity);
        $res = (new GradeGroup)->newCollection();
        foreach ($groupGradeId as $key => $gradeGroupId) {
            if (!Cache::has("group-grade-first-$gradeGroupId")) {
                $listObjActiviesCheck = GradeGroup::query()
                ->where('id', $gradeGroupId)
                ->first();

                Cache::put("group-grade-first-$gradeGroupId", $listObjActiviesCheck, (1200 + (rand() % 200)));
            }

            $listObjActiviesCheck = Cache::get("group-grade-first-$gradeGroupId");
            if ($oneArray == true) {
                $res = $res->merge($listObjActiviesCheck);
            } else {
                $res->push($listObjActiviesCheck);
            }
        }

        return $res;
    }
}
