<?php

namespace App\Repositories\Admin;

use Carbon\Carbon;
use App\Models\Fu\Content;
use App\Models\Fu\Announcement;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
class NewsletterStudentRepository extends BaseRepository
{

    public function getModel()
    {
        return Announcement::class;
    }

    public function indexNewsletterStudent()
    {
        return $this->view('newsletter_student.index');
    }

    /**
     * getListNewsletterStudent - get danh sách bản tin
     *
     * @param  mixed $request
     * @return void
     */
    public function getListNewsletterStudent($request)
    {
        try {
            $list_newsletter_students = Announcement::orderBy('created_at', 'desc');

            // Tạo danh sách categories từ enum types
            $list_categorys = collect([
                (object)['id' => 'academic', 'title' => 'Học vụ'],
                (object)['id' => 'finance', 'title' => 'Tài chính'],
                (object)['id' => 'event', 'title' => 'Sự kiện'],
                (object)['id' => 'regulation', 'title' => 'Quy định'],
                (object)['id' => 'opportunity', 'title' => 'Cơ hội'],
                (object)['id' => 'emergency', 'title' => 'Khẩn cấp']
            ]);

            // Filter theo thời gian xuất bản
            if (isset($request->start_time) && ($request->start_time != null)) {
                $list_newsletter_students->where('published_at', '>=', $request->start_time);
            }
            if (isset($request->end_time) && ($request->end_time != null)) {
                $list_newsletter_students->where('published_at', '<=', $request->end_time);
            }

            // Filter theo type (thay thế catid)
            if (isset($request->catid) && ($request->catid != null)) {
                $list_newsletter_students->where('type', $request->catid);
            }

            // Filter theo priority (thay thế ordering)
            if (isset($request->ordering) && $request->ordering !== null && $request->ordering !== '') {
                $priorityMap = [
                    1 => 'urgent',    // top 1 -> urgent
                    2 => 'high',      // top 2 -> high
                    3 => 'medium',    // top 3 -> medium
                    4 => 'low'        // không ghim -> low
                ];
                if (isset($priorityMap[$request->ordering])) {
                    $list_newsletter_students->where('priority', $priorityMap[$request->ordering]);
                }
            }

            // Filter theo trạng thái xuất bản (thay thế state)
            if (isset($request->state) && $request->state !== null && $request->state !== '') {
                if ($request->state == 1) {
                    // Đã xuất bản và chưa hết hạn
                    $list_newsletter_students->where('is_published', true);
                    $list_newsletter_students->where(function ($query) {
                        $query->where('expires_at', '>=', Carbon::now())
                              ->orWhereNull('expires_at');
                    });
                } else {
                    // Chưa xuất bản hoặc đã hết hạn
                    $list_newsletter_students->where(function ($query) {
                        $query->where('is_published', false)
                              ->orWhere('expires_at', '<', Carbon::now());
                    });
                }
            }

            // Tìm kiếm theo tiêu đề
            if ($request->search_title != null && !empty($request->search_title)) {
                $list_newsletter_students->where('title', 'like', '%' . $request->search_title . '%');
            }

            $list_newsletter_students = $list_newsletter_students->paginate(10);

            return response()->json([
                'list_newsletter_students' => $list_newsletter_students,
                'list_categorys' => $list_categorys,
            ], 200);
        } catch (\Throwable $th) {
            Log::error("----------------- start err createEditNewsletterStudent -------------------");
            Log::error($th);
            error_log($th);
            Log::error("----------------- end err createEditNewsletterStudent -------------------");
            return response("", 500);
        }
    }

    /**
     * createEditNewsletterStudent - tạo và sửa, bản tin
     *
     * @param  mixed $request
     * @return void
     */
    public function createEditNewsletterStudent($request)
    {
        try {
            // Mapping từ ordering cũ sang priority mới
            $priorityMap = [
                1 => 'urgent',    // top 1 -> urgent
                2 => 'high',      // top 2 -> high
                3 => 'medium',    // top 3 -> medium
                4 => 'low'        // không ghim -> low
            ];

            $priority = $priorityMap[$request->ordering] ?? 'low';
            $is_featured = in_array($request->ordering, [1, 2, 3]); // Top 1,2,3 là featured
            $is_urgent = ($request->ordering == 1); // Top 1 là urgent

            // Kiểm tra trùng lặp priority cho cùng type (nếu cần)
            if ($request->accept_overwrite_top == false && $is_featured) {
                $existing_announcement = Announcement::where([
                    ['id', '!=', $request->id],
                    ['type', $request->catid],
                    ['priority', $priority],
                    ['is_featured', true]
                ])->first();

                if ($existing_announcement) {
                    $typeNames = [
                        'academic' => 'Học vụ',
                        'finance' => 'Tài chính',
                        'event' => 'Sự kiện',
                        'regulation' => 'Quy định',
                        'opportunity' => 'Cơ hội',
                        'emergency' => 'Khẩn cấp'
                    ];

                    return response()->json([
                        'error' => true,
                        'text_error' => 'Độ ưu tiên ' . $priority . ' của ' . ($typeNames[$request->catid] ?? $request->catid) .
                            ' đã tồn tại, vui lòng chuyển trạng thái. Nếu bạn muốn tiếp tục ghi đè, nhấn OK!',
                    ]);
                }
            }

            // Tạo mới announcement
            if ($request->id == -1) {
                $announcement = new Announcement();
                $announcement->title = $request->title;
                $announcement->content = $request->full_text;
                $announcement->excerpt = $this->generateExcerpt($request->full_text);
                $announcement->type = $request->catid;
                $announcement->priority = $priority;
                $announcement->author = Auth::user()->user_login;
                $announcement->author_department = 'Admin';
                $announcement->is_published = ($request->state == 1);
                $announcement->is_featured = $is_featured;
                $announcement->is_urgent = $is_urgent;
                $announcement->published_at = ($request->state == 1) ? Carbon::now() : null;
                $announcement->expires_at = ($request->end_time && $request->end_time != '0000-00-00 00:00:00') ? $request->end_time : null;
                $announcement->target_audience = ['student']; // Mặc định cho sinh viên
                $announcement->save();
            }

            // Cập nhật announcement
            if ($request->id != -1) {
                $announcement = Announcement::findOrFail($request->id);
                $announcement->title = $request->title;
                $announcement->content = $request->full_text;
                $announcement->excerpt = $this->generateExcerpt($request->full_text);
                $announcement->type = $request->catid;
                $announcement->priority = $priority;
                $announcement->updated_by = Auth::user()->user_login;
                $announcement->is_published = ($request->state == 1);
                $announcement->is_featured = $is_featured;
                $announcement->is_urgent = $is_urgent;

                // Cập nhật thời gian xuất bản
                if ($request->state == 1 && !$announcement->published_at) {
                    $announcement->published_at = Carbon::now();
                } elseif ($request->state != 1) {
                    $announcement->published_at = null;
                }

                $announcement->expires_at = ($request->end_time && $request->end_time != '0000-00-00 00:00:00') ? $request->end_time : null;
                $announcement->update();
            }

            // Xử lý ghi đè priority nếu user đồng ý
            if ($request->accept_overwrite_top == true && $is_featured) {
                $this->updateOverwriteTopAnnouncements($priority, $announcement->id, $request->catid);
            }

            return response()->json([
                'id' => $announcement->id,
                'state' => $announcement->is_published ? 1 : 0,
                'created_by' => $announcement->author,
                'last_modifier' => $announcement->updated_by ?? $announcement->author,
                'start_time' => $announcement->published_at ? $announcement->published_at->format('Y-m-d H:i:s') : null,
                'end_time' => $announcement->expires_at ? $announcement->expires_at->format('Y-m-d H:i:s') : null,
                'type' => $announcement->type,
                'priority' => $announcement->priority,
            ], 200);
        } catch (\Throwable $th) {
            Log::error("----------------- start err createEditNewsletterStudent -------------------");
            Log::error($th);
            error_log($th);
            Log::error("----------------- end err createEditNewsletterStudent -------------------");
            return response("", 500);
        }
    }

    /**
     * updateoverwriteTopNewsletterStudent - update ghi đè và lùi lại các TOP tin phía sau
     *
     * @param  mixed $ordering
     * @param  mixed $content_id
     * @param  mixed $catid
     * @return void
     */
    public function updateOverwriteTopNewsletterStudent($ordering, $content_id, $catid)
    {
        try {
            // check xem TOP ordering yêu cầu chuyển đổi và, TOP ordering 2-3 có tồn tại hay ko
            $content_check_ordering = Content::where([['id', '!=', $content_id], ['catid', $catid], ['ordering', $ordering]])->first();
            $content_check_top_ordering_2 = Content::where([['id', '!=', $content_id], ['catid', $catid], ['ordering', 2]])->first();
            $content_check_top_ordering_3 = Content::where([['id', '!=', $content_id], ['catid', $catid], ['ordering', 3]])->first();

            if ($ordering == 1) {
                // nếu TOP ordering yêu cầu chuyển đổi có tồn tại thì sẽ chuyển TOP ordering sang là 2, vì ordering hiện tại là 1
                if (!empty($content_check_ordering) && $content_check_ordering != null) {
                    Content::find($content_check_ordering->id)->update(['ordering' => 2]);
                }
                // nếu TOP ordering 2 có tồn tại thì sẽ chuyển TOP ordering sang là 3, vì ordering hiện tại là 2
                if (!empty($content_check_ordering) && $content_check_ordering != null && !empty($content_check_top_ordering_2) && $content_check_top_ordering_2 != null) {
                    Content::find($content_check_top_ordering_2->id)->update(['ordering' => 3]);
                }
                // nếu TOP ordering 3 có tồn tại thì sẽ chuyển TOP ordering sang là 4, vì ordering hiện tại là 3
                if (!empty($content_check_top_ordering_2) && $content_check_top_ordering_2 != null && !empty($content_check_top_ordering_3) && $content_check_top_ordering_3 != null) {
                    Content::find($content_check_top_ordering_3->id)->update(['ordering' => 4]);
                }
                return;
            }
            if ($ordering == 2) {
                // nếu TOP ordering 2 có tồn tại thì sẽ chuyển TOP ordering sang là 3, vì ordering hiện tại là 2
                if (!empty($content_check_top_ordering_2) && $content_check_top_ordering_2 != null) {
                    Content::find($content_check_top_ordering_2->id)->update(['ordering' => 3]);
                }
                // nếu TOP ordering 3 có tồn tại thì sẽ chuyển TOP ordering sang là 4, vì ordering hiện tại là 3
                if (!empty($content_check_top_ordering_3) && $content_check_top_ordering_3 != null && !empty($content_check_top_ordering_2) && $content_check_top_ordering_2 != null) {
                    Content::find($content_check_top_ordering_3->id)->update(['ordering' => 4]);
                }
                return;
            }
            if ($ordering == 3) {
                // nếu TOP ordering 3 có tồn tại thì sẽ chuyển TOP ordering sang là 4, vì ordering hiện tại là 3
                if (!empty($content_check_top_ordering_3) && $content_check_top_ordering_3 != null) {
                    Content::find($content_check_top_ordering_3->id)->update(['ordering' => 4]);
                }
                return;
            }
        } catch (\Throwable $th) {
            Log::error("----------------- start err createEditNewsletterStudent -------------------");
            Log::error($th);
            error_log($th);
            Log::error("----------------- end err createEditNewsletterStudent -------------------");
            return response("", 500);
        }
    }

    /**
     * updateStateNewsletterStudent - update state trạng thái của bản tin
     *
     * @param  mixed $request
     * @return void
     */
    public function updateStateNewsletterStudent($request)
    {
        try {
            $announcement = Announcement::findOrFail($request->id);

            if (!$announcement->is_published) {
                // Chuyển sang trạng thái xuất bản
                $announcement->update([
                    'is_published' => true,
                    'published_at' => Carbon::now(),
                    'updated_by' => Auth::user()->user_login
                ]);
            } else {
                // Chuyển sang trạng thái không xuất bản
                $announcement->update([
                    'is_published' => false,
                    'published_at' => null,
                    'updated_by' => Auth::user()->user_login
                ]);
            }

            return response()->json([
                'state' => $announcement->is_published ? 1 : 0,
                'start_time' => $announcement->published_at ? $announcement->published_at->format('Y-m-d H:i:s') : null,
                'end_time' => $announcement->expires_at ? $announcement->expires_at->format('Y-m-d H:i:s') : null,
                'last_modifier' => $announcement->updated_by,
            ]);
        } catch (\Throwable $th) {
            Log::error("----------------- start err updateStateNewsletterStudent -------------------");
            Log::error($th);
            error_log($th);
            Log::error("----------------- end err updateStateNewsletterStudent -------------------");
            return response("", 500);
        }
    }


    /**
     * deleteNewsletterStudent - xóa bản tin
     *
     * @param  mixed $request
     * @return void
     */
    public function deleteNewsletterStudent($request)
    {
        try {
            $announcement = Announcement::findOrFail($request->id);
            $announcement->delete();
            return response('Successful', 200);
        } catch (\Throwable $th) {
            Log::error("----------------- start err deleteNewsletterStudent -------------------");
            Log::error($th);
            error_log($th);
            Log::error("----------------- end err deleteNewsletterStudent -------------------");
            return response("", 500);
        }
    }



    /**
     * generateExcerpt - Tạo excerpt từ content
     *
     * @param  string $content
     * @return string
     */
    private function generateExcerpt($content)
    {
        $text = strip_tags($content);
        return strlen($text) > 200 ? substr($text, 0, 200) . '...' : $text;
    }

    /**
     * updateOverwriteTopAnnouncements - Cập nhật priority cho các announcements khác
     *
     * @param  string $priority
     * @param  int $id
     * @param  string $type
     * @return void
     */
    private function updateOverwriteTopAnnouncements($priority, $id, $type)
    {
        try {
            // Hạ priority của các announcements khác cùng type
            $announcements = Announcement::where('priority', $priority)
                ->where('type', $type)
                ->where('id', '!=', $id)
                ->where('is_featured', true)
                ->get();

            foreach ($announcements as $announcement) {
                // Hạ priority xuống 1 bậc
                $newPriority = match($announcement->priority) {
                    'urgent' => 'high',
                    'high' => 'medium',
                    'medium' => 'low',
                    default => 'low'
                };

                $announcement->update([
                    'priority' => $newPriority,
                    'is_featured' => ($newPriority !== 'low'),
                    'is_urgent' => false
                ]);
            }
        } catch (\Throwable $th) {
            Log::error("----------------- start err updateOverwriteTopAnnouncements -------------------");
            Log::error($th);
            Log::error("----------------- end err updateOverwriteTopAnnouncements -------------------");
        }
    }

}
