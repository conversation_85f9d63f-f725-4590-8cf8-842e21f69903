<?php

namespace App\Repositories\Admin;

use App\Imports\BaseModelImport;
use App\Imports\DisciplineImport;
use App\Imports\GraduationSubjectImport;
use App\Models\Dra\StudentSubject;

use App\Models\Fu\Subject;
use App\Repositories\BaseRepository;
use App\Models\T7\GradeSyllabus;
use App\Models\Fu\Department;
use App\Models\T7\GradeGroup;
use App\Models\T7\SyllabusPlan;
use App\Models\Fu\Group;
use App\Utils\ResponseBuilder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\MienGiamTapTrung;
use App\Models\SessionType;
use Illuminate\Support\Carbon;
use App\Models\SystemLog;
use App\Models\T7\Grade;
use Illuminate\Support\Facades\DB;
use App\Models\TranscriptDetail;
use App\Models\Transcript;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Fu\User;
use App\Models\ChangeSubjectStudent;
use App\Models\Dra\Period;
use App\Models\Dra\T1UserRole;
use App\Models\Fu\ActionLog;
use App\Models\Fu\SubjectsForGraduation;
use App\Models\T7\CourseResult;
use Illuminate\Notifications\Action;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class SubjectRepository extends BaseRepository
{
    public function getModel()
    {
        return GradeSyllabus::class;
    }

    public function list($per_page = 10, $filter = [])
    {
        $query = Subject::query();
        if (isset($filter['department'])) {
            $query->where('department_id', $filter['department']);
        }

        if (isset($filter['subject_type'])) {
            $query->where('subject_type', $filter['subject_type']);
        }

        if (isset($filter['search'])) {
            $query->where('subject_code', 'like', $filter['search'].'%');
        }

        if (isset($filter['status'])) {
            $query->with([
                "GradeSyllabus" => function ($query) use ($filter) {
                    $query->select('id as syllabus_id', 'subject_id', 'status', 'syllabus_name', 'subject_name', 'inactive', 'syllabus_code')
                    ->where('grade_syllabus.inactive', $filter['status']);
                }
            ])->whereHas('GradeSyllabus', function ($query) use ($filter) {
                $query->where('inactive', $filter['status']);
            });
        }else{
            $query->with(["GradeSyllabus" => function ($query) {
                    $query->select('id as syllabus_id', 'subject_id', 'status', 'syllabus_name', 'subject_name', 'inactive', 'syllabus_code');
                }
            ]);
        }
        
        return $query->paginate($per_page, ['*']);
    }

    public function listOfDepartment()
    {
        return Department::get(['*']);
    }

    public function syllabusSubjectDetail($id)
    {
        try {
            $subject = Subject::where('id', $id)->with([
                "GradeSyllabus" => function ($query) {
                    $query->select('id as syllabus_id', 'subject_id', 'syllabus_name', 'subject_name', 'inactive', 'syllabus_code');
                },
                "Department" => function ($query) {
                    $query->select('id', 'department_name');
                }
            ])->firstOrFail([
                'subject.id',
                'subject.department_id',
                'subject.subject_name',
                'subject.subject_name_en',
                'subject.short_name',
                'subject.subject_code',
                'subject.num_of_credit',
                'subject.skill_code',
                'subject.level',
                'subject.subject_type',
            ]);

            if ($subject) {
                return $subject;
            }
        } catch (\Throwable $th) {
            return ResponseBuilder::Fail("Error", $th->getMessage(), 500);
        }
    }

    public function syllabusPlanDetai($subject_id, $syllabus_id)
    {
        $subject = Subject::where('id', $subject_id)->get()->first();

        $syllabusPlan = SyllabusPlan::select([
                'id',
                'course_session',
                'num_of_lessons',
                'session_type',
                'syllabus_name',
                'noi_dung',
                'description',
                'tutor_required',
                'syllabus_id'
            ])
            ->where('subject_code', $subject->subject_code)
            ->where('syllabus_id', $syllabus_id)
            ->orderBy('course_session', 'asc')
            ->get();
        
        $gradeGroup = GradeGroup::where('syllabus_id', $syllabus_id)->with([
            'grade' => function ($query) {
                $query->select(
                    'id',
                    'grade_group_id',
                    'syllabus_id',
                    'syllabus_name',
                    'subject_id',
                    'grade_name',
                    'weight',
                    'minimum_required',
                    'max_point',
                    'course_session',
                    'allow_resit',
                    'is_final_exam',
                    'master_grade',
                    'substitute_grade',
                    'is_locked',
                    'day_to_lock',
                    'grade_type',
                    'duration'
                );
            }
        ])->get(['*']);

        $syllabus = GradeSyllabus::select([
            'id',
            'syllabus_name',
            'syllabus_code',
            'number_group',
            'num_of_session',
            'num_of_lessons',
            'attendance_cutoff',
            'minimum_required',
            'status',
            'inactive',
            'duyet',
            'offline',
            'lock_type',
            'author',
            'subject_id',
            'subject_code',
            'subject_name',
        ])
        ->where('id', $syllabus_id)->first();

        return [
            "syllabus" => $syllabus,
            "calendar" => $syllabusPlan,
            "grade_group" => $gradeGroup,
            "session_type" => SessionType::get(['id as value', 'session_type as text']),
        ];
    }

    public function syllabusClass($subject_id, $term_id)
    {
        return Group::where('psubject_id', $subject_id)->where('pterm_id', $term_id)->get(['id', 'group_name']);
    }

    private function handleSaveSubject(Request $request)
    {
        $checkNewSubject = false;
        DB::beginTransaction();
        try {
            if (isset($request->id) && $request->id) {
                $subject = Subject::where('id', $request->id)->first();
            } else {
                $subject = new Subject();
            }
    
            if (isset($subject->id)) {
                // Khởi tạo biến lưu thay đổi
                $listFieldUpdate = [];
                // Danh sách trường không so sánh
                $listFieldNoChange = ['id'];
                // Lấy danh sách thay đổi
                $listEdit = $request->all();
                // Lấy các trường trong fillable (lưu ý phần này phải khai báo fillable trong Model)
                $listFiledCanUpdate = $subject->getFillable();
                // Duyệt dữ liệu xem có gì thay đổi không
              
                foreach ($listEdit as $key => $value) {            
                    // kiểm tra có trong fillable không và không tồn tại trong Danh sách trường không so sánh
                    if (in_array($key, $listFiledCanUpdate) && !in_array($key, $listFieldNoChange)) {
                        if ($subject->$key != $value) {
                            $listFieldUpdate[$key] = [
                                'old_val' => $subject->$key,
                                'new_val' => $value
                            ];
                        }
                    }
                }

                // kiểm tra nếu có sự thay đổi
                if (count($listFieldUpdate) > 0) {
                    $logUpdate = [];
                    foreach ($listFieldUpdate as $key => $value) {
                        $subject->$key = $value['new_val'];
                        $logUpdate[] = "{ $key: từ (" . $value['old_val'] . ') thành (' . $value['new_val'] . ') }';
                    }

                    ActionLog::create([
                        'object'        => 'fu_subjcet',
                        'auth'          => auth()->user()->user_login,
                        'action'        => 'update',
                        'description'   => "Cập nhập môn học với id {$subject->id}" ,
                        'object_id'     => "$subject->id",
                        'data_changed'  => json_encode($listFieldUpdate),
                        'ip'            => request()->getClientIp(),
                    ]);
                }
            } else {
                $checkNewSubject = true;
            }

            $subject->department_id = $request->department_id ? $request->department_id : ($subject->department_id ? $subject->department_id : 0);
            $subject->subject_code = $request->subject_code ? $request->subject_code : ($subject->subject_code ? $subject->subject_code : '');
            $subject->subject_name = $request->subject_name ? $request->subject_name : ($subject->subject_name ? $subject->subject_name : '');
            $subject->subject_name_en = $request->subject_name_en ? $request->subject_name_en : ($subject->subject_name_en ? $subject->subject_name_en : '');
            $subject->short_name = $request->short_name ? $request->short_name : ($subject->short_name ? $subject->short_name : '');
            $subject->skill_code = $request->skill_code ? $request->skill_code : ($subject->skill_code ? $subject->skill_code : 0);
            $subject->level = $request->level ? $request->level : ($subject->level ? $subject->level : 0);
            $subject->num_of_credit = $request->num_of_credit ? $request->num_of_credit : ($subject->num_of_credit ? $subject->num_of_credit : 0);
            $subject->subject_type = $request->subject_type ? $request->subject_type : ($subject->subject_type ? $subject->subject_type : 0);
            $subject->is_mandatory = 0;
            $subject->fee_next_time = 0;
            $subject->fee_first_time = 0;
            $subject->precondition_learned = '';
            $subject->subject_credit = 0;
            $subject->alt_subject_list = '';
            $subject->need_ordering = 0;
            $subject->ordering = 0;
            $subject->muc_tieu_mon = '';
            $subject->number_of_syllabus = 0;
            $result = $subject->save();
            if ($checkNewSubject == true) {
                ActionLog::create([
                    'object'        => 'fu_subjcet',
                    'auth'          => auth()->user()->user_login,
                    'action'        => 'update',
                    'description'   => "Tạo môn học mới: $subject->subject_code " . $subject->subject_name . " - id: " . ($subject->id ?? ""),
                    'object_id'     => ($subject->id ?? ""),
                    'data_changed'  => json_encode($subject),
                    'ip'            => request()->getClientIp(),
                ]);
            }

            DB::commit();
            if ($result) {
                return $subject->id;
            } else {
                return 0;
            }
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return 0;
        }
    }

    public function syllabusSubjectAdd(Request $request)
    {
        try {
            $result = $this->handleSaveSubject($request);
            if ($result) {
                return ResponseBuilder::Success([
                    'message' => 'Thêm môn học thành công',
                    'permitted' => true,
                    'success' => true
                ]);
            } else {
                return ResponseBuilder::Success([
                    'message' => 'Thêm môn học thất bại',
                    'permitted' => true,

                ]);
            }
        } catch (\Throwable $th) {
            Log::error('SyllabusController@syllabusCurriculumframeAdd: ' . $th->getLine() . ' - ' . $th->getMessage());
            return ResponseBuilder::Fail([
                'message' => 'Thêm môn học thất bại - ' . $th->getMessage(),
                'permitted' => true,
                'success' => false
            ]);
        }
    }

    public function syllabusSubjectEdit(Request $request)
    {

        try {
            $result = $this->handleSaveSubject($request);
            if ($result) {
                return ResponseBuilder::Success([
                    'message' => 'Sửa môn học thành công',
                    'permitted' => true,
                    'success' => true
                ]);
            } else {
                return ResponseBuilder::Success([
                    'message' => 'Sửa môn học thất bại',
                    'permitted' => true,
                    'success' => false
                ]);
            }
        } catch (\Throwable $th) {
            Log::error('SyllabusController@syllabusCurriculumframeAdd: ' . $th->getLine() . ' - ' . $th->getMessage());
            return ResponseBuilder::Fail([
                'message' => 'Sửa môn học thất bại - ' . $th->getMessage(),
                'permitted' => true,
                'success' => false
            ]);
        }
    }

    public function getSubjectsExempted($per_page = 20, $student_code = null, $term_id = null)
    {
        try {
            $query = MienGiamTapTrung::query();
            if ($student_code != null) {
                $query = MienGiamTapTrung::where('student_login', 'LIKE', "%$student_code%");
            }
            if ($term_id != null) {
                $query = MienGiamTapTrung::where('term_id', $term_id);
            }
            $result = $query->where('type', 2)->orderBy('id', 'DESC')->paginate($per_page);
            return response(['list' => $result], 200);
        } catch (\Throwable $th) {
            Log::error($th);
            return response(['list' => []], 500);
        }   
    }

    public function deleteSubjectExempted($mgtt_id)
    {
        try {
            DB::beginTransaction();
            $now = Carbon::now()->format('Y-m-d H:i:s');
            $mienGiam = MienGiamTapTrung::where('id', $mgtt_id)->where('type', 2)->first();
            if(!$mienGiam) {
                return response(["status" => 0, "message" => "Thất bại! Không tìm thấy môn miễn giảm!"]);
            }
            StudentSubject::where('student_login', $mienGiam->student_login)
            ->where('subject_code', $mienGiam->subject_code)
            ->update([
                'status' => 0,
                'is_lock_edit' => 0,
                'resourse' => '',
                'in_result' => '',
                'grade' => '',
                'note' => '',
                'pass_term_id' => '',
                'type' => '',
                'is_start' => ''
            ]);
            SystemLog::create([
                'object_name' => "final_grade",
                'actor' => Auth::user()->user_login,
                'log_time' => $now,
                'action' => 'delele transfer credit',
                'description' => 'delele transfer credit type mg : ' . $mienGiam->subject_code,
                'object_id' => 0,
                'brief' => 'transfer credit',
                'from_ip' => "",
                'relation_login' => $mienGiam->student_login,
                'relation_id' => "",
                'nganh_cu' => "",
                'nganh_moi' => "",
                'ky_chuyen_den' => "",
                'ky_thu_cu' => ""
            ]);
            $mienGiam->delete();
            DB::commit();
            return response(["status" => 1, "message" => "Xoá môn miễn giảm thành công!"], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error(['deleteSubjectExempted'=>$e->getMessage()]);
            return response(["status" => 0, "message" => "Xoá môn miễn giảm thất bại!"]);
        }
    }

    public function deleteAlternativeSubject(Request $request)
    {
        try {
            $mienGiam = MienGiamTapTrung::where('student_login', $request->student_login)
            ->where('type', 1)
            ->where('subject_code', $request->old_subject)->where('subject_code_new', $request->new_subject)
            ->first();
            if (!$mienGiam) return response(['status' => false, "message" => "Thất bại, Không tìm thấy môn thay thế!"], 500);
            DB::beginTransaction();
            $transcrip = Transcript::where('user_login', $mienGiam->student_login)->first();
            $updateTranscrip = TranscriptDetail::where([
                'subject_code' => $mienGiam->subject_code,
                'transcript_id' => $transcrip->id
            ])->update([
                'subject_code_replace' => NULL,
                'subject_name_replace' => NULL,
                'skill_code_replace' => NULL,
                'subject_code_pass' => NULL,
                'subject_name_pass' => NULL,
                'type' => 0,
                'status' => 0,
                'point' => 0
            ]);
            if(!$updateTranscrip) {
                DB::rollBack();
                return response(['status' => false, "message" => "Thất bại! Lỗi khi cập nhật TranscriptDetail."], 500);
            }
            $log = SystemLog::create([
                'object_name' => "Thay thế môn",
                'actor' => Auth::user()->user_login,
                'log_time' =>  Carbon::now()->format('Y-m-d H:i:s'),
                'action' => "xóa thay thế môn",
                'description' => "xóa thay thế môn",
                'relation_login' => $mienGiam->student_login
            ]);
            if(!$log) {
                DB::rollBack();
                return response(['status' => false, "message" => "Thất bại! Lỗi khi tạo log."], 500);
            }
            $deleteAlternativeSubject = ChangeSubjectStudent::where('student_login', $mienGiam->student_login)
            ->where('new_subject', $mienGiam->subject_code_new)
            ->delete();
            if(!$deleteAlternativeSubject) {
                DB::rollBack();
                return response(['status' => false, "message" => "Thất bại! Lỗi khi xoá môn thay thế."], 500);
            }
            $mienGiam->delete();
                
            DB::commit();
            return response(['status' => true, "message" => "Xoá môn thay thế thành công!"], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::error('--------------- Start log deleteAlternativeSubject ---------------');
            Log::error('Error in :' . $th->getFile() . ' at line ' . $th->getLine() . ' with message: ' . $th->getMessage());
            Log::error('--------------- End log deleteAlternativeSubject ---------------');
            return response(['status' => false, "message" => "Thất bại! Có lỗi xảy ra!"], 500);
        }
    }

    public function handleSubmitImportSubjectEng(Request $request)
    {
        $numSuccess = 0;
        $numFail = 0;
        $logFail = '';
        $message = '';
        $file = $request->file('file'); // kiểm tra file 
        if (!$datas = $this->importDataNewFromFile($file, true)) {
            return back()->with([
                'reports' => [[
                    'msg' => 'File không có dữ liệu hoặc file tải lên bị lỗi.',
                    'status' => false, 
                ]]
            ]);
        }
        foreach ($datas as $index => $data) {
            if ($index == 0) {
                continue;
            }
            if ($subject_code = $data[0]) {
                try {
                    DB::beginTransaction();
                    $subject = Subject::where('subject_code', $subject_code)->first();
                    if (!$subject) {
                        $numFail += 1;
                        $logFail .= "Mã môn học {$subject_code} không tồn tại trong hệ thống.\n";
                        continue;
                    }
                    $old_subject_name_en = $subject->subject_name_en;
                    $new_subject_name_en = $data[1];
                    $subject->subject_name_en = $new_subject_name_en;
                    if ($subject->save()) {
                        ActionLog::create([
                            'object'        => 'subject',
                            'auth'          => auth()->user()->user_login,
                            'action'        => 'update',
                            'description'   => "Cập nhập môn học với id {$subject_code}, đổi tên tiếng anh từ $old_subject_name_en thành $new_subject_name_en",
                            'object_id'     => "$subject_code",
                            'data_changed'  => $new_subject_name_en,
                            'ip'            => request()->getClientIp(),
                        ]);
                        DB::commit();
                        $numSuccess += 1;
                    }
                } catch (\Throwable $th) {
                    DB::rollBack();
                    $numFail += 1;
                    $logFail .= "Lỗi cập nhật môn học {$subject_code}: {$th->getMessage()}\n";
                }
            }
        }
        $message .= "Thành công: {$numSuccess}.\n";
        $message .= "Thất bại: {$numFail}.\n";
        if ($numFail > 0) {
            $message .= "Chi tiết lỗi:\n" . $logFail;
        }
        if ($numSuccess == 0) {
            return ResponseBuilder::Fail("Thất bại!",[
                'message' => $message,
                'numSuccess' => $numSuccess,
                'numFail' => $numFail,
            ]);
        }
        return ResponseBuilder::Success([
            'message' => $message,
            'numSuccess' => $numSuccess,
            'numFail' => $numFail,
        ]);
    }

    public function importDataNewFromFile($file, $fullRow = false)
    {
        $data = Excel::toArray(new DisciplineImport(), $file);
        if (count($data[0]) <= 1) {
            return false;
        }

        if ($fullRow == false) {
            unset($data[0][0]);
        }

        return $data[0];
    }

    public function handleSubmitImportSubjectCredits(Request $request)
    {
        $numSuccess = 0;
        $numFail = 0;
        $logFail = '';
        $message = '';
        $file = $request->file('file'); // kiểm tra file 
        if (!$datas = $this->importDataNewFromFile($file, true)) {
            $message .= "File không có dữ liệu hoặc file tải lên bị lỗi.";
            return ResponseBuilder::Fail("Thất bại!", [
                'message' => $message,
                'numSuccess' => $numSuccess,
                'numFail' => $numFail,
            ]);
        }
        foreach ($datas as $index => $data) {
            if ($index == 0) {
                continue;
            }
            if ($subject_code = $data[0]) {
                try {
                    DB::beginTransaction();
                    $subject = Subject::where('subject_code', $subject_code)->first();
                    if (!$subject) {
                        $numFail += 1;
                        $logFail .= "Mã môn học {$subject_code} không tồn tại trong hệ thống.\n";
                        continue;
                    }
                    $old_num_of_credit = $subject->num_of_credit;
                    $new_num_of_credit = $data[1];
                    $subject->num_of_credit = $new_num_of_credit;
                    if ($subject->save()) {
                        ActionLog::create([
                            'object'        => 'subject',
                            'auth'          => auth()->user()->user_login,
                            'action'        => 'update',
                            'description'   => "Cập nhập môn học với id {$subject_code}, đổi tên tiếng anh từ $old_num_of_credit thành $new_num_of_credit",
                            'object_id'     => "$subject_code",
                            'data_changed'  => $new_num_of_credit,
                            'ip'            => request()->getClientIp(),
                        ]);
                        DB::commit();
                        $numSuccess += 1;
                    }
                } catch (\Throwable $th) {
                    DB::rollBack();
                    $numFail += 1;
                    $logFail .= "Lỗi cập nhật môn học {$subject_code}: {$th->getMessage()}\n";
                }
            }
        }
        $message .= "Thành công: {$numSuccess}.\n";
        if ($numFail > 0) {
            $message .= "Thất bại: {$numFail}.\n";
            $message .= "Chi tiết lỗi:\n" . $logFail;
        }
        if ($numSuccess == 0) {
            return ResponseBuilder::Fail("Thất bại!",[
                'message' => $message,
                'numSuccess' => $numSuccess,
                'numFail' => $numFail,
            ]);
        }
        return ResponseBuilder::Success([
            'message' => $message,
            'numSuccess' => $numSuccess,
            'numFail' => $numFail,
        ]);
    }

    public function handleSaveSyllabus(Request $request)
    {
        DB::beginTransaction();
        try {
            if ($request->action === "update") {
                $grade_syllabus = GradeSyllabus::where('id', $request->id)->firstOrFail();
            } else {
                $grade_syllabus = new GradeSyllabus();
                $gradeSyllabusCheck = GradeSyllabus::where('syllabus_code', trim($request->syllabus_code))
                    ->orWhere('syllabus_name', trim($request->syllabus_name))
                    ->first();
                if ($gradeSyllabusCheck) {
                    DB::rollBack();
                    return ResponseBuilder::Fail("Tên hoặc mã của giáo trình đang bị trùng. Vui lòng kiểm tra lại!", 500);
                }
            }

            $subject = Subject::where('id', $grade_syllabus->subject_id)->firstOrFail();

            if ($request->action === "create") {
                $grade_syllabus->created_date = Carbon::now()->format('Y-m-d');
            }
            $grade_syllabus->last_modified = Carbon::now()->format('Y-m-d H:i:s');
            $grade_syllabus->is_locked = $request->is_locked ? $request->is_locked : ($grade_syllabus->is_locked ? $grade_syllabus->is_locked : 0);
            $grade_syllabus->author = auth()->user()->user_login;
            $grade_syllabus->number_grade = $request->number_grade ? $request->number_grade : ($grade_syllabus->number_grade ? $grade_syllabus->number_grade : 0);
            $grade_syllabus->number_group = $request->number_group ? $request->number_group : ($grade_syllabus->number_group ? $grade_syllabus->number_group : 0);
            $grade_syllabus->attendance_cutoff = $request->attendance_cutoff ? $request->attendance_cutoff : ($grade_syllabus->attendance_cutoff ? $grade_syllabus->attendance_cutoff : 0);
            $grade_syllabus->lock_type = $request->lock_type ? $request->lock_type : ($grade_syllabus->lock_type ? $grade_syllabus->lock_type : 1);
            $grade_syllabus->num_of_session = $request->num_of_session ? $request->num_of_session : ($grade_syllabus->num_of_session ? $grade_syllabus->num_of_session : 0);
            $grade_syllabus->num_of_lessons = $request->num_of_lessons ? $request->num_of_lessons : ($grade_syllabus->num_of_lessons ? $grade_syllabus->num_of_lessons : 0);
            $grade_syllabus->minimum_required = $request->minimum_required ? $request->minimum_required : ($grade_syllabus->minimum_required ? $grade_syllabus->minimum_required : 0);
            $grade_syllabus->num_of_credit = $request->num_of_credit ? $request->num_of_credit : ($grade_syllabus->num_of_credit ? $grade_syllabus->num_of_credit : 0);
            $grade_syllabus->fee_amount = $request->fee_amount ? $request->fee_amount : ($grade_syllabus->fee_amount ? $grade_syllabus->fee_amount : 0);
            $grade_syllabus->status = $request->status ? $request->status : ($grade_syllabus->status ? $grade_syllabus->status : 0);
            $grade_syllabus->syllabus_code = trim($request->syllabus_code ? $request->syllabus_code : ($grade_syllabus->syllabus_code ? $grade_syllabus->syllabus_code : ''));
            $grade_syllabus->duyet = isset($request->duyet) ? $request->duyet : ($grade_syllabus->duyet ? $grade_syllabus->duyet : 0);
            $grade_syllabus->inactive = isset($request->inactive) ? $request->inactive : ($grade_syllabus->inactive ? $grade_syllabus->inactive : 1);
            $grade_syllabus->offline = $request->offline ? $request->offline : ($grade_syllabus->offline ? $grade_syllabus->offline : 1);
            $grade_syllabus->subject_id = $request->subject_id ? $request->subject_id : ($grade_syllabus->subject_id ? $grade_syllabus->subject_id : null);
            $grade_syllabus->subject_code = $request->subject_code ? $request->subject_code : ($grade_syllabus->subject_code ? $grade_syllabus->subject_code : null);
            $grade_syllabus->syllabus_name = $request->syllabus_name ? $request->syllabus_name : ($grade_syllabus->syllabus_name ? $grade_syllabus->syllabus_name : null);

            $result = $grade_syllabus->save();
            $blank_res = 1;
            if ($grade_syllabus->num_of_session > 0 || $grade_syllabus->number_group > 0) {
                $data = new \stdClass();
                $data->syllabus_id = $grade_syllabus->id;
                $data->syllabus_name = $grade_syllabus->syllabus_name;
                $data->subject_id = $grade_syllabus->subject_id;
                $data->subject_name = $grade_syllabus->subject_name;
                $data->subject_code = $subject->subject_code;
                $data->num_of_session = $grade_syllabus->num_of_session;
                $data->num_of_lessons = $grade_syllabus->num_of_lessons;
                $data->syllabus_code = $grade_syllabus->syllabus_code;
                $data->number_grade = $grade_syllabus->number_grade;
                $data->number_group = $grade_syllabus->number_group;
                $blank_res = $this->createNewBlankGradeGroupAndSyllabusPlan($data);
            }
            
            // them action log
            $dataLog = [
                'id' => $grade_syllabus->id,
                'syllabus_name' => $grade_syllabus->syllabus_name,
                'subject_name' => $grade_syllabus->subject_name,
                'subject_code' => $grade_syllabus->subject_code,
                'syllabus_code' => $grade_syllabus->syllabus_code,
                'number_grade' => $grade_syllabus->number_grade,
                'number_group' => $grade_syllabus->number_group,
                'num_of_session' => $grade_syllabus->num_of_session
            ];
            
            $dataActionLog = [
                'object'        => 'grade_syllabus',
                'auth'          => auth()->user()->user_login,
                'action'        => ($request->action === "update" ? 'update' : 'create'),
                'description'   => "Tạo grade_syllabus có id là {$grade_syllabus->id}" ,
                'object_id'     => "",
                'data_changed'  => json_encode($dataLog),
                'ip'            => $request->getClientIp(),
            ];
            if ($request->action === "update") {
                $dataActionLog['description'] = "Cập nhật grade_syllabus có id là {$grade_syllabus->id}";
            }

            ActionLog::create($dataActionLog);
            
            DB::commit();
            if ($result && $blank_res) {
                return ResponseBuilder::Success([], ($request->action === "update" ? 'Cập nhập' : 'Thêm mới') . " thành công");
            } else {
                DB::rollBack();
                return ResponseBuilder::Fail(($request->action === "update" ? 'Cập nhập' : 'Thêm mới') . " thất bại");
            }
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return ResponseBuilder::Fail(($request->action === "update" ? 'Cập nhập' : 'Thêm mới') . " thất bại, vui lòng liên hệ với cán bộ IT");
        }
    }

    public function handleDeleteSyllabus(Request $request)
    {
        DB::beginTransaction();
        try {
            $grade_syllabus = GradeSyllabus::where('id', $request->id)->firstOrFail();
            $grade_syllabus->delete();
            $grade = Grade::where('syllabus_id', $request->id)->get();
            foreach ($grade as $key => $value) {
                $value->delete();
            }
            $grade_group = GradeGroup::where('syllabus_id', $request->id)->get();
            foreach ($grade_group as $key => $value) {
                $value->delete();
            }
            $syllabus_plan = SyllabusPlan::where('syllabus_id', $request->id)->get();
            foreach ($syllabus_plan as $key => $value) {
                $value->delete();
            }

            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            return ResponseBuilder::Fail([
                'message' => 'Xóa thất bại - ' . $th->getMessage(),
                'permitted' => true,
                'success' => false
            ]);
        }
    }

    public function ImportPlanByFileExcel(Request $request)
    {
        try {
            if (empty($request->file('file'))) {
                return ResponseBuilder::Fail([
                    'message' => 'Vui lòng chọn file',
                    'permitted' => true,
                    'success' => false
                ]);
            }
            $file = $request->file;
            $theCollection = Excel::toArray(new BaseModelImport, $file);
            $data = $theCollection[0];

            return ResponseBuilder::Success([
                'message' => 'Import thành công',
                'permitted' => true,
                'success' => true
            ]);
        } catch (\Throwable $th) {
            Log::error("Error ImportPlanByFileExcel: " . $th->getMessage() . " - " . $th->getLine() . " - " . $th->getFile());
            return ResponseBuilder::Fail([
                'message' => 'Import thất bại - ' . $th->getLine() . " - " . $th->getMessage(),
                'permitted' => true,
                'success' => false
            ]);
        }
    }

    private function createNewBlankGradeGroupAndSyllabusPlan($data)
    {
        try {
            $default_session_type = SessionType::orderBy('id', 'asc')->firstOrFail();
            if ($data->num_of_session != SyllabusPlan::where('syllabus_id', $data->syllabus_id)->count()) {
                SyllabusPlan::where('syllabus_id', $data->syllabus_id)->delete();
                for ($i = 0; $i < $data->num_of_session; $i++) {
                    $syllabus_plan = new SyllabusPlan();
                    $syllabus_plan->syllabus_id = $data->syllabus_id;
                    $syllabus_plan->syllabus_name = $data->syllabus_name;
                    $syllabus_plan->subject_name = $data->subject_name;
                    $syllabus_plan->subject_code = $data->subject_code;
                    $syllabus_plan->subject_id = $data->subject_id;
                    $syllabus_plan->course_session = $i + 1;
                    $syllabus_plan->description = $default_session_type->session_type;
                    $syllabus_plan->guide = '';
                    // $syllabus_plan->campus_id = $data->campus_id;
                    $syllabus_plan->session_type = $default_session_type->id;
                    $syllabus_plan->tutor_required = 0;
                    $syllabus_plan->syllabus_code = $data->syllabus_code;
                    $syllabus_plan->noi_dung = '';
                    $syllabus_plan->nv_sinh_vien = '';
                    $syllabus_plan->nv_giang_vien = '';
                    $syllabus_plan->hoc_lieu_mon = '';
                    $syllabus_plan->tai_lieu_tk = '';
                    $syllabus_plan->tu_hoc = '';
                    $syllabus_plan->tl_buoi_hoc = '';
                    $syllabus_plan->last_modified = Carbon::now()->format('Y-m-d H:i:s');
                    $syllabus_plan->save();
                }
            }

            if ($data->number_group != GradeGroup::where('syllabus_id', $data->syllabus_id)->count()) {
                GradeGroup::where('syllabus_id', $data->syllabus_id)->delete();
                $perGroupGrade = round(100 / $data->number_group, 2);
                $perGroupGrade2 = $perGroupGrade;
                if ($data->number_group > 2 && 100 != $perGroupGrade * $data->number_group) {
                    $perGroupGrade2 = 100 - ($perGroupGrade * ($data->number_group - 1));
                }

                for ($i = 0; $i < $data->number_group; $i++) {
                    $grade_group = new GradeGroup();
                    $grade_group->syllabus_id = $data->syllabus_id;
                    $grade_group->syllabus_name = $data->syllabus_name;
                    $grade_group->grade_group_name = "Grade Group Name " . ($i + 1);
                    $grade_group->weight = 0;
                    if (request()->id == null) {
                        if ($i == 0) {
                            $grade_group->weight = $perGroupGrade2;
                        } else {
                            $grade_group->weight = $perGroupGrade;
                        }
                    }

                    $grade_group->minimum_required = 0;
                    $grade_group->subject_name = $data->subject_name;
                    $grade_group->subject_id = $data->subject_id;
                    $grade_group->number_grade = 0;
                    // $grade_group->campus_id = $data->campus_id;
                    $grade_group->ordering = $i + 1;
                    $grade_group->save();
                }
            }
            return 1;
        } catch (\Throwable $th) {
            Log::error("Error createNewBlankGradeGroupAndSyllabusPlan: " . $th->getLine() . " - " . $th->getMessage());
            return 0;
        }
    }

    public function handleEditSyllabusPlan(Request $request)
    {
        DB::beginTransaction();
        try {
            $syllabus = GradeSyllabus::where('id', $request->syllabus_id)->first();
            if (!$syllabus) {
                return ResponseBuilder::Fail([
                    'message' => 'Không tìm thấy giáo trình',
                    'permitted' => true,
                    'success' => false
                ]);
            }
            $num_of_lessons = $syllabus->num_of_lessons;
            $lessons_check = 0;
            foreach ($request->plan as $request_syllabus_plan) {
                $request_syllabus_plan = (object)$request_syllabus_plan;
                $syllabus_plan = SyllabusPlan::find($request_syllabus_plan->id);
                if (!$syllabus_plan) {
                    return ResponseBuilder::Fail([
                        'message' => 'Không tìm thấy kế hoạch',
                        'permitted' => true,
                        'success' => false
                    ]);
                }
                if ($request_syllabus_plan->need_delete) {
                    $syllabus_plan->delete();
                    if ($syllabus->id !== $request_syllabus_plan->syllabus_id) {
                        return ResponseBuilder::Fail([
                            'message' => 'Lỗi khi xoá lịch học. Có thể giáo trình đã bị xoá hoặc không đúng với giáo trình hiện tại.',
                            'permitted' => true,
                            'success' => false
                        ]);
                    }
                    $syllabus->num_of_session = $syllabus->num_of_session - 1;
                    $syllabus->save();
                    continue;
                } else {
                    $syllabus_plan->course_session = $request_syllabus_plan->course_session;
                    $syllabus_plan->num_of_lessons = $request_syllabus_plan->num_of_lessons;
                    $syllabus_plan->session_type = $request_syllabus_plan->session_type;
                    $session_type = SessionType::find($request_syllabus_plan->session_type);
                    if (!$session_type) {
                        return ResponseBuilder::Fail([
                            'message' => 'Không tìm thấy loại buổi học',
                            'permitted' => true,
                            'success' => false
                        ]);
                    }
                    $syllabus_plan->tutor_required = intval($request_syllabus_plan->tutor_required);
                    $syllabus_plan->noi_dung = $request_syllabus_plan->noi_dung;
                    $syllabus_plan->description = $session_type->session_type;
                    $syllabus_plan->save();
                    $lessons_check += $request_syllabus_plan->num_of_lessons;
                }
            }
            if ($lessons_check != $num_of_lessons) {
                DB::rollBack();
                return ResponseBuilder::Fail([
                    'message' => 'Tổng số tiết học đang lệch so với giáo trình.',
                    'permitted' => true,
                    'success' => false
                ]);
            }
            DB::commit();
            return ResponseBuilder::Success([],[
                'message' => 'Cập nhật thành công',
                'permitted' => true,
                'success' => true
            ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::error("Error handleEditSyllabusPlan: " . $th->getLine() . " - " . $th->getMessage());
            return ResponseBuilder::Fail([
                'message' => 'Cập nhật thất bại - ' . $th->getLine() . " - " . $th->getMessage(),
                'permitted' => true,
                'success' => false
            ]);
        }
    }

    public function handleEditGradeGroup(Request $request)
    {
        DB::beginTransaction();
        try {
            foreach ($request->grade_group as $grade_group_request) {
                $grade_group_request =  (object) $grade_group_request;
                $grade_group = GradeGroup::where('id', $grade_group_request->id)->firstOrFail();
                if ($grade_group_request->need_delete) {
                    GradeGroup::where('id', $grade_group_request->id)->delete();
                    Grade::where('grade_group_id', $grade_group_request->id)->delete();
                    continue;
                } else {
                    $grade_group->grade_group_name = $grade_group_request->grade_group_name;
                    $grade_group->weight = $grade_group_request->weight;
                    $grade_group->minimum_required = $grade_group_request->minimum_required;
                    $grade_group->number_grade = $grade_group_request->number_grade;
                    if ($grade_group_request->number_grade != Grade::where('grade_group_id', $grade_group_request->id)->count()) {
                        Grade::where('grade_group_id', $grade_group_request->id)->delete();
                        for ($i = 0; $i < $grade_group_request->number_grade; $i++) {
                            $data = new \stdClass();
                            $data->grade_group_id = $grade_group_request->id;
                            $data->syllabus_id = $grade_group_request->syllabus_id;
                            $data->syllabus_name = $grade_group_request->syllabus_name;
                            $data->subject_id = $grade_group_request->subject_id;
                            $data->subject_name = $grade_group_request->subject_name;

                            $res = $this->handleCreateEditGrade($data);
                            if ($res == 0) {
                                DB::rollBack();
                                return ResponseBuilder::Fail([
                                    'message' => 'Cập nhật thất bại - khởi tạo t7_grade thất bại ',
                                    'permitted' => true,
                                    'success' => false
                                ]);
                            }
                        }
                    }
                    $grade_group->save();
                }
            }
            DB::commit();
            return ResponseBuilder::Success([
                'message' => 'Cập nhật thành công',
                'permitted' => true,
                'success' => true
            ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::error("Error handleEditGradeGroup: " . $th->getLine() . " - " . $th->getMessage());
            return ResponseBuilder::Fail([
                'message' => 'Cập nhật thất bại - ' . $th->getMessage(),
                'permitted' => true,
                'success' => false
            ]);
        }
    }

    public function handleEditGrades(Request $request)
    {
        DB::beginTransaction();
        try {
            foreach ($request->grades as $grade_request) {
                $grade_request = (object) $grade_request;
                $res = $this->handleCreateEditGrade($grade_request);
                if ($res == 0) {
                    return ResponseBuilder::Fail([
                        'message' => 'Cập nhật thất bại - khởi tạo t7_grade thất bại ',
                        'permitted' => true,
                        'success' => false
                    ]);
                }
            }
            DB::commit();
            return ResponseBuilder::Success([
                'message' => 'Cập nhật thành công',
                'permitted' => true,
                'success' => true
            ]);
        } catch (\Throwable $th) {
            Log::error("Error editGrades: " . $th->getLine() . " - " . $th->getMessage());
            DB::rollBack();
            return ResponseBuilder::Fail([
                'message' => 'Cập nhật thất bại - ' . $th->getMessage(),
                'permitted' => true,
                'success' => false
            ]);
        }
    }

    private function handleCreateEditGrade($data)
    {
        try {
            if (isset($data->id) && $data->id != null) {
                $grade = Grade::where('id', $data->id)->firstOrFail();
            } else {
                $grade = new Grade();
            }
            $grade->grade_name = isset($grade->grade_name) ? $data->grade_name : ($grade->grade_name ? $grade->grade_name : "Grade Name ");
            $grade->grade_group_id = isset($data->grade_group_id) ? $data->grade_group_id : ($grade->grade_group_id ? $grade->grade_group_id : 0);
            $grade->syllabus_id = isset($data->syllabus_id) ? $data->syllabus_id : ($grade->syllabus_id ? $grade->syllabus_id : 0);
            $grade->syllabus_name = isset($data->syllabus_name) ? $data->syllabus_name : ($grade->syllabus_name ? $grade->syllabus_name : "");
            $grade->subject_id = isset($data->subject_id) ? $data->subject_id : ($grade->subject_id ? $grade->subject_id : 0);
            $grade->subject_name = isset($data->subject_name) ? $data->subject_name : ($grade->subject_name ? $grade->subject_name : "");
            $grade->weight = isset($data->weight) ? $data->weight : ($grade->weight ? $grade->weight : 0);
            $grade->minimum_required = isset($data->minimum_required) ? $data->minimum_required : ($grade->minimum_required ? $grade->minimum_required : 0);
            $grade->course_session  = isset($data->course_session) ? $data->course_session : ($grade->course_session ? $grade->course_session : 0);
            $grade->allow_resit = isset($data->allow_resit) ? $data->allow_resit : ($grade->allow_resit ? $grade->allow_resit : 0);
            $grade->is_final_exam = isset($data->is_final_exam) ? $data->is_final_exam : ($grade->is_final_exam ? $grade->is_final_exam : 0);
            $grade->master_grade = isset($data->master_grade) ? $data->master_grade : ($grade->master_grade ? $grade->master_grade : 0);
            $grade->substitute_grade = isset($data->substitute_grade) ? $data->substitute_grade : ($grade->substitute_grade ? $grade->substitute_grade : 0);
            $grade->is_locked = isset($data->is_locked) ? $data->is_locked : ($grade->is_locked ? $grade->is_locked : 0);
            $grade->day_to_lock = isset($data->day_to_lock) ? $data->day_to_lock : ($grade->day_to_lock ? $grade->day_to_lock : 0);
            $grade->grade_type = isset($data->grade_type) ? $data->grade_type : ($grade->grade_type ? $grade->grade_type : 0);
            $grade->pgrade_group_name = isset($data->pgrade_group_name) ? $data->pgrade_group_name : ($grade->pgrade_group_name ? $grade->pgrade_group_name : "");
            $grade->duration = isset($data->duration) ? $data->duration : ($grade->duration ? $grade->duration : 0);
            $grade->bonus_type = isset($data->bonus_type) ? $data->bonus_type : ($grade->bonus_type ? $grade->bonus_type : 0);
            $grade->max_point = isset($data->max_point) ? $data->max_point : ($grade->max_point ? $grade->max_point : 0);
            $res = $grade->save();

            if (!(isset($data->id) && $data->id != null)) {
                $grade->grade_name = $grade->grade_name . " " . $grade->id;
                $res = $grade->save();
            }
            $res2nd = true;
            if ($grade->allow_resit == 1) {
                $grade_group = GradeGroup::where('id', $grade->grade_group_id)->firstOrFail();
                $grade_group->number_grade = $grade_group->number_grade + 1;
                $grade_group->save();
                $grade_2nd = $grade->replicate();
                $grade_2nd->grade_name = $grade_2nd->grade_name . " 2nd";
                $grade_2nd->allow_resit = 0;
                $grade_2nd->master_grade = $grade->id;
                $res2nd = $grade_2nd->save();
                $grade->substitute_grade = $grade_2nd->id;
                $grade->save();
            }
            if ($res && $res2nd) {
                return true;
            } else {
                return false;
            }
        } catch (\Throwable $th) {
            Log::error("Error handleCreateEditGrade: " . $th->getLine() . " - " . $th->getMessage());
            return false;
        }
    }

    public function getListSubject(Request $request){
        try {
            // get all subject order by id desc
            $subjects = Subject::orderBy('id', 'desc')->get();
            // if $request->only_subject_code == true then return only subject_code
            if($request->only_subject_code){
                $subjects = $subjects->pluck('subject_code');
            }
            return ResponseBuilder::Success($subjects);
        } catch (\Throwable $th) {
            Log::error("Error getListSubject: " . $th->getLine() . " - " . $th->getMessage());
            return ResponseBuilder::Fail($th->getMessage());
        }
    }

    public function getChangeSubjectStudent($request) {
        $curriculum_id = Arr::get($request->all(), 'curriculum_id', null);
        $student_code = Arr::get($request->all(), 'student_code', null);
        $original_subject = Arr::get($request->all(), 'original_subject', null);
        $alternative_subject = Arr::get($request->all(), 'alternative_subject', null);
        $query = User::query();
        $query->with('change_subject_student', function ($query) {
            $query->select('student_login', 'old_subject', 'new_subject');
        });
        $query->whereHas('change_subject_student', function ($query) use ($original_subject, $alternative_subject) {
            $query->select('student_login', 'old_subject', 'new_subject');
            if ($original_subject) {
                $query->where('old_subject', $original_subject);
            }
            if ($alternative_subject) {
                $query->where('new_subject', $alternative_subject);
            }
        });
        $query->join('curriculum', 'curriculum.id', '=', 'user.curriculum_id');
        if ($curriculum_id) {
            $query->where('curriculum_id', $curriculum_id);
        }
        if ($student_code) {
            $query->where('user_code', $student_code);
        }

        $result = $query->paginate($request->per_page, [
            'user.user_surname',
            'user.user_middlename',
            'user.user_givenname',
            'user.user_login',
            'user.user_code',
            'user.curriculum_id',
            'curriculum.name'
        ]);
        return $result;
    }

    public function ChangeSubjectStudentAdd($student_code, $old_subject, $new_subject, $number_decicion, $actor) {
        if ($user = User::where('user_code', $student_code)->first()) {
            $subject = Subject::where('subject_code', $new_subject)->orWhere('skill_code', $new_subject)->first();
            $oldSubject = Subject::where('subject_code', $old_subject)->orWhere('skill_code', $old_subject)->first();
            if ($subject) {
                DB::beginTransaction();
                try {
                    // $courseResult = CourseResult::where('psubject_code', $subject->subject_code)->where('student_login', $user->user_login)->first();
                    $period_ordering = StudentSubject::where('subject_code', $old_subject)->where('student_login', $user->user_login)->first();

                    // Nếu chưa được add vào studentSubject
                    if (!$period_ordering) {
                        // kiểm tra trong syllabus
                        $oldSubjectPeriod = Period::select([
                            'period.id',
                            'period.ordering',
                            'period_subject.subject_id',
                            'period_subject.skill_code',
                            'period_subject.subject_code',
                        ])
                        ->join('period_subject', 'period_subject.period_id', '=', 'period.id')
                        ->where('period_subject.skill_code', $oldSubject->skill_code)
                        ->where('period.curriculum_id', $user->curriculum_id)
                        ->first();
                        
                        // Kiểm tra trong lịch sử học
                        if ($oldSubjectPeriod) {
                            $courseResultOldSubject = CourseResult::query()
                            ->where('student_login', $user->user_login)
                            ->where('skill_code', $oldSubject->skill_code)
                            ->first();

                            // có thì thêm vào thôi
                            if ($courseResultOldSubject) {
                                $studentSubject = StudentSubject::create([
                                    "student_login" => $user->user_login,
                                    "subject_id" => $oldSubject->id,
                                    "number_of_credit" => $oldSubject->num_of_credit,
                                    "skill_code" => $oldSubject->skill_code,
                                    "subject_name" => $oldSubject->subject_name,
                                    "subject_code" => $oldSubject->subject_code,
                                    "is_start" => 0,
                                    "is_lock_edit" => 0,
                                    "number_decide" => $number_decicion,
                                    "note" => "",
                                    "period_ordering" => $oldSubjectPeriod->ordering,
                                ]);
                            }
                        }
                    }

                    if ($period_ordering) {
                        $studentSubject = StudentSubject::updateOrCreate(
                            [
                                "subject_code" => $subject->subject_code,
                                "student_login" => $user->user_login,
                            ],
                            [
                                "subject_id" => $subject->id,
                                "student_login" => $user->user_login,
                                "subject_id" => $subject->id,
                                "number_of_credit" => $subject->num_of_credit,
                                "skill_code" => $subject->skill_code,
                                "subject_name" => $subject->subject_name,
                                "subject_code" => $subject->subject_code,
                                "is_start" => 0,
                                "is_lock_edit" => 0,
                                "number_decide" => $number_decicion,
                                "note" => "",
                                "period_ordering" => $period_ordering->period_ordering,
                            ]
                        );
                    }
                    
                    if (isset($studentSubject) && $studentSubject) {
                        // $term = Term::where('id', $courseResult->term_id)->first();
                        $oldSubject = Subject::where('subject_code', $old_subject)->orWhere('skill_code', $old_subject)->first();
                        $changeSubjectStudent = ChangeSubjectStudent::create([
                            'student_login' => $user->user_login,
                            'old_subject' => $old_subject,
                            'new_subject' => $new_subject
                        ]);

                        $mienGiamTapTrung = MienGiamTapTrung::create([
                            'user_code' => $user->user_code,
                            'student_login' => $user->user_login,
                            'subject_code' => $old_subject,
                            'skill_code' => $oldSubject->skill_code,
                            'subject_code_new' => $subject->subject_code,
                            'skill_code_new' => $subject->skill_code,
                            'type' => 1,
                            'term_name' => "",
                            'so_quyet_dinh' => $number_decicion,
                            'time_action' => "0000-00-00",
                            'user_action' => "",
                            'status' => 0,
                            'created_at' => null,
                            'updated_at' => null,
                            'created_by' => Auth::user()->user_login,
                            'term_id'  => null
                        ]);

                        $updateStudentSubject = StudentSubject::where('skill_code', $oldSubject->skill_code)
                        ->where('student_login', $user->user_login)->update([
                            'active' => -1,
                            'is_lock_edit' => 1
                        ]);

                        $systemLog = SystemLog::create([
                            'object_name' => "Thay thế môn",
                            'actor' => $actor->user_login,
                            'log_time' => Carbon::now()->format('Y-m-d H:i:s'),
                            'action' => "Thay thế môn khác",
                            'description' => "Thay thế môn $old_subject bằng môn $new_subject cho sinh viên $user->user_login.",
                            'object_id' => $changeSubjectStudent->id,
                            'brief' => "",
                            'from_ip' => "",
                            'relation_login' => $user->user_login,
                            'relation_id' => $user->id,
                            'nganh_cu' => "",
                            'nganh_moi' => "",
                            'ky_chuyen_den' => "",
                            'ky_thu_cu' => ""
                        ]);

                        DB::commit();
                        return [
                            "message" => "Thêm mới thay thế thành công",
                            "log" => $systemLog,
                            "status" => 200
                        ];
                    } else {
                        DB::rollBack();
                        return [
                            "message" => "fail, Hệ thống không cho phép Thay thế môn $old_subject bằng môn $new_subject.",
                            "status" => 500
                        ];
                    }
                } catch (\Throwable $th) {
                    throw $th;
                    DB::rollback();
                    return [
                        "message" => "fail",
                        "log" => $th,
                        "status" => 500
                    ];
                }
            } else {
                DB::rollBack();
                return [
                    "message" => "fail, Mon hoc khong ton tai $new_subject.",
                    "status" => 500
                ];
            }
        } else {
            return [
                "message" => "Student not found",
                "status" => 500
            ];
        }
    }


    /**
     * Cập nhập số lượng min-max theo môn
     * 
     * <AUTHOR>
     * @param  \Illuminate\Http\Request $request
     * @return App\Utils\ResponseBuilder  
     */

    public function updateNumberStudent($request) {
        $rules = [
            'id' => "required|exists:subject,id",
            'min_student' => "required|numeric|min:5|max:10000",
            'max_student' => "required|numeric|min:5|max:10000",
        ];
        
        $messages = [
            'id.required' => 'Môn học không được bỏ trống',
            'id.exists' => 'Môn học Không tồn tại',
            'min_student.required' => 'số lượng sinh viên tối thiểu được bỏ trống',
            'min_student.numeric' => 'số lượng sinh viên tối thiểu không đúng định dạng',
            'min_student.min' => 'số lượng sinh viên tối thiểu ít nhất phải bằng 5',
            'max_student.required' => 'số lượng sinh viên tối đa được bỏ trống',
            'max_student.numeric' => 'số lượng sinh viên tối đa không đúng định dạng',
            'max_student.min' => 'số lượng sinh viên tối đa ít nhất phải bằng 5',
        ];

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            $mess = $validator->errors()->first();
            return ResponseBuilder::Fail($mess);
        }

        // Khởi tạo dữ liệu
        $id = $request->get('id');
        $minStudent = $request->get('min_student');
        $maxStudent = $request->get('max_student');

        // Lấy môn học dựa vào ID
        $subjectUpdate = Subject::Where('id', $id)->first();
        try {
            $subjectUpdate->max_student = $maxStudent;
            $subjectUpdate->min_student = $minStudent;
            $subjectUpdate->save();

            return ResponseBuilder::Success(null, "Cập nhập số lượng sinh viên thành công");
        } catch (\Exception $ex) {
            Log::error("[dev check] : Cập nhập số lượng sinh viên lỗi");
            Log::error($ex);
            return ResponseBuilder::Fail($ex->getMessage());
        }
    }

    public function downloadSubject($request) {
        $data = Subject::select([
            'subject.id',
            'subject.subject_code',
            'subject.subject_name',
            'subject.skill_code',
            'subject.num_of_credit',
            'subject.max_student',
            'subject.min_student',
            'subject.subject_type',
            DB::raw("CASE 
                WHEN subject_type = 'Online' THEN 'Học trực tuyến'
                WHEN subject_type = 'Blended' THEN 'Học kết hợp'
                WHEN subject_type = 'Integrated' THEN 'Học tích hợp'
                WHEN subject_type = 'Traditional' THEN 'Học truyền thống'
                ELSE '--' 
                END as subject_type_name"
            ),
            'subject.department_id',
            'department.department_name'
            ])->leftJoin('department', 'department.id', '=', 'subject.department_id')
            ->get(); 

        return $data;
    }

    public function getGDQPSubjects($request) {
        $subjects = SubjectsForGraduation::where('status', 1)->where('subject_type', 2)->get();
        return ResponseBuilder::Success($subjects);
    }

    public function getSubjectsForGraduation($request) {
        try {
            $subjectType = $request->subject_type;
            $subjectCode = $request->subject_code;
            $perPage = $request->per_page ?? 8;
            $subjects = SubjectsForGraduation::query();
            
            $subjects->where('status', 1)->where('subject_type', $subjectType);
            if ($subjectCode) {
                $subjects->where('subject_code', $subjectCode);
            }
            $list = $subjects->paginate($perPage);
            return ResponseBuilder::Success([
                'list' => $list,
                'subject_type' => $subjectType
            ]);
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::Fail($th->getMessage());
        }
    }

    public function importSubjectsForGraduation($request) {
        try {
            $fail = 0;
            $success = 0;
            $actor = Auth::user();
            $currentDate = Carbon::now();
            
            $subjectType = $request->subject_type ?? null;
            if (!$subjectType && !in_array($subjectType, [1, 2, 3, 4])) {
                return response()->json([
                    'message' => 'Loại môn học không hợp lệ.',
                    'status_code' => 500
                ], 200);
            }

            $rows = Excel::toArray(new BaseModelImport, $request->file('file'))[0];
            $subjectCodes = array_column($rows, 'subject_code');
            $subjects = Subject::whereIn('subject_code', $subjectCodes)->get();

            foreach ($subjects as $subject) {
                SubjectsForGraduation::where('subject_type', $subjectType)
                ->where('subject_code', $subject->subject_code)
                ->update([
                    'status' => 0,
                ]);
                SubjectsForGraduation::create([
                    'subject_id' => $subject->id,
                    'subject_code' => $subject->subject_code,
                    'skill_code' => $subject->skill_code,
                    'subject_name' => $subject->subject_name,
                    'subject_type' => $subjectType,
                    'status' => 1,
                    'created_at' => $currentDate,
                    'updated_at' => $currentDate,
                    'updated_by' => $actor->user_login,
                ]);
                ActionLog::create([
                    'object'        => 'mon_hoc',
                    'auth'          => $actor->user_login,
                    'action'        => 'add',
                    'description'   => "Thêm mới môn học vào bảng subjects_for_graduation $subject->subject_code",
                    'object_id'     => "",
                    'data_changed'  => "",
                    'ip'            => $request->getClientIp(),
                ]);
                $success++;
            }
            $fail = count($subjects) - $success;
            return ResponseBuilder::Success([
                'success' => $success,
                'fail' => $fail,
            ]);
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::Fail($th->getMessage());
        }
    }

    public function deleteSubjectsForGraduation($request, $id) {
        try {
            $actor = Auth::user();
            if (!$actor) {
                return ResponseBuilder::Fail('Không tìm thấy người dùng');
            }
            $subjectsForGraduation = SubjectsForGraduation::find($id);
            if (!$subjectsForGraduation) {
                return ResponseBuilder::Fail('Môn học không tồn tại');
            }
            $subjectsForGraduation->status = 0;
            $subjectsForGraduation->save();
            ActionLog::create([
                'object'        => 'mon_hoc',
                'auth'          => $actor->user_login,
                'action'        => 'delete',
                'description'   => "Xóa môn học khỏi bảng subjects_for_graduation $subjectsForGraduation->subject_code",
                'object_id'     => "",
                'data_changed'  => "",
                'ip'            => $request->getClientIp(),
            ]);
            return ResponseBuilder::Success('Xóa môn học thành công.');
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::Fail($th->getMessage());
        }
    }
}
