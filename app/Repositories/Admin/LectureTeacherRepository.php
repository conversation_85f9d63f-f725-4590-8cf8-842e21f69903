<?php

namespace App\Repositories\Admin;

use App\Http\Lib;
use Carbon\Carbon;
use App\Models\Fu\User;
use App\Models\Fu\Subject;
use App\Models\Fu\Activity;
use App\Models\SessionType;
use Illuminate\Http\Request;
use App\Models\Fu\Attendance;
use App\Models\Fu\Department;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportLectureTeacher;
use Illuminate\Support\Facades\Storage;

class LectureTeacherRepository extends BaseRepository
{

    public function getModel()
    {
        return Activity::class;
    }

    public function indexLectureTeacher()
    {
        return $this->view('lecture.teacher');
    }

    /**
     * <AUTHOR> dev
     * @version 1.1
     * @since 27/11/2023
     * 
     * Lấy dữ liệu và export thống kê ca học, coi thi
     */
    public function getListLectureTeacher(Request $request)
    {
        
        ini_set('max_execution_time', -1);
        try {
            $exportExcel = isset($request->exportExcel) ? $request->exportExcel : null;
            $list_Traditional = [];
            $list_Integrated = [];
            $list_Online = [];
            $list_Blended = [];
            $list_Null = [];
            $total_time_lecture_teacher = [];
            $total_slot_lecture_teacher = [];

            // tìm ki?m nh?ng môn h?c D? án t?t nghi?p
            $graduation_project = Subject::where('subject_code', 'like', 'PRO2%')->get('subject_code');
            $list_department_lesson = Department::query()
                ->select(['id', 'department_name'])
                ->get();
            $list_subject_code = Subject::query()
                ->whereNotIn('subject_code', $graduation_project)
                ->select(['subject_code', 'subject_name'])
                ->get();
            $list_teacher_lesson = User::query()
                ->where('user_level', '=', 2)
                ->select('user_login')
                ->get();
            $list_lecture_teacher = User::query()->where('user_level', '=', 2);

            if (isset($exportExcel) && $exportExcel == 'exportExcel' || $request->filter_total_time == 1) {
                // limit data query
                $totalUser = $list_lecture_teacher->count();
                $listData = null;
                $limit = 500;
                $offset = ceil($totalUser / $limit);
                for ($i = 0; $i < $offset * $limit; $i += $limit) {
                    $result_query = $this->queryGetData($list_lecture_teacher, $request, $i, $limit, $graduation_project, $exportExcel);
                    if ($listData == null) {
                        $listData = $result_query;
                    } else {
                        $listData = $listData->merge($result_query);
                    }
                }
                $list_lecture_teacher = $listData;
            } else {
                // get data view
                $list_lecture_teacher = $this->queryGetData($list_lecture_teacher, $request, null, null, $graduation_project, $exportExcel);
            }
            // tính tổng ca, thời gian coi thi
            $start_date = $request->start_date;
            $end_date = $request->end_date;
            $subject_code = $request->get('subject_code', null);

            $moreString = '';
            if ($subject_code != null) {
                $moreString = 'AND list_group.psubject_code = "' . $subject_code . '"';
            }

            // Query lấy số lượng slot coi thi
            // 1, Lấy tổng số ca coi thi theo leader_login
            // 2, Lấy tổng số ca coi thi theo leader_login2
            // UNION lại và Tính tổng số ca 
            $sqlExam = "SELECT 
                    custom_total_exam_slot.leader_login,
                    SUM(custom_total_exam_slot.total_x) AS total_slot
                FROM (
                    (
                        SELECT 
                            activity.leader_login AS leader_login, 
                            COUNT(activity.leader_login) AS total_x
                        FROM activity
                        LEFT JOIN list_group ON activity.groupid = list_group.id
                        WHERE 1 = 1
                            AND activity.day >= ? AND activity.day <= ?
                            AND activity.session_type IN (9, 10, 11, 19, 23)
                            AND list_group.type = 1
                            AND activity.slot > 0
                            $moreString
                        GROUP BY activity.leader_login
                    )
                    UNION 
                    (
                        SELECT 
                            activity.leader_login2 AS leader_login, 
                            COUNT(activity.leader_login2) AS total_x
                        FROM activity
                        LEFT JOIN list_group ON activity.groupid = list_group.id
                        WHERE 1 = 1
                            AND activity.day >= ? AND activity.day <= ?
                            AND activity.session_type IN (9, 10, 11, 19, 23)
                            AND list_group.type = 1
                            AND activity.slot > 0
                            $moreString
                            AND activity.leader_login2 IS NOT NULL
			                AND activity.leader_login2 <> ''
                        GROUP BY activity.leader_login2
                    )
                ) custom_total_exam_slot
                GROUP BY leader_login";
            $TotalExams =  DB::select($sqlExam, [$start_date, $end_date, $start_date, $end_date]);

            // format dữ liệu sau khi có kết quả
            $arrLeaderSlot = [];
            foreach ($TotalExams as $key => $value) {
                $arrLeaderSlot[$value->leader_login] = $value->total_slot;
            }

            foreach ($list_lecture_teacher as $lecture_teacher) {
                if (!isset($list_Traditional[$lecture_teacher->user_login]) && !isset($list_Integrated[$lecture_teacher->user_login]) && !isset($list_Online[$lecture_teacher->user_login]) && !isset($list_Blended[$lecture_teacher->user_login]) && !isset($list_Null[$lecture_teacher->user_login])) {
                    $list_Traditional[$lecture_teacher->user_login] = 0;
                    $list_Integrated[$lecture_teacher->user_login] = 0;
                    $list_Online[$lecture_teacher->user_login] = 0;
                    $list_Blended[$lecture_teacher->user_login] = 0;
                    $list_Null[$lecture_teacher->user_login] = 0;
                }

                if (count($lecture_teacher->activity) >= 1) {
                    foreach ($lecture_teacher->activity as $activity) {
                        if ((count($activity->attendance) >= 1 && $activity->session_type != 21) || ($activity->session_type == 21)) {
                            $subject_type = $activity->subject_type;
                            // slot traditional
                            if (strtolower($subject_type) == 'traditional') {
                                $list_Traditional[$lecture_teacher->user_login] += 1;
                            }
                            // slot Integrated
                            if (strtolower($subject_type) == 'integrated') {
                                $list_Integrated[$lecture_teacher->user_login] += 1;
                            }
                            // slot Online
                            if (strtolower($subject_type) == 'online') {
                                $list_Online[$lecture_teacher->user_login] += 1;
                            }
                            // slot Blended
                            if (strtolower($subject_type) == 'blended') {
                                $list_Blended[$lecture_teacher->user_login] += 1;
                            }
                            // slot Null
                            if ($subject_type == null || strtolower($subject_type) == 'null') {
                                $list_Null[$lecture_teacher->user_login] += 1;
                            }
                        }
                    }
                }

                // Gán dữ liệu vào (Chạy nhanh như gió)
                $lecture_teacher->total_slot_exam = $arrLeaderSlot[$lecture_teacher->user_login] ?? 0;
                $lecture_teacher->total_time_exam = ($arrLeaderSlot[$lecture_teacher->user_login] ?? 0) * 2;
            }

            foreach ($list_lecture_teacher as $key => $lecture_teacher) {
                if (empty($total_slot_lecture_teacher[$lecture_teacher->user_login])) {
                    $total_Traditional = $list_Traditional[$lecture_teacher->user_login];
                    $total_Integrated = $list_Integrated[$lecture_teacher->user_login];
                    $total_Online = $list_Online[$lecture_teacher->user_login];
                    $total_Blended = $list_Blended[$lecture_teacher->user_login];
                    $total_Null = $list_Null[$lecture_teacher->user_login];
                    if ($request->filter_total_time == 1) {
                        if ($total_Traditional == 0 && $total_Integrated == 0 && $total_Online == 0 && $total_Blended == 0 && $total_Null == 0) {
                            unset($list_lecture_teacher[$key]);
                            continue;
                        }
                    }

                    $total_slot_lecture_teacher[$lecture_teacher->user_login] = $total_Traditional + $total_Integrated + $total_Online + $total_Blended + $total_Null;
                    $total_time_lecture_teacher[$lecture_teacher->user_login] = $total_slot_lecture_teacher[$lecture_teacher->user_login] * 2;
                }
            }

            if (isset($exportExcel) && $exportExcel == 'exportExcel') {
                $export = new ExportLectureTeacher([
                    'list_lecture_teacher' => $list_lecture_teacher,
                    'list_Traditional' => $list_Traditional,
                    'list_Integrated' => $list_Integrated,
                    'list_Online' => $list_Online,
                    'list_Blended' => $list_Blended,
                    'list_Null' => $list_Null,
                    'total_slot_lecture_teacher' => $total_slot_lecture_teacher,
                    'total_time_lecture_teacher' => $total_time_lecture_teacher,
                ]);
                return Excel::download($export, 'thong_ke_gio_giang_theo_giang_vien.xlsx');
            }

            return response()->json([
                'list_lecture_teacher' => $list_lecture_teacher,
                'list_teacher_lesson' => $list_teacher_lesson,
                'list_department_lesson' => $list_department_lesson,
                'list_subject_code' => $list_subject_code,
                'list_Traditional' => $list_Traditional,
                'list_Integrated' => $list_Integrated,
                'list_Online' => $list_Online,
                'list_Blended' => $list_Blended,
                'list_Null' => $list_Null,
                'total_slot_lecture_teacher' => $total_slot_lecture_teacher,
                'total_time_lecture_teacher' => $total_time_lecture_teacher,
            ]);
        } catch (\Throwable $th) {
            Log::error("-----------------------start err getListLectureTeacher-------------------");
            Log::error($th->getFile() . " - " . $th->getLine() . " : " . $th->getMessage());
            Log::error("-----------------------end err getListLectureTeacher-------------------");
            return response("", 500);
        }
    }

    public function queryGetData($list_lecture_teacher, $request, $skip, $limit, $graduation_project, $exportExcel)
    {
        ini_set("memory_limit", "-1");
        ini_set('max_execution_time', -1);

        try {
            $department = $request->department;
            $subject_code = $request->subject_code;
            $teacher = $request->teacher;
            $start_date = $request->start_date;
            $end_date = $request->end_date;

            $list_lecture_teacher
                ->where('user_level', '!=', 3)
                ->whereRaw('LENGTH(user_login) > 0')
                ->when($exportExcel != null && $exportExcel == 'exportExcel' || $request->filter_total_time == 1, function ($query) use ($skip, $limit) {
                    $query->skip($skip)->limit($limit);
                })
                ->when(isset($teacher) && $teacher != null && $teacher != 'null', function ($query) use ($teacher) {
                    $query->where('user.user_login', $teacher);
                })
                ->with(['activity' => function ($query) use ($start_date, $end_date, $subject_code, $department, $graduation_project) {
                    $query
                        // ->where([ ['activity.day', '>=', $start_date], ['activity.day', '<=', $end_date] ])
                        ->where([['activity.done', '=', 1], ['activity.session_type', '!=', 18]])
                        ->whereNotIn('activity.session_type', [9, 10, 11, 19])
                        ->with(['attendance' => function ($q) {
                            $q->groupBy('activity_id')->select('activity_id');
                        }])
                        ->join('list_group', 'list_group.id', '=', 'activity.groupid')
                        ->where('list_group.is_virtual', '!=', 1)
                        ->join('subject', 'subject.subject_code', '=', 'activity.psubject_code')
                        ->whereNotIn('subject.subject_code', $graduation_project);

                    if (isset($start_date) && $start_date != null) {
                        $query->where('activity.day', '>=', $start_date);
                    }
                    if (isset($end_date) && $end_date != null) {
                        $query->where('activity.day', '<=', $end_date);
                    }
                    if ($department != null) {
                        $query->where('activity.department_id', $department);
                    }
                    if ($subject_code != null && $subject_code != 'null') {
                        $query->where('activity.psubject_code', $subject_code);
                    }

                    $query->select([
                        'activity.id',
                        'activity.leader_login',
                        'activity.slot',
                        'activity.day',
                        'activity.session_description',
                        'activity.session_type',
                        'list_group.group_name',
                        'list_group.id as group_id',
                        'subject.subject_type',
                    ]);
                }])
                ->select([
                    'id',
                    'user_login',
                    'user_surname',
                    'user_middlename',
                    'user_givenname',
                    'user_code',
                ]);

            if (isset($exportExcel) && $exportExcel == 'exportExcel' || $request->filter_total_time == 1) {
                $list_lecture_teacher = $list_lecture_teacher->get();
            } else {
                $list_lecture_teacher = $list_lecture_teacher->paginate(10);
            }

            return $list_lecture_teacher;
        } catch (\Throwable $th) {
            Log::error("-----------------------start err getListLectureTeacher-------------------");
            Log::error($th);
            Log::error("-----------------------end err getListLectureTeacher-------------------");
            return response([], 500);
        }
    }
}
