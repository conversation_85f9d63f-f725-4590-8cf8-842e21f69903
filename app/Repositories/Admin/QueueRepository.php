<?php


namespace App\Repositories\Admin;


use App\Models\LQueue;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Auth;

class QueueRepository extends BaseRepository
{
    public function getModel()
    {
        return LQueue::class;
    }

    public function getQueueByType($request)
    {
        $type = $request->type;
        $result = $this->_model->where('title', $type)->where('created_by', Auth::user()->user_login)->orderBy('created_at', 'desc')->limit(10)->get();
        foreach ($result as $item)
        {
            $item->diff = $item->updated_at->diffForHumans();
        }

        return $result;
    }
}
