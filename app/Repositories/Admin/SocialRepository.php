<?php


namespace App\Repositories\Admin;


use App\Models\SocialUser;
use App\Repositories\BaseRepository;
use Illuminate\Http\Request;

class SocialRepository extends BaseRepository
{
    const FACEBOOK = 2;
    public function getModel()
    {
        return SocialUser::class;
    }

    public function apiStore($request)
    {
        $sender_id = $request->sender_id;
        $provider_id = $request->provider_id;
        if (!$sender_id) {
            return 'Cyber Uni không thấy tên của bạn T_T';
        }
        $social = SocialUser::where('provider_id', $provider_id)->where('provider_type', self::FACEBOOK)->first();
        if($social) {
            if ($social->sender_id) {
                return 'Ủa mình đã là bạn rồi mà, chẳng lẽ bạn không nhớ ư. Cyber Uni *dỗi T_T';
            } else {
                $social->sender_id = $sender_id;
                $social->save();

                return 'Chúc mừng bạn đã hoàn tất kết nối với Cyber Uni, nhập =help hoặc =? để xem các lệnh hiện có nha :3';
            }
        } else {
            return 'Có vẻ mình không tìm thấy tên bạn trong danh sách, bạn đã kết nối với Cyber Uni trong hồ sơ cá nhân chưa :3';
        }
    }

    public function getStudentLogin($request)
    {
        $sender_id = $request->sender_id;
        $type = self::FACEBOOK;
        if (!$sender_id) {
            return [
                'status' => 'error',
                'messages' => 'Không tìm thấy mã!',
                'data' => [],
            ];
        }
        $social = SocialUser::where('sender_id', $sender_id)->where('provider_type', $type)->first();
        if ($social) {
            return [
                'status' => 'success',
                'messages' => '',
                'data' => [
                    'student_login' => $social->student_login,
                ]
            ];
        }

        return [
            'status' => 'error',
            'messages' => 'Bạn chưa kết nối tới Cyber Uni vui lòng nhập =connect để kết nối cùng Cyber Uni nào :3',
            'data' => [],
        ];
    }
}
