<?php
namespace App\Repositories\Admin;

use App\Models\Dra\CurriCulum;
use App\Exports\Diploma\ListDiploma;
use App\Models\Dra\T1UserRole;
use App\Models\Fu\GraduationCampaign;
use App\Models\Fu\GraduationCampaignUser;
use App\Models\Fu\User;
use App\Models\SystemLog;
use App\Repositories\BaseRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class DiplomaRepository extends BaseRepository
{

    /**
     * get model
     * @return string
     */
    public function getModel()
    {
        return User::class;
    }


    public function index(Request $request) {
        $input = $request->all();
        $curriculums = CurriCulum::orderBy('id', 'desc')->get();

        $graduationCampaigns = GraduationCampaign::select([
            'id', 
            'term_name', 
            'ordering',
            'Legal_entity',
            DB::raw('CONCAT ("[", term_name, "] [", IF(Legal_entity = 1, "trường", "khối"),"] Đợt ", ordering) AS name')
        ])->orderBy('id', 'desc')
        ->get();

        return view('admin_v1.diploma.index', [
            'curriculums' => $curriculums,
            'graduationCampaigns' => $graduationCampaigns
        ]);
    }

    public function import(Request $request) {
        $user = auth()->user();
        $request->validate([
            'file' => 'required' // mimes:xlsx,xls,csv'
        ], [
            'file.required' => "File import không được bỏ trống",
            'file.mimes' => "Không đúng định dạng file (xlsx, xls, csv)"
        ]);

        if ($user->user_level != 1) {
            $listRole = T1UserRole::where('user_login', $user->user_login)->get();
            $listRole = array_column($listRole->toArray(), 'role_id');
            if (!in_array(48, $listRole) && !in_array(44, $listRole) && !in_array(1, $listRole)) {
                return redirect()->back()->with([
                    'status' => [
                        'type' => 'danger', 
                        'messages' => 'Bạn không có quyền import'
                    ]
                ]);
            }
        }

        $file = $request->file('file');
        $data = $this->importDataFromFile($file);
        if(!$data) {
            return redirect()->back()->with([
               'status' => [
                    'type' => 'danger', 
                   'messages' => 'Không có dữ liệu để import'
                ]
            ]);
        }

        // Lấy dữ liệu import
        $arrayDataCheck = $this->checkDataImport($data);
        $data = $arrayDataCheck['data'];
        $error = $arrayDataCheck['error'];
        DB::beginTransaction();
        try {
            $this->handleImportData($data);
            DB::commit();
            if ($error) {
                $errorMessageArr = array_map(function($item) {
                    return $item['message'];
                }, $error); 
                $errorMessage = implode(',<br />', $errorMessageArr);
                return redirect()->back()->with([
                    'status' => [
                        'type' => 'danger', 
                        'messages' => $errorMessage
                    ]
                ]);
            } else {
                return redirect()->back()->with([
                    'status' => [
                        'type' => 'success', 
                        'messages' => 'Import thành công'
                    ]
                ]);
            }
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollback();
            return redirect()->back()->with([
                'status' => [
                    'type' => 'danger', 
                    'messages' => 'Import thất bại vui lòng kiểm tra lại dữ liệu hoặc báo với cán bộ IT.'
                ]
            ]);
        }
    }

    protected function checkDataImport($data) {
        $dataSuccess = [];
        $error = [];

        // get subject data
        foreach($data as $key => $item) {
            $index = $key + 1;
            $user_code = trim($item[0]);
            $so_bang = trim($item[1]);

            // Kiểm tra user_code
            if (empty($user_code)) {
                $error[] = [
                    'row' => $index,
                    'message' => "Dữ liệu dòng {$index}, mã sinh viên không được để trống" 
                ]; 
                continue;
            }

            // get old subject
            $checkIssetUser = User::where('user_code', $user_code)->first();
            // Kiểm tra user có tồn tại hay không
            if (!$checkIssetUser) {
                $error[] = [
                    'row' => $index,
                    'message' => "Dữ liệu dòng {$index}, sinh viên không tồn tại trong hệ thống" 
                ]; 
                continue;
            }

            if (empty($so_bang)) {
                $error[] = [
                    'row' => $index,
                    'message' => "Dữ liệu dòng {$index}, số bằng không được để trống" 
                ]; 
                continue;
            }

            // Kiểm trá 
            if ($checkIssetUser->study_status == 8) {
                $error[] = [
                    'row' => $index,
                    'message' => "Dữ liệu dòng {$index}, sinh viên trạng TNG không được import" 
                ]; 
                continue;
            }

            $dataSuccess[] = [
                'user_code' => $user_code,
                'so_bang' => $so_bang
            ];
        }

        return [
            'data' => $dataSuccess,
            'error' => $error
        ];
    }
    
    protected function handleImportData($data) {
        $now = date('Y-m-d H:i:s', time());
        $log = [];
        foreach($data as $item) {
            $getUser = User::where('user_code', $item['user_code'])->first();
            if ($getUser) {
                $log[] = [    
                    'object_name' => 'diploma',
                    'actor' => auth()->user()->user_login,
                    'log_time' => $now,
                    'action' => 'update',
                    'description' => "update so_bang: " . $getUser->so_bang . " to " . $item['so_bang'],
                    'description' => "Cập nhập thông tin ". $getUser->user_login .": { so_bang: từ (". $getUser->so_bang .") thành (". $item['so_bang'] .") }",
                    'object_id' => $getUser->id,
                    'brief' => 'user',
                    'from_ip' => request()->ip(),
                    'relation_login' => $getUser->user_login,
                    'relation_id' => $getUser->id,
                    'nganh_cu' => '',
                    'nganh_moi' => '',
                    'ky_chuyen_den' => 0,
                    'ky_thu_cu' => 0,
                ];
    
                $getUser->so_bang = $item['so_bang'];
                $getUser->save();
            }
        }
        
        SystemLog::insert($log);
    }

    public function export(Request $request) {
        $input = $request->all();
        $search_graduation_campaign = $request->get('search_graduation_campaign', null);

        $dataQuery = User::query();

        if (!empty($input['search_curiculumn_id'])) {
            $dataQuery = $dataQuery->where('curriculum_id', $input['search_curiculumn_id']);
        }

        if (!empty($input['search_study_status'])) {
            $dataQuery = $dataQuery->where('study_status', $input['search_study_status']);
        }

        if (isset($input['search_is_van_bang'])) {
            if($input['search_is_van_bang'] == 1) {
                $dataQuery->whereNotNull('so_bang');
            }else if ($input['search_is_van_bang'] == 0) {
                $dataQuery->whereNull('so_bang');              
            }
        }

        if(isset($search_graduation_campaign) && is_string($search_graduation_campaign)) {
            // phan tach chuoi
            $search_graduation_campaign_array = explode(',', $search_graduation_campaign);
            $graduation_campaign_users = GraduationCampaignUser::select([
                'campaign_id',
                'user_code',
                'user_login'
            ])->whereIn('campaign_id', $search_graduation_campaign_array)->get();

            $campaign_user_code = $graduation_campaign_users->pluck('user_code')->toArray();

            $dataQuery->whereIn('user_code', $campaign_user_code);
        }

        $dataQuery->select([
            "user_code",
            "user_surname",
            "user_middlename",
            "user_givenname",
            DB::raw("CONCAT(TRIM(user_surname),' ', TRIM(user_middlename),' ',TRIM(user_givenname)) AS full_name"), 
            "so_bang"
        ])->where('user_level', 3);
        
        $data = $dataQuery->get();
        return Excel::download(new ListDiploma($data), 'danh_sach_van_bang'. time() .'.xlsx');
    }

    public function updateVanBang(Request $request) {
        $request->validate([
            'user_code' => ['required'],
            'so_bang' => ['nullable']
        ]);

        $now = date('Y-m-d H:i:s', time());
        try {
            DB::beginTransaction();
            $user = User::where("user_code", $request->user_code)->first();
            SystemLog::create([    
                'object_name' => 'diploma',
                'actor' => auth()->user()->user_login,
                'log_time' => $now,
                'action' => 'update',
                'description' => "update so_bang: " . $user->so_bang . " to " . $request->so_bang,
                'object_id' => $user->id,
                'brief' => 'user',
                'from_ip' => request()->ip(),
                'relation_login' => $user->user_login,
                'relation_id' => $user->id,
                'nganh_cu' => '',
                'nganh_moi' => '',
                'ky_chuyen_den' => 0,
                'ky_thu_cu' => 0,
            ]);

            $user->so_bang = $request->so_bang;
            $user->save();

            DB::commit();
            return redirect()->back()->with([
                'status' => [
                    'type' => 'success', 
                    'messages' => 'Cập nhập văn bằng thành công.'
                ]
            ]);
        } catch (\Throwable $th) {
            //throw $th;
            Log::error('DiplomaRepository::updateVanBang', [
                'file' => $th->getFile(),
                'line' => $th->getLine(),
                'message' => $th->getMessage()
            ]);
            DB::rollback();
            return redirect()->back()->with([
                'status' => [
                    'type' => 'danger', 
                    'messages' => 'Cập nhập thất bại vui lòng kiểm tra lại dữ liệu hoặc báo với cán bộ IT.'
                ]
            ]);
        }
    }

}
