<?php


namespace App\Repositories\Admin;

use App\Exports\EosExam\ExportNewMultipleListStudentExam;
use App\Exports\XepGiamThiMutipleEOSExport;
use App\helper\ExamHelper;
use Exception;
use App\Models\Fu\Term;
use App\Models\Fu\User;
use App\Models\Fu\Block;
use App\Models\Fu\Group;
use App\Models\Fu\Course;
use App\Models\Fu\Subject;
use App\Models\Fu\Activity;
use Illuminate\Http\Request;
use App\Models\Fu\Attendance;
use App\Models\Fu\Department;
use App\Models\T7\GradeGroup;
use App\Models\T7\CourseResult;
use App\Models\T7\SyllabusPlan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Repositories\BaseRepository;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\GroupEOS;
// use App\Models\GroupGraduate;
use App\Models\T7\CourseGrade;
use App\Utils\ResponseBuilder;
use Box\Spout\Reader\Common\Creator\ReaderEntityFactory;
use Illuminate\Support\Facades\Validator;

class EosRepository  extends BaseRepository
{
    public function getModel()
    {
        return GroupEOS::class;
    }

    public function danhSachThi(Request $request)
    {
        $blocks = [];
        $ds_lop = [];
        $ds_mon = [];
        $ds_bo_mon = Department::select('id', 'department_name', 'dean')->get();
        $user_login = auth()->user()->user_login;

        //lấy tất cả kỳ học
        // $terms = Term::where('is_locked', 0)->orderBy('id', 'desc')->get();
        $terms = Term::orderBy('id', 'desc')->get();

        $term_id = $request->term_id;

        if ($term_id != null && $term = Term::where('id', $term_id)->first() != null) {
            $blocks = Block::where('term_id', $term_id)->get();
            $block_id = $request->block_id;
            $block_search = null;
            if ($block_id) {
                $block_search = $blocks->where('id', $block_id)->first();
            }

            $department_id = $request->department_id;
            if ($department_id != null && $department_id > 0) {
                $subject = Subject::where('department_id', $department_id)->get();

                $ds_mon = Course::when($department_id, function ($query) use ($subject) {
                    $query->whereIn('subject_id', $subject->pluck('id'));
                })->orderBy('psubject_code')->where('term_id', $term_id)->get();

                $course_id = $request->course_id;
                if ($course_id != null && $course_id > 0) {
                    $course_search = $ds_mon->where('id', $course_id)->pluck('id');

                    $ds_lop = Group::whereIn('body_id', $course_search)
                        ->where('list_group.is_virtual', 0)
                        ->where('pterm_id', $term_id)
                        ->when($block_search, function ($query) use ($block_search) {
                            $query->where('end_date', '>=', $block_search->start_day)->where('end_date', '<=', $block_search->end_day);
                        })
                        ->where('type', 1)
                        ->where(function ($q) {
                            $q->where('list_group.group_name', 'not like', "TL_%")
                                ->whereRaw('LENGTH(list_group.group_name) < 20');
                        })
                        ->orderBy('id', 'desc')
                        ->get();
                }
            }
        }

        return $this->view('calendar.danh_sach_thi', [
            'terms' => $terms,
            'blocks' => $blocks,
            'ds_bo_mon' => $ds_bo_mon,
            'ds_lop' => $ds_lop,
            'ds_mon' => $ds_mon
        ]);
    }


    /**
     * 
     * Xuất danh sách thi
     * 
     * <AUTHOR>
     * @since 30/05/2023
     * @todo Tải danh sách sinh viên cấm thi hoặc danh sách đủ điều kiện thi
     * @param Illuminate\Http\Request $request
     * @return ExportNewMultipleListStudentExam 
     */
    public function exportListStudentExam(Request $request)
    {
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', '-1');
        $urlBack = (url()->current() == route('admin.calendar.danh_sach_thi') ? url()->previous() : route('admin.calendar.danh_sach_thi', $request->all()));
        $rules = [
            'term_id' => "required|exists:term,id",
            // 'block_id' => "exists:block,id",
            'department_id' => "required|exists:department,id",
            'course_id' => "required|exists:course,id",
            // 'group_id' => "exists:list_group,id",
        ];
        
        $messages = [
            'term_id.required' => 'Kỳ học không được bỏ trống',
            'department_id.required' => 'Bộ môn không được bỏ trống',
            'course_id.required' => 'Khóa học không được bỏ trống',
            'term_id.exists' => 'kỳ học không tồn tại',
            'block_id.exists' => 'block không tồn tại',
            'department_id.exists' => 'Bộ môn không tồn tại',
            'course_id.exists' => 'Khóa học không tồn tại',
            'group_id.exists' => 'lớp không tồn tại',
        ];

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            return $this->redirectWithStatus('danger', $validator->errors()->first(), $urlBack);
        }

        // Lấy danh sách thi
        try {
            // Log::error("Dev: " . time());
            ini_set('memory_limit', '-1');
            set_time_limit(0);
            // \Debugbar::disable();

            $termId = $request->term_id;
            $blockId = $request->get('block_id', null);
            $departmentId = $request->department_id;
            $courseId = $request->course_id;
            $groupId = $request->get('group_id', null);
            $camThi = $request->get('cam_thi', 0);
            $user_login = auth()->user()->user_login;

            if ( $termId == null || $termId <= 0 || $departmentId == null || $departmentId <= 0) {
                return $this->redirectWithStatus('danger', 'Vui lòng chọn môn hoặc lớp môn cần in danh sách thi!', $urlBack);
            }

            $term = Term::where('id', $termId)->first();
            if ($term == null) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy kỳ cần in danh sách thi!', $urlBack);
            }

            $blocks = Block::where('term_id', $termId)->get();
            $block_search = null;
            if ($blockId) {
                $block_search = $blocks->where('id', $blockId)->first();
            }

            // Lấy khóa học
            $course = Course::find($courseId);

            // kiểm tra có lớp chưa được up lên chưa 
            $checkGroupCalendar = Group::select([
                "list_group.id",
                "list_group.body_id",
                "list_group.group_name",
                "list_group.pterm_name",
                DB::raw("COUNT(group_member.member_login) as number_student"),
                DB::raw("(
                    SELECT COUNT(group_eos.student_login)
                    FROM group_eos 
                    WHERE `group_eos`.`group_id` = `list_group`.`id`
                ) as number_student_exam"),  
            ])
            ->join('group_member', 'list_group.id', '=', 'group_member.groupid')
            ->where('list_group.pterm_id', $termId)
            ->where('list_group.body_id', $courseId)
            ->where('list_group.is_virtual', 0)
            ->when($groupId, function ($q, $groupId) {
                $q->where('list_group.id', $groupId);
            })
            ->when($blockId, function ($q, $blockId) {
                $q->where('list_group.block_id', $blockId);
            })
            ->groupBy('list_group.id')
            ->havingRaw('number_student > number_student_exam')
            ->get();

            if (count($checkGroupCalendar) > 0) {
                $checkGroupCalendar = $checkGroupCalendar->pluck('group_name')->toArray();
                $strReport = count($checkGroupCalendar) . ' lớp môn ' . $course->psubject_code . ' chưa xếp hết lịch thi cho sinh viên, vui lòng kiểm tra lại các lớp: ' . implode(', ', $checkGroupCalendar);
                return $this->redirectWithStatus('danger', $strReport, $urlBack);
            }

            // Lấy danh sách lớp
            $groups = Group::where('body_id', $course->id)
            ->with([
                'groupMembers' => function($q) {
                    $q->select([
                        'group_member.id', 
                        'group_member.groupid', 
                        'group_member.member_login', 
                        'group_member.user_code',
                        'user.user_code',
                        DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ", user.user_givenname) as full_name')
                    ])
                    ->join('user', 'user.user_login', 'group_member.member_login')
                    ->get();
                }])
            ->where('pterm_id', $termId)
            ->where('list_group.is_virtual', 0)
            ->when($groupId, function ($q, $groupId) {
                $q->where('list_group.id', $groupId);
            })
            ->get();

            if (count($groups) == 0) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy lớp nào!');
            }

            // Nếu pass qua quét từng lớp 1 
            // Lấy ra số buổi học + đầu điểm
            
            // Kiểm tra xem có các buổi bảo vệ không ?
            $checkCouseSlotDef = SyllabusPlan::where('syllabus_id', $course->syllabus_id)
                ->where('session_type', '=', 9)
                // ->whereIn('session_type', [11, 9])
                ->orderBy('course_session')
                ->pluck('course_session');
                // ->get();
            if ($checkCouseSlotDef == null || count($checkCouseSlotDef) != 3) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy 3 buổi bảo vệ của môn hoặc lớp môn trong syllabus');
            }
            
            // Danh sách buổi bảo vệ
            $listActivitiesDef = Activity::select([
                'groupid',
                'slot', 
                'day', 
                'is_online', 
                'room_name', 
                'url_room_online',
                'course_slot',
                'session_type', 
            ])
                ->with('slotDetail:id,slot_start,slot_end')
                ->whereIn('groupid', $groups->pluck('id')->toArray())
                ->whereIn('course_slot', $checkCouseSlotDef)
                ->get();
        
            // Danh sách buổi học
            $listActivities = Activity::leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
                ->whereIn('activity.groupid', $groups->pluck('id')->toArray())
                ->where('session_type.is_exam', 0)
                ->orderBy('activity.course_slot')
                ->get();
            
            // $listAllGroupGraduate = GroupEos::select([
            //     'id',
            //     'student_login', 
            //     'group_id', 
            //     'course_session'
            // ])
            // ->whereIn('group_id', $groups->pluck('id')->toArray())
            // ->whereNotNull('course_session')
            // ->groupBy('student_login')
            // ->get();



            // Lấy danh sách các nhóm đầu điểm cần tính toán
            $listGradeGroupCheck = GradeGroup::query()
            ->whereRaw("syllabus_id = ? AND Id NOT IN (
                SELECT grade_group_id 
                FROM t7_grade WHERE syllabus_id = ?
                AND (
                    is_final_exam = 1	
                    OR t7_grade.bonus_type = 1
                )
            )", [$course->syllabus_id, $course->syllabus_id])->with(['grades'])->get();
                

            // duyệt lớp
            $res = [];
            foreach ($groups as $key => $group) {
                // if ($key == 6) break;
                $members = $group->groupMembers;
                // lấy danh sách buổi học
                $listActivityByGroup = $listActivities->where('groupid', $group->id);
                $listActivityByGroupId = $listActivities->pluck('id')->toArray();
                $listActivitiesDefByGroup = $listActivitiesDef->where('groupid', $group->id)->sortBy('course_slot');
                // if (count($listActivitiesDefByGroup) != 3) {
                //     return $this->redirectWithStatus('danger', "Không tìm thấy 3 buổi bảo vệ của lớp $group->group_name($group->id)", $urlBack);
                // }
                
                $res[$group->id]['course'] = array_values($listActivitiesDefByGroup->toArray());
                $res[$group->id]['group'] = $group;
                $res[$group->id]['list_no_course'] = [];

                // lịch sử học của cả lớp
                // $courseResults = CourseResult::select([
                //     'id',
                //     'student_login',
                //     'groupid',
                //     'val'
                // ])->where('groupid', $group->id)->get();
                
                // $listUserInfo = User::select([
                //     'user.user_login',
                //     DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ", user.user_givenname) as full_name'),
                // ])
                // ->whereIn('user_login', $members->pluck('member_login')->toArray())
                // ->pluck('full_name', 'user_login')->toArray();
            
                // lấy danh sách điểm theo lớp
                $listStudentGrades = CourseGrade::select([
                    'id',
                    'val', 
                    'grade_id',
                    'groupid',
                    'login',
                    'grade_group_id'
                ])
                // ->whereIn('groupid', $groups->pluck('id')->toArray())
                ->where('groupid', $group->id)
                ->orderBy('grade_id')
                ->get();
                
                // Lấy danh sách điểm danh của lớp
                if (count($listActivityByGroupId) > 500) {
                    DB::getPdo()->setAttribute(\PDO::ATTR_EMULATE_PREPARES, true);
                }
                $listAllAttendance = Attendance::select([
                    'user_login',
                    DB::raw('count(attendance.id) as total_att')
                ])
                // ->whereIn('groupid', $groups->pluck('id')->toArray())
                ->whereIn('activity_id', $listActivityByGroupId)
                ->where('groupid', $group->id)
                ->groupBy('user_login')
                ->pluck('total_att', 'user_login')->toArray();

                foreach ($members as $member) {
                    $listMsgFail = [];
                    $memberStatus = 0;
                    // $courseResult = $courseResults->where('student_login', $member->member_login)->first();
                    $courseResult = CourseResult::select([
                        'id',
                        'student_login',
                        'groupid',
                        'val'
                    ])->where('student_login', $member->member_login)
                    ->where('groupid', $group->id)
                    ->first();
                    
                    if (!$courseResult) {
                        return $this->redirectWithStatus('danger', 'Không thể xuất file excel. Vui lòng kiểm tra lại "Lịch sử học" sinh viên ' . $member->member_login . " lớp $group->group_name($group->id)!", $urlBack);
                    }

                    // $member->user_code = $member->user_code;
                    // $member->full_name = $listUserInfo[$member->member_login];
                    $member->group_name = $group->group_name;
                    $memberGroupEos = GroupEos::select([
                        'id',
                        'student_login', 
                        'group_id', 
                        'course_session'
                    ])
                    ->whereNotNull('course_session')
                    ->groupBy('student_login')
                    ->where('student_login', $member->member_login)
                    ->where('group_id', $group->id)
                    ->first();

                    if ($courseResult->val == -1) {
                        $member->status = 1;
                        $member->reason_fail = "Cấm thi do trượt điểm danh";
                        if ($memberGroupEos->course_session == 1) {
                            $member->course_detail = $res[$group->id]['course'][0];
                        } elseif ($memberGroupEos->course_session == 2) {
                            $member->course_detail = $res[$group->id]['course'][1];
                        } elseif ($memberGroupEos->course_session == 3) {
                            $member->course_detail = $res[$group->id]['course'][2] ?? [];
                        }

                        $res[$group->id]['list_fail'][] = $member->toArray();
                        continue;
                    }

                    // kiểm tra sinh viên đầy đủ buổi điểm danh chưa
                    if (!isset($listAllAttendance[$member->member_login])) {
                        return $this->redirectWithStatus('danger', 'Không thể xuất file excel. Vui lòng điểm danh tất cả các buổi học cho sinh viên ' . $member->member_login . " lớp $group->group_name($group->id)!", $urlBack);
                    }
                    
                    $studentAttendance = $listAllAttendance[$member->member_login];
                    if (count($listActivityByGroup) != $studentAttendance) {
                        return $this->redirectWithStatus('danger', 'Không thể xuất file excel. Vui lòng điểm danh tất cả các buổi học cho sinh viên ' . $member->member_login . " lớp $group->group_name($group->id)!", $urlBack);
                    } 

                    // Duyệt nhóm đầu điểm
                    foreach ($listGradeGroupCheck as $GradeGroup) {
                        //lấy ds nhóm đầu điểm của nhóm đầu điểm
                        $listGradeByGroup = $GradeGroup->grades;
                        //lấy tất cả điểm quá trình sinh viên đạt được
                        $listStudentGrade = $listStudentGrades
                        // ->where('groupid', $group->id)
                        ->where('login', $member->member_login)
                        ->where('grade_group_id', $GradeGroup->id);
                        
                        $listStudentGradeArr = $listStudentGrade->pluck('val', 'grade_id');
                        $groupGradeTotalPoint = 0;
                        // Kiểm tra nhóm điểm
                        foreach ($listGradeByGroup as $item) {
                            if (isset($listStudentGradeArr[$item->id])) {
                                if ($listStudentGradeArr[$item->id] < $item->minimum_required) {
                                    $listMsgFail[] = "Đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]: " . $listStudentGradeArr[$item->id] . " < " . $item->minimum_required;
                                }
    
                                //tính điểm theo nhóm cho sv
                                if ($GradeGroup->weight > 0) {
                                    $qt_detail_score = ($item->weight / $GradeGroup->weight) * $listStudentGradeArr[$item->id];
                                    $groupGradeTotalPoint += $qt_detail_score;
                                }
                            } else {
                                $listMsgFail[] = "Thiếu đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]";
                            }
                        }
    
                        // Làm tròn nhóm đầu điểm để xét điều kiện
                        $groupGradeTotalPointCheck = round($groupGradeTotalPoint, 1);
                        if ($groupGradeTotalPointCheck < $GradeGroup->minimum_required) {
                            $listMsgFail[] = "Nhóm điểm [$GradeGroup->grade_group_name]: " . $groupGradeTotalPointCheck . " < " . $GradeGroup->minimum_required . " - Chưa làm tròn: " . $groupGradeTotalPointCheck;
                        }
                    }
                    
                    // kiểm tra xem 
                    if (count($listMsgFail) > 0) {
                        $member->status = 2;
                        $member->reason_fail = "Cấm thi do điểm thành phần: " . implode(", ", $listMsgFail);
                        if ($memberGroupEos->course_session == 1) {
                            $member->course_detail = $res[$group->id]['course'][0];
                        } elseif ($memberGroupEos->course_session == 2) {
                            $member->course_detail = $res[$group->id]['course'][1];
                        } elseif ($memberGroupEos->course_session == 3) {
                            $member->course_detail = $res[$group->id]['course'][2];
                        }
                        
                        $res[$group->id]['list_fail'][] = $member->toArray();
                        continue;
                    } else {
                        $member->reason_fail = "";
                    }

                    $member->status = $memberStatus;
                    if ($memberGroupEos->course_session == 1) {
                        $res[$group->id]['course'][0]['members'][] = $member->toArray();
                    } elseif ($memberGroupEos->course_session == 2) {
                        $res[$group->id]['course'][1]['members'][] = $member->toArray();
                    } elseif ($memberGroupEos->course_session == 3) {
                        $res[$group->id]['course'][2]['members'][] = $member->toArray();
                    } else {
                        $res[$group->id]['list_no_course'][] = $member->toArray();
                    }
                }

                unset($listActivityByGroup);
                unset($listActivitiesDefByGroup);
                unset($listStudentGrades);
            }

            $file_name = now()->format('d_m_y_h_i_s_') . 'xuat_lich_thi_EOS.xlsx';
            return Excel::download(new ExportNewMultipleListStudentExam($res, $term, $block_search, $course, $camThi), $file_name);
        } catch (Exception $th) {
            Log::error("---------dev check------------");
            Log::error($th);
            Log::error("---------err end export_danh_sach_thi ------------");
            return $this->redirectWithStatus('danger', 'Có lỗi xảy ra. Không thể export file excel!');
        }
    }

    /**
     * 
     * Xuất danh sách cấm thi
     * 
     * <AUTHOR>
     * @since 30/05/2023
     * @param Illuminate\Http\Request $request
     */
    public function exportListStudentNotExam(Request $request)
    {
        # code...
    }

    public function exportDanhSachGiamThi(Request $request)
    {
        $blocks = [];
        $ds_lop = [];
        $ds_mon = [];
        $ds_bo_mon = Department::select('id', 'department_name', 'dean')->get();
        $user_login = auth()->user()->user_login;

        //lấy tất cả kỳ học
        // $terms = Term::where('is_locked', 0)->orderBy('id', 'desc')->get();
        $terms = Term::orderBy('id', 'desc')->get();

        $term_id = $request->term_id;
        if ($term_id != null && $terms->contains('id', $term_id)) {
            $blocks = Block::where('term_id', $term_id)->get();
            $block_id = $request->block_id;
            $block_search = null;
            if ($block_id) {
                $block_search = $blocks->where('id', $block_id)->first();
            }

            $department_id = $request->department_id;
            if ($department_id != null && $department_id > 0) {
                $subject = Subject::where('department_id', $department_id)->get();

                $ds_mon = Course::when($department_id, function ($query) use ($subject) {
                    $query->whereIn('subject_id', $subject->pluck('id'));
                })->orderBy('psubject_code')->where('term_id', $term_id)->get();

                $course_id = $request->course_id;
                if ($course_id != null && $course_id > 0) {
                    $course_search = $ds_mon->where('id', $course_id)->pluck('id');

                    $ds_lop = Group::whereIn('body_id', $course_search)->where('list_group.is_virtual', 0)
                        ->where('pterm_id', $term_id)
                        ->when($block_search, function ($query) use ($block_search) {
                            $query->where('end_date', '>=', $block_search->start_day)->where('end_date', '<=', $block_search->end_day);
                        })
                        ->where('type', 1)
                        ->where(function ($q) {
                            $q->where('list_group.group_name', 'not like', "TL_%")
                                ->whereRaw('LENGTH(list_group.group_name) < 20');
                        })
                        ->orderBy('id', 'desc')
                        ->get();
                }
            }
        }

        return $this->view('eos.export_danh_sach_giam_thi', [
            'terms' => $terms,
            'blocks' => $blocks,
            'ds_bo_mon' => $ds_bo_mon,
            'ds_mon' => $ds_mon,
            'ds_lop' => $ds_lop,
        ]);
    }

    public function processExportDanhSachGiamThi(Request $request)
    {
        try {
            ini_set('memory_limit', '-1');
            set_time_limit(0);
            \Debugbar::disable();

            $term_id = $request->term_id;
            $block_id = $request->block_id;
            $department_id = $request->department_id;
            $course_id = $request->course_id;
            $group_id = $request->group_id;
            $user_login = auth()->user()->user_login;

            if ($term_id == null || $term_id <= 0 || $department_id == null || $department_id <= 0) {
                return $this->redirectWithStatus('danger', 'Vui lòng chọn bộ môn cần in danh sách giám thị!', url()->previous());
            }

            $term = Term::where('id', $term_id)->first();
            if ($term == null) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy kỳ cần in danh sách giám thị! Vui lòng thử lại!', url()->previous());
            }

            $blocks = Block::where('term_id', $term_id)->get();
            $block_search = null;
            if ($block_id) {
                $block_search = $blocks->where('id', $block_id)->first();
            }

            $subject = Subject::where('department_id', $department_id)->get();
            $ds_mon = Course::when($department_id, function ($query) use ($subject) {
                $query->whereIn('subject_id', $subject->pluck('id'));
            })->orderBy('psubject_code')->where('term_id', $term_id)->get();

            $query = Group::query();
            $query = $query->where('list_group.is_virtual', 0)->where('pterm_id', $term_id);
            $query = $query->when($block_search, function ($query) use ($block_search) {
                $query->where('end_date', '>=', $block_search->start_day)->where('end_date', '<=', $block_search->end_day);
            });

            if ($course_id != null && $course_id > 0) {
                $course_search = $ds_mon->where('id', $course_id)->pluck('id');
                $query = $query->whereIn('body_id', $course_search);
            } else {
                $course_search = $ds_mon->pluck('id');
                $query = $query->whereIn('body_id', $course_search);
            }

            if ($group_id != null && $group_id > 0) {
                $query = $query->where('id', '=', $group_id);
            }

            $query = $query->where('type', 1);
            $query = $query->where(function ($q) {
                $q->where('list_group.group_name', 'not like', "TL_%")
                    ->whereRaw('LENGTH(list_group.group_name) < 20');
            });
            $query = $query->orderBy('id', 'desc');
            $main_result = $query->get();

            if ($main_result == null || count($main_result) <= 0) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy môn cần in ra danh sách giám thị! Vui lòng thử lại!', url()->previous());
            }

            $ds_error = [];
            $ds_mon_total = [];

            foreach ($main_result as $item) {
                $group = Group::where('id', $item->id)->first();

                if ($group == null || $group->id <= 0) {
                    $ds_error[] = [
                        'group_name' => $group->group_name,
                        'reason' => 'Không tìm thấy lớp thuộc kỳ đã chọn'
                    ];
                    continue;
                }

                //kiểm tra xem môn học có 3 buổi bảo vệ hay ko?
                //lấy 3 buổi thi của lớp đã được đào tạo phân công
                $result_course_slot_bao_ve = SyllabusPlan::where('syllabus_id', $group->syllabus_id)
                    ->where('session_type', '=', 9)
                    // ->whereIn('session_type', [11, 9])
                    ->orderBy('course_session')
                    ->pluck('course_session');

                if ($result_course_slot_bao_ve != null && count($result_course_slot_bao_ve) <= 0) {
                    $ds_error[] = [
                        'group_name' => $group->group_name,
                        'reason' => 'Không tìm thấy 3 buổi thi của môn hoặc lớp môn trong syllabus'
                    ];
                    continue;
                }

                $ds_slot_assignment = Activity::select('course_slot', 'slot', 'day', 'is_online', 'room_name', 'url_room_online', 'leader_login', 'leader_login2')
                    ->with('slotDetail:id,slot_start,slot_end')
                    ->where('groupid', '=', $group->id)
                    ->whereIn('course_slot', $result_course_slot_bao_ve)
                    ->get();

                if ($ds_slot_assignment == null || count($ds_slot_assignment) <= 0) {
                    $ds_error[] = [
                        'group_name' => $group->group_name,
                        'reason' => 'Không tìm thấy 3 buổi thi trong kế hoạch của môn hoặc lớp môn'
                    ];
                    continue;
                }

                //lấy tất cả buổi bảo vệ được xếp lịch của môn đó?
                $ds_slot_eos = GroupEOS::where('group_id', '=', $group->id)
                    ->select('course_session')
                    ->distinct()
                    ->get();

                //nếu chưa xếp lịch => chỉ lấy tên của lớp đó + giám thị
                if ($ds_slot_eos == null || count($ds_slot_eos) <= 0) {
                    $new_item = clone $item;
                    $temp = [
                        'is_error' => false,
                        'group_name' => $group->group_name,
                        'mon_info' => $new_item
                    ];
                    array_push($ds_mon_total, $temp);
                } else {
                    //lấy tất cả + buổi thi => ngày thi + slot
                    $ds_slot_300 = [];

                    foreach ($ds_slot_eos as $slotFor) {
                        if ($slotFor->course_session == 1) {
                            $key = $result_course_slot_bao_ve[0];
                            $ds_slot_300[$key] = 1;
                        } elseif ($slotFor->course_session == 2) {
                            $key = $result_course_slot_bao_ve[1];
                            $ds_slot_300[$key] = 2;
                        } elseif ($slotFor->course_session == 3) {
                            $key = $result_course_slot_bao_ve[2];
                            $ds_slot_300[$key] = 3;
                        }
                    }

                    foreach ($ds_slot_assignment as $value) {
                        if (in_array($value->course_slot, array_keys($ds_slot_300))) {
                            $new_item = clone $item;
                            $new_item->slot_assignment = $value;
                            $new_item->course_slot_syllybus = $ds_slot_300[$value->course_slot];

                            $temp = [
                                'is_error' => false,
                                'group_name' => $group->group_name,
                                'mon_info' => $new_item
                            ];
                            array_push($ds_mon_total, $temp);
                        }
                    }
                }
            }

            $ds_giam_thi[] = [
                'is_error' => false,
                'group_name' => $group->group_name,
                'ds_mon' => $ds_mon_total,
            ];

            if ($ds_error != null && count($ds_error) > 0) {
                $item = [
                    'is_error' => true,
                    'ds_error' => $ds_error,
                ];

                //thêm lớp bị lỗi lên đầu file excel
                array_unshift($ds_giam_thi, $item);
            }

            $excel_info = [];

            $file_name = now()->format('d_m_y_h_i_s_') . 'xep_giam_thi' . '.xlsx';
            $export = new XepGiamThiMutipleEOSExport($ds_giam_thi, $excel_info);
            return Excel::download($export, $file_name);
        } catch (\Throwable $th) {
            Log::error("---------dont------------");
            Log::error($th);
            Log::error("---------err end process_export_danh_sach_giam_thi ------------");
            return $this->redirectWithStatus('danger', 'Có lỗi xảy ra. Không thể export file excel!');
        }
    }

    public function XepGiamThi(Request $request)
    {
        //lấy tất cả kỳ học
        // $terms = Term::where('is_locked', 0)->orderBy('id', 'desc')->get();
        $terms = Term::orderBy('id', 'desc')->get();

        return $this->view('eos.xep_giam_thi', [
            'terms' => $terms,
        ]);
    }

    public function processImportXepGiamThi(Request $request)
    {
        try {
            ini_set('memory_limit', '-1');
            set_time_limit(0);
            \Debugbar::disable();

            $user_login = auth()->user()->user_login;
            $term_id = $request->term_id;

            //lấy tất cả kỳ học
            $term = Term::where('id', $term_id)->first();
            if ($term == null || $term->id <= 0) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy kỳ cần xếp giám thị! Vui lòng thử lại!', url()->previous());
            }

            $tempFile = $request->file('file');
            $reader = ReaderEntityFactory::createXLSXReader();
            $reader->open($tempFile);

            $report = [];
            $result = [];

            // $ds_slots = Slot::select('slot_id')->where('lock', 0)->orderBy('slot_id')->get();
            // $ds_users = User::whereIn('user_level', [1, 2, 11])->get();
            $ds_users = User::where('user_level', '!=', 3)->get();

            foreach ($reader->getSheetIterator() as $sheet) {
                foreach ($sheet->getRowIterator() as $i => $row) {
                    if ($i <= 1)
                        continue;

                    $item = $row->toArray();

                    $psubject_code_import = $item[1];
                    $group_id_import = $item[2];
                    $group_name_import = $item[3];
                    $day_import = $item[4];

                    $leader_login1_import = $item[7];
                    $leader_login2_import = $item[8];

                    if (
                        $psubject_code_import == null || empty($psubject_code_import) ||
                        $group_id_import < 0 ||
                        $group_name_import == null || empty($group_name_import) ||
                        $day_import == null || empty($day_import)
                        // $leader_login1_import == null || empty($leader_login1_import) ||
                        // $leader_login2_import == null || empty($leader_login2_import)
                    ) {
                        $report[] = "Dòng " . ($i - 1) . " - Vui lòng nhập đầy đủ dữ liệu";
                        continue;
                    }

                    /**
                     * ktra buổi thi
                     */
                    if ($day_import != 1 && $day_import != 2 && $day_import != 3) {
                        $report[] = "Dòng " . ($i - 1) . " - Buổi thi chấp nhận giá trị 1, 2 hoặc 3";
                        continue;
                    }

                    /**
                     * ktra mã môn + mã lớp => theo group_id
                     */
                    $group = Group::where('id', $group_id_import)->where('list_group.is_virtual', 0)->first();
                    if ($group == null || $group->id <= 0) {
                        $report[] = "Dòng " . ($i - 1) . " - Không tìm thấy môn học theo ID lớp";
                        continue;
                    }

                    //lấy 3 buổi thi của lớp đã được đào tạo phân công
                    $result_course_slot_bao_ve = SyllabusPlan::where('syllabus_id', $group->syllabus_id)
                        ->where('session_type', '=', 9)
                        // ->whereIn('session_type', [11, 9])
                        ->orderBy('course_session')
                        ->pluck('course_session');

                    if ($result_course_slot_bao_ve == null || count($result_course_slot_bao_ve) <= 0) {
                        $report[] = "Dòng " . ($i - 1) . " - Không tìm thấy 3 buổi bảo vệ của môn hoặc lớp môn trong syllabus";
                        continue;
                    }

                    //ktra giảng viên
                    if (($leader_login1_import != null && !empty($leader_login1_import)) && !$ds_users->contains('user_login', $leader_login1_import)) {
                        $report[] = "Dòng " . ($i - 1) . " - Giám thị 1 $leader_login1_import không tồn tại";
                        continue;
                    }

                    if (($leader_login2_import != null && !empty($leader_login2_import)) && !$ds_users->contains('user_login', $leader_login2_import)) {
                        $report[] = "Dòng " . ($i - 1) . " - Giám thị 2 $leader_login2_import không tồn tại";
                        continue;
                    }

                    //chuyển course_session từ 0 => 300
                    $course_session_300 = null;
                    if ($day_import == 1) {
                        $course_session_300 = $result_course_slot_bao_ve[0];
                    } elseif ($day_import == 2) {
                        $course_session_300 = $result_course_slot_bao_ve[1];
                    } elseif ($day_import == 3) {
                        $course_session_300 = $result_course_slot_bao_ve[2];
                    }

                    $item = [
                        'groupid' => $group_id_import,
                        'course_slot' => $course_session_300,
                        'psubject_code' => $psubject_code_import,
                        'group_name' => $group_name_import,
                        'leader_login' => $leader_login1_import,
                    ];

                    array_push($result, $item);
                }
            }
            $reader->close();

            if ($report != null && count($report) > 0) {
                return $this->redirectWithStatus('danger', implode('<br/>', $report), url()->previous());
            }

            if ($result == null || count($result) <= 0) {
                return $this->redirectWithStatus('danger', 'Không thể import file excel. Không tìm thấy dữ liệu. Vui lòng thử lại', url()->previous());
            }

            $sql = "";
            foreach ($result as $item) {
                Activity::where("groupid", $item['groupid'])
                ->where('course_slot', $item['course_slot'])
                ->update([
                    'leader_login' => $item['leader_login'],
                    'lastmodifier_login' => auth()->user()->user_login,
                    'lastmodified_time' => now()->format('Y-m-d H:i:s')
                ]);

                // $sql .= "UPDATE activity SET leader_login=" . "'" . $item['leader_login'] . "'"
                //     . ", leader_login2=" . "'" . $item['leader_login2'] . "'"
                //     . ", lastmodifier_login=" . "'" . auth()->user()->user_login . "'"
                //     . ", lastmodified_time=" . "'" . Carbon::now()->format('Y-m-d H:i:s') . "'"
                //     . " WHERE groupid=" . $item['groupid']
                //     // . " AND psubject_code=" . "'" . $item['psubject_code'] . "'"
                //     . " AND course_slot=" . $item['course_slot']
                //     // . " AND slot=" . $item['slot']
                //     // . " AND `day`=" . "'" . $item['day'] . "'"
                //     . ";";
            }

            // DB::connectistatement(DB::raw($sql));
            // DB::connectiunprepared(DB::raw($sql));

            $this->systemLog('activity', 'update', $sql, $user_login, 0, 0, 'processImportXepGiamThiEos');

            DB::commit();

            return $this->redirectWithStatus('success', "Import thành công giám thị", url()->previous());
        } catch (\Throwable $th) {
            DB::rollback();

            Log::error("---------dont------------");
            Log::error($th);
            Log::error("---------err end process_import_xep_giam_thi ------------");
            return $this->redirectWithStatus('danger', 'Có lỗi xảy ra. Không thể import file excel!');
        }
    }

    /**
     * 
     * Đồng bộ danh sách thi
     * 
     * <AUTHOR>
     * @since 29/11/2023
     * @param Illuminate\Http\Request $request
     */
    public function syncGraduateEos($request) {
        $rule  =  array(
            'group_id' => "required|exists:list_group,id"
        ) ;

        $messages = [
            'group_id.required'     => 'Lớp Không được bỏ trống',
            'group_id.exists'       => 'Lớp Không hợp lệ',
        ];

        $validator = Validator::make($request->all(),$rule, $messages);
        if($validator->fails()) {
            $mess = $validator->errors()->first();
            return ResponseBuilder::Fail($mess);
        }

        $groupId = $request->group_id;
        $group = Group::select([
                'id',
                'pterm_id',
                'body_id',
                'is_virtual'
            ])
            ->where('id', $groupId)
            ->where('is_virtual', 0)
            ->first();
        
        if (!$group) {
            return ResponseBuilder::Fail("Lớp không hợp lệ");
        }

        $dataCheck = ExamHelper::SyncExamEos($group->pterm_id, $group->body_id, [$groupId]);
        if ($dataCheck === true) {
            return ResponseBuilder::Success([], 'Đồng bộ thành công');
        } else {
            return ResponseBuilder::Fail($dataCheck[1]);
        }
    }
}
