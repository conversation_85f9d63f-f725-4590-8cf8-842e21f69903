<?php


namespace App\Repositories\Admin;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

use App\Http\Lib;
use App\Models\FeedbackRole;
use App\Models\FeedbackRoleUser;
use App\Models\Fu\Activity;
use App\Models\Fu\Block;
use App\Models\Fu\Feedback;
use App\Models\Fu\FeedbackDetail;
use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use App\Models\Fu\Subject;
use App\Models\Fu\Term;
use App\Models\T7\Configuration;
use App\Models\Dra\T1UserRole;
use App\Repositories\BaseRepository;
use App\helper\FeedbackHelper;
use App\Exports\Feedback\FeedbackExport;
use App\Models\Fu\User;
use App\Utils\ResponseBuilder;
use function foo\func;

class NewFeedbackRepository extends BaseRepository
{
    public function getModel()
    {
        return Feedback::class;
    }

    public function changeStatus($request)
    {
        if ($request->get('feedback_id', null) == null) {
            return ResponseBuilder::Fail('Có vẻ đã có lỗi xảy ra, vui lòng thử lại', null);
        }

        $feedBack = Feedback::find($request->feedback_id);
        if (!$feedBack) {
            return ResponseBuilder::Fail(null, 'Không tìm thấy feedback', 404);
        }

        try {
            $status = $request->get('status', 'false');
            if ($status == 'true') {
                $feedBack->open = 1;
            } else {
                $feedBack->open = 0;
            }

            $feedBack->save();
            return ResponseBuilder::Success($feedBack, 'Thay đổi trạng thái thành công');
        } catch (\Exception $ex) {
            Log::error($ex);
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng thử lại.');
        }
    }

    /**
     * mở feedback bằng tay
     * <AUTHOR>
     */
    public function openFeedback($request)
    {
        $user = auth()->user();
        $currentTerm = Term::whereRaw('NOW() >= startday')
            ->whereRaw('NOW() <= endday')
            ->first();

        if (!$currentTerm) {
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng thử lại.');
        }
    
        $currentBlock = Block::whereRaw('start_day <= CURRENT_DATE')
            ->whereRaw('end_day >= CURRENT_DATE')
            ->first();
        if ($currentBlock->block_name == 'Block 1') {
            $configBlock = Configuration::where('module_name', 'feedback_config_day_block_1')->first();
        } else {
            $configBlock = Configuration::where('module_name', 'feedback_config_day_block_2')->first();
        }

        $configEnglish = Configuration::where('module_name', 'feedback_config_day_english')->first();

        $minDay = 22 + ($configBlock->variable_value ?? 0);
        $maxDay = 33 + ($configBlock->variable_value ?? 0);
        $minDayEng = 42 + ($configEnglish->variable_value ?? 0);
        $maxDayEng = 54 + ($configEnglish->variable_value ?? 0);
        $blockCheck = Block::select([        
            'id',
            'start_day',
            'term_id',
            'end_day',
            DB::raw('DATE_ADD( start_day, INTERVAL ' . $minDay . ' DAY ) AS min_day_check'),
            DB::raw('DATE_ADD( start_day, INTERVAL ' . $maxDay . ' DAY ) AS max_day_check')
        ])
        ->where('term_id', $currentTerm->id)
        ->whereRaw('DATE_ADD( start_day, INTERVAL ' . $minDay . ' DAY ) <= CURRENT_DATE')
        ->whereRaw('DATE_ADD( start_day, INTERVAL ' . $maxDay . ' DAY ) >= CURRENT_DATE')
        ->first();

        $term_id = $currentTerm->id;
        $termCheckEnglish = Term::select([        
            'id',
            'startday',
            'endday',
            DB::raw('DATE_ADD( startday, INTERVAL ' . $minDayEng . ' DAY ) AS min_day_check'),
            DB::raw('DATE_ADD( startday, INTERVAL ' . $maxDayEng . ' DAY ) AS max_day_check')
        ])
        ->where('id', $term_id)
        ->whereRaw('DATE_ADD( startday, INTERVAL ' . $minDayEng . ' DAY ) <= CURRENT_DATE')
        ->whereRaw('DATE_ADD( startday, INTERVAL ' . $maxDayEng . ' DAY ) >= CURRENT_DATE')
        ->first();

        $feedbackHelper = new FeedbackHelper();
        $dataCreate = [];
        DB::beginTransaction();
        try {
            if ($blockCheck) {
                $feedbackHelper->createFeedback($blockCheck, $user->user_login);
            }
    
            DB::commit();
            return ResponseBuilder::Success([], 'Tạo feedback thành công');
        } catch (\Exception $ex) {
            Log::error($ex);
            DB::rollback();
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng thử lại.');
        }
    }


    /**
     * mở feedback cho các lơp mở đặc biệt 
     * (các lớp mở sau cho sinh viên với không theo block và kỳ)
     * <AUTHOR>
     * @since 03/09/2022
     * @version 1.0
     * @param Request $request  
     */
    public function openFeedbackByActivity($request)
    {
        $user = auth()->user();
        $listGoupId = [];
        $currentTerm = Term::whereRaw('NOW() >= startday')
            ->whereRaw('NOW() <= endday')
            ->first();
        if (!$currentTerm) {
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng thử lại.');
        }

        $dataCheck = DB::select("SELECT
                list_group.id,
                ( CASE WHEN ( SUM( CASE WHEN activity.DAY <= CURRENT_DATE THEN 1 END ) / count(*)) > 0.3 THEN 1 ELSE 0 END ) AS 'check' 
            FROM
                `activity`
                LEFT JOIN list_group ON list_group.id = activity.groupid 
                LEFT JOIN session_type ON session_type.id = activity.session_type
            WHERE
                list_group.start_date <= CURRENT_DATE AND list_group.end_date >= CURRENT_DATE 
                AND list_group.pterm_id = ? 
                AND session_type.is_exam = 0
                AND list_group.id NOT IN (
                    SELECT groupid 
                    FROM feedback
                    LEFT JOIN list_group ON list_group.id = feedback.groupid 
                    AND list_group.pterm_id = ? 
                )
            GROUP BY groupid 
            HAVING `check` = 1", [$currentTerm->id, $currentTerm->id]);

        $listGoupId = array_map(function ($a) {
            return $a->id;
        }, $dataCheck);

        $feedbackHelper = new FeedbackHelper();
        $dataCreate = [];
        DB::beginTransaction();
        try {
            $feedbackHelper->createFeedbackByGroupId($currentTerm, $listGoupId, $user->user_login);

            DB::commit();
            return ResponseBuilder::Success([], 'Thành công!');
        } catch (\Exception $ex) {
            Log::error($ex);
            DB::rollback();
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng thử lại.');
        }
    }


    /**
     * 
     * Cập nhập Thay đổi ngày mời feedback 
     * 
     * <AUTHOR>
     * @since 03/09/2022
     * @version 1.0
     * @param Request $request  
     */

    public function updateConfigTimeFeedback($request)
    {
        $rule  =  array(
            'feedback_config_day_block_1' => 'numeric|min:-30|max:30',
            'feedback_config_day_block_2' => 'numeric|min:-30|max:30',
            // 'feedback_config_day_english' => 'numeric|min:-30|max:30'
        ) ;

        $messages = [
            'feedback_config_day_block_1.numeric' => 'Số ngày phải là kiểu số',
            'feedback_config_day_block_1.min' => 'Số ngày không được bé hơn -30',
            'feedback_config_day_block_1.max' => 'Số ngày không được lớn hơn 30',
            'feedback_config_day_block_2.numeric' => 'Số ngày phải là kiểu số',
            'feedback_config_day_block_2.min' => 'Số ngày không được bé hơn -30',
            'feedback_config_day_block_2.max' => 'Số ngày không được lớn hơn 30',
            // 'feedback_config_day_english.numeric' => 'Số ngày phải là kiểu số',
            // 'feedback_config_day_english.min' => 'Số ngày không được bé hơn -30',
            // 'feedback_config_day_english.max' => 'Số ngày không được lớn hơn 30',
        ];

        $validator = Validator::make($request->all(),$rule, $messages);
        if($validator->fails()) {
            $mess = $validator->errors()->first();
            return ResponseBuilder::Fail($mess);
        }

        $configBlock1 = Configuration::query()
            ->where('module_name', 'feedback_config_day_block_1')->first();
        $configBlock2 = Configuration::query()
            ->where('module_name', 'feedback_config_day_block_2')->first();
        // $configEnglish = Configuration::query()
        //     ->where('module_name', 'feedback_config_day_english')->first();
        DB::beginTransaction();
        try {
            $configBlock1->variable_value = $request->get('feedback_config_day_block_1', 0);
            $configBlock1->save();
            $configBlock2->variable_value = $request->get('feedback_config_day_block_2', 0);
            $configBlock2->save();
            // $configEnglish->variable_value = $request->get('feedback_config_day_english', 0);
            // $configEnglish->save();
            DB::commit();
            return ResponseBuilder::Success([], 'Cập nhập thành công!');
        } catch (\Exception $ex) {
            DB::rollback();
            dd($ex);
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng thử lại.');
        }
    }


    /**
     * Đồng bộ dữ liệu của lớp
     */
    public function syncData($request)
    {
        ini_set('max_execution_time', -1);
        if ($request->get('feedback_id', null) == null) {
            return ResponseBuilder::Fail('Có vẻ đã có lỗi xảy ra, vui lòng thử lại', null);
        }

        $feedBack = Feedback::find($request->feedback_id);
        if (!$feedBack) {
            return ResponseBuilder::Fail(null, 'Không tìm thấy feedback', 404);
        }
        
        try {
            $sql = "UPDATE feedback 
            LEFT JOIN
                (
                SELECT
                    feedback.id AS id ,
			        count(feedback_detail.feedbackID) AS hit,
                    AVG(( Q1 + Q2 + Q3 + Q4 + Q5 ) / 5 ) AS avg 
                FROM
                    feedback_detail
                    LEFT JOIN feedback ON feedback_detail.feedbackID = feedback.id 
                WHERE
                    feedback.id = $feedBack->id
                GROUP BY
                    feedbackID 
                ) cstbl 
            ON
                cstbl.id = feedback.id
            SET feedback.GPA = cstbl.avg, feedback.hit = cstbl.hit";
            
            DB::select($sql);
            return ResponseBuilder::Success([], 'Thay đổi trạng thái thành công');
        } catch (\Exception $ex) {
            Log::error($ex);
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng thử lại.');
        }
    }

    /**
     * Đồng bộ lại GPA trước khi xuất báo cáo
     * <AUTHOR>
     * 
     */
    public function syncFeedback($request)
    {
        ini_set('max_execution_time', -1);
        $termUpdate = $request->get('term_id', null);
        if ($termUpdate == null) {
            $currentTerm = Term::where('startday', '<=', now())
                ->where('endday', '>=', now())
                ->first();
        } else {
            $currentTerm = Term::find($termUpdate);
            if ($currentTerm == null) {
                $currentTerm = Term::where('startday', '<=', now())
                    ->where('endday', '>=', now())
                    ->first();
            }
        }

        $feedbackHelper = new FeedbackHelper();
        DB::beginTransaction();
        try {
            $result = $feedbackHelper->updateFeedback($currentTerm->id);
            if ($result) {
                DB::commit();
                return ResponseBuilder::Success([], 'Thành công!');
            }
            DB::rollBack();
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng thử lại.');
        } catch (\Exception $ex) {
            Log::error($ex);
            DB::rollback();
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng thử lại.');
        }
    }

    /**
     * Cập nhập "planer_login" cho cán bộ quản lý
     * <AUTHOR>
     */
    public function setPlanerUser($request)
    {
        ini_set('max_execution_time', -1);
        $user = auth()->user();
        $userPlan = $request->user_login;
        
        // kiểm tra quyền chỉnh sửa
        $listRole = T1UserRole::where('user_login', $user->user_login)->get();
        $listRole = array_column($listRole->toArray(), 'role_id');
        if ($user->user_level != 1 && !in_array(48, $listRole) && !in_array(88, $listRole)) {
            return ResponseBuilder::Fail('bạn không đủ quyền để phân');
        }

        $currentTerm = Term::whereRaw('startday <= CURRENT_DATE')
            ->whereRaw('endday >= CURRENT_DATE')
            ->first();
        $listSubjectCode = Subject::select('subject_code')->where('department_id', $request->department_id)->get();
        if(count($listSubjectCode) == 0) {
            return ResponseBuilder::Success([], 'Cập nhập thành công');
        }

        $arrSubjectCode = $listSubjectCode->pluck('subject_code')->toArray();
        $listGroupObject = Group::select('id')->where('list_group.is_virtual', 0)
        ->where('pterm_id', $currentTerm->id)
        ->whereIn('psubject_code', $arrSubjectCode)
        ->get();

        if(count($listGroupObject) == 0) {
            return ResponseBuilder::Success([], 'Cập nhập thành công');
        }

        $arrGroupId = $listGroupObject->pluck('id')->toArray();
        DB::beginTransaction();
        try {
            DB::commit();
            Feedback::whereIn('groupID', $arrGroupId)
            ->update([
                'planer_login' => $userPlan
            ]);

            return ResponseBuilder::Success([], 'Cập nhập thành công');
        } catch (\Exception $ex) {
            Log::error($ex);
            DB::rollback();
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng thử lại.');
        }
    }


    /**
     * Export dữ liệu feedback
     * <AUTHOR>
     * @
     */
    public function exportFeedback($request)
    {
        ini_set('max_execution_time', -1);
        $termId = $request->get('term_id', null);
        $startDate = $request->get('start_date', null);
        $endDate = $request->get('end_date', null);
        $strDate = "";
        if ($startDate != null && $endDate != null) {
            $strDate = "AND ( open_day >= '$startDate' AND open_day <= '$endDate')";
        }

        $currentTerm = null;
        if ($termId == null) {
            $currentTerm = Term::whereRaw('startday <= CURRENT_DATE')
            ->whereRaw('endday >= CURRENT_DATE')
            ->first();

            $termId = $currentTerm->id;
        } else {
            $currentTerm = Term::find($termId);
        }

        $res = [];
        $dataComment = [];
        $infoReport = [];

        // lấy tên giảng viên
        $listNameteacherObj = User::select([
            'user.user_login',
            DB::raw("CONCAT(user.user_surname,' ',user.user_middlename,' ', user.user_givenname) as user_name")
        ])
        ->rightJoin('feedback', 'feedback.teacher_login', '=', 'user.user_login')
        ->leftJoin('list_group', 'list_group.id', '=', 'feedback.groupID')
        ->where('list_group.pterm_id', $termId)
        ->groupBy('user.user_login')
        ->get();
        
        // lấy danh sách bộ môn
        $listDeparmentObj = Subject::select([
            DB::raw('subject.subject_code'),
            DB::raw('department.department_name')
        ])
        ->leftJoin('list_group', 'list_group.psubject_code', '=', 'subject.subject_code')
        ->leftJoin('department', 'department.id', '=', 'subject.department_id')
        ->where('list_group.pterm_id', $termId)
        ->groupBy('subject.subject_code')
        ->get();
        
        
        $listNameteacher = [];
        $listDeparment = [];
        if (sizeof($listNameteacherObj) > 0){
            $listNameteacher = $listNameteacherObj->pluck('user_name', 'user_login')->toArray();
        }

        if (sizeof($listDeparmentObj) > 0){
            $listDeparment = $listDeparmentObj->pluck('department_name', 'subject_code')->toArray();
            $listDeparment = array_map(function ($a) {
                return preg_replace('/(\v|\s)+/', ' ', $a);
            }, $listDeparment);
        }
        
        // Khởi tạo query cơ bản
        $query = Feedback::selectRaw("
                feedback.id,
                feedback.teacher_login,
                list_group.group_name,
                list_group.psubject_code,
                list_group.psubject_name,
                ROUND(AVG(feedback_detail.Q1), 2) as Q1,
                ROUND(AVG(feedback_detail.Q2), 2) as Q2,
                ROUND(AVG(feedback_detail.Q3), 2) as Q3,
                ROUND(AVG(feedback_detail.Q4), 2) as Q4,
                ROUND(AVG(feedback_detail.Q5), 2) as Q5,
                ROUND(feedback.GPA, 2) as GPA,
                feedback.hit,
                (SELECT count(group_member.id) 
                FROM group_member 
                WHERE feedback.groupID = group_member.groupid 
                GROUP BY group_member.groupid) AS total_member
            ")
            ->leftJoin('list_group', 'list_group.id', '=', 'feedback.groupID')
            ->rightJoin('feedback_detail', 'feedback_detail.feedbackID', '=', 'feedback.id')
            ->where('list_group.pterm_id', $termId)
            ->where('list_group.is_virtual', 0);

        // Thêm điều kiện Subject Code nếu có
        if (!empty($listSubjectCode)) {
            $query->whereIn('list_group.psubject_code', $listSubjectCode);
        }

        // Thêm điều kiện Teacher Login nếu có
        if (!empty($listTeacherLogin)) {
            $query->whereIn('list_group.teacher', $listTeacherLogin);
        }

        // Thêm điều kiện startDate, endDate
        if ($startDate != null && $endDate != null) {
            $query->where(function($q) use ($startDate, $endDate) {
                $q->where('open_day', '>=', $startDate)
                    ->where('open_day', '<=', $endDate);
            });
        }

        // Thêm Group By
        $query->groupBy('feedback.groupID');

        // Lấy kết quả
        $baseData = $query->get();

        // lấy dữ liệu comment của sinh viên
        $listComment = FeedbackDetail::select([
                'feedback_detail.feedbackID',
                'feedback_detail.comment',
                'feedback_detail.edited_comment'
            ])
            ->join('feedback', 'feedback.id', '=', 'feedback_detail.feedbackID')
            ->join('list_group', 'list_group.id', '=', 'feedback.groupID')
            ->where('list_group.pterm_id', $termId)
            ->where('list_group.is_virtual', 0)
            ->whereNotNull('feedback_detail.comment')
            ->where('feedback_detail.comment', '!=', '')
            ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                $query->whereBetween('feedback.open_day', [$startDate, $endDate]);
            })
            ->orderBy('feedback_detail.feedbackID')
            ->get();
    

        foreach ($listComment as $k => $v) {
        $dataComment[$v->feedbackID][] = [
            'comment' => $v->comment,
            'edited_comment' => $v->edited_comment
        ];
        }

        // lấy thông tin số số lượng làm feedback 
        $infoReport = DB::select("SELECT
        count(*) as total,
        SUM( CASE WHEN custom_table.avg > 3.8 THEN 1 END ) AS a1,
        SUM( CASE WHEN ( custom_table.avg <= 3.8 AND custom_table.avg > 3.6 ) THEN 1 END ) AS a2,
        SUM( CASE WHEN ( custom_table.avg <= 3.6 AND custom_table.avg > 3.4 ) THEN 1 END ) AS a3,
        SUM( CASE WHEN custom_table.avg <= 3.4 THEN 1 END ) AS a4
        FROM
        (
        SELECT
            (( Q1 + Q2 + Q3 + Q4 + Q5 ) / 5 ) AS avg 
        FROM
            feedback_detail
            LEFT JOIN feedback ON feedback.id = feedback_detail.feedbackID 
        WHERE
        feedback.groupID IN ( SELECT id FROM list_group WHERE pterm_id = $termId ) 
        $strDate
        ) custom_table");

        // format dữ liệu đầu ra cuối
        $infoReport = (array)$infoReport[0];
        foreach ($baseData as $value) {
            $res[] = [
                'id' => $value->id,
                'teacher_login' => $value->teacher_login,
                'teacher_name' => $listNameteacher[$value->teacher_login] ?? "",
                'group_name' => $value->group_name,
                'deparment' => $listDeparment[$value->psubject_code] ?? "-",
                'psubject_code' => $value->psubject_code,
                'psubject_name' => $value->psubject_name,
                'Q1' => $value->Q1,
                'Q2' => $value->Q2,
                'Q3' => $value->Q3,
                'Q4' => $value->Q4,
                'Q5' => $value->Q5,
                'GPA' => $value->GPA,
                'hit' => $value->hit,
                'total_member' => $value->total_member,
                'list_comment' => $dataComment[$value->id] ?? []
            ];
        }

        return Excel::download(
            new FeedbackExport($res, $infoReport), 
            "feedback - $currentTerm->term_name.xlsx"
        );
    }


    /**
     * Export dữ liệu feedback Cả kỳ
     * <AUTHOR>
     * @
     */
    public function exportFeedbackTerm($request)
    {
        ini_set('max_execution_time', -1);
        $termId = $request->get('term_id', null);

        $currentTerm = null;
        if ($termId == null) {
            $currentTerm = Term::whereRaw('startday <= CURRENT_DATE')
            ->whereRaw('endday >= CURRENT_DATE')
            ->first();

            $termId = $currentTerm->id;
        } else {
            $currentTerm = Term::find($termId);
        }

        $res = [];
        $dataComment = [];
        $infoReport = [];

        // lấy tên giảng viên
        $listNameteacherObj = User::select([
            'user.user_login',
            DB::raw("CONCAT(user.user_surname,' ',user.user_middlename,' ', user.user_givenname) as user_name")
        ])
        ->rightJoin('feedback', 'feedback.teacher_login', '=', 'user.user_login')
        ->leftJoin('list_group', 'list_group.id', '=', 'feedback.groupID')
        ->where('list_group.pterm_id', $termId)
        ->groupBy('user.user_login')
        ->get();
        
        // lấy danh sách bộ môn
        $listDeparmentObj = Subject::select([
            DB::raw('subject.subject_code'),
            DB::raw('department.department_name')
        ])
        ->leftJoin('list_group', 'list_group.psubject_code', '=', 'subject.subject_code')
        ->leftJoin('department', 'department.id', '=', 'subject.department_id')
        ->where('list_group.pterm_id', $termId)
        ->groupBy('subject.subject_code')
        ->get();
        
        
        $listNameteacher = [];
        $listDeparment = [];
        if (sizeof($listNameteacherObj) > 0){
            $listNameteacher = $listNameteacherObj->pluck('user_name', 'user_login')->toArray();
        }

        if (sizeof($listDeparmentObj) > 0){
            $listDeparment = $listDeparmentObj->pluck('department_name', 'subject_code')->toArray();
            $listDeparment = array_map(function ($a) {
                return preg_replace('/(\v|\s)+/', ' ', $a);
            }, $listDeparment);
        }
        
        // Lấy thông tin các feedback theo ngày mở
        $baseData = DB::select("SELECT
            feedback.id,
            feedback.teacher_login,
            list_group.group_name,
            list_group.psubject_code,
            list_group.psubject_name,
            ROUND(AVG(feedback_detail.Q1), 2) as Q1,
            ROUND(AVG(feedback_detail.Q2), 2) as Q2,
            ROUND(AVG(feedback_detail.Q3), 2) as Q3,
            ROUND(AVG(feedback_detail.Q4), 2) as Q4,
            ROUND(AVG(feedback_detail.Q5), 2) as Q5,
            ROUND(feedback.GPA, 2) as GPA,
            feedback.hit,
            (SELECT count(group_member.id) FROM group_member WHERE feedback.groupID = group_member.groupid GROUP BY group_member.groupid) AS total_member
        FROM
            feedback
            LEFT JOIN list_group ON list_group.id = feedback.groupID
            RIGHT JOIN feedback_detail ON feedback_detail.feedbackID = feedback.id
            WHERE list_group.pterm_id = $termId
            AND list_group.is_virtual = 0
        GROUP BY feedback.groupID");

        // lấy dữ liệu comment của sinh viên
        $listComment = DB::select("SELECT
            feedback_detail.feedbackID,
            feedback_detail.`comment`,
            feedback_detail.edited_comment 
        FROM
            feedback
            LEFT JOIN list_group ON list_group.id = feedback.groupID
            RIGHT JOIN feedback_detail ON feedback_detail.feedbackID = feedback.id 
        WHERE
            list_group.pterm_id = $termId
            AND feedback_detail.`comment` != '' 
            AND feedback_detail.`comment` IS NOT NULL
            AND list_group.is_virtual = 0
            ORDER BY feedbackID");

        foreach ($listComment as $k => $v) {
            $dataComment[$v->feedbackID][] = [
                'comment' => $v->comment,
                'edited_comment' => $v->edited_comment
            ];
        }
        
        // lấy thông tin số số lượng làm feedback 
        $infoReport = DB::select("SELECT
            count(*) as total,
            SUM( CASE WHEN custom_table.avg > 3.8 THEN 1 END ) AS a1,
            SUM( CASE WHEN ( custom_table.avg <= 3.8 AND custom_table.avg > 3.6 ) THEN 1 END ) AS a2,
            SUM( CASE WHEN ( custom_table.avg <= 3.6 AND custom_table.avg > 3.4 ) THEN 1 END ) AS a3,
            SUM( CASE WHEN custom_table.avg <= 3.4 THEN 1 END ) AS a4
        FROM
        (
            SELECT
                (( Q1 + Q2 + Q3 + Q4 + Q5 ) / 5 ) AS avg 
            FROM
                feedback_detail
                LEFT JOIN feedback ON feedback.id = feedback_detail.feedbackID 
            WHERE
            feedback.groupID IN ( SELECT id FROM list_group WHERE pterm_id = $termId ) 
        ) custom_table");

        // format dữ liệu đầu ra cuối
        $infoReport = (array)$infoReport[0];
        foreach ($baseData as $value) {
            $res[] = [
                'id' => $value->id,
                'teacher_login' => $value->teacher_login,
                'teacher_name' => $listNameteacher[$value->teacher_login] ?? "",
                'group_name' => $value->group_name,
                'deparment' => $listDeparment[$value->psubject_code] ?? "-",
                'psubject_code' => $value->psubject_code,
                'psubject_name' => $value->psubject_name,
                'Q1' => $value->Q1,
                'Q2' => $value->Q2,
                'Q3' => $value->Q3,
                'Q4' => $value->Q4,
                'Q5' => $value->Q5,
                'GPA' => $value->GPA,
                'hit' => $value->hit,
                'total_member' => $value->total_member,
                'list_comment' => $dataComment[$value->id] ?? []
            ];
        }

        return Excel::download(
            new FeedbackExport($res, $infoReport), 
            "feedback - $currentTerm->term_name.xlsx"
        );
    }
}
