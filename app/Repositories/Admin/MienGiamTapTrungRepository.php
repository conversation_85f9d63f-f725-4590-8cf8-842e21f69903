<?php
namespace App\Repositories\Admin;

use App\Http\Requests\Admin\MienGiamTapTrungRequest;
use App\Imports\MienGiamTapTrungImport;
use App\Models\Dra\StudentSubject;
use App\Models\Fu\Subject;
use App\Models\Fu\Term;
use App\Models\Fu\User;
use App\Models\MienGiamTapTrung;
use App\Models\SystemLog;
use App\Repositories\BaseRepository;
use App\Utils\ResponseBuilder;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class MienGiamTapTrungRepository extends BaseRepository
{

    /**
     * get model
     * @return string
     */
    public function getModel()
    {
        return MienGiamTapTrung::class;
    }


    public function index(Request $request) {
        $input = $request->all();
        $input['limit'] = $input['limit'] ?? 10;
        return view('admin_v1.substitute_subjects.index', []);
    }

    public function import(Request $request) {
        $input = $request->all();
        $file = $request->file('file');
        // dd($file);
        try {
            $import = new MienGiamTapTrungImport();
            Excel::import($import, $file);
            $arrMessage = $import->getErrors();
            if($import->getErrors() && is_array($arrMessage) && count($arrMessage) > 0){
                $message = implode(',<br />', $arrMessage);

                return redirect()->back()->with('status', [
                    'type' => 'danger',
                    'message' => $message
                ]);
            }
        } catch (\Exception $ex) {
            Log::error("--------- start err MienGiamTapTrungRepository:import ---------");
            Log::error($ex);
            Log::error("--------- end err MienGiamTapTrungRepository:import ---------");
            return redirect()->back()->with('status', [
                'type' => 'danger',
                'message' => "Import thất bại"
            ]);
        }
        
        return redirect()->back()->with('status', [
            'type' => 'success',
            'message' => "Import thành công"
        ]);
    }

    public function store(MienGiamTapTrungRequest $request) {
        $input = $request->all();
        try {
            DB::beginTransaction();
            
            $student = User::where('user_login', $input['user_login'])->first();
            if($input['type'] == 1){
                // $subject_update_able = DB::table('subject_update_able')
                //     ->where('old_subject_code', $input['subject_code'])
                //     ->where('new_subject_code', $input['subject_code_new'])
                //     ->first();
                    // dd($input); 
                $create_mgtt = MienGiamTapTrung::create([
                    'student_login' => $student->user_login ?? null,
                    'subject_code' => $input['subject_code'] ?? '',
                    'skill_code' => $input['subject_code'] ?? '',
                    'subject_code_new' => $input['subject_code_new'] ?? '',
                    'skill_code_new' =>  $input['subject_code_new'] ?? '',
                    'type' => $input['type'],
                    'so_quyet_dinh' => $input['so_quyet_dinh'] ?? '',
                    'time_action' => '0000-00-00',
                    'status' => 0,
                    'created_by' => auth()->user()->user_login,
                ]);
    
                SystemLog::create([
                    'object_name' => 'thay_the_mon',
                    'actor' => auth()->user()->user_login,
                    'log_time' => date('Y-m-d H:i:s', time()),
                    'action' => 'add',
                    'description' => "Add mien_giam_tap_trung(type = 1) with id = {$create_mgtt->id} for student {$create_mgtt->student_login}",
                    'object_id' => 0,
                    'brief' => 'add thay_the_mon',
                    'from_ip'=> $request->ip(),
                    'relation_login' => $create_mgtt->student_login,
                    'relation_id' => 0,
                    'nganh_cu' => '',
                    'nganh_moi' => '',
                    'ky_chuyen_den' => 0,
                    'ky_thu_cu' => 0
                ]);
            }else{
                $subject = Subject::where('subject_code', $input['subject_code'])->first();
                if(!$subject) {
                    // return redirect()->back()->with('status', [
                    //     'type' => 'danger',
                    //     'message' => 'Không tồn tại mã môn này'
                    // ]);
                    return ResponseBuilder::Fail("Không tồn tại mã môn này");

                }
                $create_mgtt = MienGiamTapTrung::create([
                    'student_login' => $student->user_login ?? '',
                    'subject_code' => $input['subject_code'] ?? '',
                    'skill_code' => $subject->skill_code ?? '',
                    'subject_code_new' =>  "",
                    'skill_code_new' =>   "",
                    'type' => $input['type'],
                    'so_quyet_dinh' => $input['so_quyet_dinh'] ?? '',
                    'time_action' => '0000-00-00',
                    'status' => 0,
                    'created_by' => auth()->user()->user_login,
                ]);

                SystemLog::create([
                    'object_name' => 'mien_giam',
                    'actor' => auth()->user()->user_login,
                    'log_time' => date('Y-m-d H:i:s', time()),
                    'action' => 'add',
                    'description' => "Add mien_giam_tap_trung(type = 2) with id = {$create_mgtt->id} for student {$create_mgtt->student_login}",
                    'object_id' => 0,
                    'brief' => 'add mien_giam',
                    'from_ip'=> $request->ip(),
                    'relation_login' => $create_mgtt->student_login,
                    'relation_id' => 0,
                    'nganh_cu' => '',
                    'nganh_moi' => '',
                    'ky_chuyen_den' => 0,
                    'ky_thu_cu' => 0
                ]);

                
            }

            $message = "";
            if($input['type'] == 1){
                $message = 'Thêm mới thay thế môn thành công';
            }else{
                $message = 'Thêm mới miễn giảm môn thành công';

            }

            DB::commit();
            // return redirect()->back()->with('status', [
            //     'type' => 'success',
            //     'message' => $message
            // ]);

            return ResponseBuilder::Success(null, $message);
        } catch (\Exception $th) {
            //throw $th;
            DB::rollBack();
            dd($th);
        }
    }


    public function detele($id)
    {
        // Mặc định để auth()->user()->user_level == 1 mới được xóa
        // check permission
        if(auth()->user()->user_level == 1) {
            $mien_giam_tap_trung =  MienGiamTapTrung::find($id);
            $mien_giam_tap_trung->delete();
            return redirect()->back()->with('status', [
                'type' => 'success',
                'message' => 'Xóa dữ liệu thành công'
            ]);
        }else{
            return redirect()->back()->with('status', [
                'type' => 'danger',
                'message' => 'Không có quyền xóa dữ liệu'
            ]);
        }
    }

    public function importMienGiam(Request $request) {
        $request->validate([
            'file' => ['required', 'mimes:xlsx']
        ], [
            'required' => "Không được bỏ trống :attribute",
            'mimes' => ":attribute không đúng định dạng"
        ], [
            'file' => "File"
        ]);

        $file = $request->file('file');
        $data = $this->importDataFromFile($file);

        if(!$data) {
            return redirect()->back()->with([
                'status' => [
                    'type' => 'danger', 
                    'messages' => 'Không có dữ liệu để import'
                ]
            ]);
        }

        $arrayDataCheck = $this->checkDataImportMienGiam($data);
        $data = $arrayDataCheck['data'];
        $error = $arrayDataCheck['error'];

        try {
            //code...
            DB::beginTransaction();
            DB::commit();
            if($error) {
                $errorMessageArr = array_map(function($item) {
                    return $item['message'];
                }, $error); 
                $errorMessage = implode(',<br />', $errorMessageArr);
                return redirect()->back()->with('status' ,[
                    'type' => 'danger', 
                    'message' => $errorMessage
                ]);
            }else{
                return redirect()->back()->with('status' ,[
                    'type' => 'success', 
                    'message' => "Import thành công"
                ]);
            }
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return redirect()->back()->with('status' ,[
                'type' => 'danger', 
                'message' => "Import thất bại"
            ]);
        }
    }

    public function checkDataImportMienGiam($data) {
        $dataCheck = [];
        $error = [];

        $data = array_map(function ($item) {
            return [
                'student_login' => trim($item[0]),
                'subject_code' => trim($item[1]),
                'in_result' => trim($item[2]),
                'grade' => trim($item[4]),
                'note' => trim($item[5]),
                'term_name' => trim($item[6])
            ];
        }, $data);

        $dataSuccess = [];

        foreach($data as $key => $dataCheck) {
            // check student_login
            $checkStudent = User::where('user_login', $dataCheck['student_login'])->orWhere('user_code', $dataCheck['student_login'])->first();

            if(!$checkStudent) {
                $index = $key + 1;
                $error[] = [
                    'row' => $index,
                    'message' => "Dữ liệu dòng {$index}, không tồn tại student_login" 
                ]; 
                continue;
            } 

            // check term_name
            $checkTerm = Term::where('term_name', $dataCheck['term_name'])->exists();

            if(!$checkTerm) {
                $index = $key + 1;
                $error[] = [
                    'row' => $index,
                    'message' => "Dữ liệu dòng {$index}, không tồn tại term_name" 
                ]; 
                continue;
            } 

            // check sinh vien da tot nghiep
            $study_status = $checkStudent->study_status;

            if($study_status == 8) {
                $index = $key + 1;
                $error[] = [
                    'row' => $index,
                    'message' => "Dữ liệu dòng {$index}, sinh viên đã tốt nghiệp" 
                ]; 
                continue;
            } 

            // Check duplicate
            $isDuplicate = MienGiamTapTrung::where('student_login', $dataCheck['student_login'])
                ->where('subject_code', $dataCheck['subject_code'])->exists();

            if($isDuplicate) {
                $index = $key + 1;
                $error[] = [
                    'row' => $index,
                    'message' => "Dữ liệu dòng {$index}, Dữ liệu đã tồn tại" 
                ]; 
                continue;
            }

            // check subject_code
            $subject = Subject::where('subject_code', $dataCheck['subject_code'])->orWhere('skill_code', $dataCheck['subject_code'])->exists();

            if(!$subject) {
                $index = $key + 1;
                $error[] = [
                    'row' => $index,
                    'message' => "Dữ liệu dòng {$index}, Không tồn tại subject_code {$dataCheck['subject_code']}" 
                ]; 
                continue;
            }


            $dataSuccess[] = $dataCheck;
        }

        return [
            'data' => $dataSuccess,
            'error' => $error
        ];
    }

    public function handleImportDataMienGiam($data) {
        $now = date('Y-m-d H:i:s', time());

        foreach ($data as $item) {
            $subject_code = $item['subject_code'];
            // get user
            $user = User::where('user_login', $item['student_login'])
                ->orWhere('user_code', $item['student_login'])->first();

            // get subject
            $subject = Subject::where('subject_code', $subject_code)->orWhere('skill_code', $subject_code)->first();

            // get term
            $term = Term::where('term_name', $item['term_name'])->first();

            // update  StudentSubject
            StudentSubject::where('student_login', $user->user_login)
            ->where(function ($query) use($subject_code) {
                $query->where('subject_code', $subject_code)->orWhere('skill_code', $subject_code);
            })
            ->update([
                'status' => 2,
                'is_lock_edit' => 1,
                'resourse' => 'credit transfer',
                'in_result' => $item['in_result'],
                'grade' => $item['grade'],
                'note' => $item['note'],
                'pass_term_id' => $term->id,
                'type' => 2,
                'is_start' => ($item['in_result'] == 1) ? 1: 0
            ]);

            // create mien_giam_tap_trung
            MienGiamTapTrung::create([
                'user_code' => $user->user_code,
                'student_login' => $user->user_login,
                'subject_code' => $subject_code,
                'skill_code' => $subject->skill_code,
                'subject_code_new' => "",
                'skill_code_new' => "",
                'type' => 2,
                'term_name' => $term->term_name,
                'so_quyet_dinh' => "",
                'time_action' => "0000:00:00",
                'user_action' => "",
                'status' => 0,
                'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'created_by' => auth()->user()->user_login,
                'term_id' => $term->id
            ]);


            // create system_log
            SystemLog::create([
                'object_name' => "final_grade",
                'actor' => auth()->user()->user_login,
                'log_time' => Carbon::now()->format('Y-m-d H:i:s'),
                'action' => "transfer credit",
                'description' => 'transfer credit type 2: ' . $subject_code . ' with note ' . $item['note'],
                'object_id' => 0,
                'brief' => 'transfer credit',
                'from_ip' => "",
                'relation_login' => $user->user_login,
                'relation_id' => $user->id,
                'nganh_cu' => "",
                'nganh_moi' => "",
                'ky_chuyen_den' => "",
                'ky_thu_cu' => ""
            ]);
        }
    }
}
