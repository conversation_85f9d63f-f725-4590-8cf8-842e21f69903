<?php

namespace App\Repositories\Admin;

use App\Models\Dra\T1Permission;
use App\Models\Dra\T1Role;
use App\Models\Dra\T1RolePermission;
use App\Models\Dra\T1UserRole;
use App\Repositories\BaseRepository;
use App\Utils\ResponseBuilder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use function foo\func;

class RoleRepository extends BaseRepository
{
    public function getModel()
    {
        return T1Role::class;
    }

    public function index($request)
    {
        $role_id = $request->role_id;
        $roles = $this->_model->orderBy('role_name')->with('permissions')->get();
        $user_roles = T1UserRole::orderBy('id', 'desc')->with('user:user_surname,user_middlename,user_givenname,user_login,user_email,id');

        if ($role_id) {
            $user_roles->where('role_id', $role_id);
        }
    
        $user_roles = $user_roles->paginate(20);
        $error_array = [];
        foreach ($user_roles as $user_role) {
            if (!$user_role->user) {
                $error_array[] = $user_role->user_login . "(role_id: $user_role->role_id)";
            }
        }
        
        foreach ($roles as $role) {
            $role->total_permission = $role->permissions->count();
        }

        return $this->view('role.index', [
            'roles' => $roles,
            'user_roles' => $user_roles,
            'error_array' => $error_array,
        ]);
    }

    public function deleteUser($request)
    {
        $user_login = $request->user_login;
        $id = $request->id;

        if ($this->mySelfCompareUserLogin($user_login)) {
            return $this->redirectWithStatus('warning', 'Bạn không thể xoá quyền tài khoản của chính bạn');
        }

        $result = T1UserRole::find($id);
        if ($result) {
            $result->delete();
            return $this->redirectWithStatus('success', "Xoá quyền tài khoản $result->user_login thành công");
        }

        return $this->redirectWithStatus('danger', 'Xoá quyền tài khoản thất bại');
    }

    public function add()
    {
        $permissions = $this->getAllPermission();

        return $this->view('role.add', [
            'permissions' => $permissions,
        ]);
    }

    public function store($request)
    {
        $data = $request->all();
        if ($request->id) {
            DB::beginTransaction();
            try {
                if (!isset($data['is_active'])) {
                    $data['is_active'] = 0;
                }

                $role = $this->_model->find($request->id);
                if ($role) {
                    $role->update($request->all());
                    T1RolePermission::where('role_id', $role->id)->delete();
                    $permissions = $request->permissions ?? [];
                    $array = [];
                    foreach ($permissions as $permission) {
                        $array[] = ['role_id' => $role->id, 'permission_id' => $permission];
                    }

                    T1RolePermission::insert($array);
                    T1UserRole::where('role_id', $role->id)->where('user_login', '!=', Auth::user()->user_login)->delete();
                    $user_array = [];
                    foreach ($request->users as $user) {
                        if ($user == Auth::user()->user_login) {
                            T1UserRole::updateOrInsert(['user_login' => $user, 'role_id' => $role->id]);
                        } else {
                            $user_array[] = ['user_login' => $user, 'role_id' => $role->id];
                        }
                    }

                    T1UserRole::insert($user_array);
                }

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                return $this->redirectWithStatus('danger', 'Đã xảy ra lỗi', route('admin.role'));
            }

            return $this->redirectWithStatus('success', 'Chỉnh sửa  hoàn tất', route('admin.role'));
        } else {
            DB::beginTransaction();
            try {
                if (!isset($data['is_active'])) {
                    $data['is_active'] = 0;
                }

                $role = $this->_model->create($request->all());
                $permissions = $request->permissions ?? [];
                $array = [];
                foreach ($permissions as $permission) {
                    $array[] = ['role_id' => $role->id, 'permission_id' => $permission];
                }

                T1RolePermission::insert($array);
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();

                return $this->redirectWithStatus('danger', 'Đã xảy ra lỗi', route('admin.role'));
            }

            return $this->redirectWithStatus('success', 'Tạo hoàn tất', route('admin.role'));
        }
    }

    public function edit($id)
    {
        $role = $this->_model->find($id);
        if ($role) {
            $role->load('permissions');
            $permissions = $this->getAllPermission();
            $array_permission = [];
            foreach ($role->permissions as $permission) {
                $array_permission[] = $permission->permission_id;
            }

            foreach ($permissions as $permission) {
                if (in_array($permission->id, $array_permission)) {
                    $permission->check = 1;
                    continue;
                }

                $permission->check = 0;
            }

            $users = T1UserRole::where('role_id', $role->id)->get();
            return $this->view('role.edit', [
                'role' => $role,
                'permissions' => $permissions,
                'users' => $users,
            ]);
        }

        return $this->redirectWithStatus('warning', 'Không tìm thấy');
    }

    public function checkPermission($request)
    {
        $user = auth()->user();
        $roleType = $request->get('role_name', 'null');
        $checkPermission = T1UserRole::checkPermission($user, $roleType);
        if (in_array($roleType, ['activity_add','activity_change','activity_delete'])) {
            if ($user->user_login == 'thaodp') {
                return ResponseBuilder::Success($checkPermission, 'Bạn đủ thẩm quyền');
            } else {
                return ResponseBuilder::Success(null, 'Bạn không đủ thẩm quyền');
            }
        } else {
            return ResponseBuilder::Success($checkPermission, 'Bạn đủ thẩm quyền');
        }
    }
}
