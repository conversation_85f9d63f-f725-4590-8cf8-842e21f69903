<?php

namespace App\Repositories\Admin;

use App\Http\Lib;
use Carbon\Carbon;
use App\Models\Fu\User;
use App\Models\Fu\Term;
use App\Models\Fu\ServiceLog;
use Illuminate\Support\Facades\Storage;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportScanStudent;

class ScanStudentRepository extends BaseRepository
{
    const list_tn123 = [3, 10, 11];

    public function getModel()
    {
        return ServiceLog::class;
    }

    public function getListScanStudent($request) {
        try {
            ini_set('memory_limit', '-1');
            ini_set('max_execution_time', -1);
            // get data những sinh viên, có trạng thái là TN1, TN2
            $list_student = User::where('user_level', 3)->whereIn('study_status', self::list_tn123);
            // get data những sinh viên, có trạng thái là TN1, TN2, để filter
            $list_student_filter = User::where('user_level', 3)->whereIn('study_status', self::list_tn123)->get([
                'user_code',
                'user_login',
                'user_surname',
                'user_middlename',
                'user_givenname',
            ]);
            if (isset($request->user_code) && $request->user_code != null && $request->user_code != 'null') {
                $list_student->where('user_code', $request->user_code);
            }
            if (isset($request->study_status) && $request->study_status != -1) {
                $list_student->where('study_status', $request->study_status);
            }
            // get data đơn học lại -> service_log, và get nhưng môn hiện tại sinh viên đang học để check -> group_member
            // service_logs join để check xem sinh viên đăng ký học lại môn nào
            $list_student = $list_student
            ->with(['service_log' => function ($service_log) {
                $now_term = Term::where('endday', '>=', Carbon::now())->first();
                $service_log
                ->orderBy('id', 'DESC')
                ->whereIn('type', [2, 23])
                ->where('payment_status', 1)
                ->with(['serviceRegister' => function ($serviceRegister) {
                    $serviceRegister
                    ->where([ ['type', 2], ['status', '!=', -1] ])
                    ->select([
                        'service_log_id',
                        'skill_code',
                    ]);
                }])
                ->with(['group_member' => function ($group_member) use ($now_term) {
                    $group_member
                    ->orderBy('id', 'DESC')
                    ->join('list_group', 'list_group.id', '=', 'group_member.groupid')
                    ->where('list_group.pterm_id', $now_term->id)
                    ->select([
                        'group_member.id',
                        'group_member.groupid',
                        'group_member.member_login',
                        'group_member.user_code',
                        'list_group.skill_code',
                        'list_group.pterm_id',
                    ]);
                }])
                ->select([
                    'id',
                    'user_id',
                    'user_code',
                    'full_name',
                    'ki_thu',
                    'study_status',
                    'status as statusLog',
                    'type',
                ]);
            }])
            ->get([
                'user_code',
                'user_login',
                'study_status',
            ]);

            // khai bao mang
            $List_correct_status_student = [];

            foreach ($list_student as $student) {
                $List_correct_status_student[$student->user_code]['user_code'] = $student->user_code;
                $List_correct_status_student[$student->user_code]['study_status'] = $student->study_status;

                // trường hợp 1: sinh viên ko có đơn học lại
                if (count($student->service_log) <= 0) {
                    $List_correct_status_student[$student->user_code]['register_service'] = 'Không';
                    $List_correct_status_student[$student->user_code]['register_turn_retest'] = 'Không';
                    $List_correct_status_student[$student->user_code]['turn_learn_again'] = 'Không';
                    $List_correct_status_student[$student->user_code]['turn_retest'] = 'Không';
                    $List_correct_status_student[$student->user_code]['status_true'] = 3;
                }

                foreach ($student->service_log as $service_log) {
                    if (empty($List_correct_status_student[$student->user_code]['register_turn_retest']) && empty($List_correct_status_student[$student->user_code]['turn_retest'])) {
                        $List_correct_status_student[$student->user_code]['register_turn_retest'] = 'Không';
                        $List_correct_status_student[$student->user_code]['turn_retest'] = 'Không';
                    }
                    if (empty($List_correct_status_student[$student->user_code]['register_service']) && empty($List_correct_status_student[$student->user_code]['turn_learn_again'])) {
                        $List_correct_status_student[$student->user_code]['register_service'] = 'Không';
                        $List_correct_status_student[$student->user_code]['turn_learn_again'] = 'Không';
                    }

                    if ($service_log->type == 23) {
                        foreach ($service_log->service_register_retest as $service_register_retest) {
                            if ($service_register_retest->relearn_online_calendar_id != null) {
                                $List_correct_status_student[$student->user_code]['turn_retest'] = 'Có';
                                $List_correct_status_student[$student->user_code]['status_true'] = 10;
                            }

                            if ($service_register_retest->relearn_online_calendar_id == null) {
                                $List_correct_status_student[$student->user_code]['register_turn_retest'] = 'Có';
                                if (empty($List_correct_status_student[$student->user_code]['status_true'])) {
                                    $List_correct_status_student[$student->user_code]['status_true'] = 11;
                                }
                            }
                        }
                    }

                    if ($service_log->type == 2) {
                        foreach ($service_log->group_member as $group_member) {
                            foreach ($service_log->serviceRegister as $service_register) {
                                // trường hợp 3: sinh viên có đơn học lại, check xem sinh viên đã đc sếp lớp hay chưa
                                if ($service_log->statusLog == 1 || $service_log->statusLog == 2) {
                                    // kiểm tra xem hiện tại student này đang học môn nó đk lại hay ko
                                    if ($service_register->skill_code == $group_member->skill_code) {
                                        $List_correct_status_student[$student->user_code]['turn_learn_again'] = 'Có';
                                        $List_correct_status_student[$student->user_code]['status_true'] = 10;
                                    }
                                }
                            }
                        }

                        // trường hợp 2: sinh viên có đơn học lại, nhưng chưa dc sếp lớp
                        if ($service_log->statusLog == 0 || $service_log->statusLog == 1) {
                            $List_correct_status_student[$student->user_code]['register_service'] = 'Có';
                            // nếu không vào trường hợp TN2 bên dưới thì status_true = TN3, vì nếu có lượt học lại trong kỳ thì status là TN2
                            if (empty($List_correct_status_student[$student->user_code]['status_true'])) {
                                $List_correct_status_student[$student->user_code]['status_true'] = 11;
                            }
                        }
                    }
                }
            }

            foreach ($List_correct_status_student as $key => $student) {
                // filter trạng thái đúng
                if (isset($student['status_true'])) {
                    if (isset($request->status_true) && $request->status_true != -1) {
                        if ($request->status_true != $student['status_true']) {
                            unset($List_correct_status_student[$key]);
                            continue;
                        }
                    }

                    // loại bỏ những trạng thái giống nhau
                    if ($student['study_status'] == $student['status_true']) {
                        unset($List_correct_status_student[$key]);
                        continue;
                    }
                }
                // loại bỏ những sinh viên ko có trạng thái đúng
                if (!isset($student['status_true'])) {
                    unset($List_correct_status_student[$key]);
                    continue;
                }
            }

            // export excel
            if (isset($request->exportExcel) && $request->exportExcel == 'exportExcel') {
                $export = new ExportScanStudent($List_correct_status_student);
                return Excel::download($export, 'danh_sach_sinh_vien_TN_da_quet.xlsx');
            }

            return response()->json([
                'count_List_correct_status_student' => count($List_correct_status_student),
                'List_correct_status_student' => $List_correct_status_student,
                'list_student_filter' => $list_student_filter,
            ]);
        } catch (\Throwable $th) {
            Log::error("----------------- start err createEditNewsletterStudent -------------------");
            Log::error($th);
            error_log($th);
            Log::error("----------------- end err createEditNewsletterStudent -------------------");
            return response("", 500);
        }
    }
}
