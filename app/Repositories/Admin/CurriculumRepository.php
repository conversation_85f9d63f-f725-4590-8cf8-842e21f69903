<?php


namespace App\Repositories\Admin;

use App\Models\Brand;
use App\Models\Dra\CurriCulum;
use App\Models\Dra\Period;
use App\Models\Dra\PeriodSubject;
use App\Models\Fu\ActionLog;
use App\Models\Fu\Subject;
use App\Models\Fu\Term;
use Illuminate\Support\Str;
use App\Repositories\BaseRepository;
use App\Utils\ResponseBuilder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CurriculumRepository extends BaseRepository
{
    public function getModel()
    {
        return CurriCulum::class;
    }


    /**
     * 
     */
    public function index(Request $request) {
        $terms = Term::orderBy('id', 'desc')->get();
        $listNganh = CurriCulum::select(['nganh'])->groupBy('nganh')->pluck('nganh');
        $listChuyenNganh = CurriCulum::select(['nganh', 'chuyen_nganh'])->groupBy('nganh', 'chuyen_nganh')->get();
        // dd($listNganh, $listChuyenNganh);

        return view('admin_v1.training_frame.list_frame', [
            'terms' => $terms,
            'listNganh' => $listNganh,
            'listChuyenNganh' => $listChuyenNganh
        ]);
    }

    /**
     * @todo Xuất danh sách khung
     * 
     */
    public function export(Request $request) {
        $listData = [];

        $listData = CurriCulum::select([
            "curriculum.id",
            "curriculum_code",
            DB::raw("(CASE curriculum.status
                WHEN 0 THEN 'Chưa kéo về cơ sở'
                WHEN 1 THEN 'Đã kéo về cơ sở'
                WHEN 2 THEN 'Có thể cập nhập'
                ELSE 'Không xác định'
            END) AS status"),
            "curriculum.name AS curriculum_name",
            "curriculum.khoa",
            "curriculum.brand_code",
            "curriculum.noi_dung",
            "curriculum.description",
            "curriculum.nganh",
            "curriculum.chuyen_nganh",
            "curriculum.nganh_in_bang",
            "curriculum.nganh_in_bang_en",
            "curriculum.chuyen_nganh_in_bang",
            "curriculum.chuyen_nganh_in_bang_en",
            DB::raw("COUNT(period_subject.id) AS total_subject"),
            DB::raw("SUM(period_subject.number_of_credit) AS total_credit"),
            DB::raw("(
                SELECT COUNT(user.id) 
                FROM user 
                WHERE `user`.`curriculum_id` = `curriculum`.`id` 
            ) AS total_student"),
        ])
        ->leftJoin('period_subject', 'curriculum.id', '=', 'period_subject.curriculum_id')
        // ->leftJoin('user', 'curriculum.id', 'user.curriculum_id')
        ->groupBy('curriculum.id')
        ->get();

        $listData = new Collection($listData);
        $headers = [
            'ID',
            'Mã khung',
            'Trạng thái',
            'Tên',
            'Khóa',
            'Mã ngành',
            'Nội dung',
            'Mô tả',
            'Ngành',
            'Chuyên ngành',
            'Ngành in bằng',
            'Ngành in bằng EN',
            'Chuyên ngành in bằng',
            'Chuyên ngành in bằng EN',
            'Tổng số môn',
            'Tổng số tín chỉ',
            'Số lượng sinh viên',
        ];
        
        $styles = [
            'header' => ("A1:P1"),
            'form' => ("A1:P" . (count($listData) + 1))
        ];
        
        return $this->fastExport(
            'exportListStudentCampaignDetail.xlsx', 
            $listData, 
            null,
            $headers,
            $styles
        );
    }


    /**
     * @todo Hiển thị màn hình tạo khung
     * 
     * <AUTHOR>
     * @since 18/02/2005
     * 
     */
    public function createCurriculum(Request $request) {
        return view('admin_v1.curriculum.create');
    }


    /**
     * @todo storageCurriculum
     * 
     * <AUTHOR>
     * @since 19/02/2005
     * 
     */
    public function storageCurriculum(Request $request) {
        DB::beginTransaction();
        try {
            // init data
            $attributes = $request->all();
            $curriculum = new Curriculum;
            $dataValidate = [
                'name' => [ 'status' => 1, 'name' => 'Tên'],
                'name_en' => [ 'status' => 1, 'name' => 'Tên tiếng anh'],
                'number_of_period' => [ 'status' => 1, 'name' => 'Tổng số giai đoạn'],
                'description' => [ 'status' => 0, 'name' => 'Mô tả'],
                'brand_code' => [ 'status' => 1, 'name' => 'Chuyên ngành học'],
                'nganh_in_bang' => [ 'status' => 1, 'name' => 'Ngành in bằng VI'],
                'nganh_in_bang_en' => [ 'status' => 1, 'name' => 'Ngành in bằng EN'],
                'chuyen_nganh_in_bang' => [ 'status' => 0, 'name' => 'Chuyên ngành in bằng VI'],
                'chuyen_nganh_in_bang_en' => [ 'status' => 0, 'name' => 'Chuyên ngành in bằng EN'],
            ];

            $dataImport = $request->only(array_keys($dataValidate));
            foreach ($dataImport as $key => $value) {
                if ($dataValidate[$key]['status'] == 0) {
                    continue;
                }
                
                if ($value == null || $value == '') {
                    return ResponseBuilder::Fail(($dataValidate[$key]['name'] ?? $key) . " không được bỏ trống, vui lòng thử lại.");
                }
            }

            // Kiểm tra số giai đoạn
            if ($dataImport['number_of_period'] < 1) {
                return ResponseBuilder::Fail("Tổng số giai đoạn không được bé hơn 1.");
            }

            $checkExistCurriculum = CurriCulum::where('name', $dataImport['name'])->count();
            if ($checkExistCurriculum > 0) {
                return ResponseBuilder::Fail("Đã tồn tại khung có tên là " . $dataImport['name']);
            }


            // Khởi tạo dữ liệu

            $curriculum->name = $dataImport['name'];
            $curriculum->name_en = $dataImport['name_en'];
            $curriculum->number_of_period = $dataImport['number_of_period'];
            $curriculum->description = $dataImport['description'] ?? '';
            $curriculum->brand_code = $dataImport['brand_code'];
            $curriculum->nganh_in_bang = $dataImport['nganh_in_bang'];
            $curriculum->nganh_in_bang_en = $dataImport['nganh_in_bang_en'];
            $curriculum->chuyen_nganh_in_bang = $dataImport['chuyen_nganh_in_bang'];
            $curriculum->chuyen_nganh_in_bang_en = $dataImport['chuyen_nganh_in_bang_en'];

            // Tạo currulum_code
            $curriculumCode = $this->generateCurriculumCode($dataImport);
            $curriculum->curriculum_code = $curriculumCode;

            // Set 1 số trường bắt buộc trong datatabase
            $curriculum->program_id = 0;
            $curriculum->mod_of_study = 0;
            $curriculum->number_of_credit = 0;
            $curriculum->number_of_subject = 0;
            $curriculum->status = 0;
            $curriculum->number_compulsory_credit = 0;
            $curriculum->number_compulsory_subject = 0;
            $curriculum->compulsory_subject_list = 0;
            $curriculum->number_optional_credit = 0;
            $curriculum->number_optional_subject = 0;
            $curriculum->optional_subject_list = 0;
            $curriculum->branch_object_id = '';
            $curriculum->branch_object_code = '';
            $curriculum->branch_table_name = '';

            // Lấy tên chuyên ngành
            $brand = Brand::where('code', $curriculum->brand_code)->first();
            $curriculum->nganh = $brand->major;
            $curriculum->chuyen_nganh = $brand->name;
            if ($brand->description != '' && $brand->description != null) {
                $curriculum->noi_dung = $brand->description;
            } else {
                $curriculum->noi_dung = $brand->name;
            }
            $curriculum->save();

            // Tạo giai đoạn
            for ($i=1; $i <= $dataImport['number_of_period']; $i++) { 
                $attributesPeriod['curriculum_id'] = $curriculum->id;
                $attributesPeriod['period_name'] = 'HK ' . $i;
                $attributesPeriod['ordering'] = $i;
                $attributesPeriod['book_rent_fee'] = 0;
                $attributesPeriod['is_current'] = 0;
                $attributesPeriod['fee_dead_line'] = 0;
                $attributesPeriod['start_date'] = '0000-00-00';
                $attributesPeriod['is_plan'] = 0;
                Period::create($attributesPeriod);
            }

            // Tạo khung
            ActionLog::create([
                'object'        => 'curriculum',
                'auth'          => auth()->user()->user_login,
                'action'        => 'create',
                'description'   => "Tạo khung với id: {$curriculum->id}",
                'object_id'     => 0,
                'data_changed'  => json_encode($curriculum->toArray()),
                'ip'            => request()->getClientIp(),
            ]);

            DB::commit();
            return ResponseBuilder::Success(null, "Tạo khung thành công");
        } catch (\Throwable $ex) {
            Log::error($ex);
            DB::rollBack();
            return ResponseBuilder::Fail("Lỗi trong quá trình tạo khung, vui lòng thử lại");
        }
        
        
    }


    /**
     * @todo Cập nhập khung
     * <AUTHOR>
     * @since 20/02/2005
     * 
     */
    public function updateCurriculum(Request $request) {
        // init data
        $attributes = $request->all();
        $curriculum = Curriculum::where('id', $request->id)->first();
        if(!$curriculum) {
            return ResponseBuilder::Fail("Khung không tồn tại, vui lòng thử lại.");
        } elseif ($curriculum->status == 1) {
            return ResponseBuilder::Fail("Khung chương trình đã khóa, liên hệ với cán bộ IT để mở cập nhập.");
        }

        $dataValidate = [
            'name' => [ 'status' => 1, 'name' => 'Tên'],
            'name_en' => [ 'status' => 1, 'name' => 'Tên tiếng anh'],
            'number_of_period' => [ 'status' => 1, 'name' => 'Tổng số giai đoạn'],
            'description' => [ 'status' => 0, 'name' => 'Mô tả'],
            'brand_code' => [ 'status' => 1, 'name' => 'Chuyên ngành học'],
            'khoa' => [ 'status' => 1, 'name' => 'Khoá'],
            'nganh' => [ 'status' => 1, 'name' => 'Ngành'],
            'chuyen_nganh' => [ 'status' => 1, 'name' => 'Chuyên ngành'],
            'noi_dung' => [ 'status' => 0, 'name' => 'Nội dung'],
            'nganh_in_bang' => [ 'status' => 1, 'name' => 'Ngành in bằng VI'],
            'nganh_in_bang_en' => [ 'status' => 1, 'name' => 'Ngành in bằng EN'],
            'chuyen_nganh_in_bang' => [ 'status' => 0, 'name' => 'Chuyên ngành in bằng VI'],
            'chuyen_nganh_in_bang_en' => [ 'status' => 0, 'name' => 'Chuyên ngành in bằng EN'],
        ];

        // ALTER TABLE `ap_poly`.`curriculum` 
        // ADD COLUMN `curriculum_code` varchar(255) NULL AFTER `id`;

        $dataImport = $request->only(array_keys($dataValidate));
        foreach ($dataImport as $key => $value) {
            if ($dataValidate[$key]['status'] == 0) {
                continue;
            }
            
            if ($value == null || $value == '') {
                return ResponseBuilder::Fail(($dataValidate[$key]['name'] ?? $key) . " Không được bỏ trống, vui lòng thử lại.");
            }
        }

        // Kiểm tra số giai đoạn
        if ($dataImport['number_of_period'] < 1) {
            return ResponseBuilder::Fail("Tổng số giai đoạn không được bé hơn 1.");
        }

        DB::beginTransaction();
        try {
            // Khởi tạo dữ liệu Lưu log
            $oldData = [
                'name' => $curriculum->name,
                'name_en' => $curriculum->name_en,
                'number_of_period' => $curriculum->number_of_period,
                'description' => $curriculum->description,
                'brand_code' => $curriculum->brand_code,
                'khoa' => $curriculum->khoa,
                'nganh' => $curriculum->nganh,
                'chuyen_nganh' => $curriculum->chuyen_nganh,
                'noi_dung' => $curriculum->noi_dung,
                'nganh_in_bang' => $curriculum->nganh_in_bang,
                'nganh_in_bang_en' => $curriculum->nganh_in_bang_en,
                'chuyen_nganh_in_bang' => $curriculum->chuyen_nganh_in_bang,
                'chuyen_nganh_in_bang_en' => $curriculum->chuyen_nganh_in_bang_en,
            ];
            $newData = [
                'name' => $dataImport['name'],
                'name_en' => $dataImport['name_en'],
                'number_of_period' => $dataImport['number_of_period'],
                'description' => $dataImport['description'] ?? '',
                'brand_code' => $dataImport['brand_code'],
                'khoa' => $dataImport['khoa'],
                'nganh' => $dataImport['nganh'],
                'chuyen_nganh' => $dataImport['chuyen_nganh'],
                'noi_dung' => $dataImport['noi_dung'] ?? '',
                'nganh_in_bang' => $dataImport['nganh_in_bang'],
                'nganh_in_bang_en' => $dataImport['nganh_in_bang_en'],
                'chuyen_nganh_in_bang' => $dataImport['chuyen_nganh_in_bang'],
                'chuyen_nganh_in_bang_en' => $dataImport['chuyen_nganh_in_bang_en'],
            ];
            $dataCreate = [
                'object'        => 'curriculum',
                'auth'          => auth()->user()->user_login,
                'action'        => 'update',
                'object_id'     => ($curriculum->id ?? ""),
                'description'   => "Tài khoản " . auth()->user()->user_login . " Cập nhập dữ liệu lịch trình của curriculum có id " . ($curriculum->id ?? "") ,
                'ip'            => $request->getClientIp(),
            ];


            /* Tiến hành update Khung */
            // Kiểm tra mã của khung có bị thay đổi không
            if ($oldData['brand_code'] != $newData['brand_code']) {
                // Lấy tên chuyên ngành
                $brand = Brand::where('code', $curriculum->brand_code)->first();
                $cnUpdate = $brand->name;
                $ndUpdate = "";
                if ($brand->noi_dung != '' && $brand->noi_dung != null) {
                    $cnUpdate = $brand->name. "(" . $brand->noi_dung . ")";
                    $ndUpdate = $brand->name. "(" . $brand->noi_dung . ")";
                }
                
                $oldData['chuyen_nganh'] = $curriculum->chuyen_nganh;
                $oldData['noi_dung'] = $curriculum->noi_dung;
                $newData['chuyen_nganh'] = $cnUpdate;
                $newData['noi_dung'] = $ndUpdate;
            }

            // Kiểm tra trạng thái nếu IT bật chỉnh sửa
            if ($curriculum->status == -1) {
                $oldData['status'] = -1;
                $newData['status'] = 1;
            }
            
            // Duyệt những dữ liệu bị thay đổi
            foreach ($oldData as $key => $value) {
                if ($oldData[$key] != $newData[$key]) {
                    $curriculum->{$key} = $newData[$key];
                }
            }

            // Lưa và thêm log
            $curriculum->save();
            $this->createActionLog($oldData, $newData, $dataCreate, 1);

            // Điều chỉnh giai đoạn
            // Lấy tổng số giai đoạn
            $currentPeriod = Period::where('curriculum_id', $curriculum->id)->orderBy('ordering')->get();
            // dump($currentPeriod->toArray());
            $totalPeriodOld = count($currentPeriod);
            $totalPeriodNew = $newData['number_of_period'];
            if ($totalPeriodOld != $totalPeriodNew) {
                for ($i=0; $i < max($totalPeriodOld, $totalPeriodNew); $i++) { 
                    if ($i == 0) continue;
                    // Nếu không tồn tại thì tạo mới
                    if(!isset($currentPeriod[$i])) {
                        $attributesPeriod['curriculum_id'] = $curriculum->id;
                        $attributesPeriod['period_name'] = 'HK ' . ($i+1);
                        $attributesPeriod['ordering'] = ($i+1);
                        $attributesPeriod['book_rent_fee'] = 0;
                        $attributesPeriod['is_current'] = 0;
                        $attributesPeriod['fee_dead_line'] = 0;
                        $attributesPeriod['start_date'] = now()->format('Y-m-d');
                        $attributesPeriod['is_plan'] = 0;
                        Period::create($attributesPeriod);
                    } elseif ($i >= $totalPeriodNew) {
                        $periodCheck = $currentPeriod[$i];
                        // log lại Danh sách Môn xóa
                        $listSubjectCode = PeriodSubject::select(DB::raw('GROUP_CONCAT(subject_code) AS list_subject'))
                        ->where('period_id', $periodCheck->id)
                        ->groupBY('period_id')
                        ->first();

                        // Thêm log xóa
                        $dataDelete = [
                            'id' => $periodCheck->id,
                            'curriculum_id' => $periodCheck->curriculum_id,
                            'period_name' => $periodCheck->period_name,
                            'ordering' => $periodCheck->ordering,
                            'list_subject' => ($listSubjectCode->list_subject ?? ""),
                        ];
                        $dataCreate = [
                            'object'        => 'dra_period',
                            'auth'          => auth()->user()->user_login,
                            'action'        => 'delete',
                            'object_id'     => ($periodCheck->id ?? ""),
                            'description'   => "Tài khoản " . auth()->user()->user_login . " Xóa kỳ có id = " . ($periodCheck->id ?? "") ,
                            'ip'            => $request->getClientIp(),
                        ];

                        $this->createActionLog($dataDelete, [], $dataCreate, 1);
                        $listSubjectPeriod = PeriodSubject::where('period_id', $periodCheck->id)->get();
                        foreach ($listSubjectPeriod as $vPeriodSubject) {
                            $vPeriodSubject->delete();
                        }
                        
                        $periodCheck->delete();
                    }
                }

            }

            DB::commit();

            // Cập nhập tổng số lượng môn + số tín của khung (Cập nhập sau vì dang dùng transaction)
            if ($totalPeriodOld != $totalPeriodNew) {
                DB::select("UPDATE curriculum
                JOIN (
                    SELECT curriculum_id, COUNT(period_subject.id)  AS total_subject, SUM(subject.num_of_credit) AS total_credit
                    FROM period_subject 
                    LEFT JOIN subject ON subject.subject_code = period_subject.subject_code
                    GROUP BY curriculum_id
                ) tbl ON (curriculum.id = curriculum_id)
                SET curriculum.number_of_subject = total_subject, curriculum.number_of_credit = total_credit
                WHERE curriculum.id = ?", [$curriculum->id]);
            }

            return ResponseBuilder::Success(null, "Cập nhật thành công");
        } catch (\Throwable $ex) {
            Log::error("lỗi trong quá trình cập nhật khung");
            Log::error($ex);
            DB::rollBack();
            return ResponseBuilder::Fail("Lỗi trong quá trình cập nhật khung, vui lòng thử lại");
        }
    }


    /**
     * @todo Tải file mẫu up môn cho học kỳ
     * 
     * <AUTHOR>
     * @since 21/02/2025
     * 
     */
    public function getPeridoSubjectTemplate(Request $request) {
        return $this->fastExport(
            'list_period_subject.xlsx', 
            (Collection::make(new CurriCulum)), 
            null,
            ['Period orderting','subject_code'],
            [
                'header' => ("A1:B1"),
                'form' => ("A1:B1")
            ]
        );
    }
    
    /**
     * @todo Cập nhập môn trong khung 
     * <AUTHOR>
     * @since 21/02/2005
     * 
     */
    public function updatePeriodSubject(Request $request) {
        // init data
        $id = $request->get('id', -1);
        $curriculum = Curriculum::where('id', $id)->first();
        if(!$curriculum) {
            return ResponseBuilder::Fail("Khung không tồn tại, vui lòng thử lại.");
        } elseif ($curriculum->status == 1) {
            return ResponseBuilder::Fail("Khung chương trình đã khóa, liên hệ với cán bộ IT để mở cập nhập.");
        }

        $file = $request->file('file');
        if (!$datas = $this->importDataFromFile($file)) {
            return ResponseBuilder::Fail('File import không hợp lệ, vui lòng thử lại sau');
        }

        // lấy danh sách kỳ học của khung
        $listPeriod = Period::where('curriculum_id', $id)->get();
        $listPeriodOrderId = $listPeriod->where('curriculum_id', $id)->pluck('id', 'ordering')->toArray();
        $listPeriodOrderName = $listPeriod->where('curriculum_id', $id)->pluck('period_name', 'id')->toArray();
        // Kiểm tra số lượng dữ liệu trong file
        if (count($datas) == 0) {
            return ResponseBuilder::Fail('File import không hợp lệ, vui lòng thử lại sau');
        }

        DB::beginTransaction();
        try {
            // Lấy danh sách mã chuyển đổi trong khung
            $listSkillCodeCheck = PeriodSubject::where('curriculum_id', $curriculum->id)
            ->pluck('subject_code', 'skill_code')
            ->toArray();
            // Duyệt dữ liệu import
            foreach ($datas as $key => $data) {
                /* Khởi tạo dữ liệu */
                // Kiểm tra kỳ
                if (!isset($listPeriodOrderId[$data[0]])) {
                    DB::rollBack();
                    return ResponseBuilder::Fail('Không tồn tại kỳ thứ ' . $data[0] . ' vui lòng thử lại!');
                }

                // Kiểm tra môn học
                $subject = Subject::where('subject_code', $data[1])->first();
                if (!$subject) {
                    DB::rollBack();
                    return ResponseBuilder::Fail('Không tồn tại môn học ' . $data[1] . ' vui lòng thử lại!');
                }

                // Kiểm tra môn đã có trong khung chưa ?
                if (in_array($subject->skill_code, array_keys($listSkillCodeCheck))) {
                    DB::rollBack();
                    return ResponseBuilder::Fail(
                        'Môn ' . $data[1] . ' Đã tồn tại trong khung với mã môn là ' . 
                        ($listSkillCodeCheck[$subject->skill_code] ?? "") . ', vui lòng kiểm tra lại!'
                    );
                }

                $idPeriod = $listPeriodOrderId[$data[0]];
                $dataInsert = [
                    'period_id' => $idPeriod,
                    'period_name' => $listPeriodOrderName[$idPeriod],
                    'curriculum_id' => $curriculum->id,
                    'curriculum_name' => $curriculum->name,
                    'subject_id' => $subject->id,
                    'skill_code' => $subject->skill_code,
                    'subject_code' => $subject->subject_code,
                    'subject_name' => $subject->subject_name,
                    'number_of_credit' => $subject->num_of_credit,
                    'is_compulsory' => 0,
                    'in_result' => 0,
                    'replacement_skill_code' => '',
                    'replacement_subject_code' => '',
                    'replacement_subject' => '',
                    'max_attempt' => 0,
                    'fee_first_time' => 0,
                    'fee_next_time' => 0,
                    'prerequisite_subject_code' => '',
                    'prerequisite_skill_code' => '',
                    'prerequisite_skill_code_attend' => '',
                    'prerequisite_subject_code_attend' => '',
                ];

                // khởi tạo dữ liệu
                $newPeriodSubject = PeriodSubject::create($dataInsert);

                // Thêm log insert
                $dataCreate = [
                    'object'        => 'period_subject',
                    'auth'          => auth()->user()->user_login,
                    'action'        => 'create',
                    'object_id'     => ($newPeriodSubject->id ?? ""),
                    'description'   => (
                            "Tài khoản " . auth()->user()->user_login . " Thêm môn " . 
                            $data[1] . " tại kỳ " . $data[0] . " tại khung " . ($curriculum->id ?? "")
                        ),
                    'ip'            => $request->getClientIp(),
                ];

                $this->createActionLog([], $dataInsert, $dataCreate, 1);
            }

            DB::commit();

            // Cập nhập tổng số lượng môn + số tín của khung (Cập nhập sau vì dang dùng transaction)
            DB::select("UPDATE curriculum dc
            JOIN (
                SELECT curriculum_id, COUNT(period_subject.id)  AS total_subject, SUM(subject.num_of_credit) AS total_credit
                FROM period_subject 
                LEFT JOIN subject ON subject.subject_code = period_subject.subject_code
                GROUP BY curriculum_id
            ) tbl ON (dc.id = curriculum_id)
            SET dc.number_of_subject = total_subject, dc.number_of_credit = total_credit
            WHERE dc.id = ?", [$curriculum->id]);
            return ResponseBuilder::Success(null, "Thêm môn thành công!");
        } catch (\Throwable $ex) {
            Log::error("lỗi trong quá trình cập nhật khung");
            Log::error($ex);
            DB::rollBack();
            return ResponseBuilder::Fail("Lỗi trong quá trình cập nhật khung, vui lòng thử lại");
        }
        
        
    }
    


    /**
     * @todo Lấy danh sách ngành
     * 
     * <AUTHOR>
     * @since 18/02/2005
     * 
     */
    public function getlistBrand(Request $request) {
        // BranchPruner
        $listBrand = Brand::select([
            'id', 
            'code', 
            'name',
            'major',
            'description',
            DB::raw("CONCAT_WS('-', 
                (CASE WHEN major_code = '' THEN NULL ELSE major_code END),
                (CASE WHEN specialized_code = '' THEN NULL ELSE specialized_code END),
                (CASE WHEN sub_specialized_code = '' THEN NULL ELSE sub_specialized_code END)
            ) as new_name")
        ])
        ->orderBy('id', 'DESC')
        ->get();

        return ResponseBuilder::Success($listBrand ?? []);
    }

    private function generateCurriculumCode($dataImport) {
        $curriculumCode = ($dataImport['brand_code'] . "-b-" . Str::random(10));
        $curriculumCode = Str::slug($curriculumCode);
        $curriculumCode = str_replace('-', '_', $curriculumCode);
        $curriculumCode = str_replace('_b_', '-', $curriculumCode);

        // Kiểm tra nếu code đã tồn tại
        if (CurriCulum::where('curriculum_code', $curriculumCode)->count() > 0) {
            return $this->generateCurriculumCode($dataImport);
        }

        return $curriculumCode;
    }

    public function autoGenerateCurriculumCode($request) {
        if (auth()->user()->user_login != 'dont') {
            Log::error(auth()->user()->user_login . " Vào tạo mã khung!");
            return ResponseBuilder::Fail("Tạo thất bại vào log xem");
        }

        $listCurriculum = CurriCulum::whereNull('curriculum_code')->get();
        DB::beginTransaction();
        try {
            foreach ($listCurriculum as $key => $curriculum) {
                if ($curriculum->curriculum_code == null || $curriculum->curriculum_code == '') {
                    $curriculum->curriculum_code = $this->generateCurriculumCode([
                        'brand_code' => $curriculum->brand_code,
                        'khoa' => $curriculum->khoa,
                    ]);
    
                    $curriculum->save();
                }
            }

            DB::commit();
            Log::error(auth()->user()->user_login . " Vào tạo mã khung thành công!");
            return ResponseBuilder::Success(null, "Tạo code thành công cho " . count($listCurriculum) . " khung!");
        } catch (\Throwable $th) {
            Log::error("Tạo thất bại vào log xem");
            Log::error($th);
            DB::rollBack();
            return ResponseBuilder::Fail("Tạo thất bại vào log xem");
        }
    }


    /**
     * @todo Xóa khung
     * 
     * <AUTHOR>
     * @since 22/02/2025
     * 
     */
    public function deleteCurriculum(Request $request) {
        // init data
        $curriculum = Curriculum::where('id', $request->id)->first();
        if(!$curriculum) {
            return ResponseBuilder::Fail("Khung không tồn tại, vui lòng thử lại.");
        } elseif ($curriculum->status != 0) {
            return ResponseBuilder::Fail("Khung chương trình đã khóa, Không thể xóa!");
        }

        $curriculumName = $curriculum->name;
        DB::beginTransaction();
        try {
            // Lấy danh sách kỳ học để xóa
            $listPeriod = Period::where('curriculum_id', $curriculum->id)
                ->orderBy('ordering')
                ->get();
            foreach ($listPeriod as $key => $period) {
                $period = $period;
                // log lại Danh sách Môn xóa
                $listSubjectCode = PeriodSubject::select(DB::raw('GROUP_CONCAT(subject_code) AS list_subject'))
                    ->where('period_id', $period->id)
                    ->groupBY('period_id')
                    ->first();

                // Thêm log xóa
                $dataDelete = [
                    'id' => $period->id,
                    'curriculum_id' => $period->curriculum_id,
                    'period_name' => $period->period_name,
                    'ordering' => $period->ordering,
                    'list_subject' => ($listSubjectCode->list_subject ?? ""),
                ];
                $dataCreate = [
                    'object'        => 'dra_period',
                    'auth'          => auth()->user()->user_login,
                    'action'        => 'delete',
                    'object_id'     => ($period->id ?? ""),
                    'description'   => "Tài khoản " . auth()->user()->user_login . " Xóa kỳ có id = " . ($period->id ?? "") ,
                    'ip'            => $request->getClientIp(),
                ];
                
                // Thêm log
                $this->createActionLog($dataDelete, [], $dataCreate, 1);

                // Xóa thôi
                PeriodSubject::where('period_id', $period->id)->delete();
                $period->delete();
            }

            // Khởi tạo dữ liệu Lưu log
            $dataDelete = [
                'name' => $curriculum->name,
                'name_en' => $curriculum->name_en,
                'number_of_period' => $curriculum->number_of_period,
                'description' => $curriculum->description,
                'brand_code' => $curriculum->brand_code,
                'khoa' => $curriculum->khoa,
                'nganh' => $curriculum->nganh,
                'noi_dung' => $curriculum->noi_dung,
                'nganh_in_bang' => $curriculum->nganh_in_bang,
                'nganh_in_bang_en' => $curriculum->nganh_in_bang_en,
                'chuyen_nganh_in_bang' => $curriculum->chuyen_nganh_in_bang,
                'chuyen_nganh_in_bang_en' => $curriculum->chuyen_nganh_in_bang_en,
            ];
            $dataCreate = [
                'object'        => 'curriculum',
                'auth'          => auth()->user()->user_login,
                'action'        => 'delete',
                'object_id'     => ($curriculum->id ?? ""),
                'description'   => "Tài khoản " . auth()->user()->user_login . " Xóa khung có id " . ($curriculum->id ?? "") ,
                'ip'            => $request->getClientIp(),
            ];

            // Xóa và thêm log
            $curriculum->delete();
            $this->createActionLog($dataDelete, [], $dataCreate, 1);
            
            DB::commit();
            return ResponseBuilder::Success(null, "Xóa thành công Khung chương trình " . $curriculumName);
        } catch (\Throwable $ex) {
            Log::error("lỗi trong quá trình cập nhật khung");
            Log::error($ex);
            DB::rollBack();
            return ResponseBuilder::Fail("Lỗi trong quá trình cập nhật khung, vui lòng thử lại");
        }
    }

    public function downloadCurriculum(Request $request) {
        $data = Curriculum::select([
            'id',
            'name as ten_khung',
            'number_of_period as so_ky',
            'description as mo_ta',
            'khoa as khoa',
            'brand_code as ma_nganh',
            'nganh as nganh',
            'chuyen_nganh as chuyen_nganh',
            'noi_dung as chuyen_nganh_hep', 
        ])->get();
        return $data;
    }
}
