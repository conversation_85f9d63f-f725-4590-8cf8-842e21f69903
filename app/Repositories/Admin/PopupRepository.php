<?php

namespace App\Repositories\Admin;

use App\Http\Lib;
use Carbon\Carbon;
use App\Models\Popup;
use App\Models\Fu\User;
use Illuminate\Support\Facades\Storage;
use App\Repositories\BaseRepository;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class PopupRepository extends BaseRepository
{

    public function getModel()
    {
        return Popup::class;
    }

    /**
     * index return view manager popup
     */
    public function index()
    {
        return $this->view('popup.index');
    }


    /**
     * getlistPopup - get list popups
     */
    public function getlistPopup($request)
    {
        try {
            $popups = Popup::orderBy('id', 'desc');
            $popups_length = Popup::all();

            if (isset($request->status) && ($request->status != null)) {
                $popups->where('status', $request->status);
            }
            if (isset($request->start_time) && ($request->start_time != null)) {
                $popups->where('start_time', '>=', $request->start_time);
            }
            if (isset($request->end_time) && ($request->end_time != null)) {
                $popups->where('end_time', '<=', $request->end_time);
            }
            if (($request->searchTitlePopup != null) && !empty($request->searchTitlePopup)) {
                $popups->where('title', 'like', '%' . $request->searchTitlePopup . '%');
            }

            $popups = $popups->get();

            return response()->json([
                'popups' => $popups,
                'lengthPopups' => sizeof($popups_length),
            ]);
        } catch (\Throwable $th) {
            Log::error("--------- start err getlistPopup ---------");
            Log::error($th);
            Log::error("--------- end err getlistPopup ---------");
            return response([], 500);
        }
    }

    /**
     * store - created and edit
     */
    public function store($request)
    {
        try {
            $popup = [];
            $popup_check = Popup::where('status', 1)->first();

            // check status popup
            if ($request->status == 1) {
                // nếu có id tức là edit, vì vậy ta where thêm id != id request, ko kiểm tra status chính nó
                if ($request->id) {
                    $popup_check = Popup::where('id', '!=', $request->id)->where('status', 1)->first();
                }
                // check status popup active does not exist
                if (empty($popup_check)) {
                    // call to storeAndEditDetail
                    $popup = $this->storeAndEditDetail($request);
                } else {
                    // status popup active exist response a warning
                    return response()->json([
                        'warning' => 'Đã có 1 thông báo đang hoạt động, vui lòng chuyển trạng thái sang chế độ Hàng chờ!',
                    ]);
                }
            } else {
                // call to storeAndEditDetail
                $popup = $this->storeAndEditDetail($request);
            }
            return response()->json([
                'popup' => $popup->only(['id', 'created_by', 'updated_by']),
            ], 200);
        } catch (\Exception $th) {
            Log::error("--------- start err created ---------");
            Log::error($th);
            Log::error("--------- end err created ---------");
            return response(0, 500);
        }
    }


    /**
     * edit - get dat edit
     */
    public function edit($id)
    {
        try {
            // find id and return data popup for client
            $popup = Popup::findOrFail($id);
            return response()->json([
                'popup' => $popup,
            ]);
        } catch (\Throwable $th) {
            Log::error("--------- start err edit ---------");
            Log::error($th);
            Log::error("--------- end err edit ---------");
            return response(0, 500);
        }
    }

    /**
     * delete popup
     */
    public function delete($id)
    {
        try {
            // where id and status != 1 and delete a popup
            $popup = Popup::where('id', $id)->first();
            if (!empty($popup)) {
                if ($popup->status != 1) {
                    $popup->delete();
                    return response()->json([
                        'warning' => 'Popup đã được xóa!',
                    ]);
                } else {
                    return response()->json([
                        'warning' => 'Popup Đang hoạt động, không thể xóa',
                    ]);
                }
            } else {
                return response()->json([
                    'warning' => 'Popup Này không tồn tại, không thể xóa',
                ]);
            }
        } catch (\Throwable $th) {
            Log::error("--------- start err delete ---------");
            Log::error($th);
            Log::error("--------- end err delete ---------");
            return response(0, 500);
        }
    }

    /**
     * storeAndEditDetail - perform store and edit
     */
    public function storeAndEditDetail($request)
    {
        try {
            if (!$request->id) {
                // add value for request
                $request->request->add([
                    'created_by' => Auth::user()->user_login,
                    'updated_by' => Auth::user()->user_login,
                ]);

                // create popup with request all
                $popup = Popup::create($request->all());
            } else {
                // add value for request
                $request->request->add([
                    'updated_by' => Auth::user()->user_login,
                ]);

                // find id popup and update popup
                $popup = Popup::findOrFail($request->id);
                $popup->update($request->all());
            }

            // return data popup
            return $popup;
        } catch (\Throwable $th) {
            Log::error("--------- start err search ---------");
            Log::error($th);
            Log::error("--------- end err search ---------");
            return response(0, 500);
        }
    }
}
