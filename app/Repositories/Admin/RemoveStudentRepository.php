<?php

namespace App\Repositories\Admin;

use App\Http\Lib;
use App\Models\Fu\Attendance;
use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use App\Models\Fu\RemovedApStudent;
use App\Models\Fu\RemoveListStudent;
use App\Models\SystemLog;
use App\Models\T7\CourseGrade;
use App\Models\T7\CourseResult;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use App\Repositories\BaseRepository;
use App\Models\Fu\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Fu\ServiceRegister;
use App\Http\UseCases\RelearnUseCase;
use App\Repositories\Admin\OnlineServiceV2\RelearnSingleRepository;
use App\Models\Fu\ServiceLog;
use App\Models\Fu\ServiceProcessLog;
use Illuminate\Support\Facades\Auth;

class RemoveStudentRepository extends BaseRepository
{

    public function getModel()
    {
        return RemoveListStudent::class;
    }

    /**
     * get list StudentRemove_30
     * @return mixed $response
     */
    public function getListStudentRemove_30($request) {
        try {
            $listStudentRemove_30 = RemoveListStudent::query();
            if (isset($request->user_code)) {
                if ($request->user_code != null && $request->user_code != "") {
                    $listStudentRemove_30->where("user_code", 'like', '%' . $request->user_code . '%');
                }
            }
            $listStudentRemove_30 = $listStudentRemove_30->where('type', self::FILE_TYPE_REMOVE_STUDENT_30)->orderBy('id', 'desc')->paginate(8);
            return response($listStudentRemove_30, 200);
        } catch (\Throwable $th) {
            Log::error("--------- start err getListStudentRemove_30 ---------");
            Log::error($th);
            Log::error("--------- end err getListStudentRemove_30 ---------");
            return response([], 500);
        }
    }

    /**
     * create list StudentRemove_30
     * @return mixed $response
        */
        public function createListStudentRemove_30($request) {
        try {
            ini_set('memory_limit', '-1');
            $whoIs = auth()->user()->user_login;
            $file = $request->file;
            $datas = $this->importDataFromFile($file);
            DB::beginTransaction();
            foreach ($datas as $key => $item) {
                $user_code = $item[0];
                $group_name = $item[1];
                $subject_code = $item[2];
                $pterm_name = $item[3];
                $student = User::where('user_code', $user_code)->first();
                $group_id = GroupMember::leftJoin('list_group', 'list_group.id', 'group_member.groupid')
                    ->where('group_member.member_login', $student->user_login)
                    ->where('list_group.group_name', $group_name)
                    ->where('list_group.psubject_code', $subject_code)
                    ->where('list_group.pterm_name', $pterm_name)
                    ->first()->groupid;
                if ($group_id && $student) {
                    RemoveListStudent::firstOrCreate(
                        [
                            'user_code' => $user_code,
                            'group_name' => $group_name,
                            'group_id' => $group_id,
                            'subject_code' => $subject_code,
                        ],
                        [
                            'type' => self::FILE_TYPE_REMOVE_STUDENT_30,
                            'created_by' => $whoIs
                        ]
                    );
                }
            }
            DB::commit();
            return response()->json([
                'error' => false,
                'message' => 'Import file thành công'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            Log::error("--------- start err createListStudentRemove_30 ---------");
            Log::error($th);
            Log::error("--------- end err createListStudentRemove_30 ---------");
            return response([], 500);
        }
    }

    /**
     * delete StudentRemove_30
     * @return mixed $response
     */
    public function deleteStudentRemove_30($request) {
        try {
            RemoveListStudent::where('group_id', $request->group_id)->delete();
            DB::commit();
            return response()->json([
                'error' => false,
                'message' => 'Xóa sinh viên ' . $request->user_code . ' ra khỏi danh sách thành công'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            Log::error("--------- start err deleteListStudentRemove_30 ---------");
            Log::error($th);
            Log::error("--------- end err deleteListStudentRemove_30 ---------");
            return response([], 500);
        }
    }

    /**
     * delete ListStudentRemove_30
     * @return mixed $response
     */
    public function deleteListStudentRemove_30() {
        try {
            $whoIs = auth()->user()->user_login;
            $listStudentRemove_30 = RemoveListStudent::where('type', self::FILE_TYPE_REMOVE_STUDENT_30)->get();

            DB::beginTransaction();

            foreach ($listStudentRemove_30 as $studentRemove_30) {
                $user_login = User::where('user_code', $studentRemove_30->user_code)->first();
                $user_login = $user_login->user_login;
                $group_id = $studentRemove_30->group_id;
                $content = "remove member $user_login from group $group_id";

                GroupMember::where('groupid', $group_id)->where('member_login', $user_login)->delete();
                Attendance::where('groupid', $group_id)->where('user_login', $user_login)->delete();
                CourseGrade::where('groupid', $group_id)->where('login', $user_login)->delete();
                CourseResult::where('groupid', $group_id)->where('student_login', $user_login)->delete();
                RemoveListStudent::where('user_code', $studentRemove_30->user_code)->delete();  

                SystemLog::create([
                    'object_name' => 'group',
                    'actor' => 'admin',
                    'log_time' => Carbon::now(),
                    'action' => 'remove member 30% created by ' . $studentRemove_30->created_by . ' and confirm by ' . $whoIs,
                    'description' => $content,
                    'object_id' => $group_id,
                    'brief' => 'remove member',
                    'from_ip' => '*********',
                    'relation_login' => $user_login,
                    'relation_id' => $group_id,
                    'nganh_cu' => '',
                    'nganh_moi' => '',
                    'ky_chuyen_den' => 0,
                    'ky_thu_cu' => 0,
                ]);

            }
            DB::commit();
            return response()->json([
                'error' => false,
                'message' => 'Rút lớp thành công'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            Log::error("--------- start err deleteListStudentRemove_30 ---------");
            Log::error($th);
            Log::error("--------- end err deleteListStudentRemove_30 ---------");
            return response([], 500);
        }
    }
    /**
     * get list StudentRemove_30
     * @return mixed $response
     */
    public function getListStudentRemoveAp($request) {
        try {
            $listStudentRemoveAp = RemoveListStudent::query();
            if (isset($request->user_code)) {
                if ($request->user_code != null && $request->user_code != "") {
                    $listStudentRemoveAp->where("user_code", 'like', '%' . $request->user_code . '%');
                }
            }
            $listStudentRemoveAp = $listStudentRemoveAp->where('type', self::FILE_TYPE_REMOVE_STUDENT_AP)->orderBy('id', 'desc')->paginate(8);
            return response($listStudentRemoveAp, 200);
        } catch (\Throwable $th) {
            Log::error("--------- start err getListStudentRemoveAp ---------");
            Log::error($th);
            Log::error("--------- end err getListStudentRemoveAp ---------");
            return response([], 500);
        }
    }

    /**
     * create list StudentRemoveAp
     * @return mixed $response
        */
        public function createListStudentRemoveAp($request) {
        try {
            ini_set('memory_limit', '-1');
            $whoIs = auth()->user()->user_login;
            $file = $request->file;
            $datas = $this->importDataFromFile($file);
            DB::beginTransaction();
            foreach ($datas as $key => $item) {
                $user_code = $item[0];
                $user_login = User::where('user_code', $user_code)->first();
                if ($user_login) {
                    RemoveListStudent::firstOrCreate(
                        [
                            'user_code' => $user_code
                        ],
                        [
                            'type' => self::FILE_TYPE_REMOVE_STUDENT_AP,
                            'created_by' => $whoIs
                        ]
                    );
                }
            }
            DB::commit();
            return response()->json([
                'error' => false,
                'message' => 'Import file thành công'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            Log::error("--------- start err createListStudentRemoveAp ---------");
            Log::error($th);
            Log::error("--------- end err createListStudentRemoveAp ---------");
            return response([], 500);
        }
    }

    /**
     * delete StudentRemoveAp
     * @return mixed $response
     */
    public function deleteStudentRemoveAp($request) {
        try {
            RemoveListStudent::where('user_code', $request->user_code)->delete();
            DB::commit();
            return response()->json([
                'error' => false,
                'message' => 'Xóa sinh viên ' . $request->user_code . ' ra khỏi danh sách thành công'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            Log::error("--------- start err deleteListStudentRemoveAp ---------");
            Log::error($th);
            Log::error("--------- end err deleteListStudentRemoveAp ---------");
            return response([], 500);
        }
    }

    /**
     * delete ListStudentRemoveAp
     * @return mixed $response
     */
    public function deleteListStudentRemoveAp() {
        try {
            $whoIs = auth()->user()->user_login;
            $listStudentRemoveAp = RemoveListStudent::where('type', self::FILE_TYPE_REMOVE_STUDENT_AP)->get();

            DB::beginTransaction();

            foreach ($listStudentRemoveAp as $studentRemoveAp) {
                $user_login = User::where('user_code', $studentRemoveAp->user_code)->first();
                $user_login = $user_login->user_login;
                $content = "remove member $user_login form ap";

                User::where('user_code',$studentRemoveAp->user_code)->delete();
                GroupMember::where('member_login',$user_login)->delete();
                Attendance::where('user_login',$user_login)->delete();
                CourseGrade::where('login',$user_login)->delete();
                CourseResult::where('student_login',$user_login)->delete();
                RemoveListStudent::where('user_code', $studentRemoveAp->user_code)->delete();
                RemovedApStudent::firstOrCreate(
                    [
                        'user_code' => $studentRemoveAp->user_code
                    ],
                    [
                        'created_by' => $studentRemoveAp->created_by,
                        'confirm_by' => $whoIs
                    ]
                );
            }
            DB::commit();
            return response()->json([
                'error' => false,
                'message' => 'Xóa sinh viên trên ap thành công'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            Log::error("--------- start err deleteListStudentRemoveAp ---------");
            Log::error($th);
            Log::error("--------- end err deleteListStudentRemoveAp ---------");
            return response([], 500);
        }
    }
}
