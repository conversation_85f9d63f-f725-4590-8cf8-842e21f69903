<?php
namespace App\Repositories\Admin;

use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Http\Resources\StudentSmsRole as ArrayRole;
use Illuminate\Http\Request;
use App\Models\Fu\User;
use App\Models\Sms\StudentSmsRole;
use App\Models\Sms\StudentSmsPermission;
Class SmsRoleRepository extends BaseRepository
{
    public function getModel()
    {
        return User::class;
    }
    public function index()
    {
        return $this->view('sms_role.index');
    }
    public function getRoles()
    {   
        try{
            $roles = StudentSmsRole::all();
            return response(['roles'=> $roles], 200);
        }catch(\Exception $e){
            error_log($e);
            Log::error($e);
            throw new \Exception($e);
        }
    }
    public function getUserPermissions(Request $request)
    {
        try{
            if(isset($request->user_login) && $request->user_login != null){
                $user_permissions = User::leftJoin('student_sms_permission', 'user.user_login', 'student_sms_permission.user_login')
                ->select([
                    'user.user_login', 
                    // 'student_sms_permission.role_id',
                    DB::raw("(SELECT 
                        CONCAT('', GROUP_CONCAT(CONCAT(student_sms_role.sms_role_code)), '')
                        FROM student_sms_permission as check_permission
                        LEFT JOIN student_sms_role ON student_sms_role.id = check_permission.role_id
                        WHERE student_sms_permission.user_login = check_permission.user_login
                        GROUP BY check_permission.user_login) AS roles
                    ")
                ])
                ->where('user.user_level','!=',3)
                ->where('user.user_login', $request->user_login)
                ->groupBy('user_login')->orderBy('roles', 'DESC')->paginate(10)->toArray();
            }else{
                $user_permissions = User::leftJoin('student_sms_permission', 'user.user_login', 'student_sms_permission.user_login')
                ->select([
                    'user.user_login', 
                    // 'student_sms_permission.role_id',
                    DB::raw("(SELECT 
                        CONCAT('', GROUP_CONCAT(CONCAT(student_sms_role.sms_role_code)), '')
                        FROM student_sms_permission as check_permission
                        LEFT JOIN student_sms_role ON student_sms_role.id = check_permission.role_id
                        WHERE student_sms_permission.user_login = check_permission.user_login
                        GROUP BY check_permission.user_login) AS roles
                    ")
                ])
                ->where('user.user_level','!=',3)
                ->groupBy('user_login')->orderBy('roles', 'DESC')
                ->paginate(10)->toArray();
            }
            $array_user = $user_permissions['data'];
            $data = [];
            for($i = 0; $i< count($array_user); $i++){
                $data[$array_user[$i]['user_login']] = [
                    'quan-tri' => false,
                    'import' => false,
                    'tao-mau-tin-nhan'=> false,
                    'duyet-tin-nhan-lv1'=> false,
                    'gui-tin-nhan'=> false, 
                    'duyet-tin-nhan-lv2'=> false,
                    'duyet-tin-cham-soc'=> false,
                    'quan-ly-quyen'=> false,
                ];
                $roles = explode(",",$array_user[$i]['roles']);
                for($j = 0; $j< count($roles); $j++) {
                    $data[$array_user[$i]['user_login']][$roles[$j]] = true;
                }
            }
            $user_permissions['data'] = $data;
            return response(['user_permissions'=> $user_permissions],200);
        }catch(\Exception $e){
            error_log($e);
            Log::error($e);
            throw new \Exception($e);
        }
    }
    public function saveChangePermissions(Request $request)
    {
        try{
            $changeData = $request->changePermissions;
            foreach ($changeData as $user_login => $user) {
                foreach ($user as $permission => $value) {
                    $role = StudentSmsRole::where('sms_role_code', $permission)->first();
                    if($role){
                        $query = StudentSmsPermission::where('user_login', $user_login)->where('role_id', $role->id)->get();
                        if(count($query)){
                            if(!$value){
                                StudentSmsPermission::where('user_login', $user_login)->where('role_id', $role->id)->delete();
                            }
                        }else{
                            if($value){
                                StudentSmsPermission::insert([
                                    'user_login' => $user_login,
                                    'role_id' => $role->id
                                ]);
                            }
                        }
                    }
                }
            } 
            return response(['status'=> "Thao tác thành công!"],200);

        }catch(\Exception $e){
            error_log($e);
            Log::error($e);
            throw new \Exception($e);
            return response(['status' => 'error', 'error' => $e], 500);
        }
    }
}

?>