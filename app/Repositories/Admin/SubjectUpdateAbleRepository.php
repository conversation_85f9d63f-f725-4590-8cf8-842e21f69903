<?php


namespace App\Repositories\Admin;

use App\Models\Fu\ActionLog;
use App\Models\Fu\Term;
use App\Models\Fu\Decision;
use App\Models\Fu\Subject;
use App\Models\Fu\SubjectUpdateAble;
use App\Repositories\BaseRepository;
use App\Models\SystemLog;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class SubjectUpdateAbleRepository extends BaseRepository
{
    public function getModel()
    {
        return SubjectUpdateAble::class;
    }
    
    public function index()
    {
        $terms = Term::orderBy('id', 'desc')->get();
        $decisions = Decision::orderBy('id', 'desc')->get();
        
        return view(env('THEME_ADMIN') . '.subject_update_able.index', [
            'terms' => $terms,
            'decision_types' => Decision::TYPES,
            'decisions' => $decisions
        ]);
    }
    
    public function createFrom()
    {
        // if( != "ho"){
        //     return redirect()->route('admin.subject_update_able.index');
        // }

        $terms = Term::orderBy('id', 'desc')->get();
        $subjects = Subject::orderBy('id', 'desc')->get();
        $decisions = Decision::where('type', Decision::TYPE_MIEN_GIAM)->orderBy('id', 'desc')->get();
        return view(env('THEME_ADMIN') . '.subject_update_able.create', [
            'subjects' => $subjects,
            'decisions' => $decisions,
            'decision_types' => Decision::TYPES,
            'terms' => $terms,
        ]);
    }
    
    public function edit($old_subject_code, $request)
    {
        // if( != "ho"){
        //     return redirect()->route('admin.subject_update_able.index');
        // }
        $checkUpdateAble = SubjectUpdateAble::select('old_subject_code', 'decision_id')
        ->where('old_subject_code', $old_subject_code)
        ->where('decision_id', $request->decision_id)
        ->groupBy('old_subject_code', 'decision_id')
        ->get();

        if (count($checkUpdateAble) > 1) {
            return back()->withErrors([
                'Có lỗi'
            ]);
        }

        $subjectUpdateAble = SubjectUpdateAble::where('old_subject_code', $old_subject_code)
        ->where('decision_id', $request->decision_id)
        ->groupBy('old_subject_code')
        ->groupBy('decision_id')
        ->first();

        $subjectChecked = SubjectUpdateAble::where('old_subject_code', $old_subject_code)
        ->where('decision_id', $request->decision_id)
        ->pluck('new_subject_code')
        ->toArray();

        // $subjectUpdateAble = SubjectUpdateAble::find($id);
        $decisions = Decision::orderBy('id', 'desc')->get();
        if (!$subjectUpdateAble) {
            return redirect()->route('admin.subject_update_able.index', [
                'decisions'         => $decisions
            ]);
        }

        $subjects = Subject::orderBy('id', 'desc')->get();
        return view(env('THEME_ADMIN') . '.subject_update_able.edit', [
            'subjectUpdateAble' => $subjectUpdateAble,
            'subjects'          => $subjects,
            'subject_checked'   => $subjectChecked,
            'decisions'         => $decisions
        ]);
    }

    public function store($request)
    {
       
        $validator = Validator::make($request->all(), [
            'old_subject_code'  => 'required',
            'new_subject_code'  => 'required',
            'decision_id'       => 'required_unless:addDecision,checked',
        ], [
            'old_subject_code.required' => 'Bạn chưa chọn môn',
            'new_subject_code.required' => 'Bạn chưa chọn môn thay thế',
            'decision_id.required_unless' => 'Quyết định không được để trống',
        ]);

        if ($validator->fails()) {
            return redirect(url()->previous())->withErrors($validator)->withInput();
        }
       
        DB::beginTransaction();
        $bug = null;
        try {
            $dataLogUpdate = [];
            $listSubjectOld = $request->old_subject_code;
            $listSubjectNew = $request->new_subject_code;

            if (isset($request->addDecision) ||  $request->addDecision == 'checked'){
               $addDecision =  $this->AddDecision($request);
            }
            $dataSubjectOld = Subject::select([
                'id',
                'subject_code',
                'subject_name',
                'skill_code'
            ])->where('id', $listSubjectOld)->get();

            $dataSubjectNew = Subject::select([
                'id',
                'subject_code',
                'subject_name',
                'skill_code'
            ])->where('id', $listSubjectNew)->get();

            foreach($dataSubjectOld as $subjectOld){
                foreach ($dataSubjectNew as $subjectNew) {
                    $dataSubject = SubjectUpdateAble::create([
                        'old_subject_code'  => $subjectOld->subject_code,
                        'new_subject_code'  => $subjectNew->subject_code,
                        'old_skill_code'    => $subjectOld->skill_code,
                        'new_skill_code'    => $subjectNew->skill_code,
                        'old_subject_name'  => $subjectOld->subject_name,
                        'new_subject_name'  => $subjectNew->subject_name,
                        'decision_id'       => !empty($addDecision) ? $addDecision->id : $request->decision_id,
                        'created_by'        => (auth()->user()->user_login ?? null),
                    ]);
    
                     // Nếu dữ liệu cũ khác dữ liệu mới thì thêm
                    $dataLogUpdate['description'] = [
                        'old_val' =>  $subjectOld->subject_code,
                        'new_val' =>  $subjectNew->subject_code
                    ];
    
                    // lấy ra mảng string thay đổi
                    $logUpdate = [];
                    foreach ($dataLogUpdate as $key => $value) {
                        $logUpdate[] = "{ $key: môn (" . $value['old_val'] . ') được thay thế bởi (' . $value['new_val'] . ') }';
                    }
    
                    // String sau khi thay đổi được gộp lại
                    $strUpdate = implode(', ', $logUpdate);
                    $strUpadte = "Cập nhập thông tin $dataSubject->id: " . $strUpdate;
    
                    SystemLog::create([           
                        'object' => 'subject_update_able',
                        'auth' => auth()->user()->user_login,
                        'action' => 'add',
                        'description' => $strUpadte,
                        'object_id' => $dataSubject->id
                    ]);
                }
            }

            DB::commit();
            return $this->redirectWithStatus('success',"Tạo môn thay thế thành công", route('admin.subject_update_able.edit', ['old_subject_code' => $dataSubject->old_subject_code]). '?decision_id='.$dataSubject->decision_id);
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error($exception);
            return $this->redirectWithStatus('danger', 'Dữ liệu không đúng định dạng, huỷ bỏ quá trình import', url()->previous());
        }
    }

    public function update($old_subject_code, $request)
    {
        $validator = Validator::make($request->all(), [
            'old_subject_code'  => 'required',
            'new_subject_code'  => 'required',
            'decision_id'       => 'required',
        ], [
            'old_subject_code.required' => 'Bạn chưa chọn môn',
            'new_subject_code.required' => 'Bạn chưa chọn môn thay thế',
            'decision_id.required' => 'Quyết định không được để trống',
        ]);

        if ($validator->fails()) {
            return redirect(url()->previous())
                ->withErrors($validator)
                ->withInput();
        }
      
        DB::beginTransaction();
        $bug = null;
        try {
            $dataLogUpdate = [];
            $listOldSubjectAble = SubjectUpdateAble::where('old_subject_code', $old_subject_code)
                ->where('decision_id', $request->input('decision_id'))
                ->pluck('new_subject_code');

            $listOldSubjectAble =  $listOldSubjectAble->toArray();
            $listSubjectNew = [$request->new_subject_code];

            $checkRemoveSubject = array_diff($listOldSubjectAble, $listSubjectNew);
            $checkAddOnSubject =  array_diff($listSubjectNew, $listOldSubjectAble);

            if($checkRemoveSubject) {
                $deleteSubjectUpdateAble = SubjectUpdateAble::whereIn('new_subject_code', $checkRemoveSubject)
                ->where('decision_id', $request->input('decision_id'))
                ->where('old_subject_code', $old_subject_code)
                ->get();

                foreach ($deleteSubjectUpdateAble as $key => $subject) {
                    $dataLogUpdate['description'] = [           
                        'old_val' =>  $subject->old_subject_code,   
                        'new_val' =>  $subject->new_subject_code
                    ];
    
                    // lấy ra mảng string thay đổi
                    $logUpdate = [];
                    foreach ($dataLogUpdate as $key => $value) {
                        $logUpdate[] = "{ $key: Đã xóa (" . $value['new_val'] . ') ra khỏi môn thay thế (' . $value['old_val'] . ') }';
                    }
    
                    // String sau khi thay đổi được gộp lại
                    $strUpdate = implode(', ', $logUpdate);
                    $strUpadte = "Cập nhập thông tin $subject->id: " . $strUpdate;
                    
                    // Thêm log ho
                    SystemLog::create([           
                        'object' => 'subject_update_able',
                        'auth' => auth()->user()->user_login,
                        'action' => 'delete',
                        'description' => $strUpadte,
                        'object_id' => $subject->id
                    ]);
                    $subject->delete();
                }
            }

            if ($checkAddOnSubject) {
                $listSubjects = Subject::all();
                // get old subject
                $oldSubject = $listSubjects->where('subject_code', $old_subject_code)->first();
                foreach($checkAddOnSubject as $item) {       
                    //  get new subject
                    $newSubject = $listSubjects->where('subject_code', $item)->first();
                    $inputInsert = [
                        'old_subject_code' => $oldSubject->subject_code,
                        'new_subject_code' => $newSubject->subject_code,
                        'old_skill_code' => $oldSubject->skill_code,
                        'new_skill_code' => $newSubject->skill_code,
                        'old_subject_name' => $oldSubject->subject_name,
                        'new_subject_name' => $newSubject->subject_name,
                        'decision_id' => $request->decision_id_change
                    ];

                    $dataLogUpdate['description'] = [           
                        'old_val' =>  $inputInsert['old_subject_code'],   
                        'new_val' =>  $inputInsert['new_subject_code']
                    ];

                    // lấy ra mảng string thay đổi
                    $logUpdate = [];
                    foreach ($dataLogUpdate as $key => $value) {
                        $logUpdate[] = "{ $key: Đã thêm (" . $value['new_val'] . ') vào môn thay thế (' . $value['old_val'] . ') }';
                    }
                    // String sau khi thay đổi được gộp lại
                    $strUpdate = implode(', ', $logUpdate);
                    $strUpadte = "Cập nhập thông tin: " . $strUpdate;

                    SystemLog::create([
                        'object' => 'subject_update_able',
                        'auth' => auth()->user()->user_login,
                        'action' => 'update',
                        'description' => $strUpadte
                    ]);
                    $subjectUpdateAble = SubjectUpdateAble::insert($inputInsert);    
                }
            }

            if ($request->decision_id_change != $request->decision_id){
                $listOldSubjectAble = SubjectUpdateAble::where('old_subject_code', $old_subject_code)
                ->where('decision_id', $request->input('decision_id'))
                ->first();

                $listOldSubjectAble->update(['decision_id' =>  $request->decision_id_change]);
                $dataLogUpdate['description'] = [           
                    'old_val' =>  $listOldSubjectAble->old_subject_code,   
                ];

                // lấy ra mảng string thay đổi
                $logUpdate = [];
                foreach ($dataLogUpdate as $key => $value) {
                    $logUpdate[] = "{ $key: Đã cập nhật quyết định cho (" . $value['old_val'] . ') }';
                }

                // String sau khi thay đổi được gộp lại
                $strUpdate = implode(', ', $logUpdate);
                $strUpadte = "Cập nhập thông tin $listOldSubjectAble->id: " . $strUpdate;
                SystemLog::create([           
                    'object' => 'subject_update_able',
                    'auth' => auth()->user()->user_login,
                    'action' => 'update',
                    'description' => $strUpadte,
                    'object_id' => $listOldSubjectAble->id
                ]);
            }
            DB::commit();
            return $this->redirectWithStatus('success',"Cập nhập môn thay thế thành công", route('admin.subject_update_able.edit', ['old_subject_code' => $old_subject_code]). '?decision_id='.$request->decision_id);
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::debug($exception);
            return $this->redirectWithStatus('danger', 'Có lỗi xảy ra, Vui lòng thử lại!', url()->previous());
        }
    }

    public function removeSubjectAble($request)
    {
        $formReturn = [
            "status" => 1,
            "code" => 200,
            "message" => "Xóa môn thay thế thành công!",
            "data" => null
        ];

        $SubjectUpdateAble = SubjectUpdateAble::find($request->subject_id);
        // Tìm theo 2 cái trái tránh trường hợp cố tình phang Id khác vào
        if (!$SubjectUpdateAble) {
            $formReturn['code'] = 400;
            $formReturn['message'] = "Không tồn tại môn thay thế";
            return response()->json($formReturn, $formReturn['code']);
        }

        DB::beginTransaction();
        try {
            SystemLog::create([           
                'object' => 'subject_update_able',
                'auth' => auth()->user()->user_login,
                'action' => 'delete',
                'description' => 'Xóa môn học ' . $request->new_subject_code  .' có thể thay thế cho môn học '. $request->old_subject .' - Quyết định '. $request->decision_id,
                'object_id' =>  $request->subject_id
            ]);

            $SubjectUpdateAble->delete();
            DB::commit();
            return response()->json($formReturn, $formReturn['code']);
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::debug($exception);
            $formReturn['code'] = 400;
            $formReturn['message'] = $exception->getMessage();
            // dd($bug, $exception);
            return response()->json($formReturn, $formReturn['code']);
        }
    }

    // public function exportData($request)
    // {
    //     header('Content-Type: text/csv; charset=UTF-8');
    //     header('Content-Disposition: attachment; filename="export_'.time().'.csv";');
        
    //     $header = [
    //         1 => "Mã sinh viên"
    //     ];

    //     $decisionUser = DecisionUser::where('decision_id', $request->id)
    //     ->get();
    //     $f = fopen('php://output', 'w');
    //     fputcsv($f, $header);
    //     foreach ($decisionUser as $key => $value) {
    //         fputcsv($f, [$value->user_code]);
    //     }
    // }

    public function loadSelectSubject($request)
    {
        $list = Subject::query();
      
        $list = $list->get();
        $res[] = [
            'id' => -1,
            'text' => "Tất cả các môn học",
        ];
        foreach ($list as $key => $value) {
            $res[] = [
                'id' => $value->id,
                'subject_name' => $value->subject_name,
                'subject_name_en' => $value->subject_name_en,
                'subject_code' => $value->subject_code,
            ];
        }

        return response()->json($res, 200);
    }

    public function uploadFileDecisionPdf($file, $no, $termName)
    {
        $fileName = $termName . '-' . $no . '-' . now()->format('d_m_y_h_i_s') . '.' . $file->getClientOriginalExtension();
        $this->uploadFile(('decision'), $file, $fileName);
        return $fileName;
    }

    // public function importDataFromFile($file)
    // {
    //     $data = Excel::toArray(new DropOutImport(), $file);
    //     if (count($data[0]) <= 1) {
    //         return false;
    //     }

    //     unset($data[0][0]);
    //     return $data[0];
    // }

    public function AddDecision($request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required',
            'so_quyet_dinh' => 'required|alpha_num',
            'nguoi_ky' => 'required',
            'drop_out_date' => 'required',
            'quyet_dinh_tu' => 'required',
        ], [
            'file.required' => 'Bạn chưa tải file quyết định lên',
            'so_quyet_dinh.required' => 'Số quyết định không được để trống',
            'nguoi_ky.required' => 'Người ký không được để trống',
            'drop_out_date.required' => 'Ngày ký không được để trống',
            'quyet_dinh_tu.required' => 'Quyết định từ không được để trống',
            'so_quyet_dinh.regex' => 'Số quyết định chỉ được nhập số',
        
        ]);

        if ($validator->fails()) {
            return redirect(url()->previous())
                ->withErrors($validator)
                ->withInput();
        }

        DB::beginTransaction();
        $bug = null;
        $termDetail = Term::find($request->term_id);
        $fileDecision = $this->uploadFileDecisionPdf($request->file('file'), $request->so_quyet_dinh, $termDetail->term_name);
        // $typeDecision = Decision::getTypeDecision($status['code']);
        try {
            $dataDecision = Decision::where('term_id', $request->term_id)
            // ->where('type', $request->decision_type)
            ->where('decision_num', $request->so_quyet_dinh)
            ->first();

            if (!$dataDecision) {
                $dataDecision = Decision::create([
                    'name' => $request->file('file')->getClientOriginalName(),
                    'decision_num' => $request->so_quyet_dinh,
                    'term_id' => $request->term_id,
                    'from' => $request->quyet_dinh_tu,
                    'type' => Decision::TYPE_MIEN_GIAM,
                    'signer' => $request->nguoi_ky,
                    'sign_day' => $request->drop_out_date,
                    'effective_time' => $request->date_affected,
                    'file' => $fileDecision,
                    'file_status' => 1,
                    'note' => ($request->note ?? ""),
                    'created_by' => (auth()->user()->user_login ?? null),
                ]);
            } else {
                $dataDecision::where('term_id', $request->term_id)
                ->where('type', $request->decision_type)
                ->where('decision_num', $request->so_quyet_dinh)
                ->update([
                    'from' => $request->quyet_dinh_tu,
                    'signer' => $request->nguoi_ky,
                    'sign_day' => $request->drop_out_date,
                    'effective_time' => $request->date_affected,
                    'note' => ($request->note ?? ""),
                ]);
            }
            DB::commit();
            return $dataDecision;
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::debug($exception);
            return $this->redirectWithStatus('danger', 'Dữ liệu không đúng định dạng, huỷ bỏ quá trình import', url()->previous());
        }
    }

    public function import($request) {
        try {

            $request->validate([
                'decision_id' => ['required'],
                'import_file' => ['required', 'mimes:xlsx']
            ], [
                'required' => "Không được bỏ trống :attribute",
                'mimes' => ":attribute không đúng định dạng"
            ], [
                'decision_id' => "Quyết định",
                'import_file' => "File"
            ]);
            $file = $request->file('import_file');
            $data = $this->importDataFromFile($file);
            if(!$data) {
                return redirect()->back()->with([
                    'status' => [
                        'type' => 'danger', 
                        'messages' => 'Không có dữ liệu để import'
                    ]
                ]);
            }
            $errorCheck = $this->checkDataImport($data, $request->decision_id);

            if (count($errorCheck) > 0) {
                $errorMessageArr = array_map(function($item) {
                    return $item['message'];
                }, $errorCheck); 
                $errorMessage = implode(',<br />', $errorMessageArr);
                return redirect()->back()->with([
                    'status' => [
                        'type' => 'danger', 
                        'messages' => $errorMessage
                    ]
                ]);
            }

            DB::beginTransaction();
            $decision_id = $request->decision_id;
            $inputInsert = [];
            $listSubjects = Subject::all();
            foreach($data as $item) {
                // get old subject
                $oldSubject = $listSubjects->where('subject_code', $item[0])->firstOrFail();
                //  get new subject
                $newSubject = $listSubjects->where('subject_code', $item[1])->firstOrFail();
    
                $inputInsert[] = [
                    'old_subject_code' => $oldSubject->subject_code,
                    'new_subject_code' => $newSubject->subject_code,
                    'old_skill_code' => $oldSubject->skill_code,
                    'new_skill_code' => $newSubject->skill_code,
                    'old_subject_name' => $oldSubject->subject_name,
                    'new_subject_name' => $newSubject->subject_name,
                    'decision_id' => $decision_id,
                ];
            }
    
            ActionLog::create([
                'object'        => 'subject_update_able',
                'auth'          => auth()->user()->user_login,
                'action'        => 'import',
                'description'   => "Tài khoản " . auth()->user()->user_login . " import ma trận môn thay thế",
                'object_id'     => '',
                'data_changed'  => json_encode($inputInsert),
                'ip'            => request()->getClientIp(),
            ]);
    
            SubjectUpdateAble::insert($inputInsert);
            DB::commit();
            return redirect()->back()->with([
                'status' => [
                    'type' => 'success', 
                    'messages' => 'Import thành công'
                ]
            ]);
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return redirect()->back()->with([
                'status' => [
                    'type' => 'danger', 
                    'messages' => 'Import thất bại vui lòng kiểm tra lại dữ liệu hoặc báo với cán bộ IT.'
                ]
            ]);
        }

    }
    
    protected function checkDataImport($data, $decision_id) {
        try {
            $error = [];

            // get subject data
            $listSubjects = Subject::all();

            foreach($data as $key => $item) {
                $old_subject_code = $item[0];
                $new_subject_code = $item[1];
                // get old subject
                $oldSubject = $listSubjects->where('subject_code', $old_subject_code)->first();

                //  get new subject
                $newSubject = $listSubjects->where('subject_code', $new_subject_code)->first();

                if(!$oldSubject) {
                    $index = $key + 1;
                    $error[] = [
                        'row' => $index,
                        'message' => "Dữ liệu dòng {$index}, không tồn tại môn $old_subject_code" 
                    ]; 
                    continue;
                }
                if(!$newSubject) {
                    $index = $key + 1;
                    $error[] = [
                        'row' => $index,
                        'message' => "Dữ liệu dòng {$index}, không tồn tại môn $new_subject_code" 
                    ]; 
                    continue;
                }

                // check tồn tại môn thay thế 
                $checkExist = SubjectUpdateAble::where('old_subject_code', $old_subject_code)
                    ->where('new_subject_code', $new_subject_code)->exists();
                if($checkExist) {
                    $index = $key + 1;
                    $error[] = [
                        'row' => $index,
                        'message' => "Dữ liệu dòng {$index}, đã tồn tại ma trận môn thay thế {$old_subject_code} và {$new_subject_code}" 
                    ]; 
                    continue;
                }
            }

            return $error;
        } catch (\Throwable $th) {
            throw new \Exception($th->getMessage());
        }
        
    }

    protected function handleImportData($data, $decision_id) {

    }

}
