<?php


namespace App\Repositories\Admin;

use App\Http\Lib;
use App\Models\Dra\T1Permission;
use App\Models\Dra\T1RolePermission;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\DB;

class PermissionRepository extends BaseRepository
{
    public function getModel()
    {
        return T1Permission::class;
    }

    public function index()
    {
        $permissions = $this->_model->orderBy('permission_name')->get();
        return $this->view('permission.index', [
            'permissions' => $permissions,
        ]);
    }

    public function store($request)
    {
        if ($request->id) {
            $permission = $this->_model->find($request->id);
            if ($permission) {
                $permission->update($request->all());

                Lib::logDiscord('info', "Edit role success: $permission->id");
                return $this->redirectWithStatus('success', 'Chỉnh sửa thành công', route('admin.permission'));
            }

            return $this->redirectWithStatus('danger', 'Chỉnh sửa thất bại id không tồn tại', route('admin.permission'));
        } else {
            $permission = $this->_model->create($request->all());
            if ($permission) {
                return $this->redirectWithStatus('success','Tạo quyền thành công');
            }

            return $this->redirectWithStatus('danger', 'Tạo quyền thất bại');
        }
    }

    public function edit($id)
    {
        $permission = $this->_model->find($id);

        if ($permission) {
            $permissions = $this->_model->orderBy('id', 'desc')->get();

            return $this->view('permission.edit', [
                'permission' => $permission,
                'permissions' => $permissions,
            ]);
        }

        return $this->redirectWithStatus('warning', 'Không tìm thấy', route('admin.permission'));
    }

    public function delete($request)
    {
        $permission = $this->_model->find($request->id);
        if ($permission) {
            DB::transaction(function () use ($permission) {
                $permission->delete();
                T1RolePermission::where('permission_id', $permission->id)->delete();
            });

            return $this->redirectWithStatus('success', 'Xoá thành công quyền hạn và các bảng liên quan', route('admin.permission'));
        }

        return $this->redirectWithStatus('warning', 'Không tìm thấy', route('admin.permission'));

    }
}
