<?php


namespace App\Repositories\Admin;


use App\Imports\DisciplineImport;
use App\Models\T7\Discipline;
use App\Models\T7\DisciplineStudent;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class DisciplineRepository extends BaseRepository
{
    public function getModel()
    {
        return Discipline::class;
    }

    public function index($request)
    {
        $terms = $this->getTerms();
        $array = [
            '1' => ['value' => '1', 'title' => 'Khen thưởng'],
            '2' => ['value' => '2', 'title' => 'Kỉ luật'],
        ];
        $term_id = $request->term_id;
        $type = $request->type;
        $disciplines = $this->_model->with(['term:id,term_name', 'students:discipline_id'])
            ->when($term_id, function ($query, $term_id) {
                return $query->where('term_id', $term_id);
            })
            ->when($type, function ($query, $type) {
                return $query->where('type', $type);
            })
            ->orderBy('date_created', 'desc')
            ->paginate(15);

        return $this->view('discipline.index', [
            'terms' => $terms,
            'disciplines' => $disciplines,
            'array' => $array,
        ]);
    }

    public function viewDiscipline($id)
    {
        $discipline = Discipline::findOrFail($id);
        $discipline->load('term:id,term_name');
        $discipline->load('students');
        $array = [
            '1' => ['value' => '1', 'title' => 'Khen thưởng'],
            '2' => ['value' => '2', 'title' => 'Kỉ luật'],
        ];

        return $this->view('discipline.view', [
            'discipline' => $discipline,
            'array' => $array,
        ]);
    }

    public function add()
    {
        $terms = $this->getTerms();
        $array = [
            '1' => ['value' => '1', 'title' => 'Khen thưởng'],
            '2' => ['value' => '2', 'title' => 'Kỉ luật'],
        ];

        return $this->view('discipline.create', [
            'terms' => $terms,
            'array' => $array,
        ]);
    }

    public function store($request)
    {
        $users = [];
        $msg = '';
        $array = $request->all();
        if (!$users = $this->importDataFromFile($array['users'])) {
            return $this->redirectWithStatus('danger', 'Không có sinh viên nào để import hoặc file tải lên bị lỗi', url()->previous());
        }
        $no = $array['decision_no'];
        $array['decision_no'] = $array['decision_no'] . '/' . $array['quyet_dinh_tu'];
        if ($this->checkDisciplineHas($array['decision_no'], $array['type'])) {
            return $this->redirectWithStatus('danger', 'Quyết định này đã tồn tại vui lòng kiểm tra lại', url()->previous());
        }
        $array['modifier_login'] = Auth::user()->user_login;
        $array['date_created'] = now();
        $array['file_name'] = $this->uploadFileToDiscipline($request->file('file'), $no);
        $array['note'] = $array['note'] ?? '';
        DB::beginTransaction();
        try {
            $discipline = Discipline::create($array);
            $this->createDisciplineStudents($users, $discipline);
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->redirectWithStatus('danger', 'Dữ liệu không đúng định dạng, huỷ bỏ quá trình import', url()->previous());
        }

        $this->uploadFile('discipline', $request->file('users'), str_replace('.pdf', '.xlsx', $discipline->file_name));

        return $this->redirectWithStatus('success', 'Quá trình import thành công', route('admin.discipline'));
    }

    public function checkDisciplineHas($name, $type)
    {
        return Discipline::where('decision_no', $name)->where('type', $type)->count();
    }

    public function uploadFileToDiscipline($file, $no)
    {
        $fileName = $no . '-' . now()->format('d_m_y_h_i_s') . '.' . $file->getClientOriginalExtension();
        $this->uploadFile('discipline', $file, $fileName);

        return $fileName;
    }

    public function importDataFromFile($file)
    {
        $data = Excel::toArray(new DisciplineImport(), $file);
        if (count($data[0]) <= 1) {
            return false;
        }
        unset($data[0][0]);

        return $data[0];
    }

    public function createDisciplineStudents($users, $discipline)
    {
        foreach ($users as $user) {
            DisciplineStudent::create([
                'discipline_id' => $discipline->id,
                'student_login' => $user[0],
                'reason' => $user[1],
            ]);
        }
    }
}
