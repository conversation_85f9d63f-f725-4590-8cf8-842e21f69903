<?php

namespace App\Repositories\Admin;

use App\Exports\UsersExport;
use App\Exports\UsersExportNew;
use App\Http\Lib;
use App\Http\Middleware\Students\Authenticate as Authenticate2;
use App\Imports\DisciplineImport;
use App\Imports\UsersImport;
use App\Models\Crm\People;
use App\Models\Crm\Rcm;
use App\Models\Crm\Student;
use App\Models\Dra\CurriCulum;
use App\Models\T7\CourseResult;
use App\Models\Fu\User;
use App\Models\Fu\UserDelete;
use App\Models\Fu\UserLevel;

use App\Repositories\BaseRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\SystemLog;
use App\Models\TranferT7Course;
use App\Models\Dra\StudentSubject;
use App\Models\Fu\GradeCreate;
use App\Models\Iaps\RcmLog;
use App\Models\Iaps\RcmPull;
use App\Models\Iaps\RcmPullUser;
use Illuminate\Support\Facades\Log;

use App\Models\Report\FZ\ListStudent;
use App\Models\Report\FZ\Terms;
use App\Models\StudentStatusLog;
use App\Utils\ResponseBuilder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class UserRepository extends BaseRepository
{

    /**
     * get model
     * @return string
     */
    public function getModel()
    {
        return User::class;
    }

    public function index($request)
    {
        $keyword = $request->keyword;
        $study_id = $request->study_status;
        $role_id = $request->role;
        $curriculum_id = $request->curriculum;
        $users = User::where(function ($query) use ($keyword, $study_id, $role_id, $curriculum_id) {
            if ($keyword) {
                $query->where('user_login', 'like', "%$keyword%")
                    ->orWhere(DB::raw('CONCAT(user_surname," ",user_middlename," ", user_givenname)'), 'like', "%$keyword%")
                    ->orWhere('user_code', 'like', "%$keyword%");
            }
        });
        if ($curriculum_id) {
            $users->where('curriculum_id', $curriculum_id);
        }
        if ($role_id) {
            $users->where('user_level', $role_id);
        }
        if ($study_id) {
            $users->where('study_status', $study_id);
        }

        $users = $users->orderBy('id', 'desc')->paginate(20);
        foreach ($users as $user) {
            if ($user->user_DOB != '0000-00-00') {
                $user->user_DOB = Carbon::createFromDate($user->user_DOB)->format('d-m-Y');
            } else {
                $user->user_DOB = '';
            }
        }
        return view('admin_v1.user.manager', [
            'users' => $users,
            'roles' => $this->getAllRole(),
            'study_status' => $this->getAllStudyStatus(),
            'study_status_json' => json_encode($this->getAllStudyStatus()),
            'curriculum' => CurriCulum::select('id', 'name')->orderBy('id', 'desc')->get(),
        ]);
    }

    /**
     * <AUTHOR>
     * @since 15/11/2023
     *
     * @param Illuminate\Http\Request $request
     *
     * @return Blade;
     */
    public function create($request)
    {
        if (auth()->user()->user_level != 1) {
            return redirect('/');
        }
        $khoa_nhap_hoc = GradeCreate::orderBy('term_id', 'desc')->orderBy('id', 'desc')->get();
        $roles = UserLevel::where('number', '!=', 1)->get();
        return view('admin_v1.user.create', [
            'roles' => $roles,
            'khoa_nhap_hoc' => $khoa_nhap_hoc,
        ]);

        return redirect()->route('admin.user')->with(['status' => ['type' => 'warning', 'messages' => 'Không tìm thấy người dùng này']]);
    }

    public function createUser($request)
    {
        $rules = [
            'user_code'      => ['required', 'regex:/^[a-zA-Z0-9_]+$/', 'unique:user,user_code'],
            'user_email'      => 'required|email|unique:user,user_email',
            'user_surname'    => 'required',
            'user_middlename' => 'required',
            'user_givenname'  => 'required',
            'user_DOB'        => 'required|date|before_or_equal:today',
            'user_address'    => 'required',
            'user_telephone'  => 'required',
            'gender'     => 'required|in:0,1',
            'user_level'      => 'required|in:1,2,3,4',
            'grade_create'    => 'required',
        ];
        $messages = [
            'user_code.required'      => 'Mã sinh viên không được bỏ trống',
            'user_code.unique'        => 'Mã sinh viên đã tồn tại',
            'user_code.regex'         => 'Mã sinh viên chỉ được chứa chữ không dấu, số và dấu gạch dưới',
            'user_email.required'      => 'Email không được bỏ trống',
            'user_email.unique'        => 'Email đã tồn tại',
            'user_email.email'         => 'Email không đúng định dạng',
            'user_surname.required'    => 'Họ không được bỏ trống',
            'user_middlename.required' => 'Tên đệm không được bỏ trống',
            'user_givenname.required'  => 'Tên không được bỏ trống',
            'user_DOB.required'        => 'Ngày sinh không được bỏ trống',
            'user_DOB.date'            => 'Ngày sinh không hợp lệ',
            'user_DOB.before_or_equal' => 'Ngày sinh không được lớn hơn ngày hiện tại',
            'user_address.required'    => 'Địa chỉ không được bỏ trống',
            'user_telephone.required'  => 'Số điện thoại không được bỏ trống',
            'user_level.required'      => 'Quyền truy cập không được bỏ trống',
            'user_level.in'            => 'Quyền truy cập không hợp lệ',
            'grade_create.required'    => 'Khóa nhập học không được bỏ trống',
        ];

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput($request->all());
        }

        $dataInsert = $request->only([
            "user_code",
            "old_user_code",
            "user_email",
            "user_surname",
            "user_middlename",
            "user_givenname",
            "user_DOB",
            "user_address",
            "gender",
            "user_telephone",
            "user_level",
            "grade_create",
            "dantoc",
            "cmt",
            "noicap"
        ]);

        $dataInsert['user_login'] = $dataInsert['user_code'];
        $dataInsert['study_status'] = 0;
        $dataInsert['study_status_code'] = '';
        $dataInsert['user_pass'] = Hash::make('123@123');
        $dataInsert['created_at'] = Carbon::now();
        $dataInsert['updated_at'] = Carbon::now();

        DB::beginTransaction();
        try {
            $newUser = User::create($dataInsert);
            DB::commit();
            return redirect()->route('admin.user.edit', ['id' => $newUser->id])->with(['status' => ['type' => 'success', 'messages' => 'Tạo mới thành công!']]);
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return back()->withErrors([$th->getMessage()])->withInput($request->all());
        }
    }

    public function edit($id)
    {
        $owner_account = false;
        if (auth()->user()->id == $id) {
            $owner_account = true;
        }
        $user = User::find($id);
        if ($user) {
            $roles = $this->getAllRole();

            return view('admin_v1.user.edit', [
                'user' => $user,
                'roles' => $roles,
                'owner_account' => $owner_account,
            ]);
        }

        return redirect()->route('admin.user')->with(['status' => ['type' => 'warning', 'messages' => 'Không tìm thấy người dùng này']]);
    }

    public function store($request)
    {
        try {
            $student_code = $request->user_code;
            $listFieldUpdate = [
                'user_surname' => $request->user_surname,
                'user_middlename' => $request->user_middlename,
                'user_givenname' => $request->user_givenname,
                'user_DOB' => $request->user_DOB,
                'user_address' => $request->user_address,
                'gender' => $request->gender,
                'user_telephone' => $request->user_telephone,
                'grade_create' => $request->grade_create,
                'cmt' => $request->cmt,
                'noicap' => $request->noicap,
                'dantoc' => $request->dantoc,
            ];

            $update = User::where('user_code', $student_code)->update($listFieldUpdate);
            if (!$update) {
                return back()->withErrors(['error' => 'Lưu thất bại!']);
            }

            $listUpdate = [];
            foreach ($listFieldUpdate as $key => $value) {
                $listUpdate[] = "{$key}: {$value}";
            }
            $strUpdate = implode(', ', $listUpdate);
            $strUpadte = "Cập nhập thông tin $student_code: " . $strUpdate;

            SystemLog::create([
                'actor' => auth()->user()->user_login,
                'object_name' => "user",
                'log_time' => now(),
                'action' => "update info",
                'description' => $strUpadte,
                'relation_login' => $student_code,
                'relation_id' => 0,
                'brief' => "update info",
                'from_ip' => request()->ip(),
                'nganh_cu' => '',
                'nganh_moi' => '',
                'ky_chuyen_den' => 0,
                'ky_thu_cu' => 0,
            ]);

            return redirect(url()->previous())->with(['status' => ['type' => 'success', 'messages' => 'Lưu thành công!']]);
        } catch (\Throwable $th) {
            Log::error($th);
            return redirect(url()->previous())->with(['status' => ['type' => 'warning', 'messages' => 'Lưu thất bại! Đã xảy ra lỗi.']]);
        }
    }

    public function lockUser($id)
    {
        $user = User::find($id);
        if ($user) {
            if ($user->learn_start_day == '0000-00-00') {
                $user->learn_start_day = '1990-01-01';
            }

            $userData = $user->toArray();

            DB::transaction(function () use ($userData, $id) {
                // Kiểm tra xem user đã tồn tại trong user_delete chưa
                $existingDeletedUser = UserDelete::where('user_login', $userData['user_login'])->first();

                if ($existingDeletedUser) {
                    // Nếu đã tồn tại, cập nhật thông tin mới nhất
                    $existingDeletedUser->update($userData);
                } else {
                    // Nếu chưa tồn tại, tạo mới
                    UserDelete::create($userData);
                }

                // Xóa user khỏi bảng users
                User::find($id)->delete();
            });

            return redirect()->route('admin.user')->with(['status' => ['type' => 'success', 'messages' => 'Khoá tài khoản thành công']]);
        }

        return redirect()->route('admin.user')->with(['status' => ['type' => 'warning', 'messages' => 'Khoá tài khoản thất bại']]);
    }

    /**
     * Unlock user - khôi phục user từ bảng user_delete
     *
     * @param string $userLogin
     * @return \Illuminate\Http\RedirectResponse
     */
    public function unlockUser($userLogin)
    {
        $deletedUser = UserDelete::where('user_login', $userLogin)->first();

        if (!$deletedUser) {
            return redirect()->route('admin.user')->with(['status' => ['type' => 'warning', 'messages' => 'Không tìm thấy tài khoản đã bị khóa']]);
        }

        DB::transaction(function () use ($deletedUser) {
            // Kiểm tra xem user đã tồn tại trong bảng users chưa
            $existingUser = User::where('user_login', $deletedUser->user_login)->first();

            if ($existingUser) {
                // Nếu đã tồn tại, cập nhật thông tin từ user_delete
                $userData = $deletedUser->toArray();
                unset($userData['id']); // Loại bỏ id để tránh conflict
                $existingUser->update($userData);
            } else {
                // Nếu chưa tồn tại, tạo mới từ dữ liệu user_delete
                $userData = $deletedUser->toArray();
                unset($userData['id']); // Loại bỏ id để tránh conflict
                User::create($userData);
            }

            // Xóa khỏi bảng user_delete
            $deletedUser->delete();
        });

        return redirect()->route('admin.user')->with(['status' => ['type' => 'success', 'messages' => 'Mở khóa tài khoản thành công']]);
    }

    public function export($request)
    {
        return Excel::download(new UsersExport($request), 'users.xlsx');
    }

    public function newExport($request)
    {
        ini_set("memory_limit", "-1");
        ini_set('max_execution_time', -1);
        return Excel::download(new UsersExportNew($request), 'users.xlsx');
    }

    public function countStatus()
    {
        $status = config('status')->trang_thai_hoc;
        $users = User::select(DB::raw('count(*) as value, study_status'))
            ->where('study_status', '!=', 0)
            ->where('user_level', 3)
            ->groupBy('study_status')
            ->get();
        $total = 0;
        foreach ($users as $user) {
            $user->label = $status[$user->study_status]['uid'];
            $user->title = $status[$user->study_status]['value'];
            $total += $user->value;
        }

        return $users;
    }

    public function profile($request)
    {
        $transfers = [];
        $action = $request->action;
        $user = User::where('user_code', $request->user_code)->first();
        if ($user) {
            $user->admission_date = $user->admission_date ? Carbon::createFromDate($user->admission_date)->format('d/m/Y') : '';
            $user->curriculum = CurriCulum::where('id', $user->curriculum_id)->first();
        }
        $checkTransferCampus = TranferT7Course::where('student_login', $user->user_login)->count();
        $student_status_logs = StudentStatusLog::leftJoin('term', 'student_status_logs.term_id', 'term.id')
            ->where('user_login', 'like', $request->user_code)
            ->orderBy('student_status_logs.id', 'DESC')->get([
                'student_status_logs.*',
                'term.term_name',
            ]);
        if ($action == 'chuyen_lich_su_11_ky') {
            $total_transfer = 0;
            $user_code_transfer = $request->user_code_transfer;
            $agree_transfer = $request->agree_transfer;
            $so_quyet_dinh = $request->so_quyet_dinh;
            $user_search = User::where('user_code', $user_code_transfer)->where('user_level', 3)->first();
            if (!$user_search) {
                return $this->redirectWithStatus('warning', 'Mã sinh viên không tồn tại');
            }

            $transfers = CourseResult::where('student_login', $user->user_login)->where('val', 1)->get();
            $transferOtherBases = TranferT7Course::where('student_login', $user->user_login)
                ->where('val', 1)
                ->get();
            if ($agree_transfer) {
                foreach ($transfers as $transfer) {
                    $check = CourseResult::where('student_login', $user_search->user_login)->where('groupid', $transfer->groupid)->count();
                    if (!$check) {
                        $transfer->student_login = $user_search->user_login;
                        $transfer->lastupdated = now();
                        $transfer->resit_date = now();
                        $transfer->final_date = now();
                        CourseResult::create($transfer->toArray());
                        $total_transfer++;
                    }
                }

                foreach ($transferOtherBases as $value) {
                    $checkTransferBase = TranferT7Course::query()
                        ->where('student_login', $user_search->user_login)
                        ->where('psubject_code', $value->psubject_code)
                        ->count();

                    if (!$checkTransferBase) {
                        $arrSave = $value->toArray();
                        $arrSave['student_login'] = $user_search->user_login;
                        if (isset($arrSave['id'])) {
                            unset($arrSave['id']);
                        }

                        TranferT7Course::create($arrSave);
                        $total_transfer++;
                    }
                }

                if ($total_transfer > 0) {
                    $this->systemLog('user', 'transfer', 'Chuyển lịch sử hoàn tất tới mã sinh viên ' . $user_search->user_code . ' ' . ($total_transfer) . ' môn, số quyết định ' . $so_quyet_dinh, $user->user_login, 0, 0, '', '', 'transfer_history');
                }

                return $this->redirectWithStatus('success', 'Chuyển lịch sử hoàn tất tới mã sinh viên ' . $user_search->user_code . ' ' . ($total_transfer) . ' môn', route('admin.profile', [
                    'user_code' => $user->user_code,
                ]));
            }

            return $this->view('profile', [
                'user' => $user,
                'checkTransfer' => ($checkTransferCampus > 0 ? true : false),
                'transfers' => $transfers,
                'transferOtherBases' => $transferOtherBases,
            ]);
        }

        return $this->view('profile', [
            'user' => $user,
            'checkTransfer' => ($checkTransferCampus > 0 ? true : false),
            'transfers' => $transfers,
            'transferOtherBases' => ($transferOtherBases ?? []),
            'student_status_logs' => $student_status_logs ?? [],
        ]);
    }

    //Create by Văn Trí
    public function print_form()
    {
        return $this->view('user.print_mass_form');
    }
    public function implodes()
    {
        $phpversion = floatval(mb_substr(phpversion(), 0, 3));
        if ($phpversion >= 7.4) {
        } else {
        }
        return false;
    }
    public function print_result($request)
    {
        $data = $request->all();
        $list_array_mssv = explode(',', $data['mssv']);
        $result = User::whereIn('user_code', $list_array_mssv)->get();
        return $this->view('user.print_mass_result', [
            'cards' => $result,
            'expire' => $data['date']
        ]);
    }
    //End Văn Trí

    // hiển thị trạng thái sinh viên các kỳ trước đó
    public function getListStatusStudent3Ben($request)
    {
        try {
            $list_status_student_3ben_before = ListStudent::where([['student_code', $request->student_code]])
                ->join('fz_terms', 'fz_list_student.term_id', 'fz_terms.id')
                ->join('curriculum', 'curriculum.id', 'fz_list_student.curriculum_id')
                ->orderBy('fz_terms.from_date', 'DESC')
                ->get(['fz_list_student.id', 'status', 'term_id', 'fz_terms.name']);

            return response()->json([
                'list_status_student_3ben_before' => $list_status_student_3ben_before,
            ]);
        } catch (\Throwable $th) {
            Log::error("-----------------------start err getListStatusStudent3Ben-------------------");
            Log::error($th->getFile() . " - " . $th->getLine() . " : " . $th->getMessage());
            Log::error("-----------------------end err getListStatusStudent3Ben-------------------");
            return response("", 500);
        }
    }

    /**
     * <AUTHOR>
     *
     */

    public function storePullUser($request)
    {
        ini_set('max_execution_time', -1);
        $rules = [
            'file' => 'required|mimes:csv,xlsx,xls',
            'khoa' => 'required',
            'rcm_id_new' => "required|exists:crm_ph.rcm,id",
        ];

        $messages = [
            'file.required' => 'File thêm sinh viên không được bỏ trống',
            'file.mimes' => 'File Không đúng định dạng, vui lòng kiểu tra lại',
            'khoa.required' => 'Khóa nhập học không được bỏ trống',
            'rcm_id_new.required' => 'Vui lòng chọn lại chiến dịch',
            'rcm_id_new.exists' => 'chiến dịch không tồn tại',
        ];

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            // dd(__LINE__);
            return back()->with([
                'reports' => [[
                    'msg' => $validator->errors()->first(),
                    'status' => false,
                ]]
            ]);
        }

        $rcmId = $request->get('rcm_id_new', null);
        $file = $request->file('file'); // kiểm tra file
        if (!$datas = $this->importDataNewFromFile($file, true)) {
            // dd(__LINE__);
            return back()->with([
                'reports' => [[
                    'msg' => 'File không có dữ liệu hoặc file tải lên bị lỗi.',
                    'status' => false,
                ]]
            ]);
        }

        // kiểm tra đợt kéo đã tồn tại chưa?
        $rcmPull = RcmPull::where('rcm_id', $rcmId)->first();
        if ($rcmPull) {
            // dd(__LINE__);
            return back()->with([
                'reports' => [[
                    'msg' => 'Đã tồn tại chiến dịch kéo sinh viên về, vui lòng kiểm tra lại',
                    'status' => false,
                ]]
            ]);
        }

        $rcm = Rcm::select([
            'rcm.id',
            'rcm.name',
        ])
            ->where('rcm.id', $rcmId)
            ->first();
        if (!$rcm) {
            $reports[] = [
                'msg' => 'Không tồn tại chiến dịch',
                'status' => false,
            ];
        }


        DB::beginTransaction();
        try {
            // Tạo chiến dịch
            $rcmPull = RcmPull::create([
                'rcm_id' => $rcmId,
                'rcm_name' => $rcm->name,
                'khoa' => $request->khoa,
                'created_by' => auth()->user()->user_login,
                'status' => 1,
                'note' => $request->get('khoa', null),
            ]);

            $listUserCode = $listUserCode2 = array_column($datas, 0);
            $students = DB::table('people')
                ->select([
                    DB::raw("people.*"),
                    DB::raw("people.id as p_id")
                ])
                ->join('student', 'people.id', '=', 'student.person_id')
                // ->where('student.status_fee', 1)
                ->where('student.rec_campaign_id', $rcmId)
                ->whereRaw('length(people.pstudent_code) <> 0')
                // ->where('people.pstatus', '!=', -1)
                // ->where('people.is_initialized', 0)
                ->whereIn('people.pstudent_code', $listUserCode)
                ->orderBy('people.pgen_code_date')
                ->get();

            $listUserCodeFromCrm = $students->pluck('pstudent_code')->toArray();
            $listUserCantAdd = array_diff($listUserCode, $listUserCodeFromCrm);
            // if (!count($listUserCantAdd))
            foreach ($students as $key => $student) {
                $temp_name = explode(' ', $student->last_name);
                if (count($temp_name) > 0) {
                    $first_name = trim($temp_name[0]);
                    unset($temp_name[0]);
                    $middle_name = trim(implode(' ', $temp_name));
                } else {
                    $first_name = trim($temp_name[0]);
                    $middle_name = '';
                }

                $last_name = trim($student->first_name);
                $full_name = $first_name . " " . $middle_name . " " . $last_name;
                RcmPullUser::create([
                    'rcm_pull_id' => $rcmPull->id,
                    'user_code' => $student->pstudent_code,
                    'full_name' => $full_name,
                    'people_id' => $student->id,
                ]);
            }

            $reports = [[
                'msg' => 'Import thành công ' . count($listUserCodeFromCrm) . ' sinh viên',
                'status' => true,
            ]];
            if (count($listUserCantAdd) > 0) {
                $reports[] = [
                    'msg' => 'Vui lòng kiểm tra lại, Không thêm được sinh viên: ' . implode(', ', $listUserCantAdd),
                    'status' => false,
                ];
            }

            // dd(__LINE__);
            DB::commit();
            return back()->with([
                'reports' => $reports
            ]);
        } catch (\Exception $th) {
            Log::error("[dev] kiểm tra lại: ");
            Log::error($th);
            // dd(__LINE__);
            return back()->with([
                'reports' => [[
                    'msg' => 'Đã có lỗi xảy ra, vui lòng liên hệ với cán bộ IT',
                    'status' => false,
                ]]
            ]);
            DB::rollBack();
        }
    }


    public function loginToUser($id)
    {
        $user = User::find($id);

        Auth::loginUsingId($user->id);
        Lib::loadPermission(Auth::user());
        Lib::loadSession();

        return redirect()->route('admin.home');
    }

    public function updatePullUser($request)
    {
        ini_set('max_execution_time', -1);
        $reposts = [];
        $rules = [
            'file' => 'required|mimes:csv,xlsx,xls',
            'khoa' => 'required',
            'rcm_id_new' => "required|exists:crm_ph.rcm,id",
        ];

        $messages = [
            'file.required' => 'File thêm sinh viên không được bỏ trống',
            'file.mimes' => 'File Không đúng định dạng, vui lòng kiểu tra lại',
            'khoa.required' => 'Khóa nhập học không được bỏ trống',
            'rcm_id_new.required' => 'Vui lòng chọn lại chiến dịch',
            'rcm_id_new.exists' => 'chiến dịch không tồn tại',
        ];

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            // dd(__LINE__);
            return back()->with([
                'reports' => [[
                    'msg' => $validator->errors()->first(),
                    'status' => false,
                ]]
            ]);
        }

        DB::getPdo()->setAttribute(\PDO::ATTR_EMULATE_PREPARES, true);
        $rcmId = $request->get('rcm_id_new', null);
        $file = $request->file('file'); // kiểm tra file
        if (!$datas = $this->importDataNewFromFile($file, true)) {
            // dd(__LINE__);
            return back()->with([
                'reports' => [[
                    'msg' => 'File không có dữ liệu hoặc file tải lên bị lỗi.',
                    'status' => false,
                ]]
            ]);
        }

        $rcmPull = RcmPull::where('rcm_id', $rcmId)->first();
        $listUserCodePulled = RcmPullUser::select([
            'rcm_pull_user.user_code',
        ])
            ->where('rcm_pull_user.rcm_pull_id', $rcmPull->id)
            ->pluck('user_code')->toArray();

        DB::beginTransaction();
        try {
            //Update chiến dịch
            // Loại bỏ những sinh viên đã được thêm trước đó
            $litsUser = RcmPullUser::select('user_code', 'status')
                ->where('rcm_pull_id', $rcmPull->id)
                ->get();
            $listUserCodePulled = $litsUser->pluck('user_code')->toArray();

            $listUserCode = array_unique(array_column($datas, 0));
            $listUserCode = array_values(array_unique(array_diff($listUserCode, $listUserCodePulled)));
            $students = DB::table('people')
                ->select([
                    DB::raw("people.*"),
                    DB::raw("people.id as p_id")
                ])
                ->join('student', 'people.id', '=', 'student.person_id')
                // ->where('student.status_fee', 1)
                ->where('student.rec_campaign_id', $rcmId)
                ->whereRaw('length(people.pstudent_code) <> 0')
                // ->where('people.pstatus', '!=', -1)
                // ->where('people.is_initialized', 0)
                ->whereIn('people.pstudent_code', $listUserCode)
                ->whereNotIn('people.pstudent_code', $listUserCodePulled)
                ->orderBy('people.pgen_code_date')
                ->get();


            $listUserCodeFromCrm = $students->pluck('pstudent_code')->toArray();
            $listUserCantAdd = array_diff($listUserCode, $listUserCodeFromCrm);
            // if (!count($listUserCantAdd))
            foreach ($students as $key => $student) {
                $temp_name = explode(' ', $student->last_name);
                if (count($temp_name) > 0) {
                    $first_name = trim($temp_name[0]);
                    unset($temp_name[0]);
                    $middle_name = trim(implode(' ', $temp_name));
                } else {
                    $first_name = trim($temp_name[0]);
                    $middle_name = '';
                }

                $last_name = trim($student->first_name);
                $full_name = $first_name . " " . $middle_name . " " . $last_name;
                RcmPullUser::create([
                    'rcm_pull_id' => $rcmPull->id,
                    'user_code' => $student->pstudent_code,
                    'full_name' => $full_name,
                    'people_id' => $student->id,
                ]);
            }

            $reports = [[
                'msg' => 'Import thành công ' . count($listUserCodeFromCrm) . ' sinh viên',
                'status' => true,
            ]];

            if (count($listUserCantAdd) > 0) {
                $reports[] = [
                    'msg' => 'Vui lòng kiểm tra lại, Không thêm được sinh viên: ' . implode(', ', $listUserCantAdd),
                    'status' => false,
                ];
            }

            // dd(__LINE__);
            DB::commit();
            return back()->with([
                'reports' => $reports
            ]);
        } catch (\Exception $th) {
            Log::error("[dev] kiểm tra lại: ");
            Log::error($th);
            // dd(__LINE__);
            return back()->with([
                'reports' => [[
                    'msg' => 'Đã có lỗi xảy ra, vui lòng liên hệ với cán bộ IT',
                    'status' => false,
                ]]
            ]);
            DB::rollBack();
        }
    }


    /**
     * Thêm sinh viên vào các chiến dịch dựa vào danh sách
     *
     */

    public function updatePullUserNoId($request)
    {
        ini_set('max_execution_time', -1);
        $reposts = [];
        $rules = [
            'file' => 'required|mimes:csv,xlsx,xls',
        ];

        $messages = [
            'file.required' => 'File thêm sinh viên không được bỏ trống',
            'file.mimes' => 'File Không đúng định dạng, vui lòng kiểu tra lại',
        ];

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            // dd(__LINE__);
            return back()->with([
                'reports' => [[
                    'msg' => $validator->errors()->first(),
                    'status' => false,
                ]]
            ]);
        }

        // DB::getPdo()->setAttribute(\PDO::ATTR_EMULATE_PREPARES, true);
        $file = $request->file('file'); // kiểm tra file
        if (!$datas = $this->importDataNewFromFile($file, true)) {
            // dd(__LINE__);
            return back()->with([
                'reports' => [[
                    'msg' => 'File không có dữ liệu hoặc file tải lên bị lỗi.',
                    'status' => false,
                ]]
            ]);
        }

        $listUserCode = $listUserCode2 = array_column($datas, 0);
        $students = DB::table('people')
            ->select([
                DB::raw("people.*"),
                DB::raw("people.id as p_id"),
                DB::raw("rcm.Name as rcm_name"),
            ])
            ->join('student', 'people.id', '=', 'student.person_id')
            ->leftJoin('rcm', 'people.precruitment_id', '=', 'rcm.id')
            ->whereIn('people.pstudent_code', $listUserCode)
            ->orderBy('people.pgen_code_date')
            ->get();
    }


    public function importDataNewFromFile($file, $fullRow = false)
    {
        $data = Excel::toArray(new DisciplineImport(), $file);
        if (count($data[0]) <= 1) {
            return false;
        }

        if ($fullRow == false) {
            unset($data[0][0]);
        }

        return $data[0];
    }

    /**
     * lấy token đăng nhập
     *
     */
    public function getTokenLogin($request)
    {
        $userNeedLogin = User::where('user_level', 3)
            ->where(function ($q) use ($request) {
                return $q->where('user_code', $request->user_code)
                    ->orWhere('user_code', $request->user_login);
            })
            ->first();
        if (!$userNeedLogin) {
            return ResponseBuilder::Fail("Không tìm thấy sinh viên");
        }

        $now = Carbon::now();
        $arr = [
            'user_login' => $userNeedLogin->user_login,
            'user_login_as' => auth()->user()->user_login,
            'time' => (string)$now,
        ];

        $token = Authenticate2::hash(json_encode($arr), 'e');
        return ResponseBuilder::Success($token);
    }

    /**
     * Import bulk user accounts from excel file
     *
     * @param Request $request
     * @return mixed
     */
    public function importUsers(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv'
        ], [
            'file.required' => 'Bạn cần chọn file để import',
            'file.mimes' => 'File không đúng định dạng (xlsx, xls, csv)'
        ]);

        DB::beginTransaction();
        try {
            $file = $request->file('file');
            $import = new UsersImport();
            Excel::import($import, $file);

            $errors = [];

            // Check standard validation failures
            if (count($import->failures()) > 0) {
                foreach ($import->failures() as $failure) {
                    $errors[] = 'Dòng ' . $failure->row() . ': ' . implode(', ', $failure->errors());
                }
            }

            // Check our custom errors (duplicates, etc)
            if (method_exists($import, 'getErrors') && count($import->getErrors()) > 0) {
                foreach ($import->getErrors() as $error) {
                    $errors[] = 'Dòng ' . ($error['row'] + 1) . ': ' . $error['message'];
                }
            }

            // If we have any errors, return with them
            if (count($errors) > 0) {
                DB::rollBack();
                $resp = ['status' => ['type' => 'danger', 'messages' => implode('<br>', $errors)]];
                if ($request->ajax()) {
                    return response()->json($resp, 400);
                }
                return back()->with($resp);
            }

            DB::commit();
            $resp = ['status' => ['type' => 'success', 'messages' => 'Import tài khoản thành công']];
            if ($request->ajax()) {
                return response()->json($resp, 200);
            }
            return back()->with($resp);
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::error($th);
            $resp = ['status' => ['type' => 'danger', 'messages' => 'Đã xảy ra lỗi: ' . $th->getMessage()]];
            if ($request->ajax()) {
                return response()->json($resp, 500);
            }
            return back()->with($resp);
        }
    }

    public function resetPassword(Request $request)
    {
        try {
            $user = User::where('id', $request->userId)->where('user_code', $request->userCode)->firstOrFail();
            $passwordReset = '123@123';
            $user->user_pass = Hash::make($passwordReset);
            $user->save();

            return ResponseBuilder::Success([], "Reset mật khẩu thành công!");
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::Fail("Đã xảy ra lỗi! Vui lòng liên hệ với cán bộ IT.");
        }
    }

    public function changePassword(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'userId' => 'required|exists:user,id',
                'password' => 'required|string|min:6',
                'newPassword' => 'required|string|min:6',
                'confirmPassword' => 'required|string|min:6|same:newPassword',
            ], [
                'userId.required' => 'Không tìm thấy người dùng!',
                'userId.exists' => 'Không tìm thấy người dùng!',
                'password.required' => 'Mật khẩu không được bỏ trống!',
                'password.min' => 'Mật khẩu phải có ít nhất 6 ký tự!',
                'newPassword.required' => 'Mật khẩu mới không được bỏ trống!',
                'newPassword.min' => 'Mật khẩu mới phải có ít nhất 6 ký tự!',
                'confirmPassword.required' => 'Xác nhận mật khẩu không được bỏ trống!',
                'confirmPassword.min' => 'Xác nhận mật khẩu phải có ít nhất 6 ký tự!',
                'confirmPassword.same' => 'Mật khẩu và xác nhận mật khẩu không khớp!',
            ]);

            if ($validator->fails()) {
                return ResponseBuilder::Fail($validator->errors()->first());
            }

            $user = User::find($request->userId);
            if (!Hash::check($request->password, $user->user_pass)) {
                return ResponseBuilder::Fail("Mật khẩu cũ không chính xác!");
            }
            $user->user_pass = Hash::make($request->newPassword);
            $user->save();

            return ResponseBuilder::Success([], "Đổi mật khẩu thành công!");
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::Fail("Đã xảy ra lỗi!");
        }
    }
}
