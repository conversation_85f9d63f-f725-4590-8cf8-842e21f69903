<?php


namespace App\Repositories\Admin;

use Error;
use stdClass;
use Exception;
use Carbon\Carbon;
use App\Models\Fu\Slot;
use App\Models\Fu\Term;
use App\Models\Fu\User;
use App\Models\Fu\Block;
use App\Models\Fu\Group;
use App\Models\Fu\Course;
use App\Models\Fu\Subject;
use App\Models\Fu\Activity;
use Illuminate\Http\Request;
use App\Models\Fu\Department;
use App\Models\GroupGraduate;
use App\Models\Fu\GroupMember;
use App\Models\T7\SyllabusPlan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Repositories\BaseRepository;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\XepGiamThiMutipleExport;
use App\Exports\GraduateStudentListExport;
use Box\Spout\Reader\Common\Creator\ReaderEntityFactory;

class GraduateRepository extends BaseRepository
{
    public function getModel()
    {
        return GroupGraduate::class;
    }

    public function exportDanhSachSinhVien(Request $request)
    {
        $blocks = [];
        $ds_lop = [];
        $ds_mon = [];
        $ds_bo_mon = Department::select('id', 'department_name', 'dean')->get();
        $user_login = auth()->user()->user_login;

        //lấy tất cả kỳ học
        // $terms = Term::where('is_locked', 0)->orderBy('id', 'desc')->get();
        $terms = Term::orderBy('id', 'desc')->get();

        $term_id = $request->term_id;
        if ($term_id != null && $terms->contains('id', $term_id)) {
            $blocks = Block::where('term_id', $term_id)->get();
            $block_id = $request->block_id;
            $block_search = null;
            if ($block_id) {
                $block_search = $blocks->where('id', $block_id)->first();
            }

            $department_id = $request->department_id;
            if ($department_id != null && $department_id > 0) {
                $subject = Subject::where('department_id', $department_id)->get();

                $ds_mon = Course::when($department_id, function ($query) use ($subject) {
                    $query->whereIn('subject_id', $subject->pluck('id'));
                })->orderBy('psubject_code')->where('term_id', $term_id)->get();

                $course_id = $request->course_id;
                if ($course_id != null && $course_id > 0) {
                    $course_search = $ds_mon->where('id', $course_id)->pluck('id');

                    $ds_lop = Group::whereIn('body_id', $course_search)->where('list_group.is_virtual', 0)
                        ->where('pterm_id', $term_id)
                        ->when($block_search, function ($query) use ($block_search) {
                            $query->where('end_date', '>=', $block_search->start_day)->where('end_date', '<=', $block_search->end_day);
                        })
                        ->where('type', 1)
                        ->where('is_virtual', 0)
                        ->where(function ($q) {
                            $q->where('list_group.group_name', 'not like', "TL_%")
                                ->whereRaw('LENGTH(list_group.group_name) < 20');
                        })
                        ->orderBy('id', 'desc')
                        ->get();
                }
            }
        }

        return $this->view('graduate.export_danh_sach_sinh_vien', [
            'terms' => $terms,
            'blocks' => $blocks,
            'ds_bo_mon' => $ds_bo_mon,
            'ds_lop' => $ds_lop,
            'ds_mon' => $ds_mon
        ]);
    }

    public function processExportDanhSachSinhVien(Request $request)
    {
        try {
            ini_set('memory_limit', '-1');
            set_time_limit(0);
            \Debugbar::disable();

            $term_id = $request->term_id;
            $block_id = $request->block_id;
            $department_id = $request->department_id;
            $course_id = $request->course_id;
            $group_id = $request->group_id;
            $user_login = auth()->user()->user_login;

            if (
                $term_id == null || $term_id <= 0 ||
                $department_id == null || $department_id <= 0 ||
                $course_id == null || $course_id <= 0 ||
                $group_id == null || $group_id <= 0
            ) {
                return $this->redirectWithStatus('danger', 'Vui lòng chọn lớp môn cần in danh sách bảo vệ!', url()->previous());
            }

            $term = Term::where('id', $term_id)->first();
            if ($term == null) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy kỳ cần in danh sách thi!', url()->previous());
            }

            //export theo môn
            $group = Group::where('id', $group_id)->first();
            if ($group == null) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy lớp môn. Vui lòng thử lại!', url()->previous());
            }

            //lấy 3 buổi thi của lớp đã được đào tạo phân công
            $result_course_slot_bao_ve = SyllabusPlan::where('syllabus_id', $group->syllabus_id)
                ->where('session_type', '=', 11)
                // ->whereIn('session_type', [11, 9])
                ->orderBy('course_session')
                ->pluck('course_session');

            if ($result_course_slot_bao_ve == null || count($result_course_slot_bao_ve) <= 0) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy 3 buổi bảo vệ của môn hoặc lớp môn trong syllabus', url()->previous());
            }

            //=> kiểm tra lớp môn đó đào tạo đã xếp lịch buổi 300 hay chưa?
            $query_lop_mon = Activity::query();
            $query_lop_mon = $query_lop_mon->where('day', '>=', $term->startday);
            $query_lop_mon = $query_lop_mon->where('day', '<=', $term->endday);
            // $query_lop_mon = $query_lop_mon->where('course_slot', '>=', 300);
            $query_lop_mon = $query_lop_mon->whereIn('course_slot', $result_course_slot_bao_ve);
            $query_lop_mon = $query_lop_mon->where('course_id', '=', $course_id);
            // $query_lop_mon = $query_lop_mon->where('department_id', '=', $department_id);
            $query_lop_mon = $query_lop_mon->where('groupid', '=', $group_id);

            $lop_mon_info = $query_lop_mon->select('psubject_name', 'psubject_code', 'psubject_id')->first();
            if ($lop_mon_info == null) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy 3 buổi bảo vệ trong kế hoạch của môn hoặc lớp môn', url()->previous());
            }

            //export theo môn
            $ds_members_total = [];
            // $members = GroupMember::where('groupid', $group_id)->orderBy('member_login')->get();
            // $members = GroupMember::leftJoin('group_graduate', function ($join) {
            //     $join->on('group_member.groupid', '=', 'group_graduate.group_id');
            //     $join->on('group_member.member_login', '=', 'group_graduate.student_login');
            // })
            //     ->where('groupid', $group_id)
            //     ->orderBy('date_graduate')
            //     ->get();

            $sqlRawQuery = "(SELECT fgg.group_id, fgg.student_login, MAX(fgg.course_session) AS course_session FROM group_graduate fgg" .
                " WHERE fgg.group_id = " . $group_id .
                " GROUP BY fgg.group_id, fgg.student_login, fgg.group_name, fgg.psubject_code, fgg.psubject_name, fgg.term_id) AS A";

            $members = GroupMember::leftJoin(DB::raw($sqlRawQuery), function ($join) {
                $join->on('group_member.groupid', '=', 'A.group_id');
                $join->on('group_member.member_login', '=', 'A.student_login');
            })
                ->join('user', 'user.user_login', 'group_member.member_login')
                ->where('groupid', $group_id)
                // ->orderBy('course_session')
                ->orderBy('groupid')
                ->orderBy('user.user_code')
                ->get();

            if ($members == null || count($members) <= 0) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy sinh viên trong lớp. Vui lòng thử lại!', url()->previous());
            }

            foreach ($members as $key => $member) {
                //lấy các thông tin sinh viên cần hiển thị
                $member->group_name = $group->group_name;
                $member->user = User::where('user_login', $member->member_login)->first();
                $member->fullname = $member->user->fullname();
                $member->user_code = $member->user->user_code;

                /**
                 * lấy ngày bảo vệ của sinh viên
                 *
                 * $member->course_session: 300, 301, 302
                 */
                if ($member->course_session > 0) {
                    //chuyển course_session từ 0 => 300
                    $course_session_300 = null;
                    if ($member->course_session == 1) {
                        $course_session_300 = $result_course_slot_bao_ve[0];
                    } elseif ($member->course_session == 2) {
                        $course_session_300 = $result_course_slot_bao_ve[1];
                    } elseif($member->course_session == 3) {
                        $course_session_300 = $result_course_slot_bao_ve[2];
                    }

                    $slot_graduate = Activity::select('course_slot', 'slot', 'day', 'is_online', 'room_name', 'url_room_online', 'leader_login', 'leader_login2')
                        ->with('slotDetail:id,slot_start,slot_end')
                        ->where('groupid', '=', $group_id)
                        // ->where('day', '=', $member->date_graduate)
                        ->where('course_slot', '=', $course_session_300)
                        ->first();

                    $member->slot_graduate = $slot_graduate;
                }

                //thêm sinh viên đủ điều kiện thi vào ds tổng
                array_push($ds_members_total, $member);
            }

            $excel_info = [
                'group_name' => $group->group_name,
                'bo_mon_info' => $lop_mon_info,
            ];

            $file_name = now()->format('d_m_y_h_i_s_') . 'xep_lich_bao_ve_' . ($group->group_name) . '.xlsx';
            $export = new GraduateStudentListExport($ds_members_total, $excel_info);
            return Excel::download($export, $file_name);
        } catch (\Throwable $th) {
            Log::error("---------dont------------");
            Log::error($th);
            Log::error("---------err end process_export_danh_sach_sinh_vien ------------");
            return $this->redirectWithStatus('danger', 'Có lỗi xảy ra. Không thể export file excel!');
        }
    }

    public function xepLichBaoVeSinhVien(Request $request)
    {
        //lấy tất cả kỳ học
        // $terms = Term::where('is_locked', 0)->orderBy('id', 'desc')->get();
        $terms = Term::orderBy('id', 'desc')->get();

        return $this->view('graduate.xep_lich_bao_ve_sinh_vien', [
            'terms' => $terms,
        ]);
    }

    public function processImportLichBaoVeSinhVien(Request $request)
    {
        try {
            ini_set('memory_limit', '-1');
            set_time_limit(0);
            \Debugbar::disable();

            $user_login = auth()->user()->user_login;

            $term_id = $request->term_id;
            //lấy tất cả kỳ học
            $term = Term::where('id', $term_id)->first();
            if ($term == null || $term->id <= 0) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy kỳ cần in danh sách bảo vệ! Vui lòng thử lại!', url()->previous());
            }

            $tempFile = $request->file('file');
            $reader = ReaderEntityFactory::createXLSXReader();
            $reader->open($tempFile);

            $report = [];
            $result = [];

            $psubject_code = "";
            $psubject_name = "";
            $group_name = "";
            $group_id = 0;

            $ds_slot_assignment = [];
            //300 - 301 - 302
            $result_course_slot_bao_ve = [];
            $ds_member_imported = [];
            $ds_member_origin = [];

            foreach ($reader->getSheetIterator() as $sheet) {
                foreach ($sheet->getRowIterator() as $i => $row) {
                    if ($i <= 1)
                        continue;

                    $item = $row->toArray();

                    $day_import = (int)$item[6];
                    $user_code_import = $item[1];

                    if ($i == 2) {
                        //lấy thông tin lớp học
                        $psubject_code = $item[4];
                        $group_name = $item[5];

                        if ($group_name == null || $psubject_code == null) {
                            $report[] = "Lớp cần import không chính xác. Vui lòng export lại danh sách bảo vệ sinh viên theo lớp hoặc liên hệ với IT để được trợ giúp!";
                            break;
                        }

                        //kiểm tra xem môn học có lịch bảo vệ trong kỳ hay ko?
                        //=> ko lấy những môn mà đào tạo chưa xếp lịch buổi 300?
                        $query_lop = Activity::query();
                        $query_lop = $query_lop->leftJoin('session_type', 'session_type.id', '=', 'activity.session_type');
                        $query_lop = $query_lop->where('activity.pterm_id', $term->id);
                        $query_lop = $query_lop->where('activity.day', '>=', $term->startday);
                        $query_lop = $query_lop->where('activity.day', '<=', $term->endday);
                        $query_lop = $query_lop->where('session_type.is_exam', 1);
                        $query_lop = $query_lop->where('activity.group_name', '=', $group_name);
                        $query_lop = $query_lop->where('activity.psubject_code', '=', $psubject_code);
                        $query_lop = $query_lop->groupBy('activity.groupid', 'activity.psubject_name', 'activity.psubject_code', 'activity.psubject_id', 'activity.group_name');
                        $lop_info = $query_lop->select(DB::raw("GROUP_CONCAT( DISTINCT activity.leader_login SEPARATOR ', ' ) AS leader_login, activity.groupid, activity.group_name, activity.psubject_code"))
                            ->first();

                        if ($lop_info == null || $lop_info->groupid <= 0) {
                            $report[] = "Không tìm thấy lớp thuộc kỳ đã chọn. Vui lòng thử lại!";
                            break;
                        }

                        // $group_id = $lop_info['groupid'];
                        $group_id = $lop_info->groupid;
                        $group = Group::where('id', $group_id)->first();
                        if ($group == null || $group->id <= 0) {
                            $report[] = "Không tìm thấy lớp thuộc kỳ đã chọn. Vui lòng thử lại!";
                            break;
                        }
                        $psubject_name = $group->psubject_name;

                        $ds_member_origin = GroupMember::where('groupid', $group_id)->orderBy('member_login')->get();
                        if ($ds_member_origin == null || count($ds_member_origin) <= 0) {
                            $report[] = "Không tìm thấy danh sách sinh viên trong lớp thuộc kỳ đã chọn. Vui lòng thử lại!";
                            break;
                        }

                        //kiểm tra xem môn học có 3 buổi bảo vệ hay ko?
                        //lấy 3 buổi thi của lớp đã được đào tạo phân công
                        if (!isset($result_course_slot_bao_ves[$group->syllabus_id])) {
                            $result_course_slot_bao_ves[$group->syllabus_id] = SyllabusPlan::where('syllabus_id', $group->syllabus_id)
                                ->where('session_type', '=', 11)
                                // ->whereIn('session_type', [11, 9])
                                ->orderBy('course_session')
                                ->pluck('course_session');
                        }

                        $result_course_slot_bao_ve = $result_course_slot_bao_ves[$group->syllabus_id];
                        if ($result_course_slot_bao_ve == null || count($result_course_slot_bao_ve) <= 0) {
                            $report[] = "Không tìm thấy 3 buổi bảo vệ của môn hoặc lớp môn trong syllabus";
                            break;
                        }

                        $ds_slot_assignment = Activity::select('course_slot', 'slot', 'day', 'is_online', 'room_name', 'url_room_online')
                            ->with('slotDetail:id,slot_start,slot_end')
                            ->where('groupid', '=', $group_id)
                            ->whereIn('course_slot', $result_course_slot_bao_ve)
                            ->get();

                        if ($ds_slot_assignment == null || count($ds_slot_assignment) <= 0) {
                            $report[] = "Không tìm thấy 3 buổi bảo vệ trong kế hoạch của môn hoặc lớp môn";
                            break;
                        }
                    }

                    // if ($day_import == null || empty($day_import) || !$this->validateNgayThi($day_import)) {
                    //     $report[] = "Dòng " . ($i - 1) . " - Ngày thi không đúng định dạng (yyyy-mm-dd)";
                    //     continue;
                    // }

                    // if (!$ds_slot_assignment->contains('day', $day_import)) {
                    //     $report[] = "Dòng " . ($i - 1) . " - Ngày thi phải là 1 trong những buổi được đào tạo phân công";
                    //     continue;
                    // }

                    if ($day_import != 1 && $day_import != 2 && $day_import != 3) {
                        $report[] = "Dòng " . ($i - 1) . " - Buổi thi chấp nhận giá trị 1, 2 hoặc 3";
                        continue;
                    }

                    if (!$ds_member_origin->contains('member_login', $user_code_import)) {
                        $report[] = "Dòng " . ($i - 1) . " - Không tìm thấy sinh viên trong lớp";
                        continue;
                    }

                    if ($day_import > 0) {
                        if (array_key_exists($day_import, $result)) {
                            array_push($result[$day_import], $user_code_import);
                        } else {
                            $result[$day_import] = [
                                $user_code_import
                            ];
                        }
                    }

                    //lấy các sinh viên trong file excel
                    array_push($ds_member_imported, $user_code_import);
                }
            }

            $reader->close();
            if ($report != null && count($report) > 0) {
                return $this->redirectWithStatus('danger', implode('<br/>', $report), url()->previous());
            }

            if ($result == null || count($result) <= 0) {
                return $this->redirectWithStatus('danger', 'Không thể import file excel. Không tìm thấy dữ liệu. Vui lòng thử lại', url()->previous());
            }

            /*
            //kiểm tra: trong 1 lớp tại 1 ngày thi ko quá 13 sinh viên
            $ds_check_member = $result;

            //lấy các sinh viên trong database
            $ds_member_exists = GroupGraduate::where('group_id', '=', $group_id)
                ->whereNotIn('student_login', $ds_member_imported)
                ->pluck('student_login', 'date_graduate');

            if ($ds_member_exists != null && count($ds_member_exists) > 0) {
                foreach ($ds_member_exists as $key => $item) {
                    if (array_key_exists($key, $ds_check_member)) {
                        array_push($ds_check_member[$key], $item);
                    } else {
                        $ds_check_member[$key] = [
                            $item
                        ];
                    }
                }
            }

            foreach ($ds_check_member as $key => $value) {
                if (count($value) > 13) {
                    return $this->redirectWithStatus('danger', 'Không thể import file excel. Trong 1 lớp tại 1 ngày thi ' . ($key) . ' ko được quá 13 sinh viên', url()->previous());
                }
            }
            */

            //lưu lại dữ liệu
            DB::beginTransaction();

            // $datasSave = GroupGraduate::where('group_id', $group_id)->get();
            foreach ($result as $key => $item) {
                foreach ($item as $student_login) {
                    $dataSave = GroupGraduate::where('group_id', $group_id)
                    ->where('student_login', $student_login)
                    ->first();
                    if ($dataSave) {
                        $dataSave->lastmodifier_login = $user_login;
                        $dataSave->course_session = (int)$key;
                        $dataSave->save();
                        $this->systemLog(
                            'group_graduate', 
                            'Update', 
                            "update group grade - pterm: " . $term_id . " - psubject_code " . $psubject_code . " - psubject_name: " . $psubject_name . " - student_login: " . $student_login . " - date_graduate: " . $key, 
                            $user_login, 
                            $group_id, 
                            0, 
                            'processImportLichBaoVeSinhVien'
                        );
                    } else {
                        GroupGraduate::create([
                            'group_id' => $group_id,
                            'group_name' => $group_name,
                            'psubject_code' => $psubject_code,
                            'psubject_name' => $psubject_name,
                            'student_login' => $student_login,
                            'course_session' => (int)$key,
                            'lastmodifier_login' => $user_login,
                            //thêm term_id => sử dụng khi cần xoá dữ liệu cũ
                            'term_id' => $term->id,
                        ]);

                        $this->systemLog('group_graduate', 
                            'Create', 
                            "create group grade - pterm: " . $term_id . " - psubject_code " . $psubject_code . " - psubject_name: " . $psubject_name . " - student_login: " . $student_login . " - date_graduate: " . $key, 
                            $user_login, 
                            $group_id, 
                            0, 
                            'processImportLichBaoVeSinhVien'
                        );
                    }

                    // GroupGraduate::updateOrCreate(
                    //     [
                    //         'group_id' => $group_id,
                    //         'group_name' => $group_name,
                    //         'psubject_code' => $psubject_code,
                    //         'psubject_name' => $psubject_name,
                    //         'student_login' => $student_login,
                    //         //thêm term_id => sử dụng khi cần xoá dữ liệu cũ
                    //         'term_id' => $term->id,
                    //     ],
                    //     [
                    //         'lastmodifier_login' => $user_login,
                    //         // 'date_graduate' => $key,
                    //         //=> thay date_graduate => course_session : 300 - 301 - 302
                    //         'course_session' => (int)$key,
                    //     ],
                    // );

                    // $this->systemLog('group_graduate', 'updateOrCreate', "update or create group grade - pterm: " . $term_id . " - psubject_code " . $psubject_code . " - psubject_name: " . $psubject_name . " - student_login: " . $student_login . " - date_graduate: " . $key, $user_login, $group_id, 0, 'processImportLichBaoVeSinhVien');
                }
            }

            DB::commit();

            return $this->redirectWithStatus('success', 'Tải lên lịch thi thành công', url()->previous());
        } catch (\Throwable $th) {
            DB::rollback();

            Log::error("---------dont------------");
            Log::error($th);
            Log::error("---------err end process_import_lich_bao_ve_sinh_vien ------------");
            return $this->redirectWithStatus('danger', 'Có lỗi xảy ra. Không thể import file excel!');
        }
    }

    public function exportDanhSachGiamThi(Request $request)
    {
        $blocks = [];
        $ds_lop = [];
        $ds_mon = [];
        $ds_bo_mon = Department::select('id', 'department_name', 'dean')->get();
        $user_login = auth()->user()->user_login;

        //lấy tất cả kỳ học
        // $terms = Term::where('is_locked', 0)->orderBy('id', 'desc')->get();
        $terms = Term::orderBy('id', 'desc')->get();

        $term_id = $request->term_id;
        if ($term_id != null && $terms->contains('id', $term_id)) {
            $blocks = Block::where('term_id', $term_id)->get();
            $block_id = $request->block_id;
            $block_search = null;
            if ($block_id) {
                $block_search = $blocks->where('id', $block_id)->first();
            }

            $department_id = $request->department_id;
            if ($department_id != null && $department_id > 0) {
                $subject = Subject::where('department_id', $department_id)->get();

                $ds_mon = Course::when($department_id, function ($query) use ($subject) {
                    $query->whereIn('subject_id', $subject->pluck('id'));
                })->orderBy('psubject_code')->where('term_id', $term_id)->get();

                $course_id = $request->course_id;
                if ($course_id != null && $course_id > 0) {
                    $course_search = $ds_mon->where('id', $course_id)->pluck('id');

                    $ds_lop = Group::whereIn('body_id', $course_search)->where('list_group.is_virtual', 0)
                        ->where('pterm_id', $term_id)
                        ->when($block_search, function ($query) use ($block_search) {
                            $query->where('end_date', '>=', $block_search->start_day)->where('end_date', '<=', $block_search->end_day);
                        })
                        ->where('type', 1)
                        ->where(function ($q) {
                            $q->where('list_group.group_name', 'not like', "TL_%")
                                ->whereRaw('LENGTH(list_group.group_name) < 20');
                        })
                        ->orderBy('id', 'desc')
                        ->get();
                }
            }
        }

        return $this->view('graduate.export_danh_sach_giam_thi', [
            'terms' => $terms,
            'blocks' => $blocks,
            'ds_bo_mon' => $ds_bo_mon,
            'ds_mon' => $ds_mon,
            'ds_lop' => $ds_lop,
        ]);
    }

    public function processExportDanhSachGiamThi(Request $request)
    {
        try {
            ini_set('memory_limit', '-1');
            set_time_limit(0);
            \Debugbar::disable();

            $term_id = $request->term_id;
            $block_id = $request->block_id;
            $department_id = $request->department_id;
            $course_id = $request->course_id;
            $group_id = $request->group_id;
            $user_login = auth()->user()->user_login;

            if ($term_id == null || $term_id <= 0 || $department_id == null || $department_id <= 0) {
                return $this->redirectWithStatus('danger', 'Vui lòng chọn bộ môn cần in danh sách giám thị!', url()->previous());
            }

            $term = Term::where('id', $term_id)->first();
            if ($term == null) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy kỳ cần in danh sách giám thị! Vui lòng thử lại!', url()->previous());
            }

            $blocks = Block::where('term_id', $term_id)->get();
            $block_search = null;
            if ($block_id) {
                $block_search = $blocks->where('id', $block_id)->first();
            }

            $subject = Subject::where('department_id', $department_id)->get();
            $ds_mon = Course::when($department_id, function ($query) use ($subject) {
                $query->whereIn('subject_id', $subject->pluck('id'));
            })->orderBy('psubject_code')->where('term_id', $term_id)->get();

            $query = Group::query();
            $query = $query->where('list_group.is_virtual', 0)->where('pterm_id', $term_id);
            $query = $query->when($block_search, function ($query) use ($block_search) {
                $query->where('end_date', '>=', $block_search->start_day)->where('end_date', '<=', $block_search->end_day);
            });

            if ($course_id != null && $course_id > 0) {
                $course_search = $ds_mon->where('id', $course_id)->pluck('id');
                $query = $query->whereIn('body_id', $course_search);
            } else {
                $course_search = $ds_mon->pluck('id');
                $query = $query->whereIn('body_id', $course_search);
            }

            if ($group_id != null && $group_id > 0) {
                $query = $query->where('id', '=', $group_id);
            }

            $query = $query->where('type', 1);
            $query = $query->where(function ($q) {
                $q->where('list_group.group_name', 'not like', "TL_%")
                    ->whereRaw('LENGTH(list_group.group_name) < 20');
            });
            $query = $query->orderBy('id', 'desc');
            $main_result = $query->get();

            if ($main_result == null || count($main_result) <= 0) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy môn cần in ra danh sách giám thị! Vui lòng thử lại!', url()->previous());
            }

            $ds_error = [];
            $ds_mon_total = [];

            foreach ($main_result as $item) {
                $group = Group::where('id', $item->id)->first();

                if ($group == null || $group->id <= 0) {
                    $ds_error[] = [
                        'group_name' => $group->group_name,
                        'reason' => 'Không tìm thấy lớp thuộc kỳ đã chọn'
                    ];
                    continue;
                }

                //kiểm tra xem môn học có 3 buổi bảo vệ hay ko?
                //lấy 3 buổi thi của lớp đã được đào tạo phân công
                $result_course_slot_bao_ve = SyllabusPlan::where('syllabus_id', $group->syllabus_id)
                    ->where('session_type', '=', 11)
                    // ->whereIn('session_type', [11, 9])
                    ->orderBy('course_session')
                    ->pluck('course_session');

                if ($result_course_slot_bao_ve != null && count($result_course_slot_bao_ve) <= 0) {
                    $ds_error[] = [
                        'group_name' => $group->group_name,
                        'reason' => 'Không tìm thấy 3 buổi bảo vệ của môn hoặc lớp môn trong syllabus'
                    ];
                    continue;
                }

                $ds_slot_assignment = Activity::select('course_slot', 'slot', 'day', 'is_online', 'room_name', 'url_room_online', 'leader_login', 'leader_login2')
                    ->with('slotDetail:id,slot_start,slot_end')
                    ->where('groupid', '=', $group->id)
                    ->whereIn('course_slot', $result_course_slot_bao_ve)
                    ->get();

                if ($ds_slot_assignment == null || count($ds_slot_assignment) <= 0) {
                    $ds_error[] = [
                        'group_name' => $group->group_name,
                        'reason' => 'Không tìm thấy 3 buổi bảo vệ trong kế hoạch của môn hoặc lớp môn'
                    ];
                    continue;
                }

                //lấy tất cả buổi bảo vệ được xếp lịch của môn đó?
                $ds_slot_graduate = GroupGraduate::where('group_id', '=', $group->id)
                    ->select('course_session')
                    ->distinct()
                    ->get();

                //nếu chưa xếp lịch => chỉ lấy tên của lớp đó + giám thị
                if ($ds_slot_graduate == null || count($ds_slot_graduate) <= 0) {
                    $new_item = clone $item;
                    $temp = [
                        'is_error' => false,
                        'group_name' => $group->group_name,
                        'mon_info' => $new_item
                    ];
                    array_push($ds_mon_total, $temp);
                } else {
                    //lấy tất cả + buổi thi => ngày thi + slot
                    $ds_slot_300 = [];

                    foreach ($ds_slot_graduate as $slotFor) {
                        if ($slotFor->course_session == 1) {
                            $key = $result_course_slot_bao_ve[0];
                            $ds_slot_300[$key] = 1;
                        } elseif ($slotFor->course_session == 2) {
                            $key = $result_course_slot_bao_ve[1];
                            $ds_slot_300[$key] = 2;
                        } elseif ($slotFor->course_session == 3) {
                            $key = $result_course_slot_bao_ve[2];
                            $ds_slot_300[$key] = 3;
                        }
                    }

                    foreach ($ds_slot_assignment as $value) {
                        if (in_array($value->course_slot, array_keys($ds_slot_300))) {
                            $new_item = clone $item;
                            $new_item->slot_assignment = $value;
                            $new_item->course_slot_syllybus = $ds_slot_300[$value->course_slot];

                            $temp = [
                                'is_error' => false,
                                'group_name' => $group->group_name,
                                'mon_info' => $new_item
                            ];
                            array_push($ds_mon_total, $temp);
                        }
                    }
                }
            }

            $ds_giam_thi[] = [
                'is_error' => false,
                'group_name' => $group->group_name,
                'ds_mon' => $ds_mon_total,
            ];

            if ($ds_error != null && count($ds_error) > 0) {
                $item = [
                    'is_error' => true,
                    'ds_error' => $ds_error,
                ];

                //thêm lớp bị lỗi lên đầu file excel
                array_unshift($ds_giam_thi, $item);
            }

            $excel_info = [];

            $file_name = now()->format('d_m_y_h_i_s_') . 'xep_giam_thi' . '.xlsx';
            $export = new XepGiamThiMutipleExport($ds_giam_thi, $excel_info);
            return Excel::download($export, $file_name);
        } catch (\Throwable $th) {
            Log::error("---------dont------------");
            Log::error($th);
            Log::error("---------err end process_export_danh_sach_giam_thi ------------");
            return $this->redirectWithStatus('danger', 'Có lỗi xảy ra. Không thể export file excel!');
        }
    }

    public function XepGiamThi(Request $request)
    {
        //lấy tất cả kỳ học
        // $terms = Term::where('is_locked', 0)->orderBy('id', 'desc')->get();
        $terms = Term::orderBy('id', 'desc')->get();

        return $this->view('graduate.xep_giam_thi', [
            'terms' => $terms,
        ]);
    }

    public function processImportXepGiamThi(Request $request)
    {
        try {
            ini_set('memory_limit', '-1');
            set_time_limit(0);
            \Debugbar::disable();

            $user_login = auth()->user()->user_login;
            $term_id = $request->term_id;

            //lấy tất cả kỳ học
            $term = Term::where('id', $term_id)->first();
            if ($term == null || $term->id <= 0) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy kỳ cần xếp giám thị! Vui lòng thử lại!', url()->previous());
            }

            $tempFile = $request->file('file');
            $reader = ReaderEntityFactory::createXLSXReader();
            $reader->open($tempFile);

            $report = [];
            $result = [];

            // $ds_slots = Slot::select('slot_id')->where('lock', 0)->orderBy('slot_id')->get();
            // $ds_users = User::whereIn('user_level', [1, 2, 11])->get();
            $ds_users = User::where('user_level', '!=', 3)->get();

            foreach ($reader->getSheetIterator() as $sheet) {
                foreach ($sheet->getRowIterator() as $i => $row) {
                    if ($i <= 1)
                        continue;

                    $item = $row->toArray();

                    $psubject_code_import = $item[1];
                    $group_id_import = $item[2];
                    $group_name_import = $item[3];
                    $day_import = $item[4];

                    $leader_login1_import = $item[7];
                    $leader_login2_import = $item[8];

                    if (
                        $psubject_code_import == null || empty($psubject_code_import) ||
                        $group_id_import < 0 ||
                        $group_name_import == null || empty($group_name_import) ||
                        $day_import == null || empty($day_import)
                        // $leader_login1_import == null || empty($leader_login1_import) ||
                        // $leader_login2_import == null || empty($leader_login2_import)
                    ) {
                        $report[] = "Dòng " . ($i - 1) . " - Vui lòng nhập đầy đủ dữ liệu";
                        continue;
                    }

                    /**
                     * ktra buổi thi
                     */
                    if ($day_import != 1 && $day_import != 2 && $day_import != 3) {
                        $report[] = "Dòng " . ($i - 1) . " - Buổi thi chấp nhận giá trị 1, 2 hoặc 3";
                        continue;
                    }

                    /**
                     * ktra mã môn + mã lớp => theo group_id
                     */
                    $group = Group::where('id', $group_id_import)->where('list_group.is_virtual', 0)->first();
                    if ($group == null || $group->id <= 0) {
                        $report[] = "Dòng " . ($i - 1) . " - Không tìm thấy môn học theo ID lớp";
                        continue;
                    }

                    //lấy 3 buổi thi của lớp đã được đào tạo phân công
                    $result_course_slot_bao_ve = SyllabusPlan::where('syllabus_id', $group->syllabus_id)
                        ->where('session_type', '=', 11)
                        // ->whereIn('session_type', [11, 9])
                        ->orderBy('course_session')
                        ->pluck('course_session');

                    if ($result_course_slot_bao_ve == null || count($result_course_slot_bao_ve) <= 0) {
                        $report[] = "Dòng " . ($i - 1) . " - Không tìm thấy 3 buổi bảo vệ của môn hoặc lớp môn trong syllabus";
                        continue;
                    }

                    //ktra giảng viên
                    if (($leader_login1_import != null && !empty($leader_login1_import)) && !$ds_users->contains('user_login', $leader_login1_import)) {
                        $report[] = "Dòng " . ($i - 1) . " - Giám thị 1 $leader_login1_import không tồn tại";
                        continue;
                    }

                    if (($leader_login2_import != null && !empty($leader_login2_import)) && !$ds_users->contains('user_login', $leader_login2_import)) {
                        $report[] = "Dòng " . ($i - 1) . " - Giám thị 2 $leader_login2_import không tồn tại";
                        continue;
                    }

                    //chuyển course_session từ 0 => 300
                    $course_session_300 = null;
                    if ($day_import == 1) {
                        $course_session_300 = $result_course_slot_bao_ve[0];
                    } elseif ($day_import == 2) {
                        $course_session_300 = $result_course_slot_bao_ve[1];
                    } elseif ($day_import == 3) {
                        $course_session_300 = $result_course_slot_bao_ve[2];
                    }

                    $item = [
                        'groupid' => $group_id_import,
                        'course_slot' => $course_session_300,
                        'psubject_code' => $psubject_code_import,
                        'group_name' => $group_name_import,
                        'leader_login' => $leader_login1_import,
                        'leader_login2' => $leader_login2_import,
                    ];

                    array_push($result, $item);
                }
            }
            $reader->close();

            if ($report != null && count($report) > 0) {
                return $this->redirectWithStatus('danger', implode('<br/>', $report), url()->previous());
            }

            if ($result == null || count($result) <= 0) {
                return $this->redirectWithStatus('danger', 'Không thể import file excel. Không tìm thấy dữ liệu. Vui lòng thử lại', url()->previous());
            }

            $sql = "";
            foreach ($result as $item) {
                Activity::where("groupid", $item['groupid'])
                ->where('course_slot', $item['course_slot'])
                ->update([
                    'leader_login' => $item['leader_login'],
                    'leader_login2' => $item['leader_login2'],
                    'lastmodifier_login' => auth()->user()->user_login,
                    'lastmodified_time' => now()->format('Y-m-d H:i:s')
                ]);

                // $sql .= "UPDATE activity SET leader_login=" . "'" . $item['leader_login'] . "'"
                //     . ", leader_login2=" . "'" . $item['leader_login2'] . "'"
                //     . ", lastmodifier_login=" . "'" . auth()->user()->user_login . "'"
                //     . ", lastmodified_time=" . "'" . Carbon::now()->format('Y-m-d H:i:s') . "'"
                //     . " WHERE groupid=" . $item['groupid']
                //     // . " AND psubject_code=" . "'" . $item['psubject_code'] . "'"
                //     . " AND course_slot=" . $item['course_slot']
                //     // . " AND slot=" . $item['slot']
                //     // . " AND `day`=" . "'" . $item['day'] . "'"
                //     . ";";
            }

            // DB::connectistatement(DB::raw($sql));
            // DB::connectiunprepared(DB::raw($sql));

            $this->systemLog('activity', 'update', $sql, $user_login, 0, 0, 'processImportXepGiamThi');

            DB::commit();

            return $this->redirectWithStatus('success', "Import thành công giám thị", url()->previous());
        } catch (\Throwable $th) {
            DB::rollback();

            Log::error("---------dont------------");
            Log::error($th);
            Log::error("---------err end process_import_xep_giam_thi ------------");
            return $this->redirectWithStatus('danger', 'Có lỗi xảy ra. Không thể import file excel!');
        }
    }

    public function validateNgayThi($date)
    {
        try {
            $format = "Y-m-d";
            if (date($format, strtotime($date)) == date($date)) {
                return true;
            } else {
                return false;
            }
        } catch (\Throwable $th) {
            return false;
        }
    }
}
