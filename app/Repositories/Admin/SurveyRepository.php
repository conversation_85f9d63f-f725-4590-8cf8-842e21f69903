<?php

namespace App\Repositories\Admin;

use App\Imports\DisciplineImport;
use App\Models\Dra\Survey;
use App\Models\Dra\SurveyStudent;
use App\Models\LimeSurvey\Surveys;
use App\Repositories\BaseRepository;
use App\Models\Fu\User;
use App\Utils\ResponseBuilder;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SurveyRepository extends BaseRepository
{
    /**
     * get model
     * @return string
     */
    public function getModel()
    {
        return User::class;
    }
    
    /**
     * managerSurvey
     *
     * @param  mixed $request
     * @return mixed view('survey.manager_survey')
     */
    public function managerSurvey($request){
        $surveyStatus = [
            0 => "Đã kết thúc",
            1 => "Đang hoạt động"
        ];

        $listSurvey = Surveys::select([
            'surveys.sid',
            'surveys.active',
            DB::raw("DATE_FORMAT(surveys.startdate, '%d\/%m\/%Y') as start_date"),
            DB::raw("DATE_FORMAT(surveys.expires, '%d\/%m\/%Y') as end_date"),
            'surveys_languagesettings.surveyls_title',
            DB::raw("COUNT('questions') AS questions_num"),
        ])
        ->join('surveys_languagesettings', 'surveys_languagesettings.surveyls_survey_id', '=', 'surveys.sid')
        ->join('questions', 'surveys.sid', '=', 'questions.sid')
        ->groupBy('surveys.sid')
        ->orderBy('surveys.sid', 'DESC')
        ->get();
        
        return $this->view('survey.manager_survey', [
            'listSurvey' => $listSurvey,
            'surveyStatus' => $surveyStatus,
        ]);
    }
    
    /**
     * detailSurvey
     *
     * @param  mixed $request { survey_id }
     * @return mixed view('survey.detail_survey')
     */
    public function detailSurvey($request)
    {
        $survey = Survey::find($request->survey_id);
        if (!$survey) {
            return back()->withErrors([
                'Chiến dịch không tồn tại'
            ]);
        }

        $surveyStatus = [
            0 => "Đã kết thúc",
            1 => "Đang hoạt động"
        ];

        $surveyDetail = Survey::select([
            'survey.id',
            'survey.survey_id',
            'survey.survey_name',
            'survey.is_active',
            'survey.question_num',
            'survey.survey_url',
            DB::raw("DATE_FORMAT(survey.start_date, '%d\/%m\/%Y') as start_date"),
            DB::raw("DATE_FORMAT(survey.end_date, '%d\/%m\/%Y') as end_date"),
        ])
        ->where('survey.id', $request->survey_id)
        ->get();
        

        return $this->view('survey.detail_survey', [
            'surveyStatus' => $surveyStatus,
            'surveyDetail' => $surveyDetail,
        ]);
    }
    
    /**
     * storeSurvey
     *
     * @param  mixed $request { surveyId, surveyType, surveyName, surveyQuestion, surveyTime }
     * @return mixed
     */
    public function storeSurvey($request) {
        ini_set('max_execution_time', -1);
        $survey_id = $request->surveyType;
        $survey_name = $request->surveyName;
        $question_num = $request->surveyQuestion;

        $date = $request->surveyTime;
        $date = str_replace('/', '-', $date);
        $date = explode(" - ", $date);
        $start_date = date('Y-m-d',strtotime($date[0]));
        $end_date = date('Y-m-d',strtotime($date[1]));
        
        $rules = [
            'file' => 'required',
        ];

        $messages = [
            'file.required' => 'File thêm sinh viên không được bỏ trống',
        ];

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            return back()->with([
                'reports' => [[
                    'msg' => $validator->errors()->first(),
                    'status' => false, 
                ]]
            ]);
        }

        $file = $request->file('file'); // kiểm tra file 
        if (!$datas = $this->importDataNewFromFile($file, true)) {
            // dd(__LINE__);
            return back()->with([
                'reports' => [[
                    'msg' => 'File không có dữ liệu hoặc file tải lên bị lỗi.',
                    'status' => false, 
                ]]
            ]);
        }

        // kiểm tra sự tồn tại của survey
        $checkSurvey = Survey::where('survey_id', $request->surveyId)
        ->where('is_active', 1)
        ->first();
        if(!$checkSurvey){
            DB::beginTransaction();
            try{
                $survey = Survey::create([
                    'survey_id' => $survey_id,
                    'survey_name' => $survey_name,
                    'is_active' => 1,
                    'question_num' => $question_num,
                    'start_date' => $start_date,
                    'end_date' => $end_date,
                ]);

                $listUserCode = array_column($datas, 0);
                $students = DB::connectitable('user')
                ->select([
                    'user.id',
                    'user.user_code',
                    'user.user_login',
                ])
                ->whereIn('user.user_code', $listUserCode)
                ->get();
                
                $listUserCodeFromSurvey = $students->pluck('user_code')->toArray();
                $listUserCantFind = array_diff($listUserCode, $listUserCodeFromSurvey);
                if(!count($listUserCantFind)){
                    foreach($students as $student){
                        SurveyStudent::create([
                            'survey_id' => $survey->id,
                            'user_code' => $student->user_code,
                            'user_login' => $student->user_login,
                            'status' => 0,
                        ]);
                    }
                }

                $reports = [[
                    'msg' => 'Tạo survey thành công ',
                    'status' => true, 
                ]];
                if (count($listUserCantFind) > 0) {
                    $reports[] = [
                        'msg' => 'Vui lòng kiểm tra lại, Không thêm được sinh viên: ' . implode(', ', $listUserCantFind) . ' làm survey',
                        'status' => false, 
                    ];
                }

                // dd(__LINE__);
                DB::commit();
                return back()->with([
                    'reports' => $reports
                ]);
            }
            catch (\Exception $th) {
                Log::error("-----------------------start err storeSurvey-------------------");
                Log::error($th);
                // dd(__LINE__);
                return back()->with([
                    'reports' => [[
                        'msg' => 'Đã có lỗi xảy ra, vui lòng liên hệ với cán bộ IT',
                        'status' => false, 
                    ]]
                ]);
                DB::rollback();
            }
        } else {
            DB::beginTransaction();
            try{
                $reports = [[
                    'msg' => 'Có 1 survey đang hoạt động, vui long kiểm tra lại',
                    'status' => false, 
                ]];
                return back()->with([
                    'reports' => $reports
                ]);
            } catch (\Exception $th) {
                Log::error("-----------------------start err storeSurvey-------------------");
                Log::error($th);
                // dd(__LINE__);
                return back()->with([
                    'reports' => [[
                        'msg' => 'Đã có lỗi xảy ra, vui lòng liên hệ với cán bộ IT',
                        'status' => false, 
                    ]]
                ]);
                DB::rollback();
            }
        }
    }
    
    /**
     * updateSurvey
     *
     * @param  mixed $request { survey_id }
     * @return mixed
     */
    public function updateSurvey($request)
    {
        ini_set('max_execution_time', -1);
        $survey_id = $request->survey_id;
        
        $survey = Surveys::select([
            'surveys.sid',
            'surveys.active',
            DB::raw("DATE_FORMAT(surveys.startdate, '%d\/%m\/%Y') as start_date"),
            DB::raw("DATE_FORMAT(surveys.expires, '%d\/%m\/%Y') as end_date"),
            'surveys_languagesettings.surveyls_title',
            DB::raw("COUNT('questions') AS questions_num"),
        ])
        ->join('surveys_languagesettings', 'surveys_languagesettings.surveyls_survey_id', '=', 'surveys.sid')
        ->join('questions', 'surveys.sid', '=', 'questions.sid')
        ->where('surveys.sid', $survey_id)
        ->groupBy('surveys.sid')
        ->orderBy('surveys.sid', 'DESC')
        ->get();
        

        DB::beginTransaction();
        try {
            foreach ($survey as $surveys) {
                $start_date = str_replace('/', '-', $surveys->start_date);
                $start_date = date('Y-m-d',strtotime($start_date));
                $end_date = str_replace('/', '-', $surveys->end_date);
                $end_date = date('Y-m-d',strtotime($end_date));
                
                Survey::updateOrCreate(
                [
                    'survey_id' => $survey_id,
                ],
                [
                    'survey_name' => $surveys->surveyls_title,
                    'questions_num' => $surveys->questions_num,
                    'start_date' => $start_date,
                    'end_date' => $end_date,
                ]);
            }

            $reports = [[
                'msg' => 'Cập nhật lại survey thành công!',
                'status' => true, 
            ]];

            // dd(__LINE__);
            DB::commit();
            return back()->with([
                'reports' => $reports
            ]);
        } catch (\Exception $th) {
            Log::error("-----------------------start err updateSurvey-------------------");
            Log::error($th);
            // dd(__LINE__);
            return back()->with([
                'reports' => [[
                    'msg' => 'Đã có lỗi xảy ra, vui lòng liên hệ với cán bộ IT',
                    'status' => false, 
                ]]
            ]);
            DB::rollback();
        }
    }

    public function changeStatusSurvey($request)
    {
        if ($request->get('survey_id', null) == null) {
            return ResponseBuilder::Fail('Có vẻ đã có lỗi xảy ra, vui lòng thử lại', null);
        }

        $survey = Survey::find($request->survey_id);
        if(!$survey){
            return ResponseBuilder::Fail(null, 'Không tìm thấy survey', 404);
        }

        try {
            $status = $request->get('status', 'false');
            if ($status == 'true') {
                $survey->is_active = 1;
            } else {
                $survey->is_active = 0;
            }

            $survey->save();
            return ResponseBuilder::Success($survey, 'Thay đổi trạng thái thành công');
        } catch (\Exception $ex) {
            Log::error($ex);
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng báo lại cán bộ IT');
        }
    }

    public function importDataNewFromFile($file, $fullRow = false)
    {
        $data = Excel::toArray(new DisciplineImport(), $file);
        if (count($data[0]) <= 1) {
            return false;
        }

        if ($fullRow == false) {
            unset($data[0][0]);
        }

        return $data[0];
    }
}