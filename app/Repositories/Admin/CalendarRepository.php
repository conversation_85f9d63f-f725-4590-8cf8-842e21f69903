<?php


namespace App\Repositories\Admin;

use App\Exports\ExportExamListDetail;
use App\Exports\ExportNewMultipleListStudentExam;
use Error;
use Exception;
use Carbon\Carbon;
use App\Models\Fu\Term;
use App\Models\Fu\User;
use App\Models\Fu\Block;
use App\Models\Fu\Group;
use App\Models\T7\Grade;
use App\Models\Fu\Course;
use App\Models\Fu\Subject;
use App\Models\Fu\Activity;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\Fu\Attendance;
use App\Models\Fu\Department;
use App\Models\GroupGraduate;
use App\Models\T7\GradeGroup;
use App\Models\Fu\GroupMember;
use App\Models\T7\CourseGrade;
use App\Models\T7\CourseResult;
use App\Models\T7\SyllabusPlan;
use App\Models\T7\GradeSyllabus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Repositories\BaseRepository;
use Maatwebsite\Excel\Facades\Excel;
use Box\Spout\Common\Entity\Style\Color;
use Box\Spout\Common\Entity\Style\Border;
use App\Exports\LichThiTheoMonMultipleExport;
use App\Exports\LichThiTheoBoMonMultipleExport;
use App\Exports\StatisticStuBannedExamsByClass;
use App\helper\ExamHelper;
use App\Utils\ResponseBuilder;
use Box\Spout\Common\Entity\Style\CellAlignment;
use Box\Spout\Writer\Common\Creator\Style\StyleBuilder;
use Box\Spout\Writer\Common\Creator\Style\BorderBuilder;
use Box\Spout\Writer\Common\Creator\WriterEntityFactory;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

class CalendarRepository extends BaseRepository
{
    public function getModel()
    {
        return GroupGraduate::class;
    }

    public function danhSachThi(Request $request)
    {
        $blocks = [];
        $ds_lop = [];
        $ds_mon = [];
        $ds_bo_mon = Department::select('id', 'department_name', 'dean')->get();
        $user_login = auth()->user()->user_login;

        //lấy tất cả kỳ học
        // $terms = Term::where('is_locked', 0)->orderBy('id', 'desc')->get();
        $terms = Term::orderBy('id', 'desc')->get();

        $term_id = $request->term_id;

        if ($term_id != null && $term = Term::where('id', $term_id)->first() != null) {
            $blocks = Block::where('term_id', $term_id)->get();
            $block_id = $request->block_id;
            $block_search = null;
            if ($block_id) {
                $block_search = $blocks->where('id', $block_id)->first();
            }

            $department_id = $request->department_id;
            if ($department_id != null && $department_id > 0) {
                $subject = Subject::where('department_id', $department_id)->get();

                $ds_mon = Course::when($department_id, function ($query) use ($subject) {
                    $query->whereIn('subject_id', $subject->pluck('id'));
                })->orderBy('psubject_code')->where('term_id', $term_id)->get();

                $course_id = $request->course_id;
                if ($course_id != null && $course_id > 0) {
                    $course_search = $ds_mon->where('id', $course_id)->pluck('id');

                    $ds_lop = Group::whereIn('body_id', $course_search)
                        ->where('list_group.is_virtual', 0)
                        ->where('pterm_id', $term_id)
                        ->when($block_search, function ($query) use ($block_search) {
                            $query->where('end_date', '>=', $block_search->start_day)->where('end_date', '<=', $block_search->end_day);
                        })
                        ->where('type', 1)
                        ->where(function ($q) {
                            $q->where('list_group.group_name', 'not like', "TL_%")
                                ->whereRaw('LENGTH(list_group.group_name) < 20');
                        })
                        ->orderBy('id', 'desc')
                        ->get();
                }
            }
        }

        return $this->view('calendar.danh_sach_thi', [
            'terms' => $terms,
            'blocks' => $blocks,
            'ds_bo_mon' => $ds_bo_mon,
            'ds_lop' => $ds_lop,
            'ds_mon' => $ds_mon
        ]);
    }

    public function exportDanhSachThi(Request $request)
    {
        try {
            // Log::error("Dev: " . time());
            ini_set('memory_limit', '-1');
            set_time_limit(0);
            \Debugbar::disable();

            $term_id = $request->term_id;
            $block_id = $request->block_id;
            $department_id = $request->department_id;
            $course_id = $request->course_id;
            $group_id = $request->group_id;
            $user_login = auth()->user()->user_login;

            if (
                $term_id == null || $term_id <= 0 ||
                $department_id == null || $department_id <= 0 || (($course_id == null || $course_id <= 0) && ($group_id == null || $group_id <= 0))
            ) {
                return $this->redirectWithStatus('danger', 'Vui lòng chọn môn hoặc lớp môn cần in danh sách thi!', url()->previous());
            }

            $term = Term::where('id', $term_id)->first();
            if ($term == null) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy kỳ cần in danh sách thi!', url()->previous());
            }

            $checkFall2022 = Term::where('id', $term_id)
                ->where('startday', '>=', '2022-09-12')
                ->count();

            $blocks = Block::where('term_id', $term_id)->get();
            $block_search = null;
            if ($block_id) {
                $block_search = $blocks->where('id', $block_id)->first();
            }

            $subject = Subject::where('department_id', $department_id)->get();
            $ds_mon = Course::when($department_id, function ($query) use ($subject) {
                $query->whereIn('subject_id', $subject->pluck('id'));
            })->orderBy('psubject_code')->where('term_id', $term_id)->get();

            if ($course_id > 0 && $group_id > 0) {
                //export theo môn
                $group = Group::where('id', $group_id)->where('list_group.is_virtual', 0)->first();

                if ($group == null || $group->id <= 0) {
                    return $this->redirectWithStatus('danger', 'Vui lòng kiểm tra lại. Không tìm thấy môn cần in danh sách thi!', url()->previous());
                }

                //lấy % phải đi học theo khung chương trình
                // $result_grade_syllabus = GradeSyllabus::where('subject_code', $group->psubject_code)->first();
                $result_grade_syllabus = GradeSyllabus::where('id', $group->syllabus_id)->first();
                if ($result_grade_syllabus == null || $result_grade_syllabus->id <= 0) {
                    return $this->redirectWithStatus('danger', 'Vui lòng kiểm tra lại. Không tìm thấy khung chương trình của môn cần export danh sách thi');
                }
                //tỷ lệ nghỉ học theo syllabus
                $syllabus_attendance_cutoff = $result_grade_syllabus->attendance_cutoff;
                //điểm tối thiểu theo syllabus
                $syllabus_minimum_required = $result_grade_syllabus->minimum_required;

                //lấy 3 buổi thi của lớp đã được đào tạo phân công
                $result_course_slot_bao_ve = SyllabusPlan::where('syllabus_id', $group->syllabus_id)
                    ->where('session_type', '=', 11)
                    // ->whereIn('session_type', [11, 9])
                    ->orderBy('course_session')
                    ->pluck('course_session');
                if ($result_course_slot_bao_ve == null || count($result_course_slot_bao_ve) <= 0) {
                    return $this->redirectWithStatus('danger', 'Không tìm thấy 3 buổi bảo vệ của môn hoặc lớp môn trong syllabus');
                }

                $ds_slot_assignment = Activity::select('slot', 'day', 'is_online', 'room_name', 'url_room_online')
                    ->with('slotDetail:id,slot_start,slot_end')
                    ->where('groupid', '=', $group_id)
                    ->whereIn('course_slot', $result_course_slot_bao_ve)
                    ->get();
                //$ds_slot_assignment == null || count($ds_slot_assignment) != 3
                if ($ds_slot_assignment == null || count($ds_slot_assignment) <= 0) {
                    return $this->redirectWithStatus('danger', 'Không tìm thấy 3 buổi bảo vệ trong kế hoạch của môn hoặc lớp môn');
                }

                //+ điều kiện: chỉ export được ds thi => khi tất cả SV của lớp đã được xếp lịch thi?
                $total_member = GroupMember::where('groupid', $group_id)->count();
                if ($total_member == null || $total_member <= 0) {
                    return $this->redirectWithStatus('danger', 'Không tìm thấy sinh viên trong lớp. Vui lòng thử lại!', url()->previous());
                }

                //=> check sv trong lớp ở thời điểm hiện tại => sv trong lớp bị thay đổi liên tục và trước đó đào tạo đã import 1 lần => thừa số lượng sv trong GroupGraduate
                $ds_member_current = GroupMember::where('groupid', $group_id)->pluck('member_login')->toArray();

                //khảo thí + gv upload 1 lúc => sinh ra những bản ghi trùng lặp => resolve
                // $number_of_student_graduate = GroupGraduate::where('group_id', '=', $group_id)
                //     ->whereIn('student_login', $ds_member_current)
                //     // ->where('date_graduate', '!=', null)
                //     ->whereNotNull('date_graduate')
                //     ->count();

                $ds_check_student_graduate = GroupGraduate::select('student_login')
                    ->where('group_id', '=', $group_id)
                    ->whereIn('student_login', $ds_member_current)
                    ->whereNotNull('course_session')
                    ->groupBy('student_login')
                    ->get();

                if ($total_member !== count($ds_check_student_graduate)) {
                    return $this->redirectWithStatus('danger', 'Không thể xuất file excel. Vui lòng xếp lịch thi cho tất cả sinh viên trong lớp');
                }

                // $members = GroupMember::leftJoin('group_graduate', function ($join) {
                //     $join->on('group_member.groupid', '=', 'group_graduate.group_id');
                //     $join->on('group_member.member_login', '=', 'group_graduate.student_login');
                // })
                //     ->where('groupid', $group_id)
                //     ->orderBy('date_graduate')
                //     ->get();

                // $sqlRawQuery = "(SELECT student_login, group_id, MAX(fgg.date_graduate) AS date_graduate FROM group_graduate fgg" .
                //     " WHERE fgg.group_id = " . $group_id .
                //     " GROUP BY fgg.student_login, fgg.group_id) AS A";

                $sqlRawQuery = "(SELECT fgg.group_id, fgg.student_login, MAX(fgg.course_session) AS course_session FROM group_graduate fgg" .
                    " WHERE fgg.group_id = " . $group_id .
                    " GROUP BY fgg.group_id, fgg.student_login, fgg.group_name, fgg.psubject_code, fgg.psubject_name, fgg.term_id) AS A";

                $members = GroupMember::leftJoin(DB::raw($sqlRawQuery), function($join) {
                    $join->on('group_member.groupid', '=', 'A.group_id');
                    $join->on('group_member.member_login', '=', 'A.student_login');
                })
                    ->where('groupid', $group_id)
                    ->orderBy('course_session')
                    ->get();

                // $ds_members_total = [];
                /**
                 * ktra có phải là môn tiếng anh hay ko?
                 *
                 *  + phần trăm đi học
                 *      => tính các buổi
                 *          + lý thuyết
                 *          + thực hành
                 *          + remote
                 *      => ko tính các buổi
                 *          + thi giữa kỳ => 8
                 *          + bảo vệ dự án => 11
                 *          + thi lần 1 => 9
                 *          + thi lần 2 => 10
                 *          + giờ học online => 18
                 *          + bảo vệ dự án 2 => 19
                 *          + remote 2 => 21
                 *
                 *  + điểm thành phần
                 */
                if ($group->psubject_code != null && (Str::contains(Str::upper($group->psubject_code), 'ENT') || Str::contains(Str::upper($group->psubject_code), 'ETO') || Str::contains(Str::upper($group->psubject_code), 'EHO'))) {
                    /**
                     * môn tiếng anh
                     *
                     * lấy số buổi sinh viên được tính điểm danh => + ko tính những buổi thi
                     */

                    $ds_syllabus_attendance = SyllabusPlan::select('course_session')
                        ->leftJoin('session_type', 't7_syllabus_plan.session_type', '=', 'session_type.id')
                        ->where('syllabus_id', $group->syllabus_id)
                        ->where('session_type.is_exam', 0)
                        ->pluck('course_session');

                    $ds_activities = Activity::where('groupid', $group_id)
                        ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
                        ->whereIn('course_slot', $ds_syllabus_attendance)
                        ->where('session_type.is_exam', 0)
                        ->orderBy('course_slot')
                        ->pluck('day');

                    //+ điều kiện: chỉ export được ds thi => lớp môn đó đã điểm danh tất cả các buổi học?
                    //=> what what what: group_id: 25637 => điểm danh 35 sinh viên trong khi lớp có mỗi 34 sinh viên????
                    $ds_check_member_attendance = GroupMember::where('groupid', $group_id)
                        ->pluck('member_login');
                    $ds_teacher_attendance = Attendance::select(DB::raw("GROUP_CONCAT( day SEPARATOR ',' ) AS attendance_date, user_login"))
                        ->where('groupid', '=', $group_id)
                        ->whereIn('day', $ds_activities)
                        ->whereIn('user_login', $ds_check_member_attendance)
                        ->groupBy('user_login')
                        ->get();

                    if ($ds_teacher_attendance == null || count($ds_teacher_attendance) != $total_member) {
                        return $this->redirectWithStatus('danger', 'Không thể xuất file excel. Vui lòng điểm danh tất cả các buổi học.', url()->previous());
                    }

                    foreach ($ds_teacher_attendance as $value) {
                        $ds_student_attendance_date = explode(',', $value->attendance_date);
                        if ($ds_student_attendance_date != null) {
                            foreach ($ds_activities as $check_attendance_date) {
                                if (!in_array($check_attendance_date, $ds_student_attendance_date)) {
                                    return $this->redirectWithStatus('danger', 'Không thể xuất file excel. Vui lòng điểm danh tất cả các buổi học. Môn học: Tiếng Anh. Student: ' . $value->user_login . '. Attendance miss: ' . $check_attendance_date, url()->previous());
                                    break;
                                }
                            }
                        } else {
                            return $this->redirectWithStatus('danger', 'Không thể xuất file excel. Vui lòng điểm danh tất cả các buổi học. Môn học: Tiếng Anh. Student: ' . $value->user_login . ' khong co du lieu diem danh', url()->previous());
                            break;
                        }
                    }

                    $listGradeGroupChecks = [];
                    $result_attendances = Attendance::where('groupid', $group_id)
                        ->whereIn('day', $ds_activities)
                        ->get();
                    $listStudentGrades = CourseGrade::select([
                        'id',
                        'val', 
                        'grade_id',
                        'groupid',
                        'login',
                        'grade_group_id'
                    ])
                    ->where('groupid', $group_id)
                    ->orderBy('grade_id')
                    ->get();
                    foreach ($members as $key => $member) {
                        $is_danger_absent = false;
                        $is_danger_point = false;
                        $danger_reason_point = "";
                        $danger_reason_absent = "";

                        /**
                         * 1. ktra số buổi sinh viên được điểm danh bởi giảng viên
                         */
                        $result_attendance = $result_attendances->where('user_login', $member->member_login);

                        $absent = 0;
                        $ds_danger_absent = [];
                        foreach ($result_attendance as $attendance) {
                            if ($attendance->val === 0) {
                                $absent += 1;
                                array_push($ds_danger_absent, $attendance->day);
                            }
                        }

                        $total_remote = count($ds_syllabus_attendance);
                        $absent_percent = $total_remote > 0 ? round(($absent * 100) / $total_remote) : 0;
                        if ($absent_percent > 100 - $syllabus_attendance_cutoff) {
                            $is_danger_absent = true;
                        }

                        if ($is_danger_absent) {
                            /**
                             * 0:"2022-05-23"
                             * 1:"2022-05-30"
                             */
                            $danger_reason_absent = "Cấm thi do trượt điểm danh: " . implode(", ", $ds_danger_absent);
                        }
                        $member->danger_reason_absent = $danger_reason_absent;

                        /**
                         * 2. ktra điểm thành phần
                         */
                        //lấy nhóm đầu điểm thành phần
                        $result_grade_group = GradeGroup::select(['id', 'grade_group_name', 'weight', 'minimum_required'])
                            ->where('syllabus_id', '=', $group->syllabus_id)
                            ->pluck('id');

                        //lấy tất cả các đầu điểm thành phần theo nhóm đầu điểm
                        $result_grade_group_detail = Grade::select(['id', 'minimum_required', 'grade_name'])
                            ->where('is_final_exam', '=', 0) //trừ môn được đào tạo đánh dấu là kết thúc
                            ->whereIn('t7_grade.grade_group_id', $result_grade_group)
                            ->orderBy('t7_grade.id')
                            ->get();

                        //lấy tất cả điểm sinh viên đạt được
                        $result_course_grade = CourseGrade::where('groupid', $group_id)
                            ->where('login', $member->member_login)
                            ->orderBy('t7_course_grade.grade_id')
                            ->pluck('val', 'grade_id');

                        //ktra điểm sinh viên đạt được vs điểm thành phần
                        $ds_danger_point = [];
                        foreach ($result_grade_group_detail as $item) {
                            if (isset($result_course_grade[$item->id])) {
                                if ($item->minimum_required > $result_course_grade[$item->id]) {
                                    array_push($ds_danger_point, $item->grade_name);
                                    break;
                                }
                            } else {
                                array_push($ds_danger_point, $item->grade_name);
                                break;
                            }
                        }

                        //3. ktra điểm tối thiểu đánh giá quá trình
                        //lấy điểm đánh giá quá trình
                        $grade_group_qt = GradeGroup::where('syllabus_id', $group->syllabus_id)
                            ->where('grade_group_name', 'LIKE', '%Đánh giá quá trình%')
                            ->first();

                        /* if ($syllabus_minimum_required > 0) {
                            if ($grade_group_qt != null && $grade_group_qt->id > 0) {
                                //trọng số tổng của điểm đánh giá quá trình
                                $qt_weight_total = $grade_group_qt->weight;

                                if ($qt_weight_total > 0) {
                                    //lấy ds nhóm đầu điểm đánh giá quá trình
                                    $ds_grade_group_qt = Grade::select(['id', 'weight', 'grade_name'])
                                        ->where('grade_group_id', $grade_group_qt->id)
                                        ->orderBy('t7_grade.id')
                                        ->get();

                                    //lấy tất cả điểm quá trình sinh viên đạt được
                                    $result_course_grade_qt = CourseGrade::where('groupid', $group_id)
                                        ->where('login', $member->member_login)
                                        ->where('grade_group_id', $grade_group_qt->id)
                                        ->orderBy('grade_id')
                                        ->pluck('val', 'grade_id');

                                    //ktra điểm sinh viên đạt được vs điểm quá trình
                                    $qt_score_total = 0;

                                    foreach ($ds_grade_group_qt as $item) {
                                        if (isset($result_course_grade_qt[$item->id])) {
                                            //tính điểm quá trình cho sv
                                            $qt_detail_score = ($item->weight / $qt_weight_total) * $result_course_grade_qt[$item->id];
                                            $qt_score_total += $qt_detail_score;
                                        }
                                    }

                                    $qt_score_total_check = round($qt_score_total, 1);

                                    if ($qt_score_total_check < $syllabus_minimum_required) {
                                        $message_reason = "Điểm quá trình: " . $qt_score_total_check . " < " . $syllabus_minimum_required . " - Chưa làm tròn: " . $qt_score_total;
                                        array_push($ds_danger_point, $message_reason);
                                    }
                                }
                            }
                        } */
                            
                        // Lấy danh sách đầu điểm cần check 
                        if (!isset($listGradeGroupChecks[$group->syllabus_id])) {
                            $listGradeGroupChecks[$group->syllabus_id] = GradeGroup::query()
                            ->whereRaw("syllabus_id = ? AND Id NOT IN (
                                SELECT grade_group_id 
                                FROM t7_grade WHERE syllabus_id = ?
                                AND (
                                    is_final_exam = 1	
                                    OR t7_grade.bonus_type = 1
                                )
                            )", [$group->syllabus_id, $group->syllabus_id])->with('grades')->get();
                        }

                        $listGradeGroupCheck = $listGradeGroupChecks[$group->syllabus_id];
                        // Duyệt nhóm đầu điểm
                        foreach ($listGradeGroupCheck as $GradeGroup) {
                            //lấy ds nhóm đầu điểm của nhóm đầu điểm
                            $listGradeByGroup = $GradeGroup->grades;

                            //lấy tất cả điểm quá trình sinh viên đạt được
                            $listStudentGrade = $listStudentGrades->where('login', $member->member_login)
                            ->where('grade_group_id', $GradeGroup->id);
                            // $listStudentGrade = CourseGrade::select([
                            //         'id',
                            //         'val', 
                            //         'grade_id',
                            //     ])
                            //     ->where('groupid', $group_id)
                            //     ->where('login', $member->member_login)
                            //     ->where('grade_group_id', $GradeGroup->id)
                            //     ->orderBy('grade_id')
                            //     ->get();
                            
                            $listStudentGradeArr = $listStudentGrade->pluck('val', 'grade_id');
                            $groupGradeTotalPoint = 0;
                            // Kiểm tra nhóm điểm
                            foreach ($listGradeByGroup as $item) {
                                if (isset($listStudentGradeArr[$item->id])) {
                                    if ($listStudentGradeArr[$item->id] < $item->minimum_required) {
                                        $message_reason = "Đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]: " . $listStudentGradeArr[$item->id] . " < " . $item->minimum_required . " - Chưa làm tròn: " . $listStudentGradeArr[$item->id];
                                        array_push($ds_danger_point, $message_reason);
                                    }

                                    //tính điểm theo nhóm cho sv
                                    if ($GradeGroup->weight > 0) {
                                        $qt_detail_score = ($item->weight / $GradeGroup->weight) * $listStudentGradeArr[$item->id];
                                        $groupGradeTotalPoint += $qt_detail_score;
                                    }
                                }
                            }

                            // Làm tròn nhóm đầu điểm để xét điều kiện
                            $groupGradeTotalPointCheck = round($groupGradeTotalPoint, 1);
                            if ($groupGradeTotalPointCheck < $GradeGroup->minimum_required) {
                                $message_reason = "Nhóm điểm [$GradeGroup->grade_group_name]: " . $groupGradeTotalPointCheck . " < " . $GradeGroup->minimum_required . " - Chưa làm tròn: " . $groupGradeTotalPointCheck;
                                array_push($ds_danger_point, $message_reason);
                            }
                        }

                        /**
                         * 0:"Quiz online 1"
                         * 1:"Quiz online 2"
                         */
                        if ($ds_danger_point != null && count($ds_danger_point) > 0) {
                            $is_danger_point = true;
                            $danger_reason_point = "Cấm thi do điểm thành phần: " . implode(", ", $ds_danger_point);
                        }
                        $member->danger_reason_point = $danger_reason_point;

                        //lấy các thông tin sinh viên cần hiển thị
                        $member->group_name = $group->group_name;
                        $member->user = User::where('user_login', $member->member_login)->first();
                        $member->fullname = $member->user->fullname();
                        $member->user_code = $member->user->user_code;

                        //lý do cấm thi
                        if ($is_danger_absent && $is_danger_point) {
                            //cấm thi cả 2 nguyên nhân
                            $is_danger = -3;
                        } elseif ($is_danger_absent && !$is_danger_point) {
                            //cấm thi do trượt điểm danh
                            $is_danger = -1;
                        } elseif (!$is_danger_absent && $is_danger_point) {
                            //cấm thi do điểm thành phần
                            $is_danger = -2;
                        } else {
                            $is_danger = 0;
                        }
                        $member->is_danger = $is_danger;

                        if ($member->course_session > 0) {
                            //chuyển course_session từ 0 => 300
                            $course_session_300 = null;
                            if ($member->course_session == 1) {
                                $course_session_300 = $result_course_slot_bao_ve[0];
                            } elseif ($member->course_session == 2) {
                                $course_session_300 = $result_course_slot_bao_ve[1];
                            } elseif ($member->course_session == 3) {
                                $course_session_300 = $result_course_slot_bao_ve[2];
                            }

                            if (!(isset($slot_graduates[$group_id . "-" . $course_session_300]))) {
                                $slot_graduates[$group_id . "-" . $course_session_300] = Activity::select('slot', 'day', 'is_online', 'room_name', 'url_room_online')
                                ->with('slotDetail:id,slot_start,slot_end')
                                ->where('groupid', '=', $group_id)
                                // ->where('day', '=', $member->date_graduate)
                                ->where('course_slot', '=', $course_session_300)
                                ->first();
                            }

                            $slot_graduate = $slot_graduates[$group_id . "-" . $course_session_300];
                            $member->slot_graduate = $slot_graduate;
                        }

                        //lấy 2 đầu điểm Document & Presentation
                        if (!(isset($result_gradess[$group->syllabus_id]))) {
                            $result_gradess[$group->syllabus_id] = Grade::where('syllabus_id', $group->syllabus_id)
                                ->whereIn('t7_grade.grade_name', ['Document', 'Presentation'])
                                ->orderBy('t7_grade.id')
                                ->pluck('grade_name', 'id');
                        }

                        $result_grades = $result_gradess[$group->syllabus_id];

                        $result_course_grade = CourseGrade::where('groupid', $group_id)
                            ->where('login', $member->member_login)
                            ->orderBy('t7_course_grade.grade_id')
                            ->pluck('val', 'grade_id');

                        $ds_grades = [];
                        foreach ($result_grades as $key => $value) {
                            $ds_grades[$value] = isset($result_course_grade[$key]) ? $result_course_grade[$key] : null;
                        }

                        $member->grade_document = isset($ds_grades['Document']) ? $ds_grades['Document'] : null;
                        $member->grade_presentation = isset($ds_grades['Presentation']) ? $ds_grades['Presentation'] : null;

                        //thêm sinh viên đủ điều kiện thi vào ds tổng
                        // array_push($ds_members_total, $member);
                    }
                } else {
                    $result_subject_type = Subject::on('ho')->select(['subject_type'])
                        ->where('subject_code', $group->psubject_code)
                        ->first();

                    /**
                     * ktra có phải là môn online hay ko?
                     *
                     * môn blended
                     * + phần trăm đi học
                     * + điểm thành phần
                     *
                     * môn truyền thống
                     *  + phần trăm đi học
                     *
                     * môn online:
                     *  + phần trăm đi học => chỉ tính buổi remote
                     *  + điểm thành phần
                     */
                    if ($result_subject_type == null || (!Str::contains(Str::upper($result_subject_type->subject_type), 'ONLINE'))) {
                        /**
                         * môn bình thường
                         *
                         * ktra số buổi sinh viên được điểm danh bởi giảng viên
                         */
                        $ds_activities = Activity::where('groupid', $group_id)
                            ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
                            ->where('session_type.is_exam', 0)
                            ->orderBy('course_slot')
                            ->pluck('day');

                        //+ điều kiện: chỉ export được ds thi => lớp môn đó đã điểm danh tất cả các buổi học?
                        $ds_check_member_attendance = GroupMember::where('groupid', $group_id)
                            ->pluck('member_login');
                        $ds_teacher_attendance = Attendance::select(DB::raw("GROUP_CONCAT( day SEPARATOR ',' ) AS attendance_date, user_login"))
                            ->where('groupid', '=', $group_id)
                            ->whereIn('day', $ds_activities)
                            ->whereIn('user_login', $ds_check_member_attendance)
                            ->groupBy('user_login')
                            ->get();

                        if ($ds_teacher_attendance == null || count($ds_teacher_attendance) != $total_member) {
                            return $this->redirectWithStatus('danger', 'Không thể xuất file excel. Vui lòng điểm danh tất cả các buổi học.', url()->previous());
                        }

                        foreach ($ds_teacher_attendance as $value) {
                            $ds_student_attendance_date = explode(',', $value->attendance_date);
                            if ($ds_student_attendance_date != null) {
                                foreach ($ds_activities as $check_attendance_date) {
                                    if (!in_array($check_attendance_date, $ds_student_attendance_date)) {
                                        return $this->redirectWithStatus('danger', 'Không thể xuất file excel. Vui lòng điểm danh tất cả các buổi học. Môn học: Bình thường. Student: ' . $value->user_login . '. Attendance miss: ' . $check_attendance_date, url()->previous());
                                        break;
                                    }
                                }
                            } else {
                                return $this->redirectWithStatus('danger', 'Không thể xuất file excel. Vui lòng điểm danh tất cả các buổi học. Môn học: Bình thường. Student: ' . $value->user_login . ' khong co du lieu diem danh', url()->previous());
                                break;
                            }
                        }

                        /**
                         * môn truyền thống
                         */
                        $listGradeGroupChecks = [];
                        $result_attendances = Attendance::where('groupid', $group_id)
                            ->whereIn('day', $ds_activities)
                            ->get();
                        $listStudentGrades = CourseGrade::select([
                            'id',
                            'val', 
                            'grade_id',
                            'groupid',
                            'login',
                            'grade_group_id'
                        ])
                        ->where('groupid', $group_id)
                        ->orderBy('grade_id')
                        ->get();
                        $course_results = CourseResult::where('groupid', $group_id)->get();
                        if (!Str::contains(Str::upper($result_subject_type->subject_type), 'BLENDED')) {
                            foreach ($members as $key => $member) {
                                $is_danger_absent = false;
                                $is_danger_point = false;
                                $danger_reason_point = "";
                                $danger_reason_absent = "";
                                $is_danger = 0;

                                $result_attendance = $result_attendances->where('user_login', $member->member_login);

                                $absent = 0;
                                $ds_danger_absent = [];
                                foreach ($result_attendance as $attendance) {
                                    if ($attendance->val === 0) {
                                        $absent += 1;
                                        array_push($ds_danger_absent, $attendance->day ?? "");
                                    }
                                }

                                $course_result = $course_results->where('student_login', $member->member_login)->first();
                                // $course_result = CourseResult::where('student_login', $member->member_login)->where('groupid', $group_id)->first();
                                $absent_percent = isset($course_result->total_session) && $course_result->total_session > 0 ? round(($absent * 100) / $course_result->total_session) : 0;
                                if ($absent_percent > 100 - $syllabus_attendance_cutoff) {
                                    //cấm thi
                                    $is_danger_absent = true;
                                }

                                if ($is_danger_absent) {
                                    $danger_reason_absent = "Cấm thi do trượt điểm danh: " . implode(", ", $ds_danger_absent);
                                }
                                $member->danger_reason_absent = $danger_reason_absent;

                                //3. ktra điểm tối thiểu đánh giá quá trình
                                // dd(__LINE__);
                                $ds_danger_point = [];
                                /* if ($syllabus_minimum_required > 0) {
                                    //lấy điểm đánh giá quá trình
                                    $grade_group_qt = GradeGroup::where('syllabus_id', $group->syllabus_id)
                                        ->where('grade_group_name', 'LIKE', '%Đánh giá quá trình%')
                                        ->first();

                                    if ($grade_group_qt != null && $grade_group_qt->id > 0) {
                                        //trọng số tổng của điểm đánh giá quá trình
                                        $qt_weight_total = $grade_group_qt->weight;

                                        if ($qt_weight_total > 0) {
                                            //lấy ds nhóm đầu điểm đánh giá quá trình
                                            $ds_grade_group_qt = Grade::select(['id', 'weight', 'grade_name'])
                                                ->where('grade_group_id', $grade_group_qt->id)
                                                ->orderBy('t7_grade.id')
                                                ->get();

                                            //lấy tất cả điểm quá trình sinh viên đạt được
                                            $result_course_grade_qt = CourseGrade::where('groupid', $group_id)
                                                ->where('login', $member->member_login)
                                                ->where('grade_group_id', $grade_group_qt->id)
                                                ->orderBy('grade_id')
                                                ->pluck('val', 'grade_id');

                                            //ktra điểm sinh viên đạt được vs điểm quá trình
                                            $qt_score_total = 0;

                                            foreach ($ds_grade_group_qt as $item) {
                                                if (isset($result_course_grade_qt[$item->id])) {
                                                    //tính điểm quá trình cho sv
                                                    $qt_detail_score = ($item->weight / $qt_weight_total) * $result_course_grade_qt[$item->id];
                                                    $qt_score_total += $qt_detail_score;
                                                }
                                            }

                                            $qt_score_total_check = round($qt_score_total, 1);

                                            if ($qt_score_total_check < $syllabus_minimum_required) {
                                                $message_reason = "Điểm quá trình: " . $qt_score_total_check . " < " . $syllabus_minimum_required . " - Chưa làm tròn: " . $qt_score_total;
                                                array_push($ds_danger_point, $message_reason);
                                            }
                                        }
                                    }
                                } */
                            
                                // Lấy danh sách đầu điểm cần check 
                                if (!isset($listGradeGroupChecks[$group->syllabus_id])) {
                                    $listGradeGroupChecks[$group->syllabus_id] = GradeGroup::query()
                                    ->whereRaw("syllabus_id = ? AND Id NOT IN (
                                        SELECT grade_group_id 
                                        FROM t7_grade WHERE syllabus_id = ?
                                        AND (
                                            is_final_exam = 1	
                                            OR t7_grade.bonus_type = 1
                                        )
                                    )", [$group->syllabus_id, $group->syllabus_id])->with('grades')->get();
                                }

                                $listGradeGroupCheck = $listGradeGroupChecks[$group->syllabus_id];
                                // Duyệt nhóm đầu điểm
                                foreach ($listGradeGroupCheck as $GradeGroup) {
                                    //lấy ds nhóm đầu điểm của nhóm đầu điểm
                                    $listGradeByGroup = $GradeGroup->grades;

                                    //lấy tất cả điểm quá trình sinh viên đạt được
                                    $listStudentGrade = $listStudentGrades->where('login', $member->member_login)
                                    ->where('grade_group_id', $GradeGroup->id);
                                    // $listStudentGrade = CourseGrade::select([
                                    //         'id',
                                    //         'val', 
                                    //         'grade_id',
                                    //     ])
                                    //     ->where('groupid', $group_id)
                                    //     ->where('login', $member->member_login)
                                    //     ->where('grade_group_id', $GradeGroup->id)
                                    //     ->orderBy('grade_id')
                                    //     ->get();
                                    
                                    $listStudentGradeArr = $listStudentGrade->pluck('val', 'grade_id');
                                    $groupGradeTotalPoint = 0;
                                    // Kiểm tra nhóm điểm
                                    foreach ($listGradeByGroup as $item) {
                                        if (isset($listStudentGradeArr[$item->id])) {
                                            if ($listStudentGradeArr[$item->id] < $item->minimum_required) {
                                                $message_reason = "Đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]: " . $listStudentGradeArr[$item->id] . " < " . $item->minimum_required . " - Chưa làm tròn: " . $listStudentGradeArr[$item->id];
                                                array_push($ds_danger_point, $message_reason);
                                            }

                                            //tính điểm theo nhóm cho sv
                                            if ($GradeGroup->weight > 0) {
                                                $qt_detail_score = ($item->weight / $GradeGroup->weight) * $listStudentGradeArr[$item->id];
                                                $groupGradeTotalPoint += $qt_detail_score;
                                            }
                                        }
                                    }

                                    // Làm tròn nhóm đầu điểm để xét điều kiện
                                    $groupGradeTotalPointCheck = round($groupGradeTotalPoint, 1);
                                    if ($groupGradeTotalPointCheck < $GradeGroup->minimum_required) {
                                        $message_reason = "Nhóm điểm [$GradeGroup->grade_group_name]: " . $groupGradeTotalPointCheck . " < " . $GradeGroup->minimum_required . " - Chưa làm tròn: " . $groupGradeTotalPointCheck;
                                        array_push($ds_danger_point, $message_reason);
                                    }
                                }

                                /**
                                 * 0:"Quiz online 1"
                                 * 1:"Quiz online 2"
                                 */
                                if ($ds_danger_point != null && count($ds_danger_point) > 0) {
                                    $is_danger_point = true;
                                    $danger_reason_point = "Cấm thi do điểm thành phần: " . implode(", ", $ds_danger_point);
                                }
                                $member->danger_reason_point = $danger_reason_point;

                                //lý do cấm thi
                                if ($is_danger_absent && $is_danger_point) {
                                    //cấm thi cả 2 nguyên nhân
                                    $is_danger = -3;
                                } elseif ($is_danger_absent && !$is_danger_point) {
                                    //cấm thi do trượt điểm danh
                                    $is_danger = -1;
                                } elseif (!$is_danger_absent && $is_danger_point) {
                                    //cấm thi do điểm thành phần
                                    $is_danger = -2;
                                } else {
                                    $is_danger = 0;
                                }

                                $member->is_danger = $is_danger;
                                $member->danger_reason_point = $danger_reason_point;

                                $member->group_name = $group->group_name;
                                $member->user = User::where('user_login', $member->member_login)->first();
                                $member->fullname = $member->user->fullname();
                                $member->user_code = $member->user->user_code;

                                if ($member->course_session > 0) {
                                    //chuyển course_session từ 0 => 300
                                    $course_session_300 = null;
                                    if ($member->course_session == 1) {
                                        $course_session_300 = $result_course_slot_bao_ve[0];
                                    } elseif ($member->course_session == 2) {
                                        $course_session_300 = $result_course_slot_bao_ve[1];
                                    } elseif ($member->course_session == 3) {
                                        $course_session_300 = $result_course_slot_bao_ve[2];
                                    }

                                    if (!(isset($slot_graduates[$group_id . "-" . $course_session_300]))) {
                                        $slot_graduates[$group_id . "-" . $course_session_300] = Activity::select('slot', 'day', 'is_online', 'room_name', 'url_room_online')
                                        ->with('slotDetail:id,slot_start,slot_end')
                                        ->where('groupid', '=', $group_id)
                                        // ->where('day', '=', $member->date_graduate)
                                        ->where('course_slot', '=', $course_session_300)
                                        ->first();
                                    }

                                    $slot_graduate = $slot_graduates[$group_id . "-" . $course_session_300];
                                    $member->slot_graduate = $slot_graduate;
                                }

                                //lấy 2 đầu điểm Document & Presentation
                                if (!(isset($result_gradess[$group->syllabus_id]))) {
                                    $result_gradess[$group->syllabus_id] = Grade::where('syllabus_id', $group->syllabus_id)
                                        ->whereIn('t7_grade.grade_name', ['Document', 'Presentation'])
                                        ->orderBy('t7_grade.id')
                                        ->pluck('grade_name', 'id');
                                }

                                $result_grades = $result_gradess[$group->syllabus_id];
                                $result_course_grade = CourseGrade::where('groupid', $group_id)
                                    ->where('login', $member->member_login)
                                    ->orderBy('t7_course_grade.grade_id')
                                    ->pluck('val', 'grade_id');

                                $ds_grades = [];
                                foreach ($result_grades as $key => $value) {
                                    $ds_grades[$value] = isset($result_course_grade[$key]) ? $result_course_grade[$key] : null;
                                }

                                $member->grade_document = isset($ds_grades['Document']) ? $ds_grades['Document'] : null;
                                $member->grade_presentation = isset($ds_grades['Presentation']) ? $ds_grades['Presentation'] : null;

                                //thêm sinh viên đủ điều kiện thi vào ds tổng
                                // array_push($ds_members_total, $member);
                            }
                        } else {
                            /**
                             * môn blended
                             */
                            $listGradeGroupCheck = [];
                            $result_attendances = Attendance::where('groupid', $group_id)
                                ->whereIn('day', $ds_activities)
                                ->get();
                            $course_results = CourseResult::where('groupid', $group_id)->get();
                            foreach ($members as $key => $member) {
                                $is_danger_absent = false;
                                $is_danger_point = false;
                                $danger_reason_absent = "";
                                $danger_reason_point = "";
                                $is_danger = 0;

                                $result_attendance = $result_attendances->where('user_login', $member->member_login);

                                $absent = 0;
                                $ds_danger_absent = [];
                                foreach ($result_attendance as $attendance) {
                                    if ($attendance->val === 0) {
                                        $absent += 1;
                                        array_push($ds_danger_absent, $attendance->day);
                                    }
                                }

                                $course_result = $course_results->where('student_login', $member->member_login)->first();
                                // $course_result = CourseResult::where('student_login', $member->member_login)->where('groupid', $group_id)->first();
                                $absent_percent = isset($course_result->total_session) && $course_result->total_session > 0 ? round(($absent * 100) / $course_result->total_session) : 0;
                                if ($absent_percent > 100 - $syllabus_attendance_cutoff) {
                                    //cấm thi
                                    $is_danger_absent = true;
                                }

                                if ($is_danger_absent) {
                                    $danger_reason_absent = "Cấm thi do trượt điểm danh: " . implode(", ", $ds_danger_absent);
                                }
                                $member->danger_reason_absent = $danger_reason_absent;

                                /**
                                 * 2. ktra điểm thành phần
                                 */
                                //lấy nhóm đầu điểm thành phần
                                $result_grade_group = GradeGroup::select(['id', 'grade_group_name', 'weight', 'minimum_required'])
                                    ->where('syllabus_id', '=', $group->syllabus_id)
                                    ->pluck('id');

                                //lấy tất cả các đầu điểm thành phần theo nhóm đầu điểm
                                $result_grade_group_detail = Grade::select(['id', 'minimum_required', 'grade_name'])
                                    ->where('is_final_exam', '=', 0) //trừ môn được đào tạo đánh dấu là kết thúc
                                    ->whereIn('t7_grade.grade_group_id', $result_grade_group)
                                    ->orderBy('t7_grade.id')
                                    ->get();

                                //lấy tất cả điểm sinh viên đạt được
                                $result_course_grade = CourseGrade::where('groupid', $group_id)
                                    ->where('login', $member->member_login)
                                    ->orderBy('t7_course_grade.grade_id')
                                    ->pluck('val', 'grade_id');

                                //ktra điểm sinh viên đạt được vs điểm thành phần
                                $ds_danger_point = [];
                                foreach ($result_grade_group_detail as $item) {
                                    if (isset($result_course_grade[$item->id])) {
                                        if ($item->minimum_required > $result_course_grade[$item->id]) {
                                            array_push($ds_danger_point, $item->grade_name);
                                        }
                                    } else {
                                        array_push($ds_danger_point, $item->grade_name);
                                    }
                                }

                                //3. ktra điểm tối thiểu đánh giá quá trình
                                /*if ($syllabus_minimum_required > 0) {
                                    //lấy điểm đánh giá quá trình
                                    $grade_group_qt = GradeGroup::where('syllabus_id', $group->syllabus_id)
                                        ->where('grade_group_name', 'LIKE', '%Đánh giá quá trình%')
                                        ->first();

                                    if ($grade_group_qt != null && $grade_group_qt->id > 0) {
                                        //trọng số tổng của điểm đánh giá quá trình
                                        $qt_weight_total = $grade_group_qt->weight;

                                        if ($qt_weight_total > 0) {
                                            //lấy ds nhóm đầu điểm đánh giá quá trình
                                            $ds_grade_group_qt = Grade::select(['id', 'weight', 'grade_name'])
                                                ->where('grade_group_id', $grade_group_qt->id)
                                                ->orderBy('t7_grade.id')
                                                ->get();

                                            //lấy tất cả điểm quá trình sinh viên đạt được
                                            $result_course_grade_qt = CourseGrade::where('groupid', $group_id)
                                                ->where('login', $member->member_login)
                                                ->where('grade_group_id', $grade_group_qt->id)
                                                ->orderBy('grade_id')
                                                ->pluck('val', 'grade_id');

                                            //ktra điểm sinh viên đạt được vs điểm quá trình
                                            $qt_score_total = 0;

                                            foreach ($ds_grade_group_qt as $item) {
                                                if (isset($result_course_grade_qt[$item->id])) {
                                                    //tính điểm quá trình cho sv
                                                    $qt_detail_score = ($item->weight / $qt_weight_total) * $result_course_grade_qt[$item->id];
                                                    $qt_score_total += $qt_detail_score;
                                                }
                                            }

                                            $qt_score_total_check = round($qt_score_total, 1);
                                            if ($qt_score_total_check < $syllabus_minimum_required) {
                                                $message_reason = "Điểm quá trình: " . $qt_score_total_check . " < " . $syllabus_minimum_required . " - Chưa làm tròn: " . $qt_score_total;
                                                array_push($ds_danger_point, $message_reason);
                                            }
                                        }
                                    }
                                }*/
                            
                                // Lấy danh sách đầu điểm cần check 
                                if (!isset($listGradeGroupChecks[$group->syllabus_id])) {
                                    $listGradeGroupChecks[$group->syllabus_id] = GradeGroup::query()
                                    ->whereRaw("syllabus_id = ? AND Id NOT IN (
                                        SELECT grade_group_id 
                                        FROM t7_grade WHERE syllabus_id = ?
                                        AND (
                                            is_final_exam = 1	
                                            OR t7_grade.bonus_type = 1
                                        )
                                    )", [$group->syllabus_id, $group->syllabus_id])->with('grades')->get();
                                }

                                $listGradeGroupCheck = $listGradeGroupChecks[$group->syllabus_id];
                                // Duyệt nhóm đầu điểm
                                foreach ($listGradeGroupCheck as $GradeGroup) {
                                    //lấy ds nhóm đầu điểm của nhóm đầu điểm
                                    $listGradeByGroup = $GradeGroup->grades;

                                    //lấy tất cả điểm quá trình sinh viên đạt được
                                    $listStudentGrade = $listStudentGrades->where('login', $member->member_login)
                                    ->where('grade_group_id', $GradeGroup->id);
                                    // $listStudentGrade = CourseGrade::select([
                                    //         'id',
                                    //         'val', 
                                    //         'grade_id',
                                    //     ])
                                    //     ->where('groupid', $group_id)
                                    //     ->where('login', $member->member_login)
                                    //     ->where('grade_group_id', $GradeGroup->id)
                                    //     ->orderBy('grade_id')
                                    //     ->get();
                                    
                                    $listStudentGradeArr = $listStudentGrade->pluck('val', 'grade_id');
                                    $groupGradeTotalPoint = 0;
                                    // Kiểm tra nhóm điểm
                                    foreach ($listGradeByGroup as $item) {
                                        if (isset($listStudentGradeArr[$item->id])) {
                                            if ($listStudentGradeArr[$item->id] < $item->minimum_required) {
                                                $message_reason = "Đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]: " . $listStudentGradeArr[$item->id] . " < " . $item->minimum_required . " - Chưa làm tròn: " . $listStudentGradeArr[$item->id];
                                                array_push($ds_danger_point, $message_reason);
                                            }

                                            //tính điểm theo nhóm cho sv
                                            if ($GradeGroup->weight > 0) {
                                                $qt_detail_score = ($item->weight / $GradeGroup->weight) * $listStudentGradeArr[$item->id];
                                                $groupGradeTotalPoint += $qt_detail_score;
                                            }
                                        }
                                    }

                                    // Làm tròn nhóm đầu điểm để xét điều kiện
                                    $groupGradeTotalPointCheck = round($groupGradeTotalPoint, 1);
                                    if ($groupGradeTotalPointCheck < $GradeGroup->minimum_required) {
                                        $message_reason = "Nhóm điểm [$GradeGroup->grade_group_name]: " . $groupGradeTotalPointCheck . " < " . $GradeGroup->minimum_required . " - Chưa làm tròn: " . $groupGradeTotalPointCheck;
                                        array_push($ds_danger_point, $message_reason);
                                    }
                                }

                                if ($ds_danger_point != null && count($ds_danger_point) > 0) {
                                    $is_danger_point = true;
                                    $danger_reason_point = "Cấm thi do điểm thành phần: " . implode(", ", $ds_danger_point);
                                }
                                $member->danger_reason_point = $danger_reason_point;

                                //lý do cấm thi
                                if ($is_danger_absent && $is_danger_point) {
                                    //cấm thi cả 2 nguyên nhân
                                    $is_danger = -3;
                                } elseif ($is_danger_absent && !$is_danger_point) {
                                    //cấm thi do trượt điểm danh
                                    $is_danger = -1;
                                } elseif (!$is_danger_absent && $is_danger_point) {
                                    //cấm thi do điểm thành phần
                                    $is_danger = -2;
                                } else {
                                    $is_danger = 0;
                                }
                                $member->is_danger = $is_danger;

                                $member->group_name = $group->group_name;
                                $member->user = User::where('user_login', $member->member_login)->first();
                                $member->fullname = $member->user->fullname();
                                $member->user_code = $member->user->user_code;

                                if ($member->course_session > 0) {
                                    //chuyển course_session từ 0 => 300
                                    $course_session_300 = null;
                                    if ($member->course_session == 1) {
                                        $course_session_300 = $result_course_slot_bao_ve[0];
                                    } elseif ($member->course_session == 2) {
                                        $course_session_300 = $result_course_slot_bao_ve[1];
                                    } elseif ($member->course_session == 3) {
                                        $course_session_300 = $result_course_slot_bao_ve[2];
                                    }

                                    if (!(isset($slot_graduates[$group_id . "-" . $course_session_300]))) {
                                        $slot_graduates[$group_id . "-" . $course_session_300] = Activity::select('slot', 'day', 'is_online', 'room_name', 'url_room_online')
                                        ->with('slotDetail:id,slot_start,slot_end')
                                        ->where('groupid', '=', $group_id)
                                        // ->where('day', '=', $member->date_graduate)
                                        ->where('course_slot', '=', $course_session_300)
                                        ->first();
                                    }

                                    $slot_graduate = $slot_graduates[$group_id . "-" . $course_session_300];
                                    $member->slot_graduate = $slot_graduate;
                                }

                                //lấy 2 đầu điểm Document & Presentation
                                if (!(isset($result_gradess[$group->syllabus_id]))) {
                                    $result_gradess[$group->syllabus_id] = Grade::where('syllabus_id', $group->syllabus_id)
                                        ->whereIn('t7_grade.grade_name', ['Document', 'Presentation'])
                                        ->orderBy('t7_grade.id')
                                        ->pluck('grade_name', 'id');
                                }

                                $result_grades = $result_gradess[$group->syllabus_id];

                                $result_course_grade = CourseGrade::where('groupid', $group_id)
                                    ->where('login', $member->member_login)
                                    ->orderBy('t7_course_grade.grade_id')
                                    ->pluck('val', 'grade_id');

                                $ds_grades = [];
                                foreach ($result_grades as $key => $value) {
                                    $ds_grades[$value] = isset($result_course_grade[$key]) ? $result_course_grade[$key] : null;
                                }

                                $member->grade_document = isset($ds_grades['Document']) ? $ds_grades['Document'] : null;
                                $member->grade_presentation = isset($ds_grades['Presentation']) ? $ds_grades['Presentation'] : null;
                                //thêm sinh viên đủ điều kiện thi vào ds tổng
                                // array_push($ds_members_total, $member);
                            }
                        }
                    } else {
                        /**
                         * môn online
                         *
                         * lấy số buổi sinh viên được tính điểm danh => buổi remote + ko tính những buổi thi
                         */
                        $ds_syllabus_attendance = SyllabusPlan::select('course_session')
                            ->leftJoin('session_type', 't7_syllabus_plan.session_type', '=', 'session_type.id')
                            ->where('t7_syllabus_plan.syllabus_id', $group->syllabus_id)
                            ->where('session_type.is_exam', 0)
                            ->pluck('t7_syllabus_plan.course_session');

                        $ds_activities = Activity::where('groupid', $group_id)
                            ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
                            ->where('session_type.is_exam', 0)
                            ->orderBy('activity.course_slot')
                            ->pluck('activity.day');

                        //+ điều kiện: chỉ export được ds thi => lớp môn đó đã điểm danh tất cả các buổi học?
                        $ds_check_member_attendance = GroupMember::where('groupid', $group_id)
                            ->pluck('member_login');
                        $ds_teacher_attendance = Attendance::select(DB::raw("GROUP_CONCAT( day SEPARATOR ',' ) AS attendance_date, user_login"))
                            ->where('groupid', '=', $group_id)
                            ->whereIn('day', $ds_activities)
                            ->whereIn('user_login', $ds_check_member_attendance)
                            ->groupBy('user_login')
                            ->get();

                        if ($ds_teacher_attendance == null || count($ds_teacher_attendance) != $total_member) {
                            return $this->redirectWithStatus('danger', 'Không thể xuất file excel. Vui lòng điểm danh tất cả các buổi học.', url()->previous());
                        }

                        foreach ($ds_teacher_attendance as $value) {
                            $ds_student_attendance_date = explode(',', $value->attendance_date);
                            if ($ds_student_attendance_date != null) {
                                foreach ($ds_activities as $check_attendance_date) {
                                    if (!in_array($check_attendance_date, $ds_student_attendance_date)) {
                                        return $this->redirectWithStatus('danger', 'Không thể xuất file excel. Vui lòng điểm danh tất cả các buổi học. Môn học: Online. Student: ' . $value->user_login . '. Attendance miss: ' . $check_attendance_date, url()->previous());
                                        break;
                                    }
                                }
                            } else {
                                return $this->redirectWithStatus('danger', 'Không thể xuất file excel. Vui lòng điểm danh tất cả các buổi học. Môn học: Online. Student: ' . $value->user_login . ' khong co du lieu diem danh', url()->previous());
                                break;
                            }
                        }

                        $listGradeGroupChecks = []; 
                        $result_attendances = Attendance::where('groupid', $group_id)
                            ->whereIn('day', $ds_activities)
                            ->get();
                        $listStudentGrades = CourseGrade::select([
                            'id',
                            'val', 
                            'grade_id',
                            'groupid',
                            'login',
                            'grade_group_id'
                        ])
                        ->where('groupid', $group_id)
                        ->orderBy('grade_id')
                        ->get();
                        foreach ($members as $key => $member) {
                            $is_danger_absent = false;
                            $is_danger_point = false;
                            $danger_reason_point = "";
                            $danger_reason_absent = "";
                            $is_danger = 0;

                            /**
                             * 1. ktra số buổi sinh viên được điểm danh bởi giảng viên
                             */
                            $result_attendance = $result_attendances->where('user_login', $member->member_login);

                            $absent = 0;
                            $ds_danger_absent = [];
                            foreach ($result_attendance as $attendance) {
                                if ($attendance->val === 0) {
                                    $absent += 1;
                                    array_push($ds_danger_absent, $attendance->day);
                                }
                            }

                            $total_remote = count($ds_syllabus_attendance);
                            $absent_percent = $total_remote > 0 ? round(($absent * 100) / $total_remote) : 0;
                            if ($absent_percent > 100 - $syllabus_attendance_cutoff) {
                                //cấm thi
                                $is_danger_absent = true;
                            }

                            if ($is_danger_absent) {
                                $danger_reason_absent = "Cấm thi do trượt điểm danh: " . implode(", ", $ds_danger_absent);
                            }
                            $member->danger_reason_absent = $danger_reason_absent;

                            /**
                             * 2. ktra điểm thành phần
                             */
                            //lấy nhóm đầu điểm thành phần
                            $result_grade_group = GradeGroup::select(['id', 'grade_group_name', 'weight', 'minimum_required'])
                                ->where('syllabus_id', '=', $group->syllabus_id)
                                ->pluck('id');

                            //lấy tất cả các đầu điểm thành phần theo nhóm đầu điểm
                            $result_grade_group_detail = Grade::select(['id', 'minimum_required', 'grade_name'])
                                ->where('is_final_exam', '=', 0) //trừ môn được đào tạo đánh dấu là kết thúc
                                ->whereIn('t7_grade.grade_group_id', $result_grade_group)
                                ->orderBy('t7_grade.id')
                                ->get();

                            //lấy tất cả điểm sinh viên đạt được
                            $result_course_grade = CourseGrade::where('groupid', $group_id)
                                ->where('login', $member->member_login)
                                ->orderBy('t7_course_grade.grade_id')
                                ->pluck('val', 'grade_id');

                            //ktra điểm sinh viên đạt được vs điểm thành phần
                            $ds_danger_point = [];
                            foreach ($result_grade_group_detail as $item) {
                                if (isset($result_course_grade[$item->id])) {
                                    if ($item->minimum_required > $result_course_grade[$item->id]) {
                                        array_push($ds_danger_point, $item->grade_name);
                                    }
                                } else {
                                    array_push($ds_danger_point, $item->grade_name);
                                }
                            }

                            //3. ktra điểm tối thiểu đánh giá quá trình
                            //lấy điểm đánh giá quá trình
                            /* if ($syllabus_minimum_required > 0) {
                                $grade_group_qt = GradeGroup::where('syllabus_id', $group->syllabus_id)
                                    ->where('grade_group_name', 'LIKE', '%Đánh giá quá trình%')
                                    ->first();

                                if ($grade_group_qt != null && $grade_group_qt->id > 0) {
                                    //trọng số tổng của điểm đánh giá quá trình
                                    $qt_weight_total = $grade_group_qt->weight;

                                    if ($qt_weight_total > 0) {
                                        //lấy ds nhóm đầu điểm đánh giá quá trình
                                        $ds_grade_group_qt = Grade::select(['id', 'weight', 'grade_name'])
                                            ->where('grade_group_id', $grade_group_qt->id)
                                            ->orderBy('t7_grade.id')
                                            ->get();

                                        //lấy tất cả điểm quá trình sinh viên đạt được
                                        $result_course_grade_qt = CourseGrade::where('groupid', $group_id)
                                            ->where('login', $member->member_login)
                                            ->where('grade_group_id', $grade_group_qt->id)
                                            ->orderBy('grade_id')
                                            ->pluck('val', 'grade_id');

                                        //ktra điểm sinh viên đạt được vs điểm quá trình
                                        $qt_score_total = 0;

                                        foreach ($ds_grade_group_qt as $item) {
                                            if (isset($result_course_grade_qt[$item->id])) {
                                                //tính điểm quá trình cho sv
                                                $qt_detail_score = ($item->weight / $qt_weight_total) * $result_course_grade_qt[$item->id];
                                                $qt_score_total += $qt_detail_score;
                                            }
                                        }

                                        $qt_score_total_check = round($qt_score_total, 1);

                                        if ($qt_score_total_check < $syllabus_minimum_required) {
                                            $message_reason = "Điểm quá trình: " . $qt_score_total_check . " < " . $syllabus_minimum_required . " - Chưa làm tròn: " . $qt_score_total;
                                            array_push($ds_danger_point, $message_reason);
                                        }
                                    }
                                }
                            }*/
                            
                            // Lấy danh sách đầu điểm cần check 
                            if (!isset($listGradeGroupChecks[$group->syllabus_id])) {
                                $listGradeGroupChecks[$group->syllabus_id] = GradeGroup::query()
                                ->whereRaw("syllabus_id = ? AND Id NOT IN (
                                    SELECT grade_group_id 
                                    FROM t7_grade WHERE syllabus_id = ?
                                    AND (
                                        is_final_exam = 1	
                                        OR t7_grade.bonus_type = 1
                                    )
                                )", [$group->syllabus_id, $group->syllabus_id])->with('grades')->get();
                            }

                            $listGradeGroupCheck = $listGradeGroupChecks[$group->syllabus_id];
                            // Duyệt nhóm đầu điểm
                            foreach ($listGradeGroupCheck as $GradeGroup) {
                                //lấy ds nhóm đầu điểm của nhóm đầu điểm
                                $listGradeByGroup = $GradeGroup->grades;

                                //lấy tất cả điểm quá trình sinh viên đạt được
                                $listStudentGrade = $listStudentGrades->where('login', $member->member_login)
                                ->where('grade_group_id', $GradeGroup->id);
                                // $listStudentGrade = CourseGrade::select([
                                //         'id',
                                //         'val', 
                                //         'grade_id',
                                //     ])
                                //     ->where('groupid', $group_id)
                                //     ->where('login', $member->member_login)
                                //     ->where('grade_group_id', $GradeGroup->id)
                                //     ->orderBy('grade_id')
                                //     ->get();
                                
                                $listStudentGradeArr = $listStudentGrade->pluck('val', 'grade_id');
                                $groupGradeTotalPoint = 0;
                                // Kiểm tra nhóm điểm
                                foreach ($listGradeByGroup as $item) {
                                    if (isset($listStudentGradeArr[$item->id])) {
                                        if ($listStudentGradeArr[$item->id] < $item->minimum_required) {
                                            $message_reason = "Đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]: " . $listStudentGradeArr[$item->id] . " < " . $item->minimum_required . " - Chưa làm tròn: " . $listStudentGradeArr[$item->id];
                                            array_push($ds_danger_point, $message_reason);
                                        }

                                        //tính điểm theo nhóm cho sv
                                        if ($GradeGroup->weight > 0) {
                                            $qt_detail_score = ($item->weight / $GradeGroup->weight) * $listStudentGradeArr[$item->id];
                                            $groupGradeTotalPoint += $qt_detail_score;
                                        }
                                    }
                                }

                                // Làm tròn nhóm đầu điểm để xét điều kiện
                                $groupGradeTotalPointCheck = round($groupGradeTotalPoint, 1);
                                if ($groupGradeTotalPointCheck < $GradeGroup->minimum_required) {
                                    $message_reason = "Nhóm điểm [$GradeGroup->grade_group_name]: " . $groupGradeTotalPointCheck . " < " . $GradeGroup->minimum_required . " - Chưa làm tròn: " . $groupGradeTotalPointCheck;
                                    array_push($ds_danger_point, $message_reason);
                                }
                            }

                            //lấy các thông tin sinh viên cần hiển thị
                            if ($ds_danger_point != null && count($ds_danger_point) > 0) {
                                $is_danger_point = true;
                                $danger_reason_point = "Cấm thi do điểm thành phần: " . implode(", ", $ds_danger_point);
                            }

                            $member->danger_reason_point = $danger_reason_point;
                            //lý do cấm thi
                            if ($is_danger_absent && $is_danger_point) {
                                //cấm thi cả 2 nguyên nhân
                                $is_danger = -3;
                            } elseif ($is_danger_absent && !$is_danger_point) {
                                //cấm thi do trượt điểm danh
                                $is_danger = -1;
                            } elseif (!$is_danger_absent && $is_danger_point) {
                                //cấm thi do điểm thành phần
                                $is_danger = -2;
                            } else {
                                $is_danger = 0;
                            }

                            $member->group_name = $group->group_name;
                            $member->user = User::where('user_login', $member->member_login)->first();
                            $member->fullname = $member->user->fullname();
                            $member->user_code = $member->user->user_code;
                            $member->is_danger = $is_danger;

                            if ($member->course_session > 0) {
                                //chuyển course_session từ 0 => 300
                                $course_session_300 = null;
                                if ($member->course_session == 1) {
                                    $course_session_300 = $result_course_slot_bao_ve[0];
                                } elseif ($member->course_session == 2) {
                                    $course_session_300 = $result_course_slot_bao_ve[1];
                                } elseif ($member->course_session == 3) {
                                    $course_session_300 = $result_course_slot_bao_ve[2];
                                }

                                if (!(isset($slot_graduates[$group_id . "-" . $course_session_300]))) {
                                    $slot_graduates[$group_id . "-" . $course_session_300] = Activity::select('slot', 'day', 'is_online', 'room_name', 'url_room_online')
                                    ->with('slotDetail:id,slot_start,slot_end')
                                    ->where('groupid', '=', $group_id)
                                    // ->where('day', '=', $member->date_graduate)
                                    ->where('course_slot', '=', $course_session_300)
                                    ->first();
                                }

                                $slot_graduate = $slot_graduates[$group_id . "-" . $course_session_300];
                                $member->slot_graduate = $slot_graduate;
                            }

                            //lấy 2 đầu điểm Document & Presentation
                            if (!(isset($result_gradess[$group->syllabus_id]))) {
                                $result_gradess[$group->syllabus_id] = Grade::where('syllabus_id', $group->syllabus_id)
                                    ->whereIn('t7_grade.grade_name', ['Document', 'Presentation'])
                                    ->orderBy('t7_grade.id')
                                    ->pluck('grade_name', 'id');
                            }

                            $result_grades = $result_gradess[$group->syllabus_id];

                            $result_course_grade = CourseGrade::where('groupid', $group_id)
                                ->where('login', $member->member_login)
                                ->orderBy('t7_course_grade.grade_id')
                                ->pluck('val', 'grade_id');

                            $ds_grades = [];
                            foreach ($result_grades as $key => $value) {
                                $ds_grades[$value] = isset($result_course_grade[$key]) ? $result_course_grade[$key] : null;
                            }

                            $member->grade_document = isset($ds_grades['Document']) ? $ds_grades['Document'] : null;
                            $member->grade_presentation = isset($ds_grades['Presentation']) ? $ds_grades['Presentation'] : null;
                            //thêm sinh viên đủ điều kiện thi vào ds tổng
                            // array_push($ds_members_total, $member);
                        }
                    }
                }

                $block = null;
                if ($block_search == null) {
                    $block = Block::where('id', $group->block_id)->first();
                } else {
                    $block = $block_search;
                }

                $excel_info = [
                    'term' => $term,
                    'block' => $block,
                    'group' => $group,
                ];

                $ds_lich_thi_theo_lop_mon = [];
                $item = [
                    'is_total' => true,
                    'ds_slot_assignment' => $ds_slot_assignment,
                    'ds_members_total' => $members->sortBy('user_code')->values()->all(),
                ];
                array_push($ds_lich_thi_theo_lop_mon, $item);

                $item = [
                    'is_total' => false,
                    'ds_slot_assignment' => $ds_slot_assignment,
                    'ds_members_total' => $members->sortBy('user_code')->values()->all(),
                ];
                array_push($ds_lich_thi_theo_lop_mon, $item);

                // Log::error("Dev1.2: " . time());
                $file_name = now()->format('d_m_y_h_i_s_') . 'lich_thi_theo_lop_mon_' . ($group->group_name) . '.xlsx';
                // $export = new LichThiTheoMonMultipleExport($ds_slot_assignment, $ds_members_total, $excel_info);
                // Log::error("Dev1.2: " . time());
                $export = new LichThiTheoMonMultipleExport($ds_lich_thi_theo_lop_mon, $excel_info);

                return Excel::download($export, $file_name);
            } else {
                //export theo bộ môn
                if ($course_id) {
                    $course_search = $ds_mon->where('id', $course_id)->pluck('id');
                } else {
                    $course_search = $ds_mon->pluck('id');
                }

                $group_search = Group::whereIn('body_id', $course_search)
                    ->where('list_group.is_virtual', 0)
                    ->where('pterm_id', $term_id)
                    ->when($block_search, function ($query) use ($block_search) {
                        $query->where('end_date', '>=', $block_search->start_day)->where('end_date', '<=', $block_search->end_day);
                    })
                    ->where('type', 1)
                    ->where(function ($q) {
                        $q->where('list_group.group_name', 'not like', "TL_%")
                            ->whereRaw('LENGTH(list_group.group_name) < 20');
                    })
                    ->orderBy('id', 'desc')
                    ->get();

                if ($group_search == null || count($group_search) <= 0) {
                    return $this->redirectWithStatus('danger', 'Vui lòng kiểm tra lại. Môn hoặc lớp môn cần in danh sách thi chưa được xếp lịch buổi các buổi bảo vệ 300, 301, 302,...');
                }

                $ds_error = [];
                $ds_lich_thi_theo_bo_mon = [];
                $ds_sinh_vien_bi_cam_thi_total = [];

                $ds_lop = $group_search->pluck('id');
                foreach ($ds_lop as $group_id) {
                    $group = Group::where('id', $group_id)->where('list_group.is_virtual', 0)->first();

                    //lấy % phải đi học theo khung chương trình
                    // $result_grade_syllabus = GradeSyllabus::where('subject_code', $group->psubject_code)->first();
                    $result_grade_syllabus = GradeSyllabus::where('id', $group->syllabus_id)->first();
                    if ($result_grade_syllabus == null || $result_grade_syllabus->id <= 0) {
                        $ds_error[] = [
                            'group_name' => $group->group_name,
                            'reason' => 'Không tìm thấy khung chương trình của môn cần export danh sách thi'
                        ];
                        continue;
                    }

                    //tỷ lệ nghỉ học theo syllabus
                    $syllabus_attendance_cutoff = $result_grade_syllabus->attendance_cutoff;
                    //điểm tối thiểu theo syllabus
                    $syllabus_minimum_required = $result_grade_syllabus->minimum_required;

                    //lấy 3 buổi thi của lớp đã được đào tạo phân công
                    $result_course_slot_bao_ve = SyllabusPlan::where('syllabus_id', $group->syllabus_id)
                        ->where('session_type', '=', 11)
                        ->orderBy('course_session')
                        ->pluck('course_session');

                    $ds_slot_assignment = Activity::select('slot', 'day', 'is_online', 'room_name', 'url_room_online')
                        ->with('slotDetail:id,slot_start,slot_end')
                        ->where('groupid', '=', $group_id)
                        ->whereIn('course_slot', $result_course_slot_bao_ve)
                        ->get();
                    if ($ds_slot_assignment == null || count($ds_slot_assignment) <= 0) {
                        $ds_error[] = [
                            'group_name' => $group->group_name,
                            'reason' => 'Đào tạo chưa xếp lịch. Vui lòng kiểm tra lại lịch thi cho lớp'
                        ];
                        continue;
                    }

                    // $members = GroupMember::leftJoin('group_graduate', function ($join) {
                    //     $join->on('group_member.groupid', '=', 'group_graduate.group_id');
                    //     $join->on('group_member.member_login', '=', 'group_graduate.student_login');
                    // })
                    //     ->where('groupid', $group_id)
                    //     ->orderBy('date_graduate')
                    //     ->get();

                    // $sqlRawQuery = "(SELECT student_login, group_id, MAX(fgg.date_graduate) AS date_graduate FROM group_graduate fgg" .
                    //     " WHERE fgg.group_id = " . $group_id .
                    //     " GROUP BY fgg.student_login, fgg.group_id) AS A";

                    $sqlRawQuery = "(SELECT fgg.group_id, fgg.student_login, MAX(fgg.course_session) AS course_session FROM group_graduate fgg" .
                        " WHERE fgg.group_id = " . $group_id .
                        " GROUP BY fgg.group_id, fgg.student_login, fgg.group_name, fgg.psubject_code, fgg.psubject_name, fgg.term_id) AS A";

                    $members = GroupMember::leftJoin(DB::raw($sqlRawQuery), function ($join) {
                        $join->on('group_member.groupid', '=', 'A.group_id');
                        $join->on('group_member.member_login', '=', 'A.student_login');
                    })
                        ->where('groupid', $group_id)
                        ->orderBy('course_session')
                        ->get();

                    /**
                     * ktra có phải là môn tiếng anh hay ko?
                     *
                     *  + phần trăm đi học
                     *      => tính các buổi
                     *          + lý thuyết
                     *          + thực hành
                     *          + remote
                     *      => ko tính các buổi
                     *          + thi giữa kỳ => 8
                     *          + bảo vệ dự án => 11
                     *          + thi lần 1 => 9
                     *          + thi lần 2 => 10
                     *          + giờ học online => 18
                     *          + bảo vệ dự án 2 => 19
                     *          + remote 2 => 21
                     *
                     *  + điểm thành phần
                     */
                    if ($group->psubject_code != null && (Str::contains(Str::upper($group->psubject_code), 'ENT') || Str::contains(Str::upper($group->psubject_code), 'ETO') || Str::contains(Str::upper($group->psubject_code), 'EHO'))) {
                        $ds_syllabus_attendance = SyllabusPlan::select('course_session')
                            ->leftJoin('session_type', 't7_syllabus_plan.session_type', '=', 'session_type.id')
                            ->where('t7_syllabus_plan.syllabus_id', $group->syllabus_id)
                            ->where('session_type.is_exam', 0)
                            ->pluck('t7_syllabus_plan.course_session');

                        $ds_activities = Activity::where('groupid', $group_id)
                            ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
                            ->where('session_type.is_exam', 0)
                            ->whereIn('activity.course_slot', $ds_syllabus_attendance)
                            ->orderBy('activity.course_slot')
                            ->pluck('activity.day');

                        $result_attendances = Attendance::where('groupid', $group_id)
                            ->whereIn('day', $ds_activities)
                            ->get();
                        $listStudentGrades = CourseGrade::select([
                            'id',
                            'val', 
                            'grade_id',
                            'groupid',
                            'login',
                            'grade_group_id'
                        ])
                        ->where('groupid', $group_id)
                        ->orderBy('grade_id')
                        ->get();
                        foreach ($members as $key => $member) {
                            $is_danger_absent = false;
                            $is_danger_point = false;
                            $danger_reason_point = "";
                            $danger_reason_absent = "";
                            $is_danger = 0;

                            /**
                             * 1. ktra số buổi sinh viên được điểm danh bởi giảng viên
                             */

                            $result_attendance = $result_attendances->where('user_login', $member->member_login);

                            $absent = 0;
                            $ds_danger_absent = [];
                            foreach ($result_attendance as $attendance) {
                                if ($attendance->val === 0) {
                                    $absent += 1;
                                    array_push($ds_danger_absent, $attendance->day);
                                }
                            }

                            $total_remote = count($ds_syllabus_attendance);
                            $absent_percent = $total_remote > 0 ? round(($absent * 100) / $total_remote) : 0;
                            if ($absent_percent > 100 - $syllabus_attendance_cutoff) {
                                //cấm thi
                                $is_danger_absent = true;
                            }

                            if ($is_danger_absent) {
                                /**
                                 * 0:"2022-05-23"
                                 * 1:"2022-05-30"
                                 */
                                $danger_reason_absent = "Cấm thi do trượt điểm danh: " . implode(", ", $ds_danger_absent);
                            }
                            $member->danger_reason_absent = $danger_reason_absent;

                            /**
                             * 2. ktra điểm thành phần
                             */
                            //lấy nhóm đầu điểm thành phần
                            $result_grade_group = GradeGroup::select(['id', 'grade_group_name', 'weight', 'minimum_required'])
                                ->where('syllabus_id', '=', $group->syllabus_id)
                                ->pluck('id');

                            //lấy tất cả các đầu điểm thành phần theo nhóm đầu điểm
                            $result_grade_group_detail = Grade::select(['id', 'minimum_required', 'grade_name'])
                                ->where('is_final_exam', '=', 0) //trừ môn được đào tạo đánh dấu là kết thúc
                                ->whereIn('t7_grade.grade_group_id', $result_grade_group)
                                ->orderBy('t7_grade.id')
                                ->get();

                            //lấy tất cả điểm sinh viên đạt được
                            $result_course_grade = CourseGrade::where('groupid', $group_id)
                                ->where('login', $member->member_login)
                                ->orderBy('t7_course_grade.grade_id')
                                ->pluck('val', 'grade_id');

                            //ktra điểm sinh viên đạt được vs điểm thành phần
                            $ds_danger_point = [];
                            foreach ($result_grade_group_detail as $item) {
                                if (isset($result_course_grade[$item->id])) {
                                    if ($item->minimum_required > $result_course_grade[$item->id]) {
                                        // $is_danger_point = true;
                                        // $danger_reason_point = "Không đạt điểm thành phần " . $item->grade_name;
                                        array_push($ds_danger_point, $item->grade_name);
                                    }
                                } else {
                                    // $is_danger_point = true;
                                    // $danger_reason_point = "Không đạt điểm thành phần " . $item->grade_name;
                                    array_push($ds_danger_point, $item->grade_name);
                                }
                            }

                            //lấy điểm đánh giá quá trình
                            /* if ($syllabus_minimum_required > 0) {
                                $grade_group_qt = GradeGroup::where('syllabus_id', $group->syllabus_id)
                                    ->where('grade_group_name', 'LIKE', '%Đánh giá quá trình%')
                                    ->first();

                                if ($grade_group_qt != null && $grade_group_qt->id > 0) {
                                    //trọng số tổng của điểm đánh giá quá trình
                                    $qt_weight_total = $grade_group_qt->weight;

                                    if ($qt_weight_total > 0) {
                                        //lấy ds nhóm đầu điểm đánh giá quá trình
                                        $ds_grade_group_qt = Grade::select(['id', 'weight', 'grade_name'])
                                            ->where('grade_group_id', $grade_group_qt->id)
                                            ->orderBy('t7_grade.id')
                                            ->get();

                                        //lấy tất cả điểm quá trình sinh viên đạt được
                                        $result_course_grade_qt = CourseGrade::where('groupid', $group_id)
                                            ->where('login', $member->member_login)
                                            ->where('grade_group_id', $grade_group_qt->id)
                                            ->orderBy('grade_id')
                                            ->pluck('val', 'grade_id');

                                        //ktra điểm sinh viên đạt được vs điểm quá trình
                                        $qt_score_total = 0;

                                        foreach ($ds_grade_group_qt as $item) {
                                            if (isset($result_course_grade_qt[$item->id])) {
                                                //tính điểm quá trình cho sv
                                                $qt_detail_score = ($item->weight / $qt_weight_total) * $result_course_grade_qt[$item->id];
                                                $qt_score_total += $qt_detail_score;
                                            }
                                        }

                                        $qt_score_total_check = round($qt_score_total, 1);

                                        if ($qt_score_total_check < $syllabus_minimum_required) {
                                            // $is_danger = true;
                                            // $danger_reason = "Không đạt điểm tối thiểu đánh giá quá trình: " . $qt_score_total_check . " < " . $syllabus_minimum_required . " - Chưa làm tròn: " . $qt_score_total;
                                            $message_reason = "Điểm quá trình: " . $qt_score_total_check . " < " . $syllabus_minimum_required . " - Chưa làm tròn: " . $qt_score_total;
                                            array_push($ds_danger_point, $message_reason);
                                        }
                                    }
                                }
                            } */
                            
                            // Lấy danh sách đầu điểm cần check 
                            if (!isset($listGradeGroupChecks[$group->syllabus_id])) {
                                $listGradeGroupChecks[$group->syllabus_id] = GradeGroup::query()
                                ->whereRaw("syllabus_id = ? AND Id NOT IN (
                                    SELECT grade_group_id 
                                    FROM t7_grade WHERE syllabus_id = ?
                                    AND (
                                        is_final_exam = 1	
                                        OR t7_grade.bonus_type = 1
                                    )
                                )", [$group->syllabus_id, $group->syllabus_id])->with('grades')->get();
                            }

                            $listGradeGroupCheck = $listGradeGroupChecks[$group->syllabus_id];
                            // Duyệt nhóm đầu điểm
                            foreach ($listGradeGroupCheck as $GradeGroup) {
                                //lấy ds nhóm đầu điểm của nhóm đầu điểm
                                $listGradeByGroup = $GradeGroup->grades;

                                //lấy tất cả điểm quá trình sinh viên đạt được
                                $listStudentGrade = $listStudentGrades->where('login', $member->member_login)
                                    ->where('grade_group_id', $GradeGroup->id);
                                
                                $listStudentGradeArr = $listStudentGrade->pluck('val', 'grade_id');
                                $groupGradeTotalPoint = 0;
                                // Kiểm tra nhóm điểm
                                foreach ($listGradeByGroup as $item) {
                                    if (isset($listStudentGradeArr[$item->id])) {
                                        if ($listStudentGradeArr[$item->id] < $item->minimum_required) {
                                            $message_reason = "Đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]: " . $listStudentGradeArr[$item->id] . " < " . $item->minimum_required . " - Chưa làm tròn: " . $listStudentGradeArr[$item->id];
                                            array_push($ds_danger_point, $message_reason);
                                        }

                                        //tính điểm theo nhóm cho sv
                                        if ($GradeGroup->weight > 0) {
                                            $qt_detail_score = ($item->weight / $GradeGroup->weight) * $listStudentGradeArr[$item->id];
                                            $groupGradeTotalPoint += $qt_detail_score;
                                        }
                                    }
                                }

                                // Làm tròn nhóm đầu điểm để xét điều kiện
                                $groupGradeTotalPointCheck = round($groupGradeTotalPoint, 1);
                                if ($groupGradeTotalPointCheck < $GradeGroup->minimum_required) {
                                    $message_reason = "Nhóm điểm [$GradeGroup->grade_group_name]: " . $groupGradeTotalPointCheck . " < " . $GradeGroup->minimum_required . " - Chưa làm tròn: " . $groupGradeTotalPointCheck;
                                    array_push($ds_danger_point, $message_reason);
                                }
                            }

                            /**
                             * 0:"Quiz online 1"
                             * 1:"Quiz online 2"
                             */
                            if ($ds_danger_point != null && count($ds_danger_point) > 0) {
                                $is_danger_point = true;
                                $danger_reason_point = "Cấm thi do điểm thành phần: " . implode(", ", $ds_danger_point);
                            }
                            $member->danger_reason_point = $danger_reason_point;

                            //lấy các thông tin sinh viên cần hiển thị
                            $member->group_name = $group->group_name;
                            $member->user = User::where('user_login', $member->member_login)->first();
                            $member->fullname = $member->user->fullname();
                            $member->user_code = $member->user->user_code;

                            //lý do cấm thi
                            if ($is_danger_absent && $is_danger_point) {
                                //cấm thi cả 2 nguyên nhân
                                $is_danger = -3;
                            } elseif ($is_danger_absent && !$is_danger_point) {
                                //cấm thi do trượt điểm danh
                                $is_danger = -1;
                            } elseif (!$is_danger_absent && $is_danger_point) {
                                //cấm thi do điểm thành phần
                                $is_danger = -2;
                            } else {
                                $is_danger = 0;
                            }
                            $member->is_danger = $is_danger;

                            // lấy ngày bảo vệ của sinh viên
                            // if ($member->date_graduate != null) {
                            //     $slot_graduate = Activity::select('slot', 'day', 'is_online', 'room_name', 'url_room_online')
                            //         ->with('slotDetail:id,slot_start,slot_end')
                            //         ->where('groupid', '=', $group_id)
                            //         ->where('day', '=', $member->date_graduate)
                            //         ->first();

                            //     $member->slot_graduate = $slot_graduate;
                            // }
                            if ($member->course_session > 0) {
                                //chuyển course_session từ 0 => 300
                                $course_session_300 = null;
                                if ($member->course_session == 1) {
                                    $course_session_300 = $result_course_slot_bao_ve[0];
                                } elseif ($member->course_session == 2) {
                                    $course_session_300 = $result_course_slot_bao_ve[1];
                                } elseif ($member->course_session == 3) {
                                    $course_session_300 = $result_course_slot_bao_ve[2];
                                }

                                if (!(isset($slot_graduates[$group_id . "-" . $course_session_300]))) {
                                    $slot_graduates[$group_id . "-" . $course_session_300] = Activity::select('slot', 'day', 'is_online', 'room_name', 'url_room_online')
                                    ->with('slotDetail:id,slot_start,slot_end')
                                    ->where('groupid', '=', $group_id)
                                    // ->where('day', '=', $member->date_graduate)
                                    ->where('course_slot', '=', $course_session_300)
                                    ->first();
                                }

                                $slot_graduate = $slot_graduates[$group_id . "-" . $course_session_300];
                                $member->slot_graduate = $slot_graduate;
                            }

                            //lấy 2 đầu điểm Document & Presentation
                            if (!(isset($result_gradess[$group->syllabus_id]))) {
                                $result_gradess[$group->syllabus_id] = Grade::where('syllabus_id', $group->syllabus_id)
                                    ->whereIn('t7_grade.grade_name', ['Document', 'Presentation'])
                                    ->orderBy('t7_grade.id')
                                    ->pluck('grade_name', 'id');
                            }

                            $result_grades = $result_gradess[$group->syllabus_id];

                            $result_course_grade = CourseGrade::where('groupid', $group_id)
                                ->where('login', $member->member_login)
                                ->orderBy('t7_course_grade.grade_id')
                                ->pluck('val', 'grade_id');

                            $ds_grades = [];
                            foreach ($result_grades as $key => $value) {
                                $ds_grades[$value] = isset($result_course_grade[$key]) ? $result_course_grade[$key] : null;
                            }

                            $member->grade_document = isset($ds_grades['Document']) ? $ds_grades['Document'] : null;
                            $member->grade_presentation = isset($ds_grades['Presentation']) ? $ds_grades['Presentation'] : null;
                        }
                    } else {
                        $result_subject_type = Subject::on('ho')->select(['subject_type'])
                            ->where('subject_code', $group->psubject_code)
                            ->first();

                        /**
                         * ktra có phải là môn online hay ko?
                         *
                         * môn bình thường
                         *  + môn trandition
                         *      => chỉ xét phần trăm đi học
                         *
                         *  + môn blended
                         *      + phần trăm đi học => tất cả các buổi
                         *      + điểm thành phần
                         *
                         * môn online:
                         *  + phần trăm đi học => chỉ tính buổi remote
                         *  + điểm thành phần
                         */
                        if ($result_subject_type == null || (!Str::contains(Str::upper($result_subject_type->subject_type), 'ONLINE'))) {
                            //môn trandition
                            if (!Str::contains(Str::upper($result_subject_type->subject_type), 'BLENDED')) {
                                $listGradeGroupChecks = [];
                                $ds_activities = Activity::leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
                                    ->where('activity.groupid', $group_id)
                                    ->where('session_type.is_exam', 0)
                                    ->orderBy('activity.course_slot')
                                    ->pluck('activity.day');
                                $result_attendances = Attendance::where('groupid', $group_id)
                                    ->whereIn('day', $ds_activities)
                                    ->get();
                                $listStudentGrades = CourseGrade::select([
                                    'id',
                                    'val', 
                                    'grade_id',
                                    'groupid',
                                    'login',
                                    'grade_group_id'
                                ])
                                ->where('groupid', $group_id)
                                ->orderBy('grade_id')
                                ->get();
                                $course_results = CourseResult::where('groupid', $group_id)->get();
                                foreach ($members as $key => $member) {
                                    $is_danger_absent = false;
                                    $is_danger_point = false;
                                    $danger_reason_point = "";
                                    $danger_reason_absent = "";
                                    $is_danger = 0;

                                    $result_attendance = $result_attendances->where('user_login', $member->member_login);

                                    $absent = 0;
                                    $ds_danger_absent = [];
                                    foreach ($result_attendance as $attendance) {
                                        if ($attendance->val === 0) {
                                            $absent += 1;
                                            array_push($ds_danger_absent, $attendance->day ?? "");
                                        }
                                    }

                                    // $course_result = CourseResult::where('student_login', $member->member_login)->where('groupid', $group_id)->first();
                                    $course_result = $course_results->where('student_login', $member->member_login)->first();
                                    $absent_percent = isset($course_result->total_session) && $course_result->total_session > 0 ? round(($absent * 100) / $course_result->total_session) : 0;
                                    if ($absent_percent > 100 - $syllabus_attendance_cutoff) {
                                        //cấm thi
                                        $is_danger_absent = true;
                                    }

                                    if ($is_danger_absent) {
                                        $danger_reason_absent = "Cấm thi do trượt điểm danh: " . implode(", ", $ds_danger_absent);
                                    }
                                    $member->danger_reason_absent = $danger_reason_absent;

                                    $ds_danger_point = [];
                                    
                                    //lấy điểm đánh giá quá trình
                                    /* if ($syllabus_minimum_required > 0) {
                                        $grade_group_qt = GradeGroup::where('syllabus_id', $group->syllabus_id)
                                            ->where('grade_group_name', 'LIKE', '%Đánh giá quá trình%')
                                            ->first();

                                        if ($grade_group_qt != null && $grade_group_qt->id > 0) {
                                            //trọng số tổng của điểm đánh giá quá trình
                                            $qt_weight_total = $grade_group_qt->weight;

                                            if ($qt_weight_total > 0) {
                                                //lấy ds nhóm đầu điểm đánh giá quá trình
                                                $ds_grade_group_qt = Grade::select(['id', 'weight', 'grade_name'])
                                                    ->where('grade_group_id', $grade_group_qt->id)
                                                    ->orderBy('t7_grade.id')
                                                    ->get();

                                                //lấy tất cả điểm quá trình sinh viên đạt được
                                                $result_course_grade_qt = CourseGrade::where('groupid', $group_id)
                                                    ->where('login', $member->member_login)
                                                    ->where('grade_group_id', $grade_group_qt->id)
                                                    ->orderBy('grade_id')
                                                    ->pluck('val', 'grade_id');

                                                //ktra điểm sinh viên đạt được vs điểm quá trình
                                                $qt_score_total = 0;

                                                foreach ($ds_grade_group_qt as $item) {
                                                    if (isset($result_course_grade_qt[$item->id])) {
                                                        //tính điểm quá trình cho sv
                                                        $qt_detail_score = ($item->weight / $qt_weight_total) * $result_course_grade_qt[$item->id];
                                                        $qt_score_total += $qt_detail_score;
                                                    }
                                                }

                                                $qt_score_total_check = round($qt_score_total, 1);

                                                if ($qt_score_total_check < $syllabus_minimum_required) {
                                                    $message_reason = "Điểm quá trình: " . $qt_score_total_check . " < " . $syllabus_minimum_required . " - Chưa làm tròn: " . $qt_score_total;
                                                    array_push($ds_danger_point, $message_reason);
                                                }
                                            }
                                        }
                                    } */
                            
                                    // Lấy danh sách đầu điểm cần check 
                                    if (!isset($listGradeGroupChecks[$group->syllabus_id])) {
                                        $listGradeGroupChecks[$group->syllabus_id] = GradeGroup::query()
                                        ->whereRaw("syllabus_id = ? AND Id NOT IN (
                                            SELECT grade_group_id 
                                            FROM t7_grade WHERE syllabus_id = ?
                                            AND (
                                                is_final_exam = 1	
                                                OR t7_grade.bonus_type = 1
                                            )
                                        )", [$group->syllabus_id, $group->syllabus_id])->with('grades')->get();
                                    }

                                    $listGradeGroupCheck = $listGradeGroupChecks[$group->syllabus_id];
                                    // Duyệt nhóm đầu điểm
                                    foreach ($listGradeGroupCheck as $GradeGroup) {
                                        //lấy ds nhóm đầu điểm của nhóm đầu điểm
                                        $listGradeByGroup = $GradeGroup->grades;

                                        //lấy tất cả điểm quá trình sinh viên đạt được
                                        $listStudentGrade = $listStudentGrades->where('login', $member->member_login)
                                        ->where('grade_group_id', $GradeGroup->id);
                                        // $listStudentGrade = CourseGrade::select([
                                        //         'id',
                                        //         'val', 
                                        //         'grade_id',
                                        //     ])
                                        //     ->where('groupid', $group_id)
                                        //     ->where('login', $member->member_login)
                                        //     ->where('grade_group_id', $GradeGroup->id)
                                        //     ->orderBy('grade_id')
                                        //     ->get();
                                        
                                        $listStudentGradeArr = $listStudentGrade->pluck('val', 'grade_id');
                                        $groupGradeTotalPoint = 0;
                                        // Kiểm tra nhóm điểm
                                        foreach ($listGradeByGroup as $item) {
                                            if (isset($listStudentGradeArr[$item->id])) {
                                                if ($listStudentGradeArr[$item->id] < $item->minimum_required) {
                                                    $message_reason = "Đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]: " . $listStudentGradeArr[$item->id] . " < " . $item->minimum_required . " - Chưa làm tròn: " . $listStudentGradeArr[$item->id];
                                                    array_push($ds_danger_point, $message_reason);
                                                }

                                                //tính điểm theo nhóm cho sv
                                                if ($GradeGroup->weight > 0) {
                                                    $qt_detail_score = ($item->weight / $GradeGroup->weight) * $listStudentGradeArr[$item->id];
                                                    $groupGradeTotalPoint += $qt_detail_score;
                                                }
                                            }
                                        }

                                        // Làm tròn nhóm đầu điểm để xét điều kiện
                                        $groupGradeTotalPointCheck = round($groupGradeTotalPoint, 1);
                                        if ($groupGradeTotalPointCheck < $GradeGroup->minimum_required) {
                                            $message_reason = "Nhóm điểm [$GradeGroup->grade_group_name]: " . $groupGradeTotalPointCheck . " < " . $GradeGroup->minimum_required . " - Chưa làm tròn: " . $groupGradeTotalPointCheck;
                                            array_push($ds_danger_point, $message_reason);
                                        }
                                    }

                                    /**
                                     * 0:"Quiz online 1"
                                     * 1:"Quiz online 2"
                                     */
                                    if ($ds_danger_point != null && count($ds_danger_point) > 0) {
                                        $is_danger_point = true;
                                        $danger_reason_point = "Cấm thi do điểm thành phần: " . implode(", ", $ds_danger_point);
                                    }
                                    $member->danger_reason_point = $danger_reason_point;

                                    //lý do cấm thi
                                    if ($is_danger_absent && $is_danger_point) {
                                        //cấm thi cả 2 nguyên nhân
                                        $is_danger = -3;
                                    } elseif ($is_danger_absent && !$is_danger_point) {
                                        //cấm thi do trượt điểm danh
                                        $is_danger = -1;
                                    } elseif (!$is_danger_absent && $is_danger_point) {
                                        //cấm thi do điểm thành phần
                                        $is_danger = -2;
                                    } else {
                                        $is_danger = 0;
                                    }

                                    $member->is_danger = $is_danger;
                                    $member->danger_reason_point = $danger_reason_point;

                                    //lấy các thông tin sinh viên cần hiển thị
                                    $member->group_name = $group->group_name;
                                    $member->user = User::where('user_login', $member->member_login)->first();
                                    $member->fullname = $member->user->fullname();
                                    $member->user_code = $member->user->user_code;

                                    // lấy ngày bảo vệ của sinh viên
                                    // if ($member->date_graduate != null) {
                                    //     $slot_graduate = Activity::select('slot', 'day', 'is_online', 'room_name', 'url_room_online')
                                    //         ->with('slotDetail:id,slot_start,slot_end')
                                    //         ->where('groupid', '=', $group_id)
                                    //         ->where('day', '=', $member->date_graduate)
                                    //         ->first();

                                    //     $member->slot_graduate = $slot_graduate;
                                    // }
                                    if ($member->course_session > 0) {
                                        //chuyển course_session từ 0 => 300
                                        $course_session_300 = null;
                                        if ($member->course_session == 1) {
                                            $course_session_300 = $result_course_slot_bao_ve[0];
                                        } elseif ($member->course_session == 2) {
                                            $course_session_300 = $result_course_slot_bao_ve[1];
                                        } elseif ($member->course_session == 3) {
                                            $course_session_300 = $result_course_slot_bao_ve[2];
                                        }

                                        if (!(isset($slot_graduates[$group_id . "-" . $course_session_300]))) {
                                            $slot_graduates[$group_id . "-" . $course_session_300] = Activity::select('slot', 'day', 'is_online', 'room_name', 'url_room_online')
                                            ->with('slotDetail:id,slot_start,slot_end')
                                            ->where('groupid', '=', $group_id)
                                            // ->where('day', '=', $member->date_graduate)
                                            ->where('course_slot', '=', $course_session_300)
                                            ->first();
                                        }
    
                                        $slot_graduate = $slot_graduates[$group_id . "-" . $course_session_300];
                                        $member->slot_graduate = $slot_graduate;
                                    }

                                    //lấy 2 đầu điểm Document & Presentation
                                    if (!(isset($result_gradess[$group->syllabus_id]))) {
                                        $result_gradess[$group->syllabus_id] = Grade::where('syllabus_id', $group->syllabus_id)
                                            ->whereIn('t7_grade.grade_name', ['Document', 'Presentation'])
                                            ->orderBy('t7_grade.id')
                                            ->pluck('grade_name', 'id');
                                    }

                                    $result_grades = $result_gradess[$group->syllabus_id];

                                    $result_course_grade = CourseGrade::where('groupid', $group_id)
                                        ->where('login', $member->member_login)
                                        ->orderBy('t7_course_grade.grade_id')
                                        ->pluck('val', 'grade_id');

                                    $ds_grades = [];
                                    foreach ($result_grades as $key => $value) {
                                        $ds_grades[$value] = isset($result_course_grade[$key]) ? $result_course_grade[$key] : null;
                                    }

                                    $member->grade_document = isset($ds_grades['Document']) ? $ds_grades['Document'] : null;
                                    $member->grade_presentation = isset($ds_grades['Presentation']) ? $ds_grades['Presentation'] : null;
                                }
                            } else {
                                //môn blended
                                $ds_activities = Activity::leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
                                    ->where('activity.groupid', $group_id)
                                    ->where('session_type.is_exam', 0)
                                    ->orderBy('activity.course_slot')
                                    ->pluck('activity.day');    
                                $result_attendances = Attendance::where('groupid', $group_id)
                                ->whereIn('day', $ds_activities)
                                ->get();
                                $listStudentGrades = CourseGrade::select([
                                    'id',
                                    'val', 
                                    'grade_id',
                                    'groupid',
                                    'login',
                                    'grade_group_id'
                                ])
                                ->where('groupid', $group_id)
                                ->orderBy('grade_id')
                                ->get();
                                $course_results = CourseResult::where('groupid', $group_id)->get();
                                foreach ($members as $key => $member) {
                                    $is_danger_absent = false;
                                    $is_danger_point = false;
                                    $danger_reason_absent = "";
                                    $danger_reason_point = "";
                                    $is_danger = 0;

                                    /**
                                     * 1. ktra số buổi sinh viên được điểm danh bởi giảng viên
                                     */

                                    $result_attendance = $result_attendances->where('user_login', $member->member_login);
                                    $absent = 0;
                                    $ds_danger_absent = [];
                                    foreach ($result_attendance as $attendance) {
                                        if ($attendance->val === 0) {
                                            $absent += 1;
                                            array_push($ds_danger_absent, $attendance->day);
                                        }
                                    }

                                    $course_result = $course_results->where('student_login', $member->member_login)->first();
                                    // $course_result = CourseResult::where('student_login', $member->member_login)->where('groupid', $group_id)->first();
                                    $absent_percent = isset($course_result->total_session) && $course_result->total_session > 0 ? round(($absent * 100) / $course_result->total_session) : 0;
                                    if ($absent_percent > 100 - $syllabus_attendance_cutoff) {
                                        //cấm thi
                                        $is_danger_absent = true;
                                    }

                                    if ($is_danger_absent) {
                                        $danger_reason_absent = "Cấm thi do trượt điểm danh: " . implode(", ", $ds_danger_absent);
                                    }
                                    $member->danger_reason_absent = $danger_reason_absent;

                                    /**
                                     * 2. ktra điểm thành phần
                                     */
                                    //lấy nhóm đầu điểm thành phần
                                    $result_grade_group = GradeGroup::select(['id', 'grade_group_name', 'weight', 'minimum_required'])
                                        ->where('syllabus_id', '=', $group->syllabus_id)
                                        ->pluck('id');

                                    //lấy tất cả các đầu điểm thành phần theo nhóm đầu điểm
                                    $result_grade_group_detail = Grade::select(['id', 'minimum_required', 'grade_name'])
                                        ->where('is_final_exam', '=', 0) //trừ môn được đào tạo đánh dấu là kết thúc
                                        ->whereIn('t7_grade.grade_group_id', $result_grade_group)
                                        ->orderBy('t7_grade.id')
                                        ->get();

                                    //lấy tất cả điểm sinh viên đạt được
                                    $result_course_grade = CourseGrade::where('groupid', $group_id)
                                        ->where('login', $member->member_login)
                                        ->orderBy('t7_course_grade.grade_id')
                                        ->pluck('val', 'grade_id');

                                    //ktra điểm sinh viên đạt được vs điểm thành phần
                                    $ds_danger_point = [];
                                    foreach ($result_grade_group_detail as $item) {
                                        if (isset($result_course_grade[$item->id])) {
                                            if ($item->minimum_required > $result_course_grade[$item->id]) {
                                                // $is_danger_point = true;
                                                // $danger_reason_point = "Không đạt điểm thành phần " . $item->grade_name;
                                                // break;
                                                array_push($ds_danger_point, $item->grade_name);
                                            }
                                        } else {
                                            // $is_danger_point = true;
                                            // $danger_reason_point = "Không đạt điểm thành phần " . $item->grade_name;
                                            // break;
                                            array_push($ds_danger_point, $item->grade_name);
                                        }
                                    }


                                    //lấy điểm đánh giá quá trình
                                    /* if ($syllabus_minimum_required > 0) {
                                        $grade_group_qt = GradeGroup::where('syllabus_id', $group->syllabus_id)
                                            ->where('grade_group_name', 'LIKE', '%Đánh giá quá trình%')
                                            ->first();

                                        if ($grade_group_qt != null && $grade_group_qt->id > 0) {
                                            //trọng số tổng của điểm đánh giá quá trình
                                            $qt_weight_total = $grade_group_qt->weight;

                                            if ($qt_weight_total > 0) {
                                                //lấy ds nhóm đầu điểm đánh giá quá trình
                                                $ds_grade_group_qt = Grade::select(['id', 'weight', 'grade_name'])
                                                    ->where('grade_group_id', $grade_group_qt->id)
                                                    ->orderBy('t7_grade.id')
                                                    ->get();

                                                //lấy tất cả điểm quá trình sinh viên đạt được
                                                $result_course_grade_qt = CourseGrade::where('groupid', $group_id)
                                                    ->where('login', $member->member_login)
                                                    ->where('grade_group_id', $grade_group_qt->id)
                                                    ->orderBy('grade_id')
                                                    ->pluck('val', 'grade_id');

                                                //ktra điểm sinh viên đạt được vs điểm quá trình
                                                $qt_score_total = 0;

                                                foreach ($ds_grade_group_qt as $item) {
                                                    if (isset($result_course_grade_qt[$item->id])) {
                                                        //tính điểm quá trình cho sv
                                                        $qt_detail_score = ($item->weight / $qt_weight_total) * $result_course_grade_qt[$item->id];
                                                        $qt_score_total += $qt_detail_score;
                                                    }
                                                }

                                                $qt_score_total_check = round($qt_score_total, 1);

                                                if ($qt_score_total_check < $syllabus_minimum_required) {
                                                    $message_reason = "Điểm quá trình: " . $qt_score_total_check . " < " . $syllabus_minimum_required . " - Chưa làm tròn: " . $qt_score_total;
                                                    array_push($ds_danger_point, $message_reason);
                                                }
                                            }
                                        }
                                    } */
                            
                                    // Lấy danh sách đầu điểm cần check 
                                    if (!isset($listGradeGroupChecks[$group->syllabus_id])) {
                                        $listGradeGroupChecks[$group->syllabus_id] = GradeGroup::query()
                                        ->whereRaw("syllabus_id = ? AND Id NOT IN (
                                            SELECT grade_group_id 
                                            FROM t7_grade WHERE syllabus_id = ?
                                            AND (
                                                is_final_exam = 1	
                                                OR t7_grade.bonus_type = 1
                                            )
                                        )", [$group->syllabus_id, $group->syllabus_id])->with('grades')->get();
                                    }

                                    $listGradeGroupCheck = $listGradeGroupChecks[$group->syllabus_id];
                                    // Duyệt nhóm đầu điểm
                                    foreach ($listGradeGroupCheck as $GradeGroup) {
                                        //lấy ds nhóm đầu điểm của nhóm đầu điểm
                                        $listGradeByGroup = $GradeGroup->grades;

                                        //lấy tất cả điểm quá trình sinh viên đạt được
                                        $listStudentGrade = $listStudentGrades->where('login', $member->member_login)
                                        ->where('grade_group_id', $GradeGroup->id);
                                        // $listStudentGrade = CourseGrade::select([
                                        //         'id',
                                        //         'val', 
                                        //         'grade_id',
                                        //     ])
                                        //     ->where('groupid', $group_id)
                                        //     ->where('login', $member->member_login)
                                        //     ->where('grade_group_id', $GradeGroup->id)
                                        //     ->orderBy('grade_id')
                                        //     ->get();
                                        
                                        $listStudentGradeArr = $listStudentGrade->pluck('val', 'grade_id');
                                        $groupGradeTotalPoint = 0;
                                        // Kiểm tra nhóm điểm
                                        foreach ($listGradeByGroup as $item) {
                                            if (isset($listStudentGradeArr[$item->id])) {
                                                if ($listStudentGradeArr[$item->id] < $item->minimum_required) {
                                                    $message_reason = "Đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]: " . $listStudentGradeArr[$item->id] . " < " . $item->minimum_required . " - Chưa làm tròn: " . $listStudentGradeArr[$item->id];
                                                    array_push($ds_danger_point, $message_reason);
                                                }

                                                //tính điểm theo nhóm cho sv
                                                if ($GradeGroup->weight > 0) {
                                                    $qt_detail_score = ($item->weight / $GradeGroup->weight) * $listStudentGradeArr[$item->id];
                                                    $groupGradeTotalPoint += $qt_detail_score;
                                                }
                                            }
                                        }

                                        // Làm tròn nhóm đầu điểm để xét điều kiện
                                        $groupGradeTotalPointCheck = round($groupGradeTotalPoint, 1);
                                        if ($groupGradeTotalPointCheck < $GradeGroup->minimum_required) {
                                            $message_reason = "Nhóm điểm [$GradeGroup->grade_group_name]: " . $groupGradeTotalPointCheck . " < " . $GradeGroup->minimum_required . " - Chưa làm tròn: " . $groupGradeTotalPointCheck;
                                            array_push($ds_danger_point, $message_reason);
                                        }
                                    }

                                    if ($ds_danger_point != null && count($ds_danger_point) > 0) {
                                        $is_danger_point = true;
                                        $danger_reason_point = "Cấm thi do điểm thành phần: " . implode(", ", $ds_danger_point);
                                    }
                                    $member->danger_reason_point = $danger_reason_point;

                                    //lý do cấm thi
                                    if ($is_danger_absent && $is_danger_point) {
                                        //cấm thi cả 2 nguyên nhân
                                        $is_danger = -3;
                                    } elseif ($is_danger_absent && !$is_danger_point) {
                                        //cấm thi do trượt điểm danh
                                        $is_danger = -1;
                                    } elseif (!$is_danger_absent && $is_danger_point) {
                                        //cấm thi do điểm thành phần
                                        $is_danger = -2;
                                    } else {
                                        $is_danger = 0;
                                    }
                                    $member->is_danger = $is_danger;

                                    //lấy các thông tin sinh viên cần hiển thị
                                    $member->group_name = $group->group_name;
                                    $member->user = User::where('user_login', $member->member_login)->first();
                                    $member->fullname = $member->user->fullname();
                                    $member->user_code = $member->user->user_code;

                                    // lấy ngày bảo vệ của sinh viên
                                    // if ($member->date_graduate != null) {
                                    //     $slot_graduate = Activity::select('slot', 'day', 'is_online', 'room_name', 'url_room_online')
                                    //         ->with('slotDetail:id,slot_start,slot_end')
                                    //         ->where('groupid', '=', $group_id)
                                    //         ->where('day', '=', $member->date_graduate)
                                    //         ->first();

                                    //     $member->slot_graduate = $slot_graduate;
                                    // }
                                    if ($member->course_session > 0) {
                                        //chuyển course_session từ 0 => 300
                                        $course_session_300 = null;
                                        if ($member->course_session == 1) {
                                            $course_session_300 = $result_course_slot_bao_ve[0];
                                        } elseif ($member->course_session == 2) {
                                            $course_session_300 = $result_course_slot_bao_ve[1];
                                        } elseif ($member->course_session == 3) {
                                            $course_session_300 = $result_course_slot_bao_ve[2];
                                        }

                                        if (!(isset($slot_graduates[$group_id . "-" . $course_session_300]))) {
                                            $slot_graduates[$group_id . "-" . $course_session_300] = Activity::select('slot', 'day', 'is_online', 'room_name', 'url_room_online')
                                            ->with('slotDetail:id,slot_start,slot_end')
                                            ->where('groupid', '=', $group_id)
                                            // ->where('day', '=', $member->date_graduate)
                                            ->where('course_slot', '=', $course_session_300)
                                            ->first();
                                        }
    
                                        $slot_graduate = $slot_graduates[$group_id . "-" . $course_session_300];
                                        $member->slot_graduate = $slot_graduate;
                                    }

                                    //lấy 2 đầu điểm Document & Presentation
                                    if (!(isset($result_gradess[$group->syllabus_id]))) {
                                        $result_gradess[$group->syllabus_id] = Grade::where('syllabus_id', $group->syllabus_id)
                                            ->whereIn('t7_grade.grade_name', ['Document', 'Presentation'])
                                            ->orderBy('t7_grade.id')
                                            ->pluck('grade_name', 'id');
                                    }

                                    $result_grades = $result_gradess[$group->syllabus_id];

                                    $result_course_grade = CourseGrade::where('groupid', $group_id)
                                        ->where('login', $member->member_login)
                                        ->orderBy('t7_course_grade.grade_id')
                                        ->pluck('val', 'grade_id');

                                    $ds_grades = [];
                                    foreach ($result_grades as $key => $value) {
                                        $ds_grades[$value] = isset($result_course_grade[$key]) ? $result_course_grade[$key] : null;
                                    }

                                    $member->grade_document = isset($ds_grades['Document']) ? $ds_grades['Document'] : null;
                                    $member->grade_presentation = isset($ds_grades['Presentation']) ? $ds_grades['Presentation'] : null;
                                }
                            }
                        } else {
                            //môn online
                            //lấy số buổi sinh viên được tính điểm danh => buổi remote + ko tính những buổi thi
                            $ds_syllabus_attendance = SyllabusPlan::select('course_session')
                                ->leftJoin('session_type', 't7_syllabus_plan.session_type', '=', 'session_type.id')
                                ->where('t7_syllabus_plan.syllabus_id', $group->syllabus_id)
                                ->where('session_type.is_exam', 0)
                                ->pluck('course_session');

                            $listGradeGroupCheck = [];
                            $ds_activities = Activity::leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
                                ->where('activity.groupid', $group_id)
                                ->where('session_type.is_exam', 0)
                                ->whereIn('activity.course_slot', $ds_syllabus_attendance)
                                ->orderBy('activity.course_slot')
                                ->pluck('activity.day');    
                            $result_attendances = Attendance::where('groupid', $group_id)
                            ->whereIn('day', $ds_activities)
                            ->get();
                            $listStudentGrades = CourseGrade::select([
                                'id',
                                'val', 
                                'grade_id',
                                'groupid',
                                'login',
                                'grade_group_id'
                            ])
                            ->where('groupid', $group_id)
                            ->orderBy('grade_id')
                            ->get();
                            foreach ($members as $key => $member) {
                                $is_danger_absent = false;
                                $is_danger_point = false;
                                $danger_reason_point = "";
                                $danger_reason_absent = "";
                                $is_danger = 0;

                                /**
                                 * 1. ktra số buổi sinh viên được điểm danh bởi giảng viên
                                 */

                                $result_attendance = $result_attendances->where('user_login', $member->member_login);

                                $absent = 0;
                                $ds_danger_absent = [];
                                foreach ($result_attendance as $attendance) {
                                    if ($attendance->val === 0) {
                                        $absent += 1;
                                    }
                                }

                                $total_remote = count($ds_syllabus_attendance);
                                $absent_percent = $total_remote > 0 ? round(($absent * 100) / $total_remote) : 0;
                                if ($absent_percent > 100 - $syllabus_attendance_cutoff) {
                                    //cấm thi
                                    $is_danger_absent = true;
                                }

                                if ($is_danger_absent) {
                                    $danger_reason_absent = "Cấm thi do trượt điểm danh: " . implode(", ", $ds_danger_absent);
                                }
                                $member->danger_reason_absent = $danger_reason_absent;

                                /**
                                 * 2. ktra điểm thành phần
                                 */
                                //lấy nhóm đầu điểm thành phần
                                $result_grade_group = GradeGroup::select(['id', 'grade_group_name', 'weight', 'minimum_required'])
                                    ->where('syllabus_id', '=', $group->syllabus_id)
                                    ->pluck('id');

                                //lấy tất cả các đầu điểm thành phần theo nhóm đầu điểm
                                $result_grade_group_detail = Grade::select(['id', 'minimum_required', 'grade_name'])
                                    ->where('is_final_exam', '=', 0) //trừ môn được đào tạo đánh dấu là kết thúc
                                    ->whereIn('t7_grade.grade_group_id', $result_grade_group)
                                    ->orderBy('t7_grade.id')
                                    ->get();

                                //lấy tất cả điểm sinh viên đạt được
                                $result_course_grade = CourseGrade::where('groupid', $group_id)
                                    ->where('login', $member->member_login)
                                    ->orderBy('t7_course_grade.grade_id')
                                    ->pluck('val', 'grade_id');

                                //ktra điểm sinh viên đạt được vs điểm thành phần
                                $ds_danger_point = [];
                                foreach ($result_grade_group_detail as $item) {
                                    if (isset($result_course_grade[$item->id])) {
                                        if ($item->minimum_required > $result_course_grade[$item->id]) {
                                            // $is_danger_point = true;
                                            // $danger_reason_point = "Không đạt điểm thành phần " . $item->grade_name;
                                            // break;
                                            array_push($ds_danger_point, $item->grade_name);
                                        }
                                    } else {
                                        // $is_danger_point = true;
                                        // $danger_reason_point = "Không đạt điểm thành phần " . $item->grade_name;
                                        // break;
                                        array_push($ds_danger_point, $item->grade_name);
                                    }
                                }

                                //lấy điểm đánh giá quá trình
                                /* if ($syllabus_minimum_required > 0) {
                                    $grade_group_qt = GradeGroup::where('syllabus_id', $group->syllabus_id)
                                        ->where('grade_group_name', 'LIKE', '%Đánh giá quá trình%')
                                        ->first();

                                    if ($grade_group_qt != null && $grade_group_qt->id > 0) {
                                        //trọng số tổng của điểm đánh giá quá trình
                                        $qt_weight_total = $grade_group_qt->weight;

                                        if ($qt_weight_total > 0) {
                                            //lấy ds nhóm đầu điểm đánh giá quá trình
                                            $ds_grade_group_qt = Grade::select(['id', 'weight', 'grade_name'])
                                                ->where('grade_group_id', $grade_group_qt->id)
                                                ->orderBy('t7_grade.id')
                                                ->get();

                                            //lấy tất cả điểm quá trình sinh viên đạt được
                                            $result_course_grade_qt = CourseGrade::where('groupid', $group_id)
                                                ->where('login', $member->member_login)
                                                ->where('grade_group_id', $grade_group_qt->id)
                                                ->orderBy('grade_id')
                                                ->pluck('val', 'grade_id');

                                            //ktra điểm sinh viên đạt được vs điểm quá trình
                                            $qt_score_total = 0;

                                            foreach ($ds_grade_group_qt as $item) {
                                                if (isset($result_course_grade_qt[$item->id])) {
                                                    //tính điểm quá trình cho sv
                                                    $qt_detail_score = ($item->weight / $qt_weight_total) * $result_course_grade_qt[$item->id];
                                                    $qt_score_total += $qt_detail_score;
                                                }
                                            }

                                            $qt_score_total_check = round($qt_score_total, 1);

                                            if ($qt_score_total_check < $syllabus_minimum_required) {
                                                $message_reason = "Điểm quá trình: " . $qt_score_total_check . " < " . $syllabus_minimum_required . " - Chưa làm tròn: " . $qt_score_total;
                                                array_push($ds_danger_point, $message_reason);
                                            }
                                        }
                                    }
                                } */
                            
                                // Lấy danh sách đầu điểm cần check 
                                if (!isset($listGradeGroupChecks[$group->syllabus_id])) {
                                    $listGradeGroupChecks[$group->syllabus_id] = GradeGroup::query()
                                    ->whereRaw("syllabus_id = ? AND Id NOT IN (
                                        SELECT grade_group_id 
                                        FROM t7_grade WHERE syllabus_id = ?
                                        AND (
                                            is_final_exam = 1	
                                            OR t7_grade.bonus_type = 1
                                        )
                                    )", [$group->syllabus_id, $group->syllabus_id])->with('grades')->get();
                                }

                                $listGradeGroupCheck = $listGradeGroupChecks[$group->syllabus_id];
                                // Duyệt nhóm đầu điểm
                                foreach ($listGradeGroupCheck as $GradeGroup) {
                                    //lấy ds nhóm đầu điểm của nhóm đầu điểm
                                    $listGradeByGroup = $GradeGroup->grades;

                                    //lấy tất cả điểm quá trình sinh viên đạt được
                                    $listStudentGrade = $listStudentGrades->where('login', $member->member_login)
                                    ->where('grade_group_id', $GradeGroup->id);
                                    // $listStudentGrade = CourseGrade::select([
                                    //         'id',
                                    //         'val', 
                                    //         'grade_id',
                                    //     ])
                                    //     ->where('groupid', $group_id)
                                    //     ->where('login', $member->member_login)
                                    //     ->where('grade_group_id', $GradeGroup->id)
                                    //     ->orderBy('grade_id')
                                    //     ->get();
                                    
                                    $listStudentGradeArr = $listStudentGrade->pluck('val', 'grade_id');
                                    $groupGradeTotalPoint = 0;
                                    // Kiểm tra nhóm điểm
                                    foreach ($listGradeByGroup as $item) {
                                        if (isset($listStudentGradeArr[$item->id])) {
                                            if ($listStudentGradeArr[$item->id] < $item->minimum_required) {
                                                $message_reason = "Đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]: " . $listStudentGradeArr[$item->id] . " < " . $item->minimum_required . " - Chưa làm tròn: " . $listStudentGradeArr[$item->id];
                                                array_push($ds_danger_point, $message_reason);
                                            }

                                            //tính điểm theo nhóm cho sv
                                            if ($GradeGroup->weight > 0) {
                                                $qt_detail_score = ($item->weight / $GradeGroup->weight) * $listStudentGradeArr[$item->id];
                                                $groupGradeTotalPoint += $qt_detail_score;
                                            }
                                        }
                                    }

                                    // Làm tròn nhóm đầu điểm để xét điều kiện
                                    $groupGradeTotalPointCheck = round($groupGradeTotalPoint, 1);
                                    if ($groupGradeTotalPointCheck < $GradeGroup->minimum_required) {
                                        $message_reason = "Nhóm điểm [$GradeGroup->grade_group_name]: " . $groupGradeTotalPointCheck . " < " . $GradeGroup->minimum_required . " - Chưa làm tròn: " . $groupGradeTotalPointCheck;
                                        array_push($ds_danger_point, $message_reason);
                                    }
                                }

                                if ($ds_danger_point != null && count($ds_danger_point) > 0) {
                                    $is_danger_point = true;
                                    $danger_reason_point = "Cấm thi do điểm thành phần: " . implode(", ", $ds_danger_point);
                                }
                                $member->danger_reason_point = $danger_reason_point;

                                //lý do cấm thi
                                if ($is_danger_absent && $is_danger_point) {
                                    //cấm thi cả 2 nguyên nhân
                                    $is_danger = -3;
                                } elseif ($is_danger_absent && !$is_danger_point) {
                                    //cấm thi do trượt điểm danh
                                    $is_danger = -1;
                                } elseif (!$is_danger_absent && $is_danger_point) {
                                    //cấm thi do điểm thành phần
                                    $is_danger = -2;
                                } else {
                                    $is_danger = 0;
                                }

                                //lấy các thông tin sinh viên cần hiển thị
                                $member->group_name = $group->group_name;
                                $member->user = User::where('user_login', $member->member_login)->first();
                                $member->fullname = $member->user->fullname();
                                $member->user_code = $member->user->user_code;
                                $member->is_danger = $is_danger;

                                // lấy ngày bảo vệ của sinh viên
                                // if ($member->date_graduate != null) {
                                //     $slot_graduate = Activity::select('slot', 'day', 'is_online', 'room_name', 'url_room_online')
                                //         ->with('slotDetail:id,slot_start,slot_end')
                                //         ->where('groupid', '=', $group_id)
                                //         ->where('day', '=', $member->date_graduate)
                                //         ->first();

                                //     $member->slot_graduate = $slot_graduate;
                                // }
                                if ($member->course_session > 0) {
                                    //chuyển course_session từ 0 => 300
                                    $course_session_300 = null;
                                    if ($member->course_session == 1) {
                                        $course_session_300 = $result_course_slot_bao_ve[0];
                                    } elseif ($member->course_session == 2) {
                                        $course_session_300 = $result_course_slot_bao_ve[1];
                                    } elseif ($member->course_session == 3) {
                                        $course_session_300 = $result_course_slot_bao_ve[2];
                                    }

                                    if (!(isset($slot_graduates[$group_id . "-" . $course_session_300]))) {
                                        $slot_graduates[$group_id . "-" . $course_session_300] = Activity::select('slot', 'day', 'is_online', 'room_name', 'url_room_online')
                                        ->with('slotDetail:id,slot_start,slot_end')
                                        ->where('groupid', '=', $group_id)
                                        // ->where('day', '=', $member->date_graduate)
                                        ->where('course_slot', '=', $course_session_300)
                                        ->first();
                                    }

                                    $slot_graduate = $slot_graduates[$group_id . "-" . $course_session_300];
                                    $member->slot_graduate = $slot_graduate;
                                }

                                //lấy 2 đầu điểm Document & Presentation
                                if (!(isset($result_gradess[$group->syllabus_id]))) {
                                    $result_gradess[$group->syllabus_id] = Grade::where('syllabus_id', $group->syllabus_id)
                                        ->whereIn('t7_grade.grade_name', ['Document', 'Presentation'])
                                        ->orderBy('t7_grade.id')
                                        ->pluck('grade_name', 'id');
                                }

                                $result_grades = $result_gradess[$group->syllabus_id];

                                $result_course_grade = CourseGrade::where('groupid', $group_id)
                                    ->where('login', $member->member_login)
                                    ->orderBy('t7_course_grade.grade_id')
                                    ->pluck('val', 'grade_id');

                                $ds_grades = [];
                                foreach ($result_grades as $key => $value) {
                                    $ds_grades[$value] = isset($result_course_grade[$key]) ? $result_course_grade[$key] : null;
                                }

                                $member->grade_document = isset($ds_grades['Document']) ? $ds_grades['Document'] : null;
                                $member->grade_presentation = isset($ds_grades['Presentation']) ? $ds_grades['Presentation'] : null;
                            }
                        }
                    }

                    $block = null;
                    if ($block_search == null) {
                        $block = Block::where('id', $group->block_id)->first();
                    } else {
                        $block = $block_search;
                    }

                    $ds_lich_thi_theo_bo_mon[] = [
                        'is_error' => false,
                        'is_ds_cam_thi' => false,
                        'group_name' => $group->group_name,
                        'block' => $block,
                        // 'members' => $members->sortBy('date_graduate')->values()->all(),
                        'members' => $members->sortBy('course_session')->values()->all(),
                    ];

                    $ds_sinh_vien_bi_cam_thi = $members->whereIn('is_danger', [-1, -2, -3]);
                    if ($ds_sinh_vien_bi_cam_thi != null && count($ds_sinh_vien_bi_cam_thi) > 0) {
                        foreach ($ds_sinh_vien_bi_cam_thi as $item) {
                            array_push($ds_sinh_vien_bi_cam_thi_total, $item);
                        }
                    }
                }

                if ($ds_error != null && count($ds_error) == count($ds_lop)) {
                    return $this->redirectWithStatus('danger', 'Không thể export file excel! Đào tạo chưa xếp lịch. Vui lòng kiểm tra lại lịch thi cho tất cả lớp môn!');
                }

                //file excel chứa danh sách sinh viên bị cấm thi
                array_unshift($ds_lich_thi_theo_bo_mon, [
                    'is_error' => false,
                    'is_ds_cam_thi' => true,
                    'group_name' => 'Danh sách sinh viên bị cấm thi',
                    'block' => null,
                    'members' => $ds_sinh_vien_bi_cam_thi_total,
                ]);

                //file excel chứa lỗi
                if ($ds_error != null && count($ds_error) > 0) {
                    $item = [
                        'is_error' => true,
                        'ds_error' => $ds_error,
                    ];

                    //thêm lớp bị lỗi lên đầu file excel
                    array_unshift($ds_lich_thi_theo_bo_mon, $item);
                }

                $excel_info = [
                    'term' => $term,
                    'group_info' => $group_search[0]
                ];

                // Log::error("Dev2.1: " . time());
                $file_name = now()->format('d_m_y_h_i_s_') . 'lich_thi_theo_bo_mon_' . ($group_search[0]->psubject_code) . '.xlsx';
                $export = new LichThiTheoBoMonMultipleExport($ds_lich_thi_theo_bo_mon, $excel_info);
                // Log::error("Dev2.2: " . time());
                return Excel::download($export, $file_name);
            }
        } catch (Exception $th) {
            Log::error("---------dont------------");
            Log::error($th);
            Log::error("---------err end export_danh_sach_thi ------------");
            return $this->redirectWithStatus('danger', 'Có lỗi xảy ra. Không thể export file excel!');
        }
    }


    /**
     * 
     * Xuất danh sách thi
     * 
     * <AUTHOR>
     * @since 30/05/2023
     * @todo Tải danh sách sinh viên cấm thi hoặc danh sách đủ điều kiện thi
     * @param Illuminate\Http\Request $request
     * @return ExportNewMultipleListStudentExam 
     */
    public function exportListStudentExam(Request $request)
    {
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', '-1');
        $urlBack = (url()->current() == route('admin.calendar.danh_sach_thi') ? url()->previous() : route('admin.calendar.danh_sach_thi', $request->all()));
        $rules = [
            'term_id' => "required|exists:term,id",
            'department_id' => "required|exists:department,id",
            'course_id' => "required|exists:course,id",
        ];
        
        $messages = [
            'term_id.required' => 'Kỳ học không được bỏ trống',
            'department_id.required' => 'Bộ môn không được bỏ trống',
            'course_id.required' => 'Khóa học không được bỏ trống',
            'term_id.exists' => 'kỳ học không tồn tại',
            'block_id.exists' => 'block không tồn tại',
            'department_id.exists' => 'Bộ môn không tồn tại',
            'course_id.exists' => 'Khóa học không tồn tại',
            'group_id.exists' => 'lớp không tồn tại',
        ];

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            return $this->redirectWithStatus('danger', $validator->errors()->first(), $urlBack);
        }

        // Lấy danh sách thi
        try {
            // Log::error("Dev: " . time());
            ini_set('memory_limit', '-1');
            set_time_limit(0);
            // \Debugbar::disable();

            $termId = $request->term_id;
            $blockId = $request->get('block_id', null);
            $departmentId = $request->department_id;
            $courseId = $request->course_id;
            $groupId = $request->get('group_id', null);
            $camThi = $request->get('cam_thi', 0);
            $user_login = auth()->user()->user_login;

            if ( $termId == null || $termId <= 0 || $departmentId == null || $departmentId <= 0) {
                return $this->redirectWithStatus('danger', 'Vui lòng chọn môn hoặc lớp môn cần in danh sách thi!', $urlBack);
            }

            $term = Term::where('id', $termId)->first();
            if ($term == null) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy kỳ cần in danh sách thi!', $urlBack);
            }

            $blocks = Block::where('term_id', $termId)->get();
            $block_search = null;
            if ($blockId) {
                $block_search = $blocks->where('id', $blockId)->first();
            }

            // Lấy khóa học
            $course = Course::find($courseId);

            // kiểm tra có lớp chưa được up lên chưa 
            $checkGroupCalendar = Group::select([
                "list_group.id",
                "list_group.body_id",
                "list_group.group_name",
                "list_group.pterm_name",
                DB::raw("COUNT(group_member.member_login) as number_student"),
                DB::raw("(
                    SELECT COUNT(group_graduate.student_login)
                    FROM group_graduate 
                    WHERE `group_graduate`.`group_id` = `list_group`.`id`
                ) as number_student_exam"),  
            ])
            ->join('group_member', 'list_group.id', '=', 'group_member.groupid')
            ->where('list_group.pterm_id', $termId)
            ->where('list_group.body_id', $courseId)
            ->where('list_group.is_virtual', 0)
            ->when($groupId, function ($q, $groupId) {
                $q->where('list_group.id', $groupId);
            })
            ->when($blockId, function ($q, $blockId) {
                $q->where('list_group.block_id', $blockId);
            })
            ->groupBy('list_group.id')
            ->havingRaw('number_student > number_student_exam')
            ->get();
            if (count($checkGroupCalendar) > 0) {
                $checkGroupCalendar = $checkGroupCalendar->pluck('group_name')->toArray();
                $strReport = count($checkGroupCalendar) . ' lớp môn ' . $course->psubject_code . ' chưa xếp hết lịch thi cho sinh viên, vui lòng kiểm tra lại các lớp: ' . implode(', ', $checkGroupCalendar);
                return $this->redirectWithStatus('danger', $strReport, $urlBack);
            }

            // Lấy danh sách lớp
            $groups = Group::where('body_id', $course->id)
            ->with([
                'groupMembers' => function($q) {
                    $q->select([
                        'group_member.id', 
                        'group_member.groupid', 
                        'group_member.member_login', 
                        'group_member.user_code',
                        'user.user_code',
                        DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ", user.user_givenname) as full_name')
                    ])
                    ->join('user', 'user.user_login', 'group_member.member_login')
                    ->get();
                }])
            ->where('pterm_id', $termId)
            ->where('list_group.is_virtual', 0)
            ->when($groupId, function ($q, $groupId) {
                $q->where('list_group.id', $groupId);
            })
            ->get();

            if (count($groups) == 0) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy lớp nào!');
            }

            // Nếu pass qua quét từng lớp 1 
            // Lấy ra số buổi học + đầu điểm
            
            // Kiểm tra xem có các buổi bảo vệ không ?
            $checkCouseSlotDef = SyllabusPlan::where('syllabus_id', $course->syllabus_id)
                ->where('session_type', '=', 11)
                // ->whereIn('session_type', [11, 9])
                ->orderBy('course_session')
                ->pluck('course_session');
                // ->get();
            if ($checkCouseSlotDef == null || count($checkCouseSlotDef) != 3) {
                return $this->redirectWithStatus('danger', 'Không tìm thấy 3 buổi bảo vệ của môn hoặc lớp môn trong syllabus');
            }
            
            // Danh sách buổi bảo vệ
            $listActivitiesDef = Activity::select([
                'groupid',
                'slot', 
                'day', 
                'is_online', 
                'room_name', 
                'url_room_online',
                'course_slot',
                'session_type', 
            ])
                ->with('slotDetail:id,slot_start,slot_end')
                ->whereIn('groupid', $groups->pluck('id')->toArray())
                ->whereIn('course_slot', $checkCouseSlotDef)
                ->get();
        
            // Danh sách buổi học
            $listActivities = Activity::whereIn('groupid', $groups->pluck('id')->toArray())
                ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
                ->where('session_type.is_exam', 0)
                ->orderBy('activity.course_slot')
                ->get();
            
            $listAllGroupGraduate = GroupGraduate::select([
                'id',
                'student_login', 
                'group_id', 
                'course_session'
            ])
            ->whereIn('group_id', $groups->pluck('id')->toArray())
            ->whereNotNull('course_session')
            ->groupBy('student_login')
            ->get();



            // Lấy danh sách các nhóm đầu điểm cần tính toán
            $listGradeGroupCheck = GradeGroup::query()
            ->whereRaw("syllabus_id = ? AND Id NOT IN (
                SELECT grade_group_id 
                FROM t7_grade WHERE syllabus_id = ?
                AND (
                    is_final_exam = 1	
                    OR t7_grade.bonus_type = 1
                )
            )", [$course->syllabus_id, $course->syllabus_id])->with(['grades'])->get();
                

            // duyệt lớp
            $res = [];
            foreach ($groups as $key => $group) {
                // if ($key == 6) break;
                $members = $group->groupMembers;
                // lấy danh sách buổi học
                $listActivityByGroup = $listActivities->where('groupid', $group->id);
                $listActivityByGroupId = $listActivities->pluck('id')->toArray();
                $listActivitiesDefByGroup = $listActivitiesDef->where('groupid', $group->id)->sortBy('course_slot');
                // if (count($listActivitiesDefByGroup) != 3) {
                //     return $this->redirectWithStatus('danger', "Không tìm thấy 3 buổi bảo vệ của lớp $group->group_name($group->id)", $urlBack);
                // }
                
                $res[$group->id]['course'] = array_values($listActivitiesDefByGroup->toArray());
                $res[$group->id]['group'] = $group;
                $res[$group->id]['list_no_course'] = [];

                // lịch sử học của cả lớp
                // $courseResults = CourseResult::select([
                //     'id',
                //     'student_login',
                //     'groupid',
                //     'val'
                // ])->where('groupid', $group->id)->get();
                
                // $listUserInfo = User::select([
                //     'user.user_login',
                //     DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ", user.user_givenname) as full_name'),
                // ])
                // ->whereIn('user_login', $members->pluck('member_login')->toArray())
                // ->pluck('full_name', 'user_login')->toArray();
            
                // lấy danh sách điểm theo lớp
                $listStudentGrades = CourseGrade::select([
                    'id',
                    'val', 
                    'grade_id',
                    'groupid',
                    'login',
                    'grade_group_id'
                ])
                // ->whereIn('groupid', $groups->pluck('id')->toArray())
                ->where('groupid', $group->id)
                ->orderBy('grade_id')
                ->get();
                
                // Lấy danh sách điểm danh của lớp
                if (count($listActivityByGroupId) > 500) {
                    DB::getPdo()->setAttribute(\PDO::ATTR_EMULATE_PREPARES, true);
                }
                $listAllAttendance = Attendance::select([
                    'user_login',
                    DB::raw('count(attendance.id) as total_att')
                ])
                // ->whereIn('groupid', $groups->pluck('id')->toArray())
                ->whereIn('activity_id', $listActivityByGroupId)
                ->where('groupid', $group->id)
                ->groupBy('user_login')
                ->pluck('total_att', 'user_login')->toArray();

                foreach ($members as $member) {
                    $listMsgFail = [];
                    $memberStatus = 0;
                    // $courseResult = $courseResults->where('student_login', $member->member_login)->first();
                    $courseResult = CourseResult::select([
                        'id',
                        'student_login',
                        'groupid',
                        'val'
                    ])->where('student_login', $member->member_login)
                    ->where('groupid', $group->id)
                    ->first();
                    
                    if (!$courseResult) {
                        return $this->redirectWithStatus('danger', 'Không thể xuất file excel. Vui lòng kiểm tra lại "Lịch sử học" sinh viên ' . $member->member_login . " lớp $group->group_name($group->id)!", $urlBack);
                    }

                    // $member->user_code = $member->user_code;
                    // $member->full_name = $listUserInfo[$member->member_login];
                    $member->group_name = $group->group_name;
                    $memberGroupGraduate = GroupGraduate::select([
                        'id',
                        'student_login', 
                        'group_id', 
                        'course_session'
                    ])
                    ->whereNotNull('course_session')
                    ->groupBy('student_login')
                    ->where('student_login', $member->member_login)
                    ->where('group_id', $group->id)
                    ->first();

                    if ($courseResult->val == -1) {
                        $member->status = 1;
                        $member->reason_fail = "Cấm thi do trượt điểm danh";
                        if ($memberGroupGraduate->course_session == 1) {
                            $member->course_detail = $res[$group->id]['course'][0];
                        } elseif ($memberGroupGraduate->course_session == 2) {
                            $member->course_detail = $res[$group->id]['course'][1];
                        } elseif ($memberGroupGraduate->course_session == 3) {
                            $member->course_detail = $res[$group->id]['course'][2] ?? [];
                        }

                        $res[$group->id]['list_fail'][] = $member->toArray();
                        continue;
                    }

                    // kiểm tra sinh viên đầy đủ buổi điểm danh chưa
                    if (!isset($listAllAttendance[$member->member_login])) {
                        return $this->redirectWithStatus('danger', 'Không thể xuất file excel. Vui lòng điểm danh tất cả các buổi học cho sinh viên ' . $member->member_login . " lớp $group->group_name($group->id)!", $urlBack);
                    }
                    
                    $studentAttendance = $listAllAttendance[$member->member_login];
                    if (count($listActivityByGroup) != $studentAttendance) {
                        return $this->redirectWithStatus('danger', 'Không thể xuất file excel. Vui lòng điểm danh tất cả các buổi học cho sinh viên ' . $member->member_login . " lớp $group->group_name($group->id)!", $urlBack);
                    } 

                    // Duyệt nhóm đầu điểm
                    foreach ($listGradeGroupCheck as $GradeGroup) {
                        //lấy ds nhóm đầu điểm của nhóm đầu điểm
                        $listGradeByGroup = $GradeGroup->grades;
                        //lấy tất cả điểm quá trình sinh viên đạt được
                        $listStudentGrade = $listStudentGrades
                        // ->where('groupid', $group->id)
                        ->where('login', $member->member_login)
                        ->where('grade_group_id', $GradeGroup->id);
                        
                        $listStudentGradeArr = $listStudentGrade->pluck('val', 'grade_id');
                        $groupGradeTotalPoint = 0;
                        // Kiểm tra nhóm điểm
                        foreach ($listGradeByGroup as $item) {
                            if (isset($listStudentGradeArr[$item->id])) {
                                if ($listStudentGradeArr[$item->id] < $item->minimum_required) {
                                    $listMsgFail[] = "Đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]: " . $listStudentGradeArr[$item->id] . " < " . $item->minimum_required;
                                }
    
                                //tính điểm theo nhóm cho sv
                                if ($GradeGroup->weight > 0) {
                                    $qt_detail_score = ($item->weight / $GradeGroup->weight) * $listStudentGradeArr[$item->id];
                                    $groupGradeTotalPoint += $qt_detail_score;
                                }
                            } else {
                                $listMsgFail[] = "Thiếu đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]";
                            }
                        }
    
                        // Làm tròn nhóm đầu điểm để xét điều kiện
                        $groupGradeTotalPointCheck = round($groupGradeTotalPoint, 1);
                        if ($groupGradeTotalPointCheck < $GradeGroup->minimum_required) {
                            $listMsgFail[] = "Nhóm điểm [$GradeGroup->grade_group_name]: " . $groupGradeTotalPointCheck . " < " . $GradeGroup->minimum_required . " - Chưa làm tròn: " . $groupGradeTotalPointCheck;
                        }
                    }
                    
                    // kiểm tra xem 
                    if (count($listMsgFail) > 0) {
                        $member->status = 2;
                        $member->reason_fail = "Cấm thi do điểm thành phần: " . implode(", ", $listMsgFail);
                        if ($memberGroupGraduate->course_session == 1) {
                            $member->course_detail = $res[$group->id]['course'][0];
                        } elseif ($memberGroupGraduate->course_session == 2) {
                            $member->course_detail = $res[$group->id]['course'][1];
                        } elseif ($memberGroupGraduate->course_session == 3) {
                            $member->course_detail = $res[$group->id]['course'][2];
                        }
                        
                        $res[$group->id]['list_fail'][] = $member->toArray();
                        continue;
                    } else {
                        $member->reason_fail = "";
                    }

                    $member->status = $memberStatus;
                    if ($memberGroupGraduate->course_session == 1) {
                        $res[$group->id]['course'][0]['members'][] = $member->toArray();
                    } elseif ($memberGroupGraduate->course_session == 2) {
                        $res[$group->id]['course'][1]['members'][] = $member->toArray();
                    } elseif ($memberGroupGraduate->course_session == 3) {
                        $res[$group->id]['course'][2]['members'][] = $member->toArray();
                    } else {
                        $res[$group->id]['list_no_course'][] = $member->toArray();
                    }
                }

                unset($listActivityByGroup);
                unset($listActivitiesDefByGroup);
                unset($listStudentGrades);
            }

            $file_name = now()->format('d_m_y_h_i_s_') . 'xuat_lich_thi.xlsx';
            return Excel::download(new ExportNewMultipleListStudentExam($res, $term, $block_search, $course, $camThi), $file_name);
        } catch (Exception $th) {
            Log::error("---------dev check------------");
            Log::error($th);
            Log::error("---------err end export_danh_sach_thi ------------");
            return $this->redirectWithStatus('danger', 'Có lỗi xảy ra. Không thể export file excel!');
        }
    }

    public function exportGroupsExamDetail(Request $request) {
        $term_id = $request->term_id;
        $query = Group::query();
        $query->join('group_graduate', 'list_group.id', '=', 'group_graduate.group_id')
        ->join('group_member', function($join){
            $join->on('list_group.id', '=','group_member.groupid');
            $join->on('group_member.member_login', '=', 'group_graduate.student_login');
        });
        if(isset($term_id) && $term_id != null) { 
            $query->where('list_group.pterm_id', '=', $term_id);
        }
        $query->where('is_virtual', '=', 0)
        ->groupBy('list_group.id')
        ->groupBy('group_graduate.course_session')
        ->select([
            'group_graduate.group_id',
            'list_group.group_name',
            'list_group.skill_code',
            'list_group.psubject_code',
            'list_group.start_date',
            'list_group.end_date',
            'group_graduate.course_session AS "Slot"',
            DB::raw('COUNT( group_member.id ) AS number_student'),
            DB::raw('SUM( CASE WHEN group_graduate.status > 0 THEN 1 ELSE 0 END ) AS number_student_fail')
        ]);
        $dataExport = $query->get();
        return Excel::download(new StatisticStuBannedExamsByClass($dataExport), "Thống kê danh sách cấm thi theo lớp.xlsx");
    }

    /**
     * 
     * Xuất danh sách cấm thi
     * 
     * <AUTHOR>
     * @since 30/05/2023
     * @param Illuminate\Http\Request $request
     */
    public function exportListStudentNotExam(Request $request)
    {
        # code...
    }

    /**
     * 
     * Đồng bộ danh sách thi
     * 
     * <AUTHOR>
     * @since 29/11/2023
     * @param Illuminate\Http\Request $request
     */
    public function syncGraduateCalendar($request) {
        $rule  =  array(
            'group_id' => "required|exists:list_group,id"
        ) ;

        $messages = [
            'group_id.required'     => 'Lớp Không được bỏ trống',
            'group_id.exists'       => 'Lớp Không hợp lệ',
        ];

        $validator = Validator::make($request->all(),$rule, $messages);
        if($validator->fails()) {
            $mess = $validator->errors()->first();
            return ResponseBuilder::Fail($mess);
        }

        $groupId = $request->group_id;
        $group = Group::select([
                'id',
                'pterm_id',
                'body_id',
                'is_virtual'
            ])
            ->where('id', $groupId)
            ->where('is_virtual', 0)
            ->first();
        
        if (!$group) {
            return ResponseBuilder::Fail("Lớp không hợp lệ");
        }

        $dataCheck = ExamHelper::SyncExamAssignment($group->pterm_id, $group->body_id, [$groupId]);
        if ($dataCheck === true) {
            return ResponseBuilder::Success([], 'Đồng bộ thành công');
        } else {
            return ResponseBuilder::Fail($dataCheck[1]);
        }
    }
}
