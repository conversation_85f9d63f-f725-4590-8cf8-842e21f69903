<?php


namespace App\Repositories\Admin;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Lib;
use App\Models\Fu\GroupMember;
use App\Models\Fu\Activity;
use App\Models\Fu\Area;
use App\Models\Fu\Room;
use App\Models\Fu\Slot;
use App\Repositories\BaseRepository;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;

class RoomRepository extends BaseRepository
{

    public function getModel()
    {
        return Room::class;
    }

    /**
     * kéo danh sách phòng học vào ngày được chỉ định
     *
     * @param  mixed $request
     * @return mixed $response
     */
    public function getCheckRoomByDate(Request $request)
    {
        try {
            $current_date = Carbon::now()->format('Y-m-d');
            if (isset($request->from_date)) {
                $from_date = $request->from_date;
            } else {
                $from_date = $current_date;
            }

            if (isset($request->to_date)) {
                $to_date = $request->to_date;
            } else {
                $to_date = $current_date;
            }
            if (isset($request->area_id) && $request->area_id != "" && $request->area_id != null) {
                $area_id = $request->area_id;
            } else {
                $area_id = null;
            }
            if (isset($request->selected_date) && $request->selected_date !=0 && $request->selected_date != null && count($request->selected_date) > 0) {
                $selected_date = $request->selected_date;
            } else {
                $selected_date = null;
            }

            $list_room = Room::where('is_deleted','=',0)->with(['activities' => function ($query) use ($from_date, $to_date, $selected_date) {
                $query->whereBetween('day', [$from_date, $to_date]);
                if ($selected_date != null) {
                    $query->whereIn('day', $selected_date);
                }
            }])
                ->whereHas('area', function ($query) use ($area_id) {
                    if ($area_id != null) {
                        $query->where('id', '=', $area_id);
                    }
                    $query->where('is_offline_area', '=', 1);
                });
            if (isset($request->room_ids) && $request->room_ids != "" && $request->room_ids != null && count($request->room_ids) > 0) {
                $list_room = $list_room->whereIn('id', $request->room_ids);
            }
            $list_room = $list_room->get();
            $list_slot = Slot::pluck('id')->toArray();
            $period = CarbonPeriod::create($from_date, $to_date);
            $list_date = [];
            foreach ($period as $date) {
                $date = $date->format('Y-m-d');
                if($selected_date == null || !in_array($date, $selected_date)){
                    continue;
                }
                $list_date[] = [
                    'date' => $date,
                    'rooms' => []
                ];
            }

            foreach ($list_date as &$date) {
                foreach ($list_room as $room) {
                    $room_slots = [];
                    foreach ($list_slot as $slot) {
                        $_room_slot = [
                            'slot_id' => $slot,
                            'slot_name' => $slot,
                            'is_booked' => false,
                            'group_name' => null,
                            'group_id' => null,
                            'subject' => null,
                            'activity_id' => null,
                            'lecturer' => null,
                            'description' => '',
                        ];
                        foreach ($room->activities as $activity) {
                            if ($activity->day == $date['date']) {
                                if ($activity->slot == $slot) {
                                    $_room_slot['is_booked'] = true;
                                    $_room_slot['group_name'] = $activity->group_name;
                                    $_room_slot['group_id'] = $activity->groupid;
                                    $_room_slot['subject'] = $activity->psubject_name . "($activity->psubject_code)";
                                    $_room_slot['subject_code'] = $activity->psubject_code;
                                    $_room_slot['activity_id'] = $activity->id;
                                    $_room_slot['lecturer'] = $activity->leader_login;
                                    $_room_slot['description'] = $activity->description;
                                    break;
                                }
                            }
                        }
                        $room_slots[] = $_room_slot;
                    }
                    $date['rooms'][] = [
                        'room_id' => $room->id,
                        'room_name' => $room->room_name,
                        'slots' => $room_slots
                    ];
                }
            }

            return response($list_date, 200);
        } catch (\Throwable $th) {
            Log::error("getCheckRoomByDate: \n" . json_encode($th));
            return response($th, 500);
        }
    }

    /**
     * đăng ký phòng 
     *
     * @param  mixed $request
     * @return mixed $response
     */
    public function postRegisterRoom(Request $request)
    {
        try {
            if (isset($request->area_id) && $request->area_id != "" && $request->area_id != null) {
                $area = Area::find($request->area_id);
            } else {
                return response([
                    'status' => 'error',
                    'message' => 'Không tìm thấy khu vực'
                ], 400);
            }


            if (isset($request->room_id) && $request->room_id != "" && $request->room_id != null) {
                $room = Room::find($request->room_id);
            } else {
                return response(
                    [
                        'status' => 'error',
                        'message' => 'Không tìm thấy phòng học'
                    ],
                    400
                );
            }
            if (isset($request->date) && $request->date != "" && $request->date != null) {
                # code...
            } else {
                return response(
                    [
                        'status' => 'error',
                        'message' => 'Không tìm thấy ngày'
                    ],
                    400
                );
            }
            if (isset($request->slot) && $request->slot != "" && $request->slot != null) {
                $slot = Slot::find($request->slot);
            } else {
                return response(
                    [
                        'status' => 'error',
                        'message' => 'Không tìm thấy buổi'
                    ],
                    400
                );
            }
            $isset_activity = Activity::where('room_id', $request->room_id)
                ->where('day', $request->date)
                ->where('slot', $request->slot)
                ->first();
            if ($isset_activity != null) {
                return response(
                    [
                        'status' => 'error',
                        'message' => 'Phòng học đã được đăng ký'
                    ],
                    400
                );
            }
            Activity::create([
                    'day' => $request->date,
                    'slot' => $request->slot,
                    'room_id' => $room->id,
                    'lastmodifier_login' => auth()->user()->user_login,
                    'create_time' => now(),
                    'lastmodified_time' => now(),
                    'description' => $request->description ?? '',
                    'room_name' => $room->room_name,
                    'area_id' => $area->id,
                    'area_name' => $area->area_name,
                    'start_time' => $request->date . " " . $slot->slot_start,
                    'end_time' => $request->date . " " . $slot->slot_end,
                    'is_online' => 0,
            ]);
        } catch (\Throwable $th) {
            Log::error("postRegisterRoom: \n" . json_encode($th));
            return response($th, 500);
        }
    }
    
    /**
     * xóa đăng ký phòng học
     *
     * @param  mixed $request
     * @return mixed $response
     */
    public function deleteBookingRoom(Request $request){
        try {
            $activity = Activity::find($request->activity_id);
            if($activity == null){
                return response([
                    'status' => 'error',
                    'message' => 'Không tìm thấy id book phòng '
                ], 400);
            }
            $activity->delete();
            return response([
                'status' => 'success',
                'message' => 'Xóa thành công'
            ], 200);
        } catch (\Throwable $th) {
            Log::error("deleteBookingRoom: \n" . json_encode($th));
            return response($th, 500);
        }
    }
    
    /**
     * chỉnh sửa thông tin phòng học
     *
     * @param  mixed $request
     * @return mixed $response
     */
    public function putUpdateBookingRoom(Request $request){
        try {
            $activity = Activity::find($request->activity_id);
            if($activity == null){
                return response([
                    'status' => 'error',
                    'message' => 'Không tìm thấy id book phòng '
                ], 400);
            }
            $activity->description = $request->description ?? '';
            $activity->lastmodifier_login = auth()->user()->user_login;
            $activity->lastmodified_time = now();
            $activity->save();
            return response([
                'status' => 'success',
                'message' => 'update thành công'
            ], 200);
        } catch (\Throwable $th) {
            Log::error("putUpdateBookingRoom: \n" . json_encode($th));
            return response($th, 500);
        }
    }
}
