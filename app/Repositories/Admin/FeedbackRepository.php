<?php


namespace App\Repositories\Admin;


use App\Http\Lib;
use App\Models\FeedbackRole;
use App\Models\FeedbackRoleUser;
use App\Models\Fu\Activity;
use App\Models\Fu\Block;
use App\Models\Fu\Feedback;
use App\Models\Fu\FeedbackDetail;
use App\Models\Fu\Term;
use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use App\Models\Fu\Subject;
use App\Models\Fu\Department;
use App\Models\T7\Configuration;
use App\Repositories\BaseRepository;
use App\Models\Dra\T1UserRole;
use App\Utils\ResponseBuilder;
use App\Models\Fu\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use function foo\func;

class FeedbackRepository extends BaseRepository
{
    public function getModel()
    {
        return Feedback::class;
    }

    public function index()
    {
        $configBlock1 = Configuration::where('module_name', 'feedback_config_day_block_1')->first();
        $configBlock2 = Configuration::where('module_name', 'feedback_config_day_block_2')->first();
        $configEnglish = Configuration::where('module_name', 'feedback_config_day_english')->first();
        $departments = Department::all();
        $users = User::select('user_login')->where('user_level', 4)->get();
        $currentTerm = Term::where('startday', '<=', now())
            ->where('endday', '>=', now())
            ->first();
        return $this->view('feedback.index', [
            'users' => $users,
            'currentTerm' => $currentTerm,
            'terms' => Term::orderBy('id', 'DESC')->get() ?? [],
            'departments' => $departments,
            'teachers' => User::where('user_level', 2)->orderBy('id', 'DESC')->get(),
            'variableConfig' => [
                'config_day_block_1' => $configBlock1,
                'config_day_block_2' => $configBlock2,
                'config_day_english' => $configEnglish,
            ]
        ]);
    }

    public function getGroupByTerm($request)
    {
        $result = Group::join('course', 'course.id', 'list_group.body_id')
            ->where('list_group.is_virtual', 0)
            ->select('list_group.group_name as group_name')
            ->distinct()
            ->where('course.term_id', $request->term_id)
            ->where('type', 1)
            ->orderBy('list_group.group_name')
            ->get();

        return Lib::res('success', 'Query success', $result);
    }

    public function getBlockNameByTerm($request)
    {
        $name = [];
        $result = Block::where('term_id', $request->term_id)->get();
        foreach ($result as $item)
        {
            $name[] = $item->block_name;
        }

        return Lib::res('success', 'Query success', $name);
    }

    public function getFeedback($request)
    {
        $result = Group::join('feedback', 'feedback.groupID', 'list_group.id')
            ->where('list_group.is_virtual', 0)
            ->where('pterm_id', $request->term_id)
            ->where('list_group.group_name', $request->group_name)
            ->select('psubject_name', 'psubject_code', 'groupid', 'feedback.id', 'GPA', 'open', 'planer_login', 'hit', 'day', 'opener_login', 'teacher_login')
            ->get();
        foreach ($result as $item)
        {
//            $item->activity = $this->getActivityByGroup($item->groupid);
            $hit = $item->hit;
            $feedback = Feedback::where('groupId', $item->groupid)->first();
            if ($feedback) {
                $hit = FeedbackDetail::where('feedbackID', $feedback->id)
                ->groupBy('student_login')
                ->orderBy('time')
                ->get();
    
                $hit = count($hit);
                $feedback->hit = $hit;
                $item->hit = $hit;
                // $feedback->save();
            }

            $item->GPA_temp = round($item->GPA, 1);
            $item->members = GroupMember::where('groupid', $item->groupid)->count();
            $item->ratio = round($item->hit / $item->members * 100, 1);
        }

        return Lib::res('success', 'Query success', $result);
    }

    public function getActivityByGroup($group_id)
    {
        return Activity::where('groupid', $group_id)
            ->select('leader_login', DB::raw('count(*) as total'))
            ->where('done', 1)->groupBy('leader_login')->first();
    }

    public function viewFeedback($id)
    {
        $feedback = Feedback::findOrFail($id);
        $feedback->load('group:id,psubject_name,psubject_code,group_name');

        $listSubjectOnline = Subject::getListSubjectOnline(true);
        $listQuestion = [];
        if (in_array($feedback->group->psubject_code, $listSubjectOnline)) {
            $listQuestion = config('localStorage')->feedback_new_1 ?? config('localStorage')->feedback;
        } else {
            $listQuestion = config('localStorage')->feedback ?? config('localStorage')->feedback_new_1;
        }

        $data = FeedbackDetail::where('feedbackID', $id)
        ->groupBy('student_login')
        ->orderBy('time')
        ->get();
        $total = 0;
        foreach ($data as $item) {
            $total += $item->gpa();
        }
        
        $feedback->average = round($total ? $total / $data->count():0, 1);

        // kiểm tra cán bộ
        $checkTeacher = false;
        $listTeacher = User::select([
            'user_login',
            DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ",user.user_givenname) as user_name'),
        ])
        ->leftJoin('activity', 'activity.leader_login', '=', 'user.user_login')
        ->where('activity.groupId', $feedback->groupID)
        // ->where('user.user_level', 2)
        ->groupBy('user_login')
        ->get();

        foreach ($listTeacher as $value) {
            if ($value->user_login == $feedback->teacher_login) {
                $checkTeacher = true;
            }
        }

        return $this->view('feedback.view', [
            'feedback_details' => $data,
            'feedback' => $feedback,
            'listQuestion' => $listQuestion,
            'listTeacher' => $listTeacher,
            'checkTeacher' => $checkTeacher,
            
        ]);
    }

    public function updateStatusById($request)
    {
        $feedback = Feedback::findOrFail($request->id);
        $feedback->open = $request->open ? 1:0;
        $feedback->planer_login = $request->planer_login ?? '';
        $feedback->save();

        return Lib::res('success', 'Update success', $feedback);
    }

    public function updateHitById($request)
    {
        $feedback = Feedback::findOrFail($request->id);
        $feedback_details = FeedbackDetail::where('feedbackID', $request->id)->get();
        $members = GroupMember::where('groupid', $feedback->groupID)->count();
        $total = 0;
        foreach($feedback_details as $item) {
            $total += $item->gpa();
        }
        $total = $total ? $total / count($feedback_details):0;
        $feedback->hit = $feedback_details->count();
        $feedback->GPA = $total;
        $feedback->GPA_temp = round($feedback->GPA, 1);
        $feedback->members = $members;
        $feedback->ratio = round($feedback->hit / $feedback->members * 100, 1);

        sleep(1);
        return Lib::res('success', 'Update success', $feedback);
    }

    public function synAllCurrentClass($request)
    {
        $ids = $request->ids;
        $array = [];
        foreach ($ids as $key => $id) {
            $feedback = Feedback::findOrFail($id['id']);
            $feedback_details = FeedbackDetail::where('feedbackID', $id['id'])->get();
            $members = GroupMember::where('groupid', $feedback->groupID)->count();
            $total = 0;
            foreach($feedback_details as $item) {
                $total += $item->gpa();
            }
            $total = $total ? $total / count($feedback_details):0;
            $feedback->hit = $feedback_details->count();
            $feedback->GPA = $total;
            $feedback->GPA_temp = round($feedback->GPA, 1);
            $feedback->members = $members;
            $feedback->ratio = round($feedback->hit / $feedback->members * 100, 1);
            $array[] = $feedback;
        }

        return Lib::res('success', 'Update all success', $array);
    }

    public function feedbackRole($request)
    {
        $roles = FeedbackRole::orderBy('id','desc')->get();
        foreach ($roles as $role) {
            $sub_master = json_decode($role->sub_master, true);
            $sub_master_data = [];
            if ($sub_master) {
                // dd($sub_master);
                foreach ($sub_master as $sub) {
                    $sub_master_data[] = $sub['user_login'];
                }
            }

            $role->sub_master_text = implode($sub_master_data, ', ');
        }

        return $this->view('feedback.role', [
            'roles' => $roles,
        ]);
    }

    public function feedbackRoleEdit($id)
    {
        $role = FeedbackRole::with('details')->findOrFail($id);
        $role->subject = json_decode($role->skill_code, true) ?? [];
        $role->sub_master_array = json_decode($role->sub_master, true) ?? [];
        $role->user = $role->details->pluck('user_login')->toArray() ?? [];
        $subject = [];
        $sub_master = [];
        foreach($role->subject as $subject) {
            $subject[$subject['skill_code']] = $subject['skill_code'];
        }
        foreach($role->sub_master_array as $sub_master_item) {
            $sub_master[$sub_master_item['user_login']] = $sub_master_item['user_login'];
        }
        $role->subject = $subject;
        $role->sub_master_array = $sub_master;
        $teachers = User::select('user_login','user_givenname','user_surname','user_middlename')->whereIn('user_level', [2,11])->get();
        $subjects = Subject::select('skill_code','subject_name')->groupBy('skill_code','subject_name')->get();

        return $this->view('feedback.role_edit', [
            'role' => $role,
            'teachers' => $teachers,
            'subjects' => $subjects,
        ]);
    }

    public function feedbackRoleStore($request)
    {
        $role_id = $request->role_id;
        $name = $request->name;
        $master = $request->master;
        $subject = $request->subject;
        $sub_master = $request->sub_master;
        $users = $request->users;
        $subject_data = [];
        $sub_master_data = [];
        if ($subject) {
            foreach ($subject as $item) {
                $subject_data[] = [
                    'skill_code' => $item,
                ];
            }
        }
        if ($sub_master) {
            foreach ($sub_master as $item) {
                $sub_master_data[] = [
                    'user_login' => $item,
                ];
            }
        }
        if ($users) {
            FeedbackRoleUser::where('role_id', $role_id)->delte();
            foreach ($users as $item) {
                FeedbackRoleUser::updateOrCreate([
                    'role_id' => $role_id,
                    'user_login' => $item,
                ]);
            }
        }
        $subject_data = json_encode($subject_data);
        $sub_master_data = json_encode($sub_master_data);
        FeedbackRole::where('id', $role_id)->update([
            'name' => $name,
            'slug' => str_slug($name),
            'skill_code' => $subject_data,
            'sub_master' => $sub_master_data,
            'master' => $master,
        ]);

        return $this->redirectWithStatus('success','Chỉnh sửa thành công');
    }

    
    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function getListGroupName(Request $request)
    {
        $termId = $request->get('term_id', null);
        // $listData = 
    }

    public function updateFeedback(Request $request)
    {
        $user = auth()->user();
        $feedback = Feedback::where('id', $request->feedbackId)->first();
        $teacherLogin = $request->get('leader_login', null);
        if ($user->user_level != 1 && $feedback->planer_login != $user->user_login) {
            $listRole = T1UserRole::where('user_login', $user->user_login)->get();
            $listRole = array_column($listRole->toArray(), 'role_id');
            if (!in_array(48, $listRole) && !in_array(88, $listRole)) {
                return back()->with([
                    'status' => [
                        'type' => 'danger', 
                        'messages' => 'bạn không đủ quyền để chỉnh sửa'
                    ]
                ]);
            }
        }

        if ($teacherLogin != null) {
            $feedback->teacher_login = $teacherLogin;
            $feedback->save();
        }

        DB::beginTransaction();
        try {
            foreach (($request->comment ?? []) as $key => $value) {
                FeedbackDetail::where('feedbackID', $request->feedbackId)
                ->where('id', $key)
                ->update(['edited_comment' => trim(strip_tags($value))]);
            }

            DB::commit();
            return back()->with([
                'status' => [
                    'type' => 'success', 
                    'messages' => 'Cập nhập thành công'
                ]
            ]);
        } catch (\Exception $th) {
            DB::rollBack();
            return back()->with([
                'status' => [
                    'type' => 'danger', 
                    'messages' => 'Cập nhập thất bại vui lòng kiểm tra lại dữ liệu hoặc báo với cán bộ IT.'
                ]
            ]);
        }
        
    }
}
