<?php


namespace App\Repositories\Admin;

use App\User;
use Carbon\Carbon;

use App\Repositories\BaseRepository;
use App\Utils\ResponseBuilder;
use App\Models\Fee\Fee;
use App\Models\Fee\FeeDetail;
use App\Models\Fee\Plan;
use App\Models\Fee\PlanDetail;
use App\Models\Fee\Transaction;
use App\Models\Fee\FeeMail;
use App\Models\Fee\FeeLog;
use App\Models\Fee\FeeMailLog;
use App\Models\T7\CourseResult;
use App\Models\TranferT7Course;
use App\Mail\FeeMail as FeeMailNew;
use App\Models\Fu\Term;
use App\Models\EnglishDetail;
use App\Models\EnglishDetailLevel;
use App\Models\Dra\CurriCulum;


use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
// use PhpOffice\PhpSpreadsheet\Shared\Date;
use App\helper\FeeHelper;
use App\helper\FeeMailHelper;
use App\Http\Controllers\Admin\SystemController;
use App\Imports\UsersImport;
use App\Models\Brand;
use App\Models\Fu\GroupMember;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Maatwebsite\Excel\Facades\Excel;

class FeeMailRepository extends BaseRepository
{
    protected $listStatusEnglish = [
        -4 => 'Thi lại',
        -3 => 'Chưa đạt',
        -2 => 'Đang học',
        -1 => 'Trượt điểm danh',
        0 => 'Chưa học',
        1 => 'Đạt',
        2 => 'Miễn Giảm'
    ];

    public function getModel()
    {
        return Fee::class;
    }


    /**
     * 
     * Màn hình quản lý mail sinh viên
     * 
     * <AUTHOR>
     * @since 06/08/2022
     * @version 1.0
     */
    public function indexFeeMail($request)
    {
        $campusCode = session('campus_db');
        $terms = Term::on($campusCode)->orderBy('id', 'desc')->get();
        $typeStudent = FeeMail::LIST_TYPE_STUDENT;
        $listBrand = Brand::select([
            'brand.code',
            'brand.name'
        ])->leftJoin('dra_curriculum', 'dra_curriculum.brand_code' ,'=', 'brand.code')
        ->whereNotNull('dra_curriculum.id')
        ->groupBy('brand.code')
        ->orderBy('brand.id', 'desc')
        ->get();
        return $this->view('fee.feemail.index', [
            'terms' => $terms,
            'campus_db' => $campusCode,
            'type_student' => $typeStudent,
            'list_brand' => $listBrand,
            // 'area_name' => $area_name,
        ]);
    }


    /**
     * 
     * Màn hình hiển thị chi tết mail phí của sinh viên
     * 
     * <AUTHOR>
     * @since 15/10/2022
     * @version 1.0
     */
    public function detailFeeMail($request, $id)
    {
        $campusCode = session('campus_db');
        $feeMail = FeeMail::on($campusCode)->find($id);
        if (!$feeMail) {
            return back();
        }
        
        $listDetailLog = FeeMailLog::LIST_NAME_LOG_DATA;
        $listDetailStatusEnglish = FeeMail::LIST_STATUS_ENGLISH;
        $listStyleStatusEnglish = [
            FeeMail::STATUS_ENGLISH_NO_PROCES   => 'btn-label-danger',
            FeeMail::STATUS_ENGLISH_PROCES_HDI  => 'btn-label-success',
            FeeMail::STATUS_ENGLISH_PROCES_HDI_UNKNOWN  => 'btn-label-success',
            FeeMail::STATUS_ENGLISH_PROCES_RELEARN_50   => 'btn-label-success',
            FeeMail::STATUS_ENGLISH_PROCES_RELEARN_100  => 'btn-label-success',
            FeeMail::STATUS_ENGLISH_PROCES_IMPROVE  => 'btn-label-success',
            FeeMail::STATUS_ENGLISH_PROCES_REFUND   => 'btn-label-success',
            FeeMail::STATUS_ENGLISH_PROCES_REFUND_BUT_LEARN => 'btn-label-success',
        ];
        $feeMailLogs = FeeMailLog::on($campusCode)
        ->where('fee_mail_id', $feeMail->id)
        ->orderBy('id', 'ASC')
        ->get();
        $terms = Term::on($campusCode)->orderBy('id', 'desc')->get();
        return $this->view('fee.feemail.detail', [
            'feeMail' => $feeMail,
            'feeMailLogs' => $feeMailLogs,
            'campus_db' => $campusCode,
            'listDetailLog' => $listDetailLog,
            'statusEnglish' => [
                'name' => $listDetailStatusEnglish,
                'style' => $listStyleStatusEnglish
            ]
        ]);
    }


    /**
     * 
     * Màn hình hiển thị chi tết mail phí của sinh viên
     * 
     * <AUTHOR>
     * @since 15/10/2022
     * @version 1.0
     */
    public function updateFeeMail($id)
    {
        $campusCode = session('campus_db');
        $feeMail = FeeMail::on($campusCode)->find($id);
        if (!$feeMail) {
            return back();
        }
        
        $listDetailLog = FeeMailLog::LIST_NAME_LOG_DATA;
        $feeMailLogs = FeeMailLog::on($campusCode)
        ->where('fee_mail_id', $feeMail->id)
        ->orderBy('id', 'ASC')
        ->get();
        $terms = Term::on($campusCode)->orderBy('id', 'desc')->get();
        return $this->view('fee.feemail.detail', [
            'feeMail' => $feeMail,
            'feeMailLogs' => $feeMailLogs,
            'campus_db' => $campusCode,
            'listDetailLog' => $listDetailLog
        ]);
    }


    public function checkEnglishAgain($request)
    {
        $id = $request->fee_mail_id;
        $campusCode = session('campus_db');
        $feeMail = FeeMail::on($campusCode)
        ->where('english_status', FeeMail::STATUS_ENGLISH_PROCES_REFUND)
        ->where('is_locked', 0)
        ->find($id);
        if (!$feeMail) {
            return ResponseBuilder::Fail("Dữ liệu đã đóng băng hoặc sinh đã được xử lý.");
        }

        $listNameStatusEnglish = FeeMail::LIST_STATUS_ENGLISH;
        $listSkillCodeEnglish = [
            1 => 'ENT111', 
            2 => 'ENT121', 
            3 => 'ENT211', 
            4 => 'ENT221'
        ];
        $lastTerm = Term::find($feeMail->term_id);
        // kiểm tra xem có lớp tiếng anh chưa 
        $checkInsideGroup = GroupMember::query()
        ->join('fu_group', 'fu_group.id', '=', 'group_member.groupid')
        ->where('group_member.member_login', $feeMail->user_login)
        ->where('fu_group.pterm_id', $lastTerm->id)
        ->whereIn('fu_group.skill_code', $listSkillCodeEnglish)
        ->where('is_virtual', 0)
        ->count();

        if ($checkInsideGroup < 1) {
            return ResponseBuilder::Fail("Sinh viên không học tiếng anh kỳ $lastTerm->term_name");
        }

        // xử lý
        $secondTerm = Term::on($campusCode)
            ->where('id', '<', $lastTerm->id)
            ->orderBy('id', 'desc')
            ->first();
        DB::connection($campusCode)->beginTransaction();
        try {
            // Kiểm tra đã xử lý phí chưa
            if ($feeMail->english_status == 1) {
                Log::channel('fee-semester')->info(("[$campusCode][$lastTerm->term_name] $feeMail->user_code:\t đã xử lý"));
            }

            // kiểm tra bản ghi trừ phí
            $checkEnglishFee = Transaction::on($campusCode)
                ->where('user_login', $feeMail->user_login)
                ->where('note', "Dự thu phí tiếng anh kỳ $lastTerm->term_name")
                ->first();

            // Nếu đã trừ
            if ($checkEnglishFee) {
                // Lấy thông tin log tiếng anh để cập nhập
                $lastEnglish = null;
                $english = EnglishDetail::on($campusCode)->where('user_code', $feeMail->user_code)->first();
                if ($english) {
                    $lastEnglish = EnglishDetailLevel::on($campusCode)
                    ->where('english_id', $english->id)
                    ->where('term_id', '<', $lastTerm->id)
                    ->orderBy('create_time','desc')
                    ->first();
                }

                // lấy thông tin ví sinh viên
                $fee = Fee::on($campusCode)->where('user_login', $feeMail->user_login)->first();
                if (!$fee) { // không có ví
                    Log::channel('fee-semester')->info(("[$campusCode][$lastTerm->term_name] $feeMail->user_code:\t Không có ví"));
                    return ResponseBuilder::Fail("[$campusCode][$lastTerm->term_name] $feeMail->user_code:\t Không có ví");
                }

                // Thông tin sinh viên
                $user_original = User::on($campusCode)->where('user_code', $fee->user_code)->first();
                $amount = $checkEnglishFee->amount;
                // Kiểm tra xem được xếp lớp chưa
                $checkLearnEnglish = GroupMember::on($campusCode)
                ->select([
                    'group_member.member_login',
                    'fu_group.id as group_id',
                    'fu_group.skill_code',
                    'fu_group.psubject_code',
                    'fu_group.group_name',
                    'fu_group.pterm_name',
                    'fu_group.pterm_id'
                ])
                ->join('fu_group', 'fu_group.id', '=', 'group_member.groupid')
                ->where('group_member.member_login', $feeMail->user_login)
                ->where('fu_group.pterm_id', $lastTerm->id)
                ->whereIn('fu_group.skill_code', $listSkillCodeEnglish)
                ->where('is_virtual', 0)
                ->first();

                // Nếu đang được xếp lớp
                if ($checkLearnEnglish) {
                    // lấy tiếng anh cuối học 
                    $lastLearnEnglish = CourseResult::on($campusCode)
                    ->select([
                        't7_course_result.id',
                        't7_course_result.student_login',
                        't7_course_result.psubject_code',
                        't7_course_result.skill_code',
                        't7_course_result.term_id',
                        't7_course_result.pterm_name',
                    ])
                    ->join('fu_group', 'fu_group.id', '=', 't7_course_result.groupid')
                    ->where('t7_course_result.skill_code', $listSkillCodeEnglish[$feeMail->english_level])
                    ->where('t7_course_result.student_login', $feeMail->user_login)
                    ->where('t7_course_result.term_id', '<', $lastTerm->id)
                    ->where('fu_list_group.is_virtual', 0)
                    ->orderBy('t7_course_result.id', 'desc')
                    ->first();
                    
                    $oldstatusName = $listNameStatusEnglish[$feeMail->english_status] ?? "";
                    // Nếu có
                    if ($lastLearnEnglish) {
                        // Kiểm tra có giống tiếng anh hiện tại không (học lại)
                        if ($lastLearnEnglish->skill_code == $checkLearnEnglish->skill_code) {
                            // kiểm tra Sinh viên có học lại liền kỳ không
                            if($secondTerm->id == $lastLearnEnglish->term_id) {
                                Log::channel('fee-semester')->info(("[$campusCode][$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Trừ tiếng anh học lại 50%"));
                                $discount = $amount / 2;

                                // trừ phí tiếng anh
                                $fee->study_wallet = $fee->study_wallet - ($amount - $discount);
                                Transaction::on($campusCode)->create([
                                    'user_code' => $fee->user_code,
                                    'user_login' => $fee->user_login,
                                    'type' => 'HP',
                                    'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $feeMail->english_level),
                                    'invoice_id' => 0,
                                    'amount' => $discount,
                                    'execute' => 1,
                                    'in_out' => 0,
                                    'note' => $fee->brand_code . '|' . ($feeMail->ki_thu) . '|LV' . ($lastEnglish->level ?? $feeMail->english_level) . '|' . $lastEnglish->note,
                                    'invoice_date_create' => now(),
                                    'term_name' => $lastTerm->term_name,
                                    'created_by' => auth()->user()->user_login,
                                ]);

                                // Thêm log mail
                                FeeMailLog::on($campusCode)->create([
                                    'fee_mail_id' => $feeMail->id,
                                    'auth' => auth()->user()->user_login,
                                    'user_login' => $feeMail->user_login,
                                    'action' => FeeMailLog::ACTION_PROCESS_FEE,
                                    'description' => "Chuyển trạng thái phí tiếng anh từ ($oldstatusName) sang (" . ($listNameStatusEnglish[FeeMail::STATUS_ENGLISH_PROCES_RELEARN_50] ?? "") . ')',
                                    'data' => json_encode([
                                        'old_english_status' => $feeMail->english_status,
                                        'new_english_status' => FeeMail::STATUS_ENGLISH_PROCES_RELEARN_50,
                                    ]),
                                    'id_action' => request()->ip()
                                ]);

                                // thay cập nhập trạng thái tiếng anh 
                                $feeMail->english_status = FeeMail::STATUS_ENGLISH_PROCES_RELEARN_50;
                                $feeMail->save();
                                $fee->save();

                                DB::connection($campusCode)->commit();
                                return ResponseBuilder::Success([], "[$campusCode][$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Trừ tiếng anh học lại 50%");
                            } else {
                                Log::channel('fee-semester')->info(("[$campusCode][$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Trừ tiếng anh học lại 100%"));
                                $discount = $amount;

                                // trừ phí tiếng anh
                                $fee->study_wallet = $fee->study_wallet - $discount;
                                Transaction::on($campusCode)->create([
                                    'user_code' => $fee->user_code,
                                    'user_login' => $fee->user_login,
                                    'type' => 'HP',
                                    'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $feeMail->english_level),
                                    'invoice_id' => 0,
                                    'amount' => $discount,
                                    'execute' => 1,
                                    'in_out' => 0,
                                    'note' => $fee->brand_code . '|' . ($feeMail->ki_thu) . '|LV' . ($lastEnglish->level ?? $feeMail->english_level) . '|' . $lastEnglish->note,
                                    'invoice_date_create' => now(),
                                    'term_name' => $lastTerm->term_name,
                                    'created_by' => auth()->user()->user_login,
                                ]);

                                // Thêm log mail
                                FeeMailLog::on($campusCode)->create([
                                    'fee_mail_id' => $feeMail->id,
                                    'auth' => auth()->user()->user_login,
                                    'user_login' => $feeMail->user_login,
                                    'action' => FeeMailLog::ACTION_PROCESS_FEE,
                                    'description' => "Chuyển trạng thái phí tiếng anh từ ($oldstatusName) sang (" . ($listNameStatusEnglish[FeeMail::STATUS_ENGLISH_PROCES_RELEARN_100] ?? "") . ')',
                                    'data' => json_encode([
                                        'old_english_status' => $feeMail->english_status,
                                        'new_english_status' => FeeMail::STATUS_ENGLISH_PROCES_RELEARN_100,
                                    ]),
                                    'id_action' => request()->ip()
                                ]);

                                // thay cập nhập trạng thái tiếng anh 
                                $feeMail->english_status = FeeMail::STATUS_ENGLISH_PROCES_RELEARN_100;
                                $feeMail->save();
                                $fee->save();

                                DB::connection($campusCode)->commit();
                                return ResponseBuilder::Success([], "[$campusCode][$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Trừ tiếng anh học lại 100%");
                            }
                        } else {
                            // học đi
                            Log::channel('fee-semester')->info("[$campusCode][$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Trừ tiếng anh học đi");
                            $amount = $checkEnglishFee->amount;
                            $discount = $amount;
                            
                            // trừ phí tiếng anh
                            $fee->study_wallet = $fee->study_wallet - $discount;
                            Transaction::on($campusCode)->create([
                                'user_code' => $fee->user_code,
                                'user_login' => $fee->user_login,
                                'type' => 'HP',
                                'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $feeMail->english_level),
                                'invoice_id' => 0,
                                'amount' => $discount,
                                'execute' => 1,
                                'in_out' => 0,
                                'note' => $fee->brand_code . '|' . ($feeMail->ki_thu) . '|LV' . ($lastEnglish->level + 1) . '|' . $lastEnglish->note,
                                'invoice_date_create' => now(),
                                'term_name' => $lastTerm->term_name,
                                'created_by' => auth()->user()->user_login,
                            ]);

                            // Thêm log mail
                            FeeMailLog::on($campusCode)->create([
                                'fee_mail_id' => $feeMail->id,
                                'auth' => auth()->user()->user_login,
                                'user_login' => $feeMail->user_login,
                                'action' => FeeMailLog::ACTION_PROCESS_FEE,
                                'description' => "Chuyển trạng thái phí tiếng anh từ ($oldstatusName) sang (" . ($listNameStatusEnglish[FeeMail::STATUS_ENGLISH_PROCES_HDI] ?? "") . ')',
                                'data' => json_encode([
                                    'old_english_status' => $feeMail->english_status,
                                    'new_english_status' => FeeMail::STATUS_ENGLISH_PROCES_HDI,
                                ]),
                                'id_action' => request()->ip()
                            ]);

                            // thay cập nhập trạng thái tiếng anh 
                            $feeMail->english_status = FeeMail::STATUS_ENGLISH_PROCES_HDI;
                            $feeMail->save();
                            $fee->save();

                            DB::connection($campusCode)->commit();
                            return ResponseBuilder::Success([], "[$campusCode][$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Trừ tiếng anh học đi");
                        }
                    } else {
                        // Mặc định trừ 2tr6
                        // Trạng thái học sinh là HDI
                        if ($user_original->study_status == 1) {
                            Log::channel('fee-semester')->info(("[$campusCode][$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Trừ tiếng anh học đi - không rõ trạng thái trước đó"));
                            $amount = $checkEnglishFee->amount;
                            $discount = $amount;
                            
                            // trừ phí tiếng anh
                            $fee->study_wallet = $fee->study_wallet - $discount;
                            Transaction::on($campusCode)->create([
                                'user_code' => $fee->user_code,
                                'user_login' => $fee->user_login,
                                'type' => 'HP',
                                'type_extension' => 'Tiếng anh|Lv' . ($feeMail->english_level),
                                'invoice_id' => 0,
                                'amount' => $discount,
                                'execute' => 1,
                                'in_out' => 0,
                                'note' => $fee->brand_code . '|' . ($feeMail->ki_thu) . '|LV' . ($feeMail->english_level) . '|Tiếng anh học đi',
                                'invoice_date_create' => now(),
                                'term_name' => $lastTerm->term_name,
                                'created_by' => auth()->user()->user_login,
                            ]);

                            // Thêm log mail
                            FeeMailLog::on($campusCode)->create([
                                'fee_mail_id' => $feeMail->id,
                                'auth' => auth()->user()->user_login,
                                'user_login' => $feeMail->user_login,
                                'action' => FeeMailLog::ACTION_PROCESS_FEE,
                                'description' => "Chuyển trạng thái phí tiếng anh từ ($oldstatusName) sang (" . ($listNameStatusEnglish[FeeMail::STATUS_ENGLISH_PROCES_HDI] ?? "") . ')',
                                'data' => json_encode([
                                    'old_english_status' => $feeMail->english_status,
                                    'new_english_status' => FeeMail::STATUS_ENGLISH_PROCES_HDI,
                                ]),
                                'id_action' => request()->ip()
                            ]);

                            // thay cập nhập trạng thái tiếng anh 
                            $feeMail->english_status = FeeMail::STATUS_ENGLISH_PROCES_HDI;
                            $feeMail->save();
                            $fee->save();

                            DB::connection($campusCode)->commit();
                            return ResponseBuilder::Success([], "[$campusCode][$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Trừ tiếng anh học đi");
                        }
                    }
                }
            } else {
                Log::channel('fee-semester')->info(("[$campusCode][$lastTerm->term_name] $feeMail->user_code:\t Chưa trừ phí dự thu"));
                return ResponseBuilder::Success("Sinh viên $feeMail->user_code có vấn đề liên lạc lại với cán bộ IT để kiểm tra");
            }

            DB::connection($campusCode)->commit();
            // DB::connection($campusCode)->rollback();
        } catch (\Exception $ex) {
            DB::connection($campusCode)->rollback();
            Log::channel('fee-semester')->error("dev check!");
            Log::channel('fee-semester')->error($ex);

            return ResponseBuilder::Fail($ex->getMessage());
        }
    }

    /**
     * 
     * Quét phí chủ động
     * 
     * <AUTHOR>
     * @since 29/08/2022
     * @version 1.0
     */
    public function scanFee($request)
    {
        /**
         * type_fee:
         *  1. quét phí cho kỳ tiếp theo
         *  2. quét phí cho kỳ hiện tại
         */
        ini_set('max_execution_time', -1);
        $campusCode = session('campus_db'); 
        $lastTerm = Term::on($campusCode)
        ->orderBy('id', 'desc')
        ->first();

        $countFee = Fee::on($campusCode)
        ->select('fees.*')
        ->leftJoin('fee_mails', 'fees.user_login', '=', 'fee_mails.user_login')
        ->where('fee_mails.term_id', $lastTerm->id)
        // ->where('fee_mails.status_fee', 0)
        ->count();
        if ($countFee == 0) {
            return ResponseBuilder::Fail('Vui lòng quét dữ liệu gửi mail trước khi quét phí.');
        }
        
        try {
            Artisan::call("fee:sync_new", [
                'campus_code' => $campusCode,
                'term_id' => $lastTerm->id,
                'auth' => auth()->user()->user_login,
            ]);
            return ResponseBuilder::Success([], 'Quét phí thành công');
        } catch (\Exception $ex) {
            Log::error("Fee");
            Log::error($ex);
            return ResponseBuilder::Fail('có lỗi xảy ra, vui lòng thử lại.');
        }
    }


    
    /**
     * 
     * Tạo dữ liệu gửi mail theo kỳ
     * 
     * <AUTHOR>
     * @since 06/08/2022
     * @version 1.0
     * @param Request $request  
     */
    public function createTermFeeMail($request)
    {
        $campusCode = session('campus_db'); 
        $rule  =  array(
            'term_id' => "required|exists:$campusCode.fu_term,id"
        ) ;

        $messages = [
            'term_id.required'    => 'Kỳ học Không được bỏ trống',
            'term_id.exists'    => 'Kỳ học Không hợp lệ',
        ];

        $validator = Validator::make($request->all(),$rule, $messages);
        if($validator->fails()) {
            $mess = $validator->errors()->first();
            return ResponseBuilder::Fail($mess);
        }

        $termId = $request->get('term_id', null);
        $term = Term::find($termId);
        // lấy dữ liệu kỳ cuối cùng
        $LastTerm = Term::orderBy('id', 'desc')->first();
        // Kiểm tra kỳ tạo phí có phải kỳ cuối cùng không 
        if ($LastTerm->id != $term->id) {
            return ResponseBuilder::Fail('Kỳ học không hợp lệ, vui lòng thử lại.');
        }

        $termGetData = Term::on($campusCode)->where('id', '<', $LastTerm->id)->orderBy('ordering', 'DESC')->first();

        $dataJson = Storage::disk('local')->get("public/confirm_student/$campusCode/$termGetData->term_name.json");
        $dataJsonArr = json_decode($dataJson, true);

        try {
            Artisan::queue("fee:create_fee_mail", [
                'campus_code' => $campusCode,
                'term_id' => $termId
            ]);
            return ResponseBuilder::Success([], 'Quá trình tạo mail thành công, quá trình có thể diễn ra khá lâu vui lòng đợi');
        } catch (\Exception $ex) {
            Log::error($ex);
            return ResponseBuilder::Fail('Kỳ học không hợp lệ, vui lòng thử lại.');
        }
    }


    
    /**
     * 
     * Tạo dữ liệu gửi mail theo kỳ
     * 
     * <AUTHOR>
     * @since 22/09/2022
     * @version 1.0
     * @param Request $request  
     */
    public function updateTermFeeMail($request)
    {
        ini_set('max_execution_time', -1);
        $campusCode = session('campus_db');
        $lastTerm = Term::on($campusCode)->orderBy('id', 'desc')->first();
        try {
            Artisan::call("fee:update_fee_mail", [
                'campus_code' => $campusCode,
                'term_id' => $lastTerm->id,
                'auth' => auth()->user()->user_login
            ]);
            return ResponseBuilder::Success([], 'Cập nhập dữ liệu thành công');
        } catch (\Exception $ex) {
            Log::error($ex);
            return ResponseBuilder::Fail('Hệ thống lỗi vui lòng thử lại, vui lòng thử lại hoặc liên hệ với cán bộ IT.');
        }
    }

    /**
     * Xuất dữ liệu feemail
     */
    public function exportFeeMail($request)
    {
        ini_set('max_execution_time', -1);
        $campusCode = session('campus_db');
        $termId = $request->get('term_id', null);
        if ($termId == null) {
            $LastTerm = Term::on($campusCode)->orderBy('id', 'desc')->first();
            $termId = $LastTerm->id;
        }

        $datas = DB::connection($campusCode)->select("SELECT
            fee_mails.user_code,
            fee_mails.brand_code,
            fee_mails.ki_thu,
            fees.study_wallet,
            fees.relearn_wallet,
            fees.etc_wallet,
            fees.promotion_wallet,
            fee_mails.amount,
            fee_mails.hoc_ky,
            fee_mails.tien_sach,
            fee_mails.tieng_anh,    
            (
                CASE fee_mails.last_english_status
                    WHEN -4 THEN 
                        \"Thi lại\"
                    WHEN -3 THEN 
                        \"Trượt\"
                    WHEN -2 THEN 
                        \"Đang học\"
                    WHEN -1 THEN 
                        \"Trượt điểm danh\"
                    WHEN 0 THEN 
                        \"Chưa học\"
                    WHEN 1 THEN 
                        \"Đạt\"
                    WHEN 2 THEN 
                        \"Miễn Giảm\"
                    ELSE
                        \"Không xác định\"
                    END
            ) AS last_english_status, 
            (
                CASE fee_mails.study_status
                    WHEN 1 THEN 
                        \"HDI ( Học đi )\"
                    WHEN 2 THEN 
                        \"Tạm ngừng bắt buộc\"
                    WHEN 3 THEN 
                        \"TN1 ( Bảo lưu tự nguyện )\"
                    WHEN 4 THEN 
                        \"THO ( Dropout )\"
                    WHEN 5 THEN 
                        \"Chuyển cơ sở\"
                    WHEN 6 THEN 
                        \"Đình chỉ\"
                    WHEN 7 THEN 
                        \"BB3 ( Chờ thi lại tốt nghiệp )\"
                    WHEN 8 THEN 
                        \"TNG ( Đã tốt nghiệp )\"
                    WHEN 9 THEN 
                        \"Đã rút hồ sơ\"
                    WHEN 10 THEN 
                        \"TN2 ( Học lại )\"
                    WHEN 11 THEN 
                        \"TN3 ( Chờ xếp lớp học lại )\"
                    WHEN 12 THEN 
                        \"BB1 ( Bảo lưu do kỷ luật ) \"
                    WHEN 13 THEN 
                        \"BB2 ( Chờ xét tốt nghiệp )\"
                    WHEN 14 THEN 
                        \"BB4 ( Chờ đủ điều kiện tốt nghiệp )\"
                    WHEN 15 THEN 
                        \"TN11 (  Bảo lưu tự nguyện )\"
                    WHEN 16 THEN 
                        \"TN21 ( Học lại )\"
                    WHEN 17 THEN 
                        \"TN31 ( Chờ xếp lớp học lại )\"
                    WHEN 18 THEN 
                        \"TN4 ( Chờ xếp lớp học đi )\"
                    WHEN 19 THEN 
                        \"BB5 ( Chờ thi đi tốt nghiệp )\"
                    WHEN 20 THEN 
                        \"TN22 ( Học lại )\"
                    ELSE
                        \"Không xác định\"
                    END
            ) AS study_status_text,
            fee_mails.status_fee,
            fee_mails.english_level
        FROM
            fee_mails 
        JOIN fees ON fees.user_login = fee_mails.user_login 
        WHERE
            term_id = ?
        ", [$request->term_id]);

        header('Content-Type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename="danh_sach_mail_phi_' . time() . '.csv";');
        $header = [
            'Mã sinh viên',
            'Ngành',
            'Kỳ thu phí',
            'Ví học phí',
            'Ví học lại',
            'Ví khác',
            'Ví ưu đãi',
            'Phí dự thu',
            'Học phí ngành',
            'Học phí sách',
            'Học phí tiếng anh',
            'Trạng thái cuối tiếng anh',
            'Trạng thái',
            'Trạng thái thu phí',
            'Level tiếng anh',
        ];

        $f = fopen('php://output', 'w');
        fputcsv($f, $header);
        foreach ($datas as $key => $line) {
            $dataExport = (array)$line;
            fputcsv($f, $dataExport);
        }
    }


    public function sendMailCurrentTerm($request)
    {
        ini_set('max_execution_time', -1);
        $campusCode = session('campus_db'); 
        $rule  =  array(
            'term_id' => "required|exists:$campusCode.fu_term,id"
        ) ;

        $messages = [
            'term_id.required'    => 'Kỳ học Không được bỏ trống',
            'term_id.exists'    => 'Kỳ học Không hợp lệ',
        ];

        $validator = Validator::make($request->all(),$rule, $messages);
        if($validator->fails()) {
            $mess = $validator->errors()->first();
            return ResponseBuilder::Fail($mess);
        }

        $termId = $request->get('term_id', null);
        $term = Term::find($termId);
        $auth = auth()->user()->user_login;
        Log::channel('fee-semester')->info((auth()->user()->user_login ?? "") . ": Tiến hành gửi mail hàng loạt cho kỳ " . ($term->term_name ?? ""));
        $listMail = FeeMail::on($campusCode)
            ->where('term_id', $term->id)
            ->where('status_fee', 0)
            ->where('is_locked', 0)
            ->get();
        foreach ($listMail as $key => $mail) {
            $fee = Fee::select([
                    'id',
                    'user_code',
                    'study_wallet'
                ])
                ->where('user_login', $mail->user_login)
                ->first();

            if ($fee == null) {
                continue;
            }
            
            if ($mail->hoc_ky == 0 && $mail->tieng_anh == 0 && $mail->amount == 0) {
                continue;
            }

            $detailFee = [
                'hoc_ky' => $mail->hoc_ky,
                'tien_sach' => $mail->tien_sach,
                'tieng_anh' => $mail->tieng_anh,
            ];
            $checkZero = ($detailFee['hoc_ky'] + $detailFee['tien_sach'] + $detailFee['tieng_anh']) - $fee->study_wallet;
            $checkZero = $checkZero < 0 ? 0 : $checkZero;
            // if ($mail->amount <= 0) {
            //     // dump($mail->amount, $fee->study_wallet);
            // } else {
            $user = User::select([
                    'user_login',
                    'user_surname',
                    'user_middlename',
                    'user_givenname'
                ])
                ->where('user_login', $mail->user_login)
                ->first();
            if ($user == null) {
                continue;
            }

            // Thêm log mail
            FeeMailLog::on($campusCode)->create([
                'fee_mail_id' => $mail->id,
                'auth' => $auth,
                'user_login' => $mail->user_login,
                'action' => FeeMailLog::ACTION_SEND_MAIL,
                'description' => "Gửi mail phí $mail->brand_code",
                'data' => json_encode($detailFee),
                'id_action' => request()->ip()
            ]);

            $full_name = $user->full_name;
            $newSendFee = new FeeMailNew($mail, $fee, $full_name, $detailFee);
            Mail::to($mail->user_login . "")->queue($newSendFee);
            // Mail::to("dev")->queue(new FeeMailNew($mail, $fee, $full_name, $detailFee));
            // }
        }

        return ResponseBuilder::Success([], 'Duyệt gửi mail thành công');
    }


    public function sendMailCurrentId($request)
    {
        ini_set('max_execution_time', -1);
        $campusCode = session('campus_db'); 
        $rule  =  array(
            'id' => "required|exists:$campusCode.fee_mails,id"
        ) ;

        $messages = [
            'id.required'    => 'Id Không được bỏ trống',
            'id.exists'    => 'Id Không hợp lệ',
        ];

        $validator = Validator::make($request->all(),$rule, $messages);
        if($validator->fails()) {
            $mess = $validator->errors()->first();
            return ResponseBuilder::Fail($mess);
        }

        $mailId = $request->get('id', null);
        $mail = FeeMail::on($campusCode)
            ->where('id', $mailId)
            ->first();

        $fee = Fee::select([
                'id',
                'user_code',
                'study_wallet'
            ])
            ->where('user_login', $mail->user_login)
            ->first();

        if ($fee == null) {
            return ResponseBuilder::Fail("Sinh viên chưa có ví!");
        }
        
        $detailFee = [
            'hoc_ky' => $mail->hoc_ky,
            'tien_sach' => $mail->tien_sach,
            'tieng_anh' => $mail->tieng_anh,
        ];
        $checkZero = ($detailFee['hoc_ky'] + $detailFee['tien_sach'] + $detailFee['tieng_anh']) - $fee->study_wallet;
        $checkZero = $checkZero < 0 ? 0 : $checkZero;
        $user = User::select([
                'user_login',
                'user_surname',
                'user_middlename',
                'user_givenname'
            ])
            ->where('user_login', $mail->user_login)
            ->first();
        if ($user == null) {
            return ResponseBuilder::Fail("Sinh viên không tồn tại!");
        }

        try {

            // Thêm log mail
            FeeMailLog::on($campusCode)->create([
                'fee_mail_id' => $mail->id,
                'auth' => auth()->user()->user_login,
                'user_login' => $mail->user_login,
                'action' => FeeMailLog::ACTION_SEND_MAIL,
                'description' => "Gửi mail Test phí $mail->brand_code",
                'data' => json_encode($detailFee),
                'id_action' => request()->ip()
            ]);
            $full_name = $user->full_name;
            $newSendFee = new FeeMailNew($mail, $fee, $full_name, $detailFee);
            Mail::to($mail->user_login . "")->queue($newSendFee);
            // Mail::to("dev")->queue($newSendFee);
    
            return ResponseBuilder::Success([], 'Duyệt gửi mail thành công');
        } catch (\Exception $ex) {
            return ResponseBuilder::Fail("Đã có lỗi xảy ra, vui lòng liên hệ với cán bộ IT!");
        }
    }


    public function sendMailTest($request)
    {
        ini_set('max_execution_time', -1);
        $campusCode = session('campus_db'); 
        $rule  =  array(
            'term_id' => "required|exists:$campusCode.fu_term,id"
        ) ;

        $messages = [
            'term_id.required'    => 'Kỳ học Không được bỏ trống',
            'term_id.exists'    => 'Kỳ học Không hợp lệ',
        ];

        $validator = Validator::make($request->all(),$rule, $messages);
        if($validator->fails()) {
            $mess = $validator->errors()->first();
            return ResponseBuilder::Fail($mess);
        }
        
        $listUser = $request->get('list_user', []);
        $listStudent = $request->get('list_student', []);
        if (count($listUser) == 0 || count($listStudent) == 0) {
            return ResponseBuilder::Fail("Dữ liệu đầu vào không hợp lệ");
        }
        
        $termId = $request->get('term_id', null);
        $term = Term::find($termId);
        $listMail = FeeMail::on($campusCode)
            ->where('term_id', $term->id)
            ->where('status_fee', 0)
            ->whereIn('user_login', $listStudent)
            ->get();
        foreach ($listMail as $key => $mail) {
            $fee = Fee::select([
                    'id',
                    'user_code',
                    'study_wallet'
                ])
                ->where('user_login', $mail->user_login)
                ->first();

            if ($fee == null) {
                continue;
            }
            
            $detailFee = [
                'hoc_ky' => $mail->hoc_ky,
                'tien_sach' => $mail->tien_sach,
                'tieng_anh' => $mail->tieng_anh,
            ];
            $checkZero = ($detailFee['hoc_ky'] + $detailFee['tien_sach'] + $detailFee['tieng_anh']) - $fee->study_wallet;
            $checkZero = $checkZero < 0 ? 0 : $checkZero;
            // if ($mail->amount <= 0) {
            //     // dump($mail->amount, $fee->study_wallet);
            // } else {
            $user = User::select([
                    'user_login',
                    'user_surname',
                    'user_middlename',
                    'user_givenname'
                ])
                ->where('user_login', $mail->user_login)
                ->first();
            if ($user == null) {
                continue;
            }

            // Thêm log mail
            FeeMailLog::on($campusCode)->create([
                'fee_mail_id' => $mail->id,
                'auth' => auth()->user()->user_login,
                'user_login' => $mail->user_login,
                'action' => FeeMailLog::ACTION_SEND_MAIL,
                'description' => "Gửi mail Test phí $mail->brand_code",
                'data' => json_encode($detailFee),
                'id_action' => request()->ip()
            ]);

            $full_name = $user->full_name;
            foreach ($listUser as $key => $userSend) {
                $newSendFee = new FeeMailNew($mail, $fee, $full_name, $detailFee);
                Mail::to("$userSend")->queue($newSendFee);
            }
            // }
        }

        return ResponseBuilder::Success([], 'Duyệt gửi mail thành công');
    }


    public function syncFeeMail($request)
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', -1);
        $campusCode = session('campus_db'); 
        $term = Term::on($campusCode)->find($request->term_id);
        if (!$term) {
            return ResponseBuilder::Fail("Dữ liệu kỳ học không hợp lệ");
        }

        DB::connection($campusCode)->beginTransaction();
        try {
            FeeHelper::synFeeMailByTerm($campusCode, $term);
            DB::connection($campusCode)->commit();
            return ResponseBuilder::Success([], 'Đồng bộ dữ liệu mail thành công');
        } catch (\Exception $ex) {
            Log::channel('fee-semester')->error($ex->getMessage());
            DB::connection($campusCode)->rollBack();
            return ResponseBuilder::Fail("Có lỗi xảy ra: " . $ex->getMessage());
        }
    }


    public function createFeeForNewUser($request)
    {
        ini_set('max_execution_time', -1);
        $campusCode = session('campus_db'); 
        $listUser = User::on($campusCode)
            ->select([
                'fu_user.id',
                'fu_user.curriculum_id',
                'fu_user.user_code',
                'fu_user.user_login',
                'fu_user.study_status',
                'fu_user.curriculum_id',
                'fu_user.kithu'
            ])
            ->leftJoin('fees', 'fees.user_code', '=', 'fu_user.user_code')
            ->whereNull('fees.id')
            ->where('fu_user.user_level', 3)
            ->where('fu_user.study_status', 1)
            ->where('fu_user.curriculum_id', '>', 0)
            ->get();

        // Lấy danh sách sinh viên mới chưa có ví
        $listCurriculum = [];
        $listFeePlan = [];

        // dd($listUser);
        $count = 0;
        DB::connection($campusCode)->beginTransaction();
        try {
            foreach ($listUser as $user) {
                $checkFee = Fee::on($campusCode)
                ->where('user_code', $user->user_code)
                ->count();
    
                // Kiểm tra có ví chưa
                if($checkFee > 0) {
                    continue;
                }
    
                // kiểm tra Feeplan
                if (!isset($listCurriculum[$user->curriculum_id])) {
                    $listCurriculum[$user->curriculum_id] = CurriCulum::on($campusCode)->find($user->curriculum_id);
                }
    
                $curriCulum = $listCurriculum[$user->curriculum_id];
                if (!$curriCulum || $curriCulum == null) {
                    continue;
                }
    
                // kiểm tra Feeplan
                if (!isset($listFeePlan[$curriCulum->id])) {
                    $feePlan = Plan::on($campusCode)->with('details')
                    // ->where('brand_code', $curriCulum->id)
                    ->where('curriculum_id', $curriCulum->id)
                    ->first();
                    if ($feePlan) {
                        $feePlan->details = PlanDetail::on($campusCode)->where('fee_plan_id', $feePlan->id)->get();
                    }

                    $listFeePlan[$curriCulum->id] = $feePlan;
                }
    
                $feePlan = $listFeePlan[$curriCulum->id];
                if (!$feePlan || $feePlan == null) {
                    continue;
                }

                $fee = Fee::on($campusCode)->create([
                    'user_code' => $user->user_code,
                    'user_login' => $user->user_login,
                    'study_status' => $user->study_status,
                    'curriculum_id' => $user->curriculum_id,
                    'brand_code' => $curriCulum->brand_code,
                    'fee_plan_id' => $feePlan->id,
                    'ki_thu' => $user->kithu,
                ]);
    
                foreach ($feePlan->details as $item) {
                    if (Str::contains($user->user_code, 'PF') && $fee->pf_transfer == 0) {
                        if ($item->type_fee == 1) {
                            if ($curriCulum->brand_code == 'MA') {
                                $cost = 3400000;
                            } else {
                                $cost = 3600000;
                            }
                            
                            FeeDetail::on($campusCode)->create([
                                'fee_id' => $fee->id,
                                'ki_thu' => $item->ki_thu,
                                'type_fee' => $item->type_fee,
                                'amount' => $cost,
                                'discount' => $item->discount,
                                'version' => $item->version,
                            ]);
                        }
                    } else {
                        FeeDetail::on($campusCode)->create([
                            'fee_id' => $fee->id,
                            'ki_thu' => $item->ki_thu,
                            'type_fee' => $item->type_fee,
                            'amount' => $item->amount,
                            'discount' => $item->discount,
                            'version' => $item->version,
                        ]);
                    }
                }

                $count++;
            }

            DB::connection($campusCode)->commit();
            // DB::connection($campusCode)->rollBack();
            return ResponseBuilder::Success($count, 'Quét phí thành công');
        } catch (\Exception $ex) {
            dd($ex);
            Log::channel('fee-semester')->error($ex);
            DB::connection($campusCode)->rollBack();
            return ResponseBuilder::Fail("Có lỗi xảy ra: " . $ex->getMessage());
        }
    }

    /**
     * 
     * Xử lý dữ liệu phí tiếng anh
     * 
     * <AUTHOR>
     * @since 30/09/2022
     * @version 1.0
     * @param Request $request  
     */
    public function ProceesEnglishFee($request)
    {
        // BaseData
        ini_set('max_execution_time', -1);
        $campusCode = session('campus_db');
        $listSkillCodeEnglish = [
            1 => 'ENT111', 
            2 => 'ENT121', 
            3 => 'ENT211', 
            4 => 'ENT221'
        ];
        $lastTerm = Term::on($campusCode)->orderBy('id', 'desc')->first();
        $secondTerm = Term::on($campusCode)
            ->where('id', '<', $lastTerm->id)
            ->orderBy('id', 'desc')
            ->first();
        
        // Lấy danh sách sinh viên dự thu tiếng
        $listMail = FeeMail::on($campusCode)
            ->where('term_id', $lastTerm->id)
            ->where('tieng_anh', '>', 0)
            ->where('english_status', 0)
            // ->where('user_login', 'haiptph25839')
            ->get();
            
        DB::connection($campusCode)->beginTransaction();
        try {
            foreach ($listMail as $key => $mail) {
                // Kiểm tra đã xử lý phí chưa
                if ($mail->english_status == 1) {
                    Log::channel('fee-semester')->info(("[$campusCode][$lastTerm->term_name] $mail->user_code:\t đã xử lý"));
                    continue;
                }

                // kiểm tra bản ghi trừ phí
                $checkEnglishFee = Transaction::on($campusCode)
                    ->where('user_login', $mail->user_login)
                    ->where('note', "Dự thu phí tiếng anh kỳ $lastTerm->term_name")
                    ->first();

                // Nếu đã trừ
                if ($checkEnglishFee) {
                    // Lấy thông tin log tiếng anh để cập nhập
                    $lastEnglish = null;
                    $english = EnglishDetail::on($campusCode)->where('user_code', $mail->user_code)->first();
                    if ($english) {
                        $lastEnglish = EnglishDetailLevel::on($campusCode)
                        ->where('english_id', $english->id)
                        ->where('term_id', '<', $lastTerm->id)
                        ->orderBy('create_time','desc')
                        ->first();
                    }

                    // lấy thông tin ví sinh viên
                    $fee = Fee::on($campusCode)->where('user_login', $mail->user_login)->first();
                    if (!$fee) { // không có ví
                        Log::channel('fee-semester')->info(("[$campusCode][$lastTerm->term_name] $mail->user_code:\t Không có ví"));
                        continue;
                    }

                    $user_original = User::on($campusCode)->where('user_code', $fee->user_code)->first();
                    $amount = $checkEnglishFee->amount;
                    // Kiểm tra xem được xếp lớp chưa
                    $checkLearnEnglish = GroupMember::on($campusCode)
                    ->select([
                        'group_member.member_login',
                        'fu_group.id as group_id',
                        'fu_group.skill_code',
                        'fu_group.psubject_code',
                        'fu_group.group_name',
                        'fu_group.pterm_name',
                        'fu_group.pterm_id'
                    ])
                    ->join('fu_group', 'fu_group.id', '=', 'group_member.groupid')
                    ->where('group_member.member_login', $mail->user_login)
                    ->where('fu_group.pterm_id', $lastTerm->id)
                    ->whereIn('fu_group.skill_code', $listSkillCodeEnglish)
                    ->where('is_virtual', 0)
                    ->first();

                    // Nếu đang được xếp lớp
                    if ($checkLearnEnglish) {
                        // lấy tiếng anh cuối học 
                        $lastLearnEnglish = CourseResult::on($campusCode)
                        ->select([
                            't7_course_result.id',
                            't7_course_result.student_login',
                            't7_course_result.psubject_code',
                            't7_course_result.skill_code',
                            't7_course_result.term_id',
                            't7_course_result.pterm_name',
                        ])
                        ->join('fu_group', 'fu_group.id', '=', 't7_course_result.groupid')
                        ->where('t7_course_result.skill_code', $listSkillCodeEnglish[$mail->english_level])
                        ->where('t7_course_result.student_login', $mail->user_login)
                        ->where('t7_course_result.term_id', '<', $lastTerm->id)
                        ->where('fu_list_group.is_virtual', 0)
                        ->orderBy('t7_course_result.id', 'desc')
                        ->first();
                        
                        // Nếu có
                        if ($lastLearnEnglish) {
                            // Kiểm tra có giống tiếng anh hiện tại không (học lại)
                            if ($lastLearnEnglish->skill_code == $checkLearnEnglish->skill_code) {
                                // kiểm tra Sinh viên có học lại liền kỳ không
                                if($secondTerm->id == $lastLearnEnglish->term_id) {
                                    Log::channel('fee-semester')->info(("[$campusCode][$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Trừ tiếng anh học lại 50%"));
                                    $discount = $amount / 2;
                                    
                                    // Hồi phí dự thu
                                    $fee->study_wallet = $fee->study_wallet + $amount;
                                    Transaction::on($campusCode)->create([
                                        'user_code' => $fee->user_code,
                                        'user_login' => $fee->user_login,
                                        'type' => 'HP',
                                        'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                                        'invoice_id' => 0,
                                        'amount' => $amount,
                                        'execute' => 1,
                                        'in_out' => 1,
                                        'note' => "Hồi phí dự thu phí tiếng anh kỳ $lastTerm->term_name",
                                        'invoice_date_create' => now(),
                                        'term_name' => $lastTerm->term_name,
                                        'created_by' => 'system',
                                    ]);

                                    // trừ phí tiếng anh
                                    $fee->study_wallet = $fee->study_wallet - ($amount - $discount);
                                    Transaction::on($campusCode)->create([
                                        'user_code' => $fee->user_code,
                                        'user_login' => $fee->user_login,
                                        'type' => 'HP',
                                        'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                                        'invoice_id' => 0,
                                        'amount' => $discount,
                                        'execute' => 1,
                                        'in_out' => 0,
                                        'note' => $fee->brand_code . '|' . (($user_original->kithu ?? $fee->ki_thu) + 1) . '|LV' . ($lastEnglish->level ?? $mail->english_level) . '|' . $lastEnglish->note,
                                        'invoice_date_create' => now(),
                                        'term_name' => $lastTerm->term_name,
                                        'created_by' => 'system',
                                    ]);

                                    if($lastEnglish != null) {
                                        $lastEnglish->payment_status = 1;
                                        $lastEnglish->discount = $discount;
                                        $lastEnglish->save();
                                    }

                                    // thay cập nhập trạng thái tiếng anh 
                                    $mail->english_status = 1;
                                    $mail->save();
                                    $fee->save();
                                } else {
                                    Log::channel('fee-semester')->info(("[$campusCode][$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Trừ tiếng anh học lại 100%"));
                                    $discount = $amount;
                                    
                                    // Hồi phí dự thu
                                    $fee->study_wallet = $fee->study_wallet + $amount;
                                    Transaction::on($campusCode)->create([
                                        'user_code' => $fee->user_code,
                                        'user_login' => $fee->user_login,
                                        'type' => 'HP',
                                        'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                                        'invoice_id' => 0,
                                        'amount' => $amount,
                                        'execute' => 1,
                                        'in_out' => 1,
                                        'note' => "Hồi phí dự thu phí tiếng anh kỳ $lastTerm->term_name",
                                        'invoice_date_create' => now(),
                                        'term_name' => $lastTerm->term_name,
                                        'created_by' => 'system',
                                    ]);

                                    // trừ phí tiếng anh
                                    $fee->study_wallet = $fee->study_wallet - $discount;
                                    Transaction::on($campusCode)->create([
                                        'user_code' => $fee->user_code,
                                        'user_login' => $fee->user_login,
                                        'type' => 'HP',
                                        'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                                        'invoice_id' => 0,
                                        'amount' => $discount,
                                        'execute' => 1,
                                        'in_out' => 0,
                                        'note' => $fee->brand_code . '|' . (($user_original->kithu ?? $fee->ki_thu) + 1) . '|LV' . ($lastEnglish->level ?? $mail->english_level) . '|' . $lastEnglish->note,
                                        'invoice_date_create' => now(),
                                        'term_name' => $lastTerm->term_name,
                                        'created_by' => 'system',
                                    ]);

                                    if($lastEnglish != null) {
                                        $lastEnglish->payment_status = 1;
                                        $lastEnglish->save();
                                    }

                                    // thay cập nhập trạng thái tiếng anh 
                                    $mail->english_status = 1;
                                    $mail->save();
                                    $fee->save();
                                }
                            } else {
                                // học đi
                                    Log::channel('fee-semester')->info(("[$campusCode][$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Trừ tiếng anh học đi"));
                                    $amount = $checkEnglishFee->amount;
                                    $discount = $amount;
                                    
                                    // Hồi phí dự thu
                                    $fee->study_wallet = $fee->study_wallet + $amount;
                                    Transaction::on($campusCode)->create([
                                        'user_code' => $fee->user_code,
                                        'user_login' => $fee->user_login,
                                        'type' => 'HP',
                                        'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                                        'invoice_id' => 0,
                                        'amount' => $amount,
                                        'execute' => 1,
                                        'in_out' => 1,
                                        'note' => "Hồi phí dự thu phí tiếng anh kỳ $lastTerm->term_name",
                                        'invoice_date_create' => now(),
                                        'term_name' => $lastTerm->term_name,
                                        'created_by' => 'system',
                                    ]);

                                    // trừ phí tiếng anh
                                    $fee->study_wallet = $fee->study_wallet - $discount;
                                    Transaction::on($campusCode)->create([
                                        'user_code' => $fee->user_code,
                                        'user_login' => $fee->user_login,
                                        'type' => 'HP',
                                        'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                                        'invoice_id' => 0,
                                        'amount' => $discount,
                                        'execute' => 1,
                                        'in_out' => 0,
                                        'note' => $fee->brand_code . '|' . (($user_original->kithu ?? $fee->ki_thu) + 1) . '|LV' . ($lastEnglish->level + 1) . '|' . $lastEnglish->note,
                                        'invoice_date_create' => now(),
                                        'term_name' => $lastTerm->term_name,
                                        'created_by' => 'system',
                                    ]);

                                    if($lastEnglish != null) {
                                        $lastEnglish->payment_status = 1;
                                        $lastEnglish->save();
                                    }

                                    // thay cập nhập trạng thái tiếng anh 
                                    $mail->english_status = 1;
                                    $mail->save();
                                    $fee->save();
                            }
                        } else {
                            Log::channel('fee-semester')->info(("[$campusCode][$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Cần check"));
                            // Mặc định trừ 2tr6
                        }
                    } else {
                        Log::channel('fee-semester')->info(("[$campusCode][$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Hồi phí tiếng anh do không học"));
                        $fee->study_wallet = $fee->study_wallet + $amount;
                        Transaction::on($campusCode)->create([
                            'user_code' => $fee->user_code,
                            'user_login' => $fee->user_login,
                            'type' => 'HP',
                            'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                            'invoice_id' => 0,
                            'amount' => $amount,
                            'execute' => 1,
                            'in_out' => 1,
                            'note' => "Hồi phí dự thu phí tiếng anh kỳ $lastTerm->term_name",
                            'invoice_date_create' => now(),
                            'term_name' => $lastTerm->term_name,
                            'created_by' => 'system',
                        ]);

                        $mail->english_status = 1;
                        $fee->save();
                        $mail->save();
                    }
                } else {
                    Log::channel('fee-semester')->info(("[$campusCode][$lastTerm->term_name] $mail->user_code:\t Chưa trừ phí dự thu"));
                }
            }

            DB::connection($campusCode)->rollback();
        } catch (\Exception $ex) {
            DB::connection($campusCode)->rollback();
            Log::channel('fee-semester')->error($ex);
        }
    }


    public function exportEnglishFee($request)
    {
        ini_set('max_execution_time', -1);
        $termObj = Term::on(session('campus_db'))->find($request->get('term_id', -1));
        if (!$termObj) return;

        $sql = "SELECT
            tbl_learn_english.user_code,
            tbl_learn_english.member_login,
            tbl_learn_english.full_name,
            fees.study_wallet,
            tbl_learn_english.kithu,
            tbl_learn_english.id AS groupid,
            tbl_learn_english.skill_code,
            tbl_learn_english.psubject_code,
            tbl_learn_english.group_name,
            (
                CASE tbl_base.`status_fee` 
                    WHEN 1 THEN 'Đã hoàn thành' 
                    WHEN 0 THEN 'Chưa hoàn thành' 
                    ELSE null 
                END 
            ) AS status_fee, 
            (
                CASE tbl_base.english_status 
                    WHEN 0 THEN 'Chưa xử lý'
                    WHEN 1 THEN 'Đã xử lý - Trừ học đi'
                    WHEN 2 THEN 'Đã xử lý - Trừ học đi (không xác định)'
                    WHEN 3 THEN 'Đã xử lý - Trừ học lại 50%'
                    WHEN 4 THEN 'Đã xử lý - Trừ học lại 100%'
                    WHEN 5 THEN 'Đã xử lý - Trừ học cải thiện'
                    WHEN 6 THEN 'Đã xử lý - Đã hồi phí'
                    WHEN 7 THEN 'Đã xử lý - Đã hồi phí (được xếp lớp)'
                    ELSE null 
                END 
            ) AS english_status, 
            tbl_base.english_level,
            tbl_base.skill_code_x AS last_english_skill_code,
            transcript_details.`status` AS status_last_learn,
            (
                CASE transcript_details.`status` 
                    WHEN 1 THEN 'Đạt' 
                    WHEN -1 THEN 'Trượt điểm danh' 
                    WHEN -2 THEN
                    -- 'Đang học' 
                    'Không xác định' 
                    WHEN -3 THEN 'Fail quiz' 
                    WHEN -4 THEN 'Thi lại' 
                    WHEN 2 THEN 'Miễn giảm' 
                    ELSE 'không xác định' 
                END 
            ) AS 'english_status_last_learn' 
            FROM
            (
                SELECT
                    fu_user.user_code,
	                CONCAT(fu_user.user_surname,\" \",fu_user.user_middlename,\" \", fu_user.user_givenname) as full_name,
                    member_login,
                    fu_user.kithu,
                    fu_group.id,
                    fu_group.skill_code,
                    fu_group.psubject_code,
                    fu_group.group_name 
                FROM
                    group_member
                    JOIN fu_group ON fu_group.id = group_member.groupid
                    JOIN fu_user ON fu_user.user_login = group_member.member_login 
                WHERE
                    fu_group.pterm_id = ? 
                    AND fu_list_group.is_virtual = 0 
                    AND fu_group.skill_code IN ( 'ENT111', 'ENT121', 'ENT211', 'ENT221' ) 
            ) tbl_learn_english
            LEFT JOIN (
                SELECT
                    fee_mails.user_login,
                    fee_mails.user_code,
                    fee_mails.status_fee,
                    fee_mails.english_status,
                    english_level,
                    ( CASE english_level WHEN 1 THEN 'ENT111' WHEN 2 THEN 'ENT121' WHEN 3 THEN 'ENT211' WHEN 4 THEN 'ENT221' END ) AS skill_code_x 
                FROM
                    fee_mails
                    JOIN fu_user ON fu_user.user_login = fee_mails.user_login 
                WHERE
                    fee_mails.term_id = ? 
                    AND fee_mails.tieng_anh > 0 
            ) tbl_base ON tbl_base.user_login = tbl_learn_english.member_login
            JOIN fees ON fees.user_login = tbl_learn_english.member_login
            LEFT JOIN transcripts ON transcripts.user_login = tbl_base.user_login
            LEFT JOIN transcript_details ON transcripts.id = transcript_details.transcript_id 
        AND tbl_base.skill_code_x = transcript_details.skill_code";

        $datas = DB::connection(session('campus_db'))->select($sql, [$termObj->id, $termObj->id]);
        header('Content-Type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename="danh_sach_phi_tieng_anh_'.time().'.csv";');
        $header = [
            '#', 
            'Mã sinh viên', 
            'Tên đăng nhập', 
            'Họ và tên', 
            'ví học phí',
            'Kì thứ', 
            'id lớp', 
            'Mã chuyển đổi', 
            'Mã môn', 
            'Tên lớp', 
            'Trạng thái mail phí', 
            'Trạng thái xử lý phí tiếng anh', 
            'LV Tiếng anh học lần cuối', 
            // 'Mã Tiếng anh học lần cuối', 
            'Trạng thái Tiếng anh học lần cuối'
        ];
        $f = fopen('php://output', 'w');
        fputcsv($f, $header);
        foreach ($datas as $key => $line) {
            fputcsv($f, [
                ($key + 1),
                $line->user_code,
                $line->member_login,
                $line->full_name,
                $line->study_wallet,
                $line->kithu,
                $line->groupid,
                $line->skill_code,
                $line->psubject_code,
                $line->group_name,
                $line->status_fee,
                $line->english_status,
                $line->last_english_skill_code,
                $line->english_level,
                $line->status_last_learn,
                $line->english_status_last_learn,
            ]);
        }
    }

    public function uploadFeeMailStudent($request)
    {
        $campusCode = session('campus_db');
        $file = $request->file('file');
        if ($file == null) {
            return redirect()->route('admin.fee.feemail.index')->with([
                'reports' => [[
                    'status' => false,
                    'msg' => "File không hợp lệ"
                ]]
            ]);
        }
        try {
            $rows = Excel::toArray(new UsersImport, $file);
            $reposts = FeeMailHelper::getFeeByOption($campusCode, $request->term_id, $rows[0]);
            // dd($reposts);
            return redirect()->route('admin.fee.feemail.index')->with([
                'reports' => $reposts
            ]);
        } catch (Exception $ex) {
            return redirect()->route('admin.fee.feemail.index')->with([
                'reports' => [[
                    'status' => false,
                    'msg' => "Hệ thông phát sinh lỗi, vui lòng liên hệ với IT"
                ]]
            ]);
        }
    }
}
