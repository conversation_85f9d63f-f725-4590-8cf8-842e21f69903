<?php

namespace App\Repositories\Admin;

use App\Models\Fee\StudentWallet;
use App\Models\Fee\WalletTransaction;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\StudentWalletExport;

class StudentWalletRepository extends BaseRepository
{
    // Loại giao dịch
    public const TRANSACTION_DEPOSIT = 'deposit';
    public const TRANSACTION_WITHDRAW = 'withdraw';
    public const TRANSACTION_PAYMENT = 'payment';

    public function getModel()
    {
        return StudentWallet::class;
    }

    public function getWalletList($request)
    {
        $keyword = $request->keyword;
        $status = $request->status;
        $balance_from = $request->balance_from;
        $balance_to = $request->balance_to;
        $date_from = $request->date_from;
        $date_to = $request->date_to;

        // Tính toán thống kê tổng quan
        $statistics = $this->getWalletStatistics($request);

        $query = StudentWallet::select([
            'student_wallets.id',
            'student_wallets.user_code',
            'student_wallets.user_login',
            'student_wallets.balance',
            'student_wallets.is_locked',
            'student_wallets.lock_reason',
            'student_wallets.created_at',
            'student_wallets.updated_at',
            DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ", user.user_givenname) as student_name'),
        ]);
        $query->leftJoin('user', 'student_wallets.user_code', '=', 'user.user_code');

        // Tìm kiếm theo từ khóa
        if ($keyword) {
            $query->where(function ($q) use ($keyword) {
                $q->where('student_wallets.user_code', 'like', "%$keyword%")
                ->orWhere('student_wallets.user_login', 'like', "%$keyword%");
            });
        }

        // Lọc theo trạng thái
        if ($status !== null) {
            $query->where('student_wallets.is_locked', $status);
        }

        // Lọc theo khoảng số dư
        if ($balance_from !== null || $balance_to !== null) {
            if ($balance_from !== null) {
                $query->where('student_wallets.balance', '>=', $balance_from);
            }
            if ($balance_to !== null) {
                $query->where('student_wallets.balance', '<=', $balance_to);
            }
        }

        // Lọc theo khoảng thời gian tạo
        if ($date_from) {
            $query->whereDate('student_wallets.created_at', '>=', $date_from);
        }
        if ($date_to) {
            $query->whereDate('student_wallets.created_at', '<=', $date_to);
        }

        $wallets = $query->orderBy('student_wallets.created_at', 'desc')->paginate(50); // Tăng từ 20 lên 50

        return $this->view('fee.wallet.index', [
            'wallets' => $wallets,
            'keyword' => $keyword,
            'status' => $status,
            'balance_from' => $balance_from,
            'balance_to' => $balance_to,
            'date_from' => $date_from,
            'date_to' => $date_to,
            'statistics' => $statistics
        ]);
    }

    /**
     * Lấy thống kê tổng quan về ví sinh viên với caching
     */
    private function getWalletStatistics($request = null)
    {
        // Cache key dựa trên filters
        $cacheKey = 'wallet_statistics_' . md5(serialize($request ? $request->all() : []));

        return cache()->remember($cacheKey, 300, function () use ($request) { // Cache 5 phút
            $baseQuery = StudentWallet::query();

            // Áp dụng các bộ lọc nếu có
            if ($request) {
                if ($request->keyword) {
                    $baseQuery->where(function ($q) use ($request) {
                        $q->where('user_code', 'like', "%{$request->keyword}%")
                            ->orWhere('user_login', 'like', "%{$request->keyword}%");
                    });
                }
                if ($request->status !== null) {
                    $baseQuery->where('is_locked', $request->status);
                }
                if ($request->date_from) {
                    $baseQuery->whereDate('created_at', '>=', $request->date_from);
                }
                if ($request->date_to) {
                    $baseQuery->whereDate('created_at', '<=', $request->date_to);
                }

                // Validate date range - date_to should not be before date_from
                if ($request->date_from && $request->date_to) {
                    if (strtotime($request->date_to) < strtotime($request->date_from)) {
                        // Swap dates if end date is before start date
                        $temp = $request->date_from;
                        $request->merge(['date_from' => $request->date_to]);
                        $request->merge(['date_to' => $temp]);

                        $baseQuery->whereDate('created_at', '>=', $request->date_from);
                        $baseQuery->whereDate('created_at', '<=', $request->date_to);
                    }
                }
            }

            // Sử dụng single query với aggregation để tối ưu performance
            $stats = $baseQuery->selectRaw('
                COUNT(*) as total_wallets,
                COUNT(CASE WHEN is_locked = 0 THEN 1 END) as active_wallets,
                COUNT(CASE WHEN is_locked = 1 THEN 1 END) as locked_wallets,
                SUM(balance) as total_balance
            ')->first();

            return [
                'total_wallets' => $stats->total_wallets ?? 0,
                'active_wallets' => $stats->active_wallets ?? 0,
                'locked_wallets' => $stats->locked_wallets ?? 0,
                'total_balance' => $stats->total_balance ?? 0
            ];
        });
    }



    public function createWalletForm()
    {
        return $this->view('fee.wallet.create');
    }

    public function createWallet($request)
    {
        $user_code = strtoupper(trim($request->user_code));
        $user_login = trim($request->user_login);

        // Kiểm tra xem sinh viên đã có ví chưa
        $existingWallet = StudentWallet::where('user_code', $user_code)->first();
        if ($existingWallet) {
            // Nếu ví đã tồn tại nhưng thiếu user_login, cập nhật nó
            if (empty($existingWallet->user_login) && !empty($user_login)) {
                $existingWallet->user_login = $user_login;
                $existingWallet->save();
                return $this->redirectWithStatus('success', 'Đã cập nhật thông tin ví cho sinh viên', route('admin.admin.wallet.list'));
            }
            return $this->redirectWithStatus('danger', 'Sinh viên đã có ví', route('admin.admin.wallet.list'));
        }

        // Validate user
        $validation = self::validateUserForWallet($user_code, $user_login);
        if (!$validation['valid']) {
            return $this->redirectWithStatus('danger', $validation['message'], route('admin.admin.wallet.list'));
        }

        DB::beginTransaction();
        try {
            // Tạo ví mới với balance = 0
            $wallet = StudentWallet::create([
                'user_code' => $user_code,
                'user_login' => $user_login,
                'balance' => 0,
                'is_locked' => false
            ]);

            // Log tạo ví thành công
            Log::channel('wallet')->info('Manual wallet created', [
                'user_code' => $user_code,
                'user_login' => $user_login,
                'created_by' => Auth::user()->user_login ?? 'system'
            ]);

            DB::commit();
            return $this->redirectWithStatus('success', 'Tạo ví thành công', route('admin.admin.wallet.list'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Create wallet error: ' . $e->getMessage());
            return $this->redirectWithStatus('danger', 'Tạo ví thất bại: ' . $e->getMessage(), route('admin.admin.wallet.list'));
        }
    }

    public function lockWalletForm($id)
    {
        $wallet = StudentWallet::findOrFail($id);
        return $this->view('fee.wallet.lock', [
            'wallet' => $wallet
        ]);
    }

    public function lockWallet($request, $id)
    {
        $wallet = StudentWallet::findOrFail($id);
        $reason = $request->reason;

        $wallet->is_locked = true;
        $wallet->lock_reason = $reason;
        $wallet->save();

        return $this->redirectWithStatus('success', 'Khóa ví thành công', route('admin.admin.wallet.list'));
    }

    public function unlockWallet($id)
    {
        $wallet = StudentWallet::findOrFail($id);
        $wallet->is_locked = false;
        $wallet->lock_reason = null;
        $wallet->save();

        return $this->redirectWithStatus('success', 'Mở khóa ví thành công', route('admin.admin.wallet.list'));
    }

    public function depositForm($id)
    {
        $wallet = StudentWallet::findOrFail($id);
        return $this->view('fee.wallet.deposit', [
            'wallet' => $wallet
        ]);
    }

    public function deposit($request, $id)
    {
        $wallet = StudentWallet::findOrFail($id);
        $amount = floatval($request->amount);
        $invoice_id = $request->invoice_id;
        $payment_method = $request->payment_method;
        $description = $request->description;

        if ($amount <= 0) {
            return $this->redirectWithStatus('danger', 'Số tiền không hợp lệ', route('admin.wallet.deposit.form', $id));
        }

        if ($wallet->is_locked) {
            return $this->redirectWithStatus('danger', 'Ví đã bị khóa', route('admin.wallet.list'));
        }

        DB::beginTransaction();
        try {
            // Sử dụng method addMoney() từ model
            $wallet->addMoney(
                $amount,
                $description ?: 'Nạp tiền thủ công',
                Auth::user()->user_login ?? 'system',
                $payment_method,
                $invoice_id
            );

            DB::commit();
            return $this->redirectWithStatus('success', 'Nạp tiền thành công', route('admin.wallet.list'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Deposit error: ' . $e->getMessage());
            return $this->redirectWithStatus('danger', 'Nạp tiền thất bại: ' . $e->getMessage(), route('admin.wallet.deposit.form', $id));
        }
    }

    public function transactions($id)
    {
        $wallet = StudentWallet::select(['id', 'user_code', 'user_login', 'balance', 'is_locked', 'created_at', 'updated_at'])
            ->findOrFail($id);

        $transactions = WalletTransaction::select([
            'id',
            'created_at',
            'wallet_type',
            'transaction_type',
            'amount',
            'balance_before',
            'balance_after',
            'payment_method',
            'invoice_id',
            'description',
            'created_by'
        ])
            ->where('user_code', $wallet->user_code)
            ->orderBy('created_at', 'desc')
            ->paginate(30); // Tăng từ 20 lên 30

        return $this->view('fee.wallet.transactions', [
            'wallet' => $wallet,
            'transactions' => $transactions
        ]);
    }

    public function exportWallets($request)
    {
        $keyword = $request->keyword;
        $status = $request->status;

        $query = StudentWallet::query()
            ->when($keyword, function ($q, $keyword) {
                $q->where(function ($subq) use ($keyword) {
                    $subq->where('user_code', 'like', "%$keyword%")
                        ->orWhere('user_login', 'like', "%$keyword%");
                });
            })
            ->when($status !== null, function ($q, $status) {
                $q->where('is_locked', $status);
            })
            ->orderBy('created_at', 'desc');

        $export = new StudentWalletExport($query);
        return Excel::download($export, 'student_wallets.xlsx');
    }

    /**
     * Bulk actions - Khóa nhiều ví cùng lúc
     */
    public function bulkLockWallets($request)
    {
        $wallet_ids = $request->wallet_ids;
        $reason = $request->reason;

        if (empty($wallet_ids)) {
            return $this->redirectWithStatus('danger', 'Vui lòng chọn ít nhất một ví', view('admin.wallet.list'));
        }

        DB::beginTransaction();
        try {
            $updated = StudentWallet::whereIn('id', $wallet_ids)
                ->where('is_locked', false)
                ->update([
                    'is_locked' => true,
                    'lock_reason' => $reason,
                    'updated_at' => now()
                ]);

            DB::commit();
            return $this->redirectWithStatus('success', "Đã khóa {$updated} ví thành công", view('admin.wallet.list'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Bulk lock wallets error: ' . $e->getMessage());
            return $this->redirectWithStatus('danger', 'Khóa ví thất bại: ' . $e->getMessage(), view('admin.wallet.list'));
        }
    }

    /**
     * Bulk actions - Mở khóa nhiều ví cùng lúc
     */
    public function bulkUnlockWallets($request)
    {
        $wallet_ids = $request->wallet_ids;

        if (empty($wallet_ids)) {
            return $this->redirectWithStatus('danger', 'Vui lòng chọn ít nhất một ví', view('admin.wallet.list'));
        }

        DB::beginTransaction();
        try {
            $updated = StudentWallet::whereIn('id', $wallet_ids)
                ->where('is_locked', true)
                ->update([
                    'is_locked' => false,
                    'lock_reason' => null,
                    'updated_at' => now()
                ]);

            DB::commit();
            return $this->redirectWithStatus('success', "Đã mở khóa {$updated} ví thành công", view('admin.wallet.list'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Bulk unlock wallets error: ' . $e->getMessage());
            return $this->redirectWithStatus('danger', 'Mở khóa ví thất bại: ' . $e->getMessage(), view('admin.wallet.list'));
        }
    }

    /**
     * Tự động tạo ví cho sinh viên mới
     */
    public function autoCreateWalletForNewStudent($user_code, $user_login, $initial_balance = 0)
    {
        $user_code = strtoupper(trim($user_code));
        $user_login = trim($user_login);

        // Kiểm tra xem sinh viên đã có ví chưa
        $existingWallet = StudentWallet::where('user_code', $user_code)->first();
        if ($existingWallet) {
            return ['success' => false, 'message' => 'Sinh viên đã có ví'];
        }

        // Validate user
        $validation = self::validateUserForWallet($user_code, $user_login);
        if (!$validation['valid']) {
            return ['success' => false, 'message' => $validation['message']];
        }

        DB::beginTransaction();
        try {
            // Tạo ví với balance = 0 (bỏ qua initial_balance)
            $wallet = StudentWallet::create([
                'user_code' => $user_code,
                'user_login' => $user_login,
                'balance' => 0,
                'is_locked' => false
            ]);

            // Tạo giao dịch khởi tạo
            WalletTransaction::create([
                'user_code' => $wallet->user_code,
                'user_login' => $wallet->user_login,
                'wallet_type' => 'main',
                'transaction_type' => 'create',
                'amount' => 0,
                'balance_before' => 0,
                'balance_after' => 0,
                'description' => 'Tạo ví tự động',
                'created_by' => 'system'
            ]);

            DB::commit();
            Log::info('Auto create wallet success', [
                'user_code' => $user_code,
                'user_login' => $user_login
            ]);

            return ['success' => true, 'message' => 'Tạo ví tự động thành công', 'wallet' => $wallet];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Auto create wallet error: ' . $e->getMessage(), [
                'user_code' => $user_code,
                'user_login' => $user_login
            ]);
            return ['success' => false, 'message' => 'Tạo ví tự động thất bại: ' . $e->getMessage()];
        }
    }

    /**
     * Import form for bulk wallet creation
     */
    public function importWalletsForm()
    {
        return $this->view('fee.wallet.import', [
            'action' => 'create'
        ]);
    }

    /**
     * Import form for bulk money deposit
     */
    public function importDepositsForm()
    {
        return $this->view('fee.wallet.import', [
            'action' => 'deposit'
        ]);
    }

    /**
     * Download import template
     */
    public function downloadImportTemplate($type = 'create')
    {
        $filename = $type === 'create' ? 'template_tao_vi_sinh_vien.xlsx' : 'template_nap_tien_vi.xlsx';
        $export = new \App\Exports\WalletImportTemplate($type);
        return Excel::download($export, $filename);
    }

    /**
     * Process bulk wallet import
     */
    public function processBulkImport($request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:10240', // 10MB max
            'action' => 'required|in:create,deposit'
        ]);

        try {
            $file = $request->file('file');
            $action = $request->action;

            Log::info('Starting wallet bulk import', [
                'action' => $action,
                'filename' => $file->getClientOriginalName(),
                'user' => auth()->user()->user_login ?? 'system'
            ]);

            // Create import instance based on action
            if ($action === 'create') {
                $import = new \App\Imports\WalletBulkImport($action);
            } else {
                $import = new \App\Imports\WalletDepositImport();
            }

            Excel::import($import, $file);

            // Get results
            $results = $import->getResults();

            // Log results
            Log::info('Wallet bulk import completed', [
                'action' => $action,
                'total_rows' => $results['total_rows'],
                'success_count' => $results['success_count'],
                'error_count' => $results['error_count'],
                'user' => auth()->user()->user_login ?? 'system'
            ]);

            // Prepare response message
            $message = "Import hoàn tất: {$results['success_count']} thành công";
            if ($results['error_count'] > 0) {
                $message .= ", {$results['error_count']} lỗi";
            }

            $status = $results['error_count'] > 0 ? 'warning' : 'success';

            return $this->redirectWithStatus($status, $message, route('admin.admin.wallet.list'))
                ->with('import_results', $results);
        } catch (\Exception $e) {
            Log::error('Wallet bulk import failed', [
                'error' => $e->getMessage(),
                'user' => auth()->user()->user_login ?? 'system'
            ]);

            return $this->redirectWithStatus(
                'danger',
                'Import thất bại: ' . $e->getMessage(),
                route('admin.admin.wallet.import.form')
            );
        }
    }

    /**
     * Validate user for wallet creation
     *
     * @param string $user_code
     * @param string $user_login
     * @return array ['valid' => bool, 'message' => string, 'user' => User|null]
     */
    public static function validateUserForWallet($user_code, $user_login)
    {
        // Kiểm tra user tồn tại theo user_code
        $userByCode = \App\Models\Fu\User::where('user_code', $user_code)->first();
        if (!$userByCode) {
            return ['valid' => false, 'message' => "Không tìm thấy sinh viên với mã {$user_code}", 'user' => null];
        }

        // Kiểm tra user tồn tại theo user_login
        $userByLogin = \App\Models\Fu\User::where('user_login', $user_login)->first();
        if (!$userByLogin) {
            return ['valid' => false, 'message' => "Không tìm thấy tài khoản {$user_login}", 'user' => null];
        }

        // Kiểm tra user_code và user_login có thuộc cùng 1 người không
        if ($userByCode->id !== $userByLogin->id) {
            return ['valid' => false, 'message' => "Mã sinh viên {$user_code} và tài khoản {$user_login} không khớp", 'user' => null];
        }

        // Kiểm tra user_level = 3 (sinh viên)
        if ($userByCode->user_level != 3) {
            return ['valid' => false, 'message' => "Tài khoản {$user_login} không phải là tài khoản sinh viên (user_level = {$userByCode->user_level})", 'user' => null];
        }

        return ['valid' => true, 'message' => 'Validation passed', 'user' => $userByCode];
    }
}
