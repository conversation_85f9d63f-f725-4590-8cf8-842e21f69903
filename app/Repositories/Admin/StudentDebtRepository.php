<?php

namespace App\Repositories\Admin;

use App\Models\Fee\StudentDebt;
use App\Models\Fee\DebtPayment;
use App\Models\Fee\StudentWallet;
use App\Models\Fee\WalletTransaction;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Fu\Term;
use App\Models\Fee\FeeType;
use App\Models\Fu\User;

class StudentDebtRepository extends BaseRepository
{
    // Trạng thái công nợ
    const DEBT_STATUS_PENDING = 0;  // Chưa thanh toán
    const DEBT_STATUS_PAID = 1;     // Đã thanh toán
    const DEBT_STATUS_CANCELED = 2; // Đã hủy
    const STUDY_FEE_TYPE = ['HP', 'HL', 'TL'];

    public function getModel()
    {
        return StudentDebt::class;
    }

    public function getDebtList($request)
    {
        $keyword = $request->keyword;
        $status = $request->status;
        $term_id = $request->term_id;

        $debts = StudentDebt::when($keyword, function ($query, $keyword) {
            $query->where(function ($q) use ($keyword) {
                $q->where('user_code', 'like', "%$keyword%")
                    ->orWhere('user_login', 'like', "%$keyword%");
            });
        })
            ->when($status !== null, function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($term_id, function ($query, $term_id) {
                $query->where('term_id', $term_id);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $terms = Term::orderBy('id', 'desc')->get(['id', 'term_name']);


        return $this->view('fee.debt.index', [
            'debts' => $debts,
            'terms' => $terms,
            'keyword' => $keyword,
            'status' => $status,
            'term_id' => $term_id
        ]);
    }

    public function create($request)
    {
        // Validate and clean input data
        $validation = $this->validateDebtData($request);
        if (!$validation['valid']) {
            return response()->json([
                'success' => false,
                'message' => $validation['message']
            ], 422);
        }
        $data = $validation['data'];
        try {
            DB::beginTransaction();
            foreach ($data as $item) {
                $this->createDebt($item);
            }
            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Tạo công nợ thành công',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Create debt error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Tạo công nợ thất bại: ' . $e->getMessage()
            ], 500);
        }
    }

    private function createDebt($data)
    {
        try {
            $subject = $data['subject'] ?? null;
            // Check for duplicate debt
            $existingDebt = StudentDebt::where('user_code', $data['user_code'])
                ->where('term_id', $data['term_id'])
                ->where('fee_type_id', $data['fee_type_id'])
                ->where('status', '=', self::DEBT_STATUS_PENDING)
                ->when($subject, function ($query) use ($subject) {
                    $query->where('subject_code', $subject['subject_code']);
                }, function ($query) {
                    $query->whereNull('subject_code');
                })
                ->first();

            if ($existingDebt) {
                throw new \Exception('Công nợ đã tồn tại trong học kỳ này.');
            }

            $debt = StudentDebt::create([
                'user_code' => $data['user_code'],
                'user_login' => $data['user_login'],
                'term_id' => $data['term_id'],
                'term_name' => $data['term_name'],
                'fee_type_id' => $data['fee_type_id'],
                'fee_type_code' => $data['fee_type_code'],
                'amount' => $data['amount'], // Số tiền thực tế phải trả (sau discount)
                'original_amount' => $data['original_amount'],
                'discount_amount' => $data['discount_amount'],
                'discount_percentage' => $data['discount_percentage'],
                'discount_reason' => $data['discount_reason'],
                'paid_amount' => 0,
                'status' => StudentDebt::STATUS_PENDING,
                'description' => $data['description'],
                'created_by' => Auth::user()->user_login ?? 'system',
                'subject_id' => $subject['subject_id'] ?? null,
                'subject_code' => $subject['subject_code'] ?? null,
                'subject_name' => $subject['subject_name'] ?? null
            ]);
            if (!$debt) {
                throw new \Exception('Không thể tạo công nợ');
            }
            // Log chi tiết việc tạo debt
            Log::channel('debt')->info('Debt created successfully', [
                'debt_id' => $debt->id,
                'user_code' => $debt->user_code,
                'user_login' => $debt->user_login,
                'term_id' => $debt->term_id,
                'term_name' => $debt->term_name,
                'fee_type_id' => $debt['fee_type_id'],
                'fee_type_code' => $debt['fee_type_code'],
                'original_amount' => $debt->original_amount,
                'discount_amount' => $debt->discount_amount,
                'discount_percentage' => $debt->discount_percentage,
                'final_amount' => $debt->amount,
                'created_by' => $debt->created_by,
                'created_at' => $debt->created_at->toISOString(),
                'ip_address' => request()->ip()
            ]);
        } catch (\Throwable $th) {
            Log::error($th);
            throw new \Exception("Có lỗi xảy ra khi tạo công nợ.");
        }
    }
    /**
     * Validate debt creation data
     */
    private function validateDebtData($request)
    {
        $data = [];
        $user_code = strtoupper(trim($request->user_code ?? ''));
        $term_id = $request->term_id;
        $fee_type = $request->fee_type;
        $subjects = $request->subjects ?? [];
        $amount = intval($request->amount);
        $original_amount = intval($request->original_amount);
        $discount_amount = intval($request->discount_amount);
        $discount_percentage = intval($request->discount_percentage);
        $discount_reason = trim($request->discount_reason ?? '');
        $description = trim($request->description ?? '');

        // Basic validation
        if (empty($user_code)) {
            return ['valid' => false, 'message' => 'Mã sinh viên không được để trống'];
        }

        // Kiểm tra sinh viên có tồn tại và có phải là sinh viên không
        $student = User::where('user_code', $user_code)
            ->where('user_level', 3) // user_level = 3 là sinh viên
            ->first();

        if (!$student) {
            return ['valid' => false, 'message' => 'Mã sinh viên không tồn tại hoặc không phải là sinh viên'];
        }

        // Lấy user_login từ database
        $user_login = $student->user_login;
        if ($original_amount <= 0) {
            return ['valid' => false, 'message' => 'Số tiền gốc phải lớn hơn 0'];
        }

        // Validate discount chặt chẽ
        if ($discount_percentage < 0 || $discount_percentage > 100) {
            return ['valid' => false, 'message' => 'Phần trăm giảm giá phải từ 0 đến 100'];
        }
        if ($discount_amount < 0 || $discount_amount > $original_amount) {
            return ['valid' => false, 'message' => 'Số tiền giảm giá không hợp lệ (0 - ' . number_format($original_amount) . ')'];
        }
        if ($discount_percentage > 0 && $discount_amount > 0) {
            return ['valid' => false, 'message' => 'Chỉ được chọn một trong hai: phần trăm giảm giá hoặc số tiền giảm giá'];
        }

        // Validate term
        $term = Term::find($term_id);
        if (!$term) {
            return ['valid' => false, 'message' => 'Kỳ học không tồn tại'];
        }

        // Validate fee type
        $feeType = FeeType::where('code', $fee_type)->first();
        if (!$feeType) {
            return ['valid' => false, 'message' => 'Loại phí không tồn tại'];
        }

        if (count($subjects) > 0 && in_array($fee_type, self::STUDY_FEE_TYPE)) {
            foreach ($subjects as $subject) {
                if (!isset($subject['subject_code']) || !isset($subject['subject_name']) || !isset($subject['subject_id'])) {
                    return ['valid' => false, 'message' => 'Thông tin môn học không đầy đủ'];
                }
                if ($subject['amount'] <= 0) {
                    return ['valid' => false, 'message' => 'Số tiền môn học đang không hợp lệ.'];
                }
                if ($discount_percentage && $discount_percentage > 0) {
                    $subjectFinalAmount = intval($subject['amount'] * (100 - $discount_percentage / 100));
                }

                $data[] = [
                    'user_code' => $user_code,
                    'user_login' => $user_login,
                    'student_name' => $student->fullname,
                    'term_id' => $term_id,
                    'term_name' => $term->term_name,
                    'fee_type_id' => $feeType->id,
                    'fee_type_code' => $feeType->code,
                    'subject' => $subject,
                    'amount' => $subjectFinalAmount,
                    'original_amount' => $subject['amount'],
                    'discount_amount' => $discount_amount,
                    'discount_percentage' => $discount_percentage,
                    'discount_reason' => $discount_reason,
                    'description' => $description,
                ];
            }
        } else {
            // Validate kết quả cuối
            $testFinalAmount = $original_amount;
            if ($discount_percentage > 0) {
                $testFinalAmount = $original_amount * (1 - $discount_percentage / 100);
            } elseif ($discount_amount > 0) {
                $testFinalAmount = $original_amount - $discount_amount;
            }

            if (intval($testFinalAmount) !== $amount) {
                return ['valid' => false, 'message' => 'Số tiền sau giảm giá đang được tính toán sai. Vui lòng liên hệ quản trị viên để được hỗ trợ.'];
            }
            if ($testFinalAmount <= 0) {
                return ['valid' => false, 'message' => 'Số tiền sau giảm giá phải lớn hơn 0'];
            }

            $data[] = [
                'user_code' => $user_code,
                'user_login' => $user_login,
                'student' => $student,
                'term_id' => $term_id,
                'term_name' => $term->term_name,
                'term' => $term,
                'fee_type_id' => $feeType->id,
                'fee_type_code' => $feeType->code,
                'subject' => [],
                'amount' => $amount,
                'original_amount' => $original_amount,
                'discount_amount' => $discount_amount,
                'discount_percentage' => $discount_percentage,
                'discount_reason' => $discount_reason,
                'description' => $description,
            ];
        }

        return [
            'valid' => true,
            'data' => $data
        ];
    }

    public function cancelDebt($request, $id)
    {
        try {
            $debt = StudentDebt::findOrFail($id);
            $reason = trim($request->reason ?? '');

            if ($debt->status == self::DEBT_STATUS_PAID) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không thể hủy công nợ đã thanh toán'
                ], 422);
            }

            if ($debt->status == self::DEBT_STATUS_CANCELED) {
                return response()->json([
                    'success' => false,
                    'message' => 'Công nợ đã được hủy trước đó'
                ], 422);
            }

            DB::beginTransaction();

            $debt->status = self::DEBT_STATUS_CANCELED;
            $debt->canceled_by = Auth::user()->user_login ?? 'system';
            $debt->canceled_at = now();
            $debt->description = $debt->description . ($reason ? " (Lý do hủy: {$reason})" : '');
            $debt->save();

            // Log cancellation activity
            $debt->logCancellation($reason);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Hủy công nợ thành công',
                'debt' => $debt
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Cancel debt error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Hủy công nợ thất bại: ' . $e->getMessage()
            ], 500);
        }
    }

    public function paymentForm($id)
    {
        $debt = StudentDebt::findOrFail($id);

        if (!$debt->canBePaid()) {
            return $this->redirectWithStatus('danger', 'Công nợ này không thể thanh toán (trạng thái: ' . $debt->status_name . ')', route('admin.debt.list'));
        }

        // Kiểm tra ví sinh viên và đảm bảo có đầy đủ thông tin
        $wallet = StudentWallet::where('user_code', $debt->user_code)->first();

        // Nếu ví tồn tại nhưng thiếu user_login, cập nhật nó
        if ($wallet && empty($wallet->user_login)) {
            $user = \App\Models\Fu\User::where('user_code', $debt->user_code)->first();
            if ($user) {
                $wallet->user_login = $user->user_login;
                $wallet->save();
            }
        }

        return $this->view('fee.debt.payment', [
            'debt' => $debt,
            'wallet' => $wallet
        ]);
    }

    public function processPayment($request, $id)
    {
        $debt = StudentDebt::findOrFail($id);
        $payment_type = $request->payment_type; // 'wallet' hoặc 'direct'
        $amount = floatval($request->amount);
        $invoice_id = $request->invoice_id;
        $payment_method = $request->payment_method;
        $note = $request->note;
        $currentUser = Auth::user()->user_login ?? 'system';

        // Log bắt đầu quá trình thanh toán
        Log::channel('debt')->info('Starting debt payment process', [
            'debt_id' => $id,
            'user_code' => $debt->user_code,
            'payment_type' => $payment_type,
            'amount' => $amount,
            'remaining_amount' => $debt->amount - $debt->paid_amount,
            'processed_by' => $currentUser
        ]);

        // Validation cơ bản
        if (!$debt->canBePaid()) {
            Log::channel('debt')->warning('Payment failed - invalid debt status', [
                'debt_id' => $id,
                'current_status' => $debt->status,
                'status_name' => $debt->status_name,
                'processed_by' => $currentUser
            ]);
            return $this->redirectWithStatus('danger', 'Công nợ này không thể thanh toán (trạng thái: ' . $debt->status_name . ')', route('admin.debt.list'));
        }

        $remainingAmount = $debt->amount - $debt->paid_amount;
        if ($amount <= 0 || $amount > $remainingAmount) {
            Log::channel('debt')->warning('Payment failed - invalid amount', [
                'debt_id' => $id,
                'requested_amount' => $amount,
                'remaining_amount' => $remainingAmount,
                'processed_by' => $currentUser
            ]);
            return $this->redirectWithStatus('danger', 'Số tiền không hợp lệ', route('admin.debt.payment.form', $id));
        }

        DB::beginTransaction();
        try {
            if ($payment_type == 'wallet') {
                // Thanh toán từ ví sinh viên
                Log::channel('debt')->info('Processing wallet payment', [
                    'debt_id' => $id,
                    'user_code' => $debt->user_code,
                    'amount' => $amount
                ]);

                $wallet = StudentWallet::where('user_code', $debt->user_code)->first();

                if (!$wallet) {
                    Log::channel('debt')->warning('Payment failed - wallet not found', [
                        'debt_id' => $id,
                        'user_code' => $debt->user_code
                    ]);
                    throw new \Exception('Sinh viên chưa có ví điện tử. Vui lòng liên hệ quản trị viên để tạo ví.');
                }

                if ($wallet->is_locked) {
                    Log::channel('debt')->warning('Payment failed - wallet is locked', [
                        'debt_id' => $id,
                        'user_code' => $debt->user_code,
                        'wallet_id' => $wallet->id,
                        'lock_reason' => $wallet->lock_reason
                    ]);
                    throw new \Exception('Ví đã bị khóa: ' . ($wallet->lock_reason ?? 'Không rõ lý do'));
                }

                // Kiểm tra số dư ví (sử dụng balance duy nhất)
                $balance_before = $wallet->balance;

                Log::channel('debt')->info('Checking wallet balance', [
                    'debt_id' => $id,
                    'user_code' => $debt->user_code,
                    'wallet_balance' => $balance_before,
                    'required_amount' => $amount
                ]);

                if ($balance_before < $amount) {
                    Log::channel('debt')->warning('Payment failed - insufficient wallet balance', [
                        'debt_id' => $id,
                        'user_code' => $debt->user_code,
                        'wallet_balance' => $balance_before,
                        'required_amount' => $amount,
                        'shortage' => $amount - $balance_before
                    ]);
                    throw new \Exception('Số dư ví không đủ. Số dư hiện tại: ' . number_format($balance_before, 0, ',', '.') . ' VNĐ');
                }

                // Trừ tiền trong ví
                $wallet->balance -= $amount;
                $balance_after = $wallet->balance;
                $wallet->save();

                Log::channel('debt')->info('Wallet balance updated', [
                    'debt_id' => $id,
                    'user_code' => $debt->user_code,
                    'balance_before' => $balance_before,
                    'balance_after' => $balance_after,
                    'deducted_amount' => $amount
                ]);

                // Tạo giao dịch ví
                $walletTransaction = WalletTransaction::create([
                    'user_code' => $wallet->user_code,
                    'user_login' => $wallet->user_login,
                    'wallet_type' => 'main',
                    'transaction_type' => 'payment',
                    'amount' => $amount,
                    'balance_before' => $balance_before,
                    'balance_after' => $balance_after,
                    'reference_id' => $debt->id,
                    'payment_method' => 'wallet',
                    'description' => 'Thanh toán công nợ ' . ($debt->term_name ?: 'ID: ' . $debt->id),
                    'created_by' => $currentUser
                ]);

                Log::channel('debt')->info('Wallet transaction created', [
                    'debt_id' => $id,
                    'wallet_transaction_id' => $walletTransaction->id,
                    'user_code' => $debt->user_code,
                    'amount' => $amount
                ]);
            }

            // Tạo giao dịch thanh toán công nợ
            $debtPayment = DebtPayment::create([
                'debt_id' => $debt->id,
                'user_code' => $debt->user_code,
                'user_login' => $debt->user_login,
                'amount' => $amount,
                'payment_method' => $payment_method ?? ($payment_type == 'wallet' ? 'wallet' : 'direct'),
                'invoice_id' => $invoice_id,
                'note' => $note,
                'created_by' => $currentUser
            ]);

            Log::channel('debt')->info('Debt payment record created', [
                'debt_id' => $id,
                'debt_payment_id' => $debtPayment->id,
                'amount' => $amount,
                'payment_method' => $payment_method ?? ($payment_type == 'wallet' ? 'wallet' : 'direct')
            ]);

            // Cập nhật công nợ
            $old_paid_amount = $debt->paid_amount;
            $debt->paid_amount += $amount;
            $old_status = $debt->status;

            // Tự động cập nhật trạng thái dựa trên số tiền đã thanh toán
            if ($debt->paid_amount >= $debt->amount) {
                $debt->status = StudentDebt::STATUS_PAID;
            } elseif ($debt->paid_amount > 0 && $old_status == StudentDebt::STATUS_PENDING) {
                $debt->status = StudentDebt::STATUS_PARTIAL_PAID;
            }

            $debt->save();

            Log::channel('debt')->info('Debt record updated', [
                'debt_id' => $id,
                'old_paid_amount' => $old_paid_amount,
                'new_paid_amount' => $debt->paid_amount,
                'old_status' => $old_status,
                'new_status' => $debt->status,
                'total_amount' => $debt->amount,
                'is_fully_paid' => $debt->paid_amount >= $debt->amount
            ]);

            // Log payment activity using debt model method
            $additionalInfo = [
                'payment_type' => $payment_type,
                'invoice_id' => $invoice_id,
                'note' => $note
            ];

            if ($payment_type == 'wallet' && isset($balance_before) && isset($balance_after)) {
                $additionalInfo['wallet_balance_before'] = $balance_before;
                $additionalInfo['wallet_balance_after'] = $balance_after;
            }

            $debt->logPayment($amount, $payment_method ?? ($payment_type == 'wallet' ? 'wallet' : 'direct'), $additionalInfo);

            DB::commit();

            Log::channel('debt')->info('Debt payment completed successfully', [
                'debt_id' => $id,
                'user_code' => $debt->user_code,
                'amount' => $amount,
                'payment_type' => $payment_type,
                'final_status' => $debt->status,
                'processed_by' => $currentUser
            ]);

            return $this->redirectWithStatus('success', 'Thanh toán công nợ thành công', route('admin.debt.list'));
        } catch (\Exception $e) {
            DB::rollBack();

            Log::channel('debt')->error('Debt payment failed', [
                'debt_id' => $id,
                'user_code' => $debt->user_code,
                'amount' => $amount,
                'payment_type' => $payment_type,
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString(),
                'processed_by' => $currentUser
            ]);

            return $this->redirectWithStatus('danger', 'Thanh toán công nợ thất bại: ' . $e->getMessage(), route('admin.debt.payment.form', $id));
        }
    }



    public function getDebtDetail($id)
    {
        try {
            // Lấy thông tin công nợ với thông tin liên quan
            $debt = StudentDebt::select('student_debts.*', 'term.term_name', 'fee_types.name as fee_type_name')
                ->leftJoin('term', 'student_debts.term_id', '=', 'term.id')
                ->leftJoin('fee_types', 'student_debts.fee_type', '=', 'fee_types.id')
                ->where('student_debts.id', $id)
                ->firstOrFail();

            // Thêm remaining_amount cho Vue component
            $debt->remaining_amount = $debt->amount - $debt->paid_amount;

            // Lấy lịch sử thanh toán của công nợ
            $payments = DebtPayment::where('debt_id', $id)
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'debt' => $debt,
                'payments' => $payments
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting debt detail: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Không thể tải thông tin công nợ: ' . $e->getMessage()
            ], 500);
        }
    }
}
