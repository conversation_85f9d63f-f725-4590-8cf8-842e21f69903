<?php

namespace App\Repositories\Admin;

use Illuminate\Http\Request;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Validator;
use App\Models\Fu\User;
use App\Models\EBook\EBook;
use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Utils\ResponseBuilder;
use Carbon\Carbon;
use App\Validations\EbookValidation;

class EBookRepository extends BaseRepository
{
    public function getModel()
    {
        return User::class;
    }

    private $validation;
    public function __construct()
    {
        $this->validation = new EbookValidation();
    }

    /**
     * Lấy ra danh sách của sách ebook
     */
    public function getlistTheEBook(Request $request)
    {
        try {
            $listEBook = EBook::query();
            $listEBook->select([
                'ebook.*',
            ]);

            if (isset($request->student_code) && $request->student_code) {
                $listEBook->where('ebook.student_code', '=', $request->student_code);
            }
            if (isset($request->expired_period) && $request->expired_period) {
                $listEBook->where('ebook.expired_period', '=', $request->expired_period);
            }
            if (isset($request->group_name) && $request->group_name) {
                $listEBook->where('ebook.group_name', '=', $request->group_name);
            }

            if (isset($request->created_at) && $request->created_at) {
                $formattedDate = Carbon::parse($request->created_at);
                $listEBook->whereDate('ebook.created_at', '=', $formattedDate);
            }

            $listEBook->orderBy('ebook.id', 'DESC')->get();

            $data = $listEBook->paginate(10);

            return ResponseBuilder::Success($data);
        } catch (\Throwable $th) {
            Log::error('-------getlistTheEBook-------');
            Log::error('Error at ' . $th->getFile() . ' : ' . __METHOD__ . $th->getLine() . ' : ' . $th->getMessage());
            return response(['error' => $th], 500);
        }
    }

    /**
     * Tạo mới sinh viên đăng ký ebook
     */
    public function createSVEBook(Request $request)
    {
        try {
            if ($request->file('file') === null) {
                return ResponseBuilder::Fail("Vui lòng tải lên file trước khi lưu!");
            }

            if (!$collection = $this->fileToArrayData($request->file)) {
                return ResponseBuilder::Fail("Không thể đọc file!");
            }
            $success = 0;
            $error = "";
            $list_data = [];
            $uploadedUser = auth()->user()->user_login;
            $createdAt = now()->format('Y-m-d H:i:s');
            foreach ($collection as $item) {
                $group = Group::select([
                        'list_group.id',
                        'list_group.group_name',
                        'list_group.psubject_code',
                    ])
                    ->join('group_member', 'list_group.id', '=', 'group_member.groupid')
                    ->where('group_member.user_code', '=', $item['ma_sinh_vien'])
                    ->orWhere('list_group.group_name', '=', $item['id_lop'])
                    ->orWhere('list_group.psubject_code', '=', $item['ma_mon'])
                    ->first();
                if (!$group) {
                    Log::error("Không tìm thấy lớp id " . $item['id_lop'] . " hoặc không có trong lịch sử học của sinh viên " . $item['ma_sinh_vien']);
                    $error = $error . "Không tìm thấy lớp id " . $item['id_lop'] . " hoặc không có trong lịch sử học của sinh viên " . $item['ma_sinh_vien'] ."\n";
                    continue;
                }
                
                $list_data[] = [
                    'student_code' => $item['ma_sinh_vien'],
                    'group_id' => $group->id,
                    'group_name' => $group->group_name,
                    'subject_code' => $group->psubject_code,
                    'book_name' => $item['ten_sach'],
                    'code' => $item['ma_code'],
                    'url' => $item['link_truy_cap'],
                    'expired_period' => $item['han_su_dung'],
                    'note' => $item['ghi_chu'],
                    'created_at' => $createdAt,
                    'updated_at' => $createdAt,
                    'staff' => $uploadedUser,
                ];
            }
            
            $detailStudent = EBook::insert($list_data);
            $success++;
            
            if ($detailStudent) {
                return ResponseBuilder::Success([
                    'success' => $success,
                    'total' => count($collection),
                    'error' => $error
                ], "Thêm thành công!", 200);
            } else {
                Log::error("EBookController - insertSVEBook: " . "Thêm thất bại!");
                return ResponseBuilder::Fail("Thêm thất bại!");
            }
        } catch (\Throwable $th) {
            Log::error('-------create SV EBook-------');
            Log::error('Error at ' . $th->getFile() . ' : ' . __METHOD__ . $th->getLine() . ' : ' . $th->getMessage());
            return ResponseBuilder::Fail("Thêm thất bại!");
        }
    }

    /**
     * chỉnh sửa sinh viên đăng ký ebook
     */
    public function updateSVEBook(Request $request)
    {
        try {
            DB::beginTransaction();
            $dataDetailEBook = $request->dataDetailEBook;

            // $this->validation->getValidator($request->dataDetailEBook);

            if ($validation = $this->validation->run($request->dataDetailEBook)) {
                return ResponseBuilder::Fail(
                    $validation,
                    null
                );
            }


            $updateStudent = EBook::where('id', $dataDetailEBook['id'])
                ->update([
                    'group_name'     => $request->dataDetailEBook['group_name'],
                    'subject_code'   => $request->dataDetailEBook['subject_code'],
                    'code'           => $request->dataDetailEBook['code'],
                    'book_name'      => $request->dataDetailEBook['book_name'],
                    'expired_period' => $request->dataDetailEBook['expired_period'],
                    'created_at'     => $request->dataDetailEBook['created_at'],
                    'url'            => $request->dataDetailEBook['url'],
                    'updated_at'     => Carbon::now()->format('Y-m-d H:i:s'),
                    'staff'          => auth()->user()->user_login,
                ]);

            if (!$updateStudent) {
                Log::error("EBookController - updateSVEBook: " . "Cập nhật thất bại!");
                return 0;
            }

            DB::commit();
            return ResponseBuilder::Success([], "Cập nhập thông tin thành công!");
        } catch (\Throwable $th) {
            Log::error('-------update SV EBook-------');
            Log::error('Error at ' . $th->getFile() . ' : ' . __METHOD__ . $th->getLine() . ' : ' . $th->getMessage());
            return response(['error' => $th], 500);
        }
    }

    /**
     * xóa sinh viên đăng ký ebook
     */
    public function deleteSVEBook(Request $request)
    {
        try {
            $deleteStudent = EBook::where('id', $request->id)->delete();
            if (!$deleteStudent) {
                return ResponseBuilder::Fail('Không tìm được sinh viên cần xóa!');
            }
            return ResponseBuilder::Success([], "Xóa thành công!");
        } catch (\Throwable $th) {
            Log::error('-------delete SV EBook-------');
            Log::error('Error at ' . $th->getFile() . ' : ' . __METHOD__ . $th->getLine() . ' : ' . $th->getMessage());
            return response(['error' => $th], 500);
        }
    }
}
