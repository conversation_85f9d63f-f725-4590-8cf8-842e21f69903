<?php

namespace App\Repositories\Admin;

use App\Repositories\BaseRepository;
use App\Models\Fu\Term;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\DisciplineExamRegulationImport;
use App\Models\RelearnOnline;
use App\Models\T7\CourseResult;
use mysql_xdevapi\Exception;
use Illuminate\Support\Facades\Log;
use App\Models\Fu\User;
use App\Exports\ExportListStudentViolateTheRules;

use App\Models\Fu\DisciplineExamRegulation;
use App\Models\Fu\DisciplineExamRegulationsStudent;
use App\Http\Controllers\Admin\TranscriptController;

class DisciplineExamRegulationRepository extends BaseRepository
{
    public function getModel()
    {
        return DisciplineExamRegulation::class;
    }

    public function index($request)
    {
        $terms = Term::orderBy('id', 'desc')->get();
        $term_id = $request->term_id;
        $disciplines = $this->_model->with(['term:id,term_name', 'students:discipline_exam_regulation_id', 'decision:*'])
            ->when($term_id, function ($query, $term_id) {
                return $query->where('term_id', $term_id);
            })

            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view(env('THEME_ADMIN') . '.discipline_exam_regulation.index', [
            'terms' => $terms,
            'disciplines' => $disciplines,
        ]);
    }

    public function store($request)
    {
        $users = [];
        $array = $request->all();
        $termAccept = Term::find($request->term_id);
        if (!$users = $this->importDataFromFile($array['users'])) {
            return $this->redirectWithStatus('danger', 'Không có sinh viên nào để import hoặc file tải lên bị lỗi', url()->previous());
        }
        $array['decision_no'] = $request->decision_name;
        $array['modifier_login'] = Auth::user()->user_login;
        $array['term_id'] = $request->term_id;
        $array['date_affected'] = now();
        $array['created_at'] = now();
        $array['updated_at'] = now();
        $array['note'] = $array['note'] ?? '';
        $array['file_student_name'] = $array['users']->getClientOriginalName();
        // $array['file_name'] = $this->uploadFileToDisciplineExam($request->file('file'), $termAccept->term_name);

        DB::beginTransaction();
        try {
            $discipline = DisciplineExamRegulation::create($array);
            $resCreateListStudent = $this->createDisciplineExamRegulationsStudent($users, $discipline);
            if (!$resCreateListStudent['status']) {
                DB::rollBack();
                return $this->redirectWithStatus('danger', $resCreateListStudent['message'], url()->previous());
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->redirectWithStatus('danger', 'Dữ liệu không đúng định dạng, huỷ bỏ quá trình import', url()->previous());
        }

        return $this->redirectWithStatus('success', 'Tải lên danh sách thành công!', route('admin.discipline_exam_regulation'));
    }

    public function uploadFileToDisciplineExam($file, $term_name)
    {
        $fileName = $term_name . '-' . now()->format('d_m_y_h_i_s') . '.' . $file->getClientOriginalExtension();
        $this->uploadFile('disciplineExam', $file, $fileName);

        return $fileName;
    }

    public function add()
    {
        $terms = $this->getTerms();
        $array = [
            '1' => ['value' => '1', 'title' => 'Khen thưởng'],
            '2' => ['value' => '2', 'title' => 'Kỉ luật'],
        ];

        return $this->view('discipline_exam_regulation.create', [
            'terms' => $terms,
            'array' => $array,
        ]);
    }

    public function updateDisciplineExam($request, $id)
    {
        try {
            if (!isset($request->term_id)) {
                return false;
            }

            $this->getModel()::find($id)->update([
                'term_id' => $request->term_id,
            ]);

            return $this->viewDiscipline($id);
        } catch (\Exception $e) {
            Log::error("Error update discipline: " . $e->getLine() . " err: " . $e->getMessage());
            throw $e;
            return $e->getMessage();
        }
    }

    public function importDataFromFile($file)
    {
        $data = Excel::toArray(new DisciplineExamRegulationImport(), $file);
        if (count($data[0]) <= 1) {
            return false;
        }
        unset($data[0][0]);

        return $data[0];
    }

    public function exportExamFile($id)
    {
        $discipline = DisciplineExamRegulationsStudent::select(['student_code', 'subject_code', 'reason', 'discipline_exam_regulation_id'])
            ->where('discipline_exam_regulation_id', $id)->get();

        $export = new ExportListStudentViolateTheRules($discipline);

        return Excel::download($export, 'export-danh-sach-hoc-sinh-vi-pham.xlsx');
    }

    public function checkDisciplineHas($name)
    {
        return DisciplineExamRegulation::where('decision_no', $name)->count();
    }

    public function createDisciplineExamRegulationsStudent($users, $discipline)
    {
        try {
            $studentsNeedSyncPoint = [];
            foreach ($users as $user) {
                if ($user[0] == '' || $user[1] == '') {
                    continue;
                }
                $student_code = trim($user[0]);
                $subject_code = trim($user[1]);

                $student = User::where('user_code', $student_code)->get();
                $studentsNeedSyncPoint[] = $student[0];

                $subjectResult = CourseResult::select('t7_course_result.*')
                    ->join('list_group', 'list_group.id', '=', 't7_course_result.groupid')
                    ->where('list_group.is_virtual', 0)
                    ->where('t7_course_result.student_login', $student[0]->user_login)
                    ->whereNotIn('t7_course_result.val', [1])
                    ->where('t7_course_result.psubject_code', $subject_code)
                    ->orderBy('t7_course_result.groupid', 'DESC')
                    ->first();
                
                Log::error('-------$subjectResult-------', [$subjectResult, $student, $user]);


                $theLastRecordOfSubject = CourseResult::where('student_login', $student[0]->user_login)
                    ->where('psubject_code', $subject_code)
                    ->whereNotIn('val', [1])
                    ->orderBy('groupid', 'DESC')
                    ->first();

                if ($theLastRecordOfSubject == null) {
                    return [
                        'status' => false,
                        'message' => 'Kiểm tra lại lịch sử học của sinh viên ' . $student[0]->user_login . '- môn học ' . $subject_code,
                    ];
                }

                DisciplineExamRegulationsStudent::create([
                    'discipline_exam_regulation_id' => $discipline->id,
                    'student_code' => $student_code,
                    'subject_code' => $subject_code,
                    'reason' => $user[2] ?? '',
                    'groupid' => $theLastRecordOfSubject->groupid,
                    'register_relearn_order_id' => null,
                ]);

                if (isset($subjectResult)) {
                    $theLastRecordOfSubject->punishment = 1;
                    $subjectResult->val = 0;
                    $subjectResult->punishment = 1;
                    $subjectResult->save();
                    $theLastRecordOfSubject->save();
                } else {
                    return [
                        'status' => false,
                        'message' => 'Kiểm tra lại lịch sử học của sinh viên ' . $student_code . '- môn học ' . $subject_code,
                    ];
                }
            }
            $res = TranscriptController::syncGradePoint($studentsNeedSyncPoint);
            if ($res == -1) {
                return [
                    'status' => true,
                    'message' => 'Tự động đồng bộ bản điểm không thành công, hãy vào bảng điểm sv để cập nhật!'
                ];
            }

            return [
                'status' => true,
                'message' => 'Update success',
            ];
        } catch (\Exception $e) {
            Log::error("Error update students have punishment: " . $e->getLine() . " err: " . $e->getMessage());
            return [
                'status' => false,
                'message' => "Lỗi: " . $e->getLine(),
            ];
        }
    }

    public function viewDiscipline($id)
    {
        $discipline = DisciplineExamRegulation::findOrFail($id);
        $terms = Term::orderBy('id', 'desc')->get();

        $discipline->load('term:id,term_name');
        $discipline->load('students');
        return $this->view('discipline_exam_regulation.view', [
            'discipline' => $discipline,
            'terms' => $terms
        ]);
    }
}
