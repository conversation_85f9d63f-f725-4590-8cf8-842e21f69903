<?php

namespace App\Repositories\Admin;

use App\Http\Lib;
use Carbon\Carbon;
use App\Models\Fu\Term;
use App\Models\Fu\User;
use App\Models\StudentStatusLog;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class StudentStatusLogsRepository extends BaseRepository
{
    public function getModel()
    {
        return StudentStatusLog::class;
    }

    public function getListStudentStatusLogs($request)
    {
        try {
            $list_study_status = config('status')->trang_thai_hoc;
            $list_study_status_array = [];

            // Chuyển đổi đối tượng thành mảng cho thành phần Vue với cấu trúc phù hợp
            foreach ($list_study_status as $key => $item) {
                $list_study_status_array[] = [
                    'uid' => $item['uid'] ?? '',
                    'value' => $item['value'] ?? ''
                ];
            }
            $list_term = Term::orderBy('id', 'DESC')->get(['id', 'term_name']);
            $list_student_status_logs = StudentStatusLog::orderBy('id', 'DESC');

            if (isset($request->term_id) && $request->term_id != -1 && $request->term_id != 'null') {
                $list_student_status_logs->where('term_id', $request->term_id);
            }
            if (isset($request->user_search) && $request->user_search != null && $request->user_search != 'null') {
                $list_student_status_logs->where('user_login', 'like', "%$request->user_search%");
            }
            if (isset($request->study_status) && $request->study_status != null && $request->study_status != 'null') {
                $list_student_status_logs->where(function ($query) use ($request) {
                    $query->where('study_status_code_old', $request->study_status)
                        ->orWhere('study_status_code_new', $request->study_status);
                });
            }
            $num_per_page = 8;
            if (isset($request->num_per_page) && $request->num_per_page != null && $request->num_per_page != 'null' && $request->num_per_page > 0) {
                $num_per_page = $request->num_per_page;
            }
            $list_student_status_logs = $list_student_status_logs
                ->with(['term' => function ($term) {
                    $term->select(['id', 'term_name']);
                }])
                ->paginate($num_per_page);

            return response()->json([
                'list_student_status_logs' => $list_student_status_logs,
                'list_study_status' => $list_study_status_array,
                'list_term' => $list_term,
            ]);
        } catch (\Throwable $th) {
            Log::error("----------------- start err getListStudentStatusLogs -------------------");
            Log::error($th);
            error_log($th);
            Log::error("----------------- end err getListStudentStatusLogs -------------------");
            return response("", 500);
        }
    }

    public function createStudentStatusLogs($user_code_login, $study_status_new, $request = null,  $staff_user_login = null)
    {
        $staff_user_login = $staff_user_login ?? Auth::user()->user_login;
        try {
            $user = User::where("user_code", $user_code_login)->orWhere('user_login', $user_code_login)->firstOrFail();
            $statusConfig = config('status')->trang_thai_hoc;
            $study_status_code_old = $statusConfig[$user->study_status]['uid'] ?? "";
            $study_status_code_new = $statusConfig[$study_status_new]['uid'] ?? "";
            $now_term = Term::where('endday', '>=', Carbon::now())->first();
            $data = [
                'user_login' => $user->user_login,
                'user_code' => $user->user_code,
                'study_status_old' => $user->study_status,
                'study_status_code_old' => $study_status_code_old,
                'study_status_new' => $study_status_new,
                'study_status_code_new' => $study_status_code_new,
                'term_id' => $now_term->id,
                'update_by' => $staff_user_login,
                'note' =>  $request->note,
            ];
            StudentStatusLog::create($data);
        } catch (\Throwable $th) {
            Log::error($th);
            throw new \Exception($th->getMessage());
        }
    }
}
