<?php


namespace App\Repositories\Admin;


use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Collection;

use App\Models\Fu\User;
use App\Models\SystemLog;
use App\Models\SessionType;
use App\Models\Fu\Activity;
use App\Models\Fu\ActivityGroup;
use App\Models\Fu\ActivityLeader;
use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use App\Models\Fu\Subject;
use App\Models\Fu\Course;
use App\Models\Fu\Term;
use App\Models\Fu\Room;
use App\Models\Fu\Area;
use App\Models\Fu\Slot;
use App\Models\Fu\Attendance;
use App\Models\T7\CourseResult;
use App\Models\T7\CourseGrade;
use App\Models\T7\SyllabusPlan;
use App\Models\Dra\T1UserRole;

use App\helper\GroupHelper;
use App\Repositories\BaseRepository;
use App\Repositories\Admin\ActivityRepository;
use App\Models\Fu\Service;

use Carbon\Carbon;
use App\Utils\ResponseBuilder;


class GroupRepository extends BaseRepository
{

    public function getModel()
    {
        return Group::class;
    }

    public function index($request)
    {
        return $this->view('group.index', []);
    }
    
    /**
     * <AUTHOR>
     * @param Request $request  
     * Quản lý rút, thêm sinh viên
     */
    public function managerStudents($request)
    {

        ini_set('memory_limit', '-1');
        return $this->view('group.management', [
            'terms' => Term::orderBy('id', 'DESC')->get() ?? [],
        ]);
    }
    /**
     * <AUTHOR>
     * @param Request $request 
     * Quản lý chuyển lớp cho sinh viên
     */
    public function suitableSchedule($request)
    {
        if (!in_array(auth()->user()->user_level, [1, 4])) {
            return redirect()->route('home');
        }

        ini_set('memory_limit', '-1');
        return $this->view('group.suitable_schedule', [
            'terms' => Term::orderBy('id', 'DESC')->get() ?? [],
            'subjects' => Subject::orderBy('id', 'DESC')->get() ?? [],
        ]);
    }

    /**
     * <AUTHOR>
     * @param Request $request 
     * Quản lý chuyển lớp cho sinh viên
     */
    public function managerSchedule($request)
    {
        if (!in_array(auth()->user()->user_level, [1, 4])) {
            return redirect()->route('home');
        }

        ini_set('memory_limit', '-1');
        return $this->view('group.schedule', [
            'terms' => Term::orderBy('id', 'DESC')->get() ?? [],
            'subjects' => Subject::orderBy('id', 'DESC')->get() ?? [],
        ]);
    }

    /**
     * <AUTHOR>
     * Thêm sinh viên vào lớp bằng file
     * @param Request $request 
     */
    public function addStudentFile($request)
    {
        ini_set('memory_limit', '-1');
        $rules = ['file' => 'required'];
        $messages = [
            'file.required' => 'File thêm sinh viên không được bỏ trống',
        ];

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            return back()->with([
                'errors1' => $validator->errors()->first(),
            ]);
        }
        
        // lấy dữ liệu từ file
        $file = $request->file('file');
        $user = auth()->user();
        if (!$datas = $this->importDataFromFile($file)) {
            return back()->with([
                'errors1' => 'Không có sinh viên nào để import hoặc file tải lên bị lỗi',
            ]);
        }

        // giới hạn số lượng sinh viên check 1 lần
        if (count($datas) > 150) {
            return back()->with([
                'errors1' => 'Số lượng dòng không được vượt quá 150',
            ]);
        }

        // KIểm tra quyền add lớp quá 30% 
        $checkItProcess = $request->get('pass_all', 0);
        $noteProcess = $request->get('note_action', '');
        if ($checkItProcess == 1) {
            if (auth()->user()->user_level != 1) {
                return back()->with([
                    'errors1' => 'Bạn không đủ quyền hạn để thực hiện chức năng này',
                ]);
            } else {
                $administrator = T1UserRole::where('user_login', auth()->user()->user_login)->where('role_id', 1)->first();
                if(!$administrator){
                    return back()->with([
                        'errors1' => 'Bạn không đủ quyền hạn để thực hiện chức năng này',
                    ]);
                }
                if (trim($noteProcess) == '') {
                    return back()->with([
                        'errors1' => 'Bạn phải điền lý do rút lớp',
                    ]);
                }
            }
        }

        $termId = $request->get('term_id', null);
        if ($termId == null) {
            $currentTerm = Term::whereRaw('startday <= CURRENT_DATE')
                ->whereRaw('endday >= CURRENT_DATE')
                ->first();

            $termId = $currentTerm->id;
        } else {
            $currentTerm = Term::find($termId);
        }

        // dữ liệu trắng
        $reports = [];
        $errorRecord = null;
        $listStudentObejct = []; // dánh
        $listGroup = []; // danh sách lớp cần thêm sinh viên
        $listCheckLimitGroup = []; //  danh sách quá 30%
        $checkExistSchedule = []; // kiểm tra lớp có lịch hay không

        // Dữ liệu nguồn
        $initData = $this->initDataAdd($datas, $currentTerm);
        // Không tồn tại sv hợp lệ
        if (isset($initData['status']) && $initData['status'] == 0) {
            return back()->with([
                'reports' => [[
                    'mgs' => $initData['msg'],
                    'status' => $initData['status']
                ]]
            ]);
        }

        $listGroupInfor = $initData['listGroupInfor'];
        $listMemberGroup = $initData['listMemberGroup'];
        $listGroupSchedule = $initData['listGroupSchedule'];

        DB::beginTransaction();
        try {
            foreach ($datas as $key => $value) {
                $errorRecord = [
                    'key' => $key,
                    'value' => $value
                ];

                $value = array_map(function ($a) {
                    return trim($a);
                }, $value);

                /* =========== Kiểm tra mã sinh viên có tồn tại hay không =========== */
                if (count($value) == 0 || $value[0] == null || trim($value[0]) == '') {
                    $reports[] = [
                        'mgs' => 'Dòng ' . ($key + 1) . ":\t Lỗi mã sinh viên $value[0]",
                        'status' => 0,
                    ];
                    continue;
                }


                /* =========== kiểm tra tài khoản sinh viên tồn tại hay không =========== */
                if (!isset($listStudentObejct[$value[0]])) {
                    $studentAdd = User::query()
                        ->select('user_code', 'user_login')
                        ->where('user_code', $value[0])
                        ->first();

                    if (!$studentAdd) {
                        $reports[] = [
                            'mgs' => 'Dòng ' . ($key + 1) . ":\t Sinh viên $value[0] không tồn tại",
                            'status' => 0,
                        ];
                        continue;
                    }

                    $listStudentObejct[$value[0]] = $studentAdd;
                }

                $studentAdd = $listStudentObejct[$value[0]];
                if (!isset($listGroup[trim($value[2]) . trim($value[1])])) {
                    $listGroup[trim($value[2]) . trim($value[1])] = Group::query()->where('list_group.is_virtual', 0)
                        ->select([
                            'list_group.id',
                            'list_group.group_name',
                            'list_group.psubject_code',
                        ])
                        ->where('group_name', $value[1])
                        ->where('psubject_code', $value[2])
                        ->where('pterm_id', $termId)
                        ->first();
                }


                /* =========== kiểm tra lớp có tồn tại hay không =========== */
                $groupJoin = $listGroup[trim($value[2]) . trim($value[1])];
                if (!$groupJoin) {
                    $reports[] = [
                        'mgs' => 'Dòng ' . ($key + 1) . ":\t Sinh viên $value[0] không thêm được vào lớp $value[1] ( $value[2] ): do lớp không tồn tại",
                        'status' => 0,
                    ];
                    continue;
                }

                /* =========== kiểm tra lớp có lịch hay không =========== */
                if (!isset($checkExistSchedule[$groupJoin->id])) {
                    $countSchedule = Activity::where('groupid', $groupJoin->id)->count();

                    if ($countSchedule == 0) {
                        $checkExistSchedule[$groupJoin->id] = false;
                        $reports[] = [
                            'mgs' => 'Dòng ' . ($key + 1) . ":\t Sinh viên $value[0] không thêm được vào lớp $value[1] ( $value[2] ): do lớp không có lịch",
                            'status' => 0,
                        ];

                        $checkExistSchedule[$groupJoin->id] = false;
                        continue;
                    }

                    $checkExistSchedule[$groupJoin->id] = true;
                } elseif ($checkExistSchedule[$groupJoin->id] == false) {
                    $reports[] = [
                        'mgs' => 'Dòng ' . ($key + 1) . ":\t Sinh viên $value[0] không thêm được vào lớp $value[1] ( $value[2] ): do lớp không có lịch",
                        'status' => 0,
                    ];
                    continue;
                }


                /* =========== Kiểm tra sinh viên tồn tại trong lớp không =========== */
                $checkInsideGroup = GroupMember::query()
                    ->where('groupid', $groupJoin->id)
                    ->where('member_login', $studentAdd->user_login)
                    ->count();
                if ($checkInsideGroup > 0) {
                    $reports[] = [
                        'mgs' => 'Dòng ' . ($key + 1) . ":\t Sinh viên $value[0] đã ở trong lớp $value[1] ( $value[2] )",
                        'status' => 2,
                    ];
                    continue;
                }


                /* =========== Kiểm tra lớp quá 30% chưa =========== */
                if (!isset($listCheckLimitGroup[$groupJoin->id])) {
                    if ($checkItProcess == 0) {
                        $listCheckLimitGroup[$groupJoin->id] = $this->checkLimitSchedule($groupJoin->id);
                    } else {
                        $listCheckLimitGroup[$groupJoin->id] = [
                            'status' => 1,
                            'msg' => "",
                        ];
                    }
                }

                $checkLimit = $listCheckLimitGroup[$groupJoin->id];
                if ($checkLimit['status'] == false) {
                    $reports[] = [
                        'mgs' => 'Dòng ' . ($key + 1) . ":\t Sinh viên $value[0] không thêm được vào lớp $value[1] ( $value[2] ): " . $checkLimit['msg'],
                        'status' => 0,
                    ];
                    continue;
                }


                /* =========== kiểm tra lớp đã có trong dữ liệu khởi tạo chưa, nếu chưa thì thêm vào. =========== */
                if (!isset($listGroupInfor[$groupJoin->id])) {
                    $listGroupInfor[$groupJoin->id] = [
                        'id' => $groupJoin->id,
                        'group_name' => $groupJoin->group_name,
                        'psubject_code' => $groupJoin->psubject_code,
                    ];

                    $listSchedule = Activity::select('day', 'slot')
                        ->leftJoin('session_type', 'session_type.id', '=', 'activity.session_type')
                        ->where('activity.groupid', $groupJoin->id)
                        ->where('activity.slot', '>', 0)
                        ->where('session_type.is_exam', 0)
                        ->get();

                    foreach ($listSchedule as $day) {
                        if (!isset($listGroupSchedule[$groupJoin->id])) {
                            $listGroupSchedule[$groupJoin->id] = [
                                1 => [],
                                2 => [],
                                3 => [],
                                4 => [],
                                5 => [],
                                6 => []
                            ];
                        }

                        $listGroupSchedule[$groupJoin->id][$day->slot][$day->day] = 1;
                    }
                }

                // phần này sẽ thêm dữ liệu rỗng với môn có chỉ có buổi học online
                if (!isset($listGroupSchedule[$groupJoin->id])) {
                    $listGroupSchedule[$groupJoin->id] = [];
                }


                /* =========== kiểm tra xem bị trùng hay không =========== */
                $checkConflig = true;
                // quét danh sách lớp hiện tại
                if (!isset($listMemberGroup[$studentAdd->user_login])) {
                    $listMemberGroup[$studentAdd->user_login] = [];
                }

                // duyệt lớp của sinh viên
                foreach ($listMemberGroup[$studentAdd->user_login] as $groupIdCheck) {
                    // quét lịch học của lớp muốn join
                    /*
                    foreach ($listGroupSchedule[$groupJoin->id] as $day => $v) {
                        // kiểm tra xem có trùng với lịch học hiện tại không
                        foreach ($v as $vSlot) {
                            if (auth()->user()->user_login == 'dev') dump('xxx', [$v, $groupIdCheck, $groupJoin->id, $day]);
                            if (isset($listGroupSchedule[$groupIdCheck][$day]) && $listGroupSchedule[$groupIdCheck][$day][$vSlot] == $listGroupSchedule[$groupJoin->id][$day][$vSlot]) {
                                $groupConfig = $listGroupInfor[$groupIdCheck];
                                $reports[] = [
                                    'mgs' => 'Dòng ' . ($key + 1) . ":\t Sinh viên $value[0] không thêm được vào lớp $value[1] ( $value[2] ): vì trùng với lớp " . $groupConfig['group_name'] . " (" . $groupConfig['psubject_code'] . ")",
                                    'status' => 0,
                                ];
        
                                $checkConflig = false;
                                break;
                            }
                        }
                    }
                    */

                    // quét lịch học của lớp muốn join
                    foreach ($listGroupSchedule[$groupJoin->id] as $slot => $listDay) {
                        // kiểm tra xem có trùng với lịch học hiện tại không
                        foreach ($listDay as $day => $checkDay) {
                            if (isset($listGroupSchedule[$groupIdCheck][$slot][$day])) {
                                $groupConfig = $listGroupInfor[$groupIdCheck];
                                $strRp = "Ngày $day tại slot $slot";
                                $reports[] = [
                                    'mgs' => 'Dòng ' . ($key + 1) . ":\t Sinh viên $value[0] không thêm được vào lớp $value[1] ( $value[2] ): vì trùng với lớp " . $groupConfig['group_name'] . " (" . $groupConfig['psubject_code'] . ") - ($strRp)",
                                    'status' => 0,
                                ];

                                $checkConflig = false;
                                break;
                            }
                        }
                    }

                    if ($checkConflig == false) break;
                }

                if ($checkConflig == false) {
                    continue;
                }


                /* =========== thêm sinh viên vào lớp =========== */
                $listMemberGroup[$studentAdd->user_login][] = $groupJoin->id;
                GroupMember::create([
                    'groupid' => $groupJoin->id,
                    'member_login' => $studentAdd->user_login,
                    'user_code' => $studentAdd->user_code,
                    'date' => now()->format('Y-m-d'),
                ]);

                $textLog = "add member $studentAdd->user_login to group $groupJoin->id";
                if ($checkItProcess == 1) {
                    $textLog = $textLog . " ($noteProcess)";
                }

                SystemLog::create([
                    'actor' => $user->user_login,
                    'object_name' => "group",
                    'log_time' => now(),
                    'action' => "add member",
                    'description' => $textLog,
                    'relation_login' => $studentAdd->user_login,
                    'object_id' => $groupJoin->id,
                    'relation_id' => 0,
                    'brief' => "add member",
                    'from_ip' => request()->ip(),
                    'nganh_cu' => '',
                    'nganh_moi' => '',
                    'ky_chuyen_den' => 0,
                    'ky_thu_cu' => 0,
                ]);

                $reports[] = [
                    'mgs' => 'Dòng ' . ($key + 1) . ":\t Sinh viên $studentAdd->user_code thêm thành công vào lớp $groupJoin->group_name ($groupJoin->psubject_code) ($groupJoin->id)",
                    'status' => 1,
                ];
            }

            // DB::rollback();
            DB::commit();
        } catch (\Exception $ex) {
            $reports = [
                [
                    'mgs' => $ex->getMessage(),
                    'status' => 0,
                ],
                [
                    'mgs' =>  'Dòng ' . (($errorRecord['key'] ?? 0) + 1) . ' có lỗi: [' . ($errorRecord['value'][0] ?? "") . '-' . ($errorRecord['value'][1] ?? "") . '-' . ($errorRecord['value'][2] ?? "") . ']',
                    'status' => 0,
                ],
            ];

            DB::rollback();
        }

        return back()->with([
            'reports' => $reports
        ]);
    }


    /**
     * <AUTHOR>
     * @param Request $request 
     * rút sinh viên ra khỏi lớp bằng file
     */
    public function removeStudentFile($request)
    {
        ini_set('memory_limit', '-1');
        $rules = ['file' => 'required'];
        $messages = [
            'file.required' => 'File rút sinh viên không được bỏ trống',
        ];

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            return back()->with([
                'errors2' => $validator->errors()->first(),
            ]);
        }

        // lấy dữ liệu từ file
        $file = $request->file('file');
        $user = auth()->user();
        if (!$datas = $this->importDataFromFile($file)) {
            return back()->with([
                'errors2' => 'Không có sinh viên nào để import hoặc file tải lên bị lỗi',
            ]);
        }

        $termId = $request->get('term_id', null);
        if ($termId == null) {
            $currentTerm = Term::whereRaw('startday <= CURRENT_DATE')
                ->whereRaw('endday >= CURRENT_DATE')
                ->first();

            $termId = $currentTerm->id;
        } else {
            $currentTerm = Term::find($termId);
        }

        $reports = [];
        $errorRecord = null;
        $listStudentObejct = []; // dánh
        $listGroup = []; // danh sách lớp cần thêm sinh viên
        $listCheckLimitGroup = []; //  danh sách quá 30%
        DB::beginTransaction();
        try {
            foreach ($datas as $key => $value) {
                $errorRecord = [
                    'key' => $key,
                    'value' => $value
                ];

                $value = array_map(function ($a) {
                    return trim($a);
                }, $value);

                /* =========== Kiểm tra mã sinh viên có tồn tại hay không =========== */
                if (count($value) == 0 || $value[0] == null || trim($value[0]) == '') {
                    $reports[] = [
                        'mgs' => 'Dòng ' . ($key + 1) . ":\t Lỗi mã sinh viên $value[0]",
                        'status' => 0,
                    ];
                    continue;
                }


                /* =========== kiểm tra tài khoản sinh viên tồn tại hay không =========== */
                if (!isset($listStudentObejct[$value[0]])) {
                    $studentRemove = User::query()
                        ->select('user_code', 'user_login')
                        ->where('user_code', $value[0])
                        ->first();

                    if (!$studentRemove) {
                        $reports[] = [
                            'mgs' => 'Dòng ' . ($key + 1) . ":\t Sinh viên $value[0] không tồn tại",
                            'status' => 0,
                        ];
                        continue;
                    }

                    $listStudentObejct[$value[0]] = $studentRemove;
                }

                $studentRemove = $listStudentObejct[$value[0]];
                if (!isset($listGroup[trim($value[2]) . trim($value[1])])) {
                    $listGroup[trim($value[2]) . trim($value[1])] = Group::query()->where('list_group.is_virtual', 0)
                        ->select([
                            'list_group.id',
                            'list_group.group_name',
                            'list_group.psubject_code',
                            'list_group.number_student'
                        ])
                        ->where('group_name', $value[1])
                        ->where('psubject_code', $value[2])
                        ->where('pterm_id', $termId)
                        ->first();
                }

                /* =========== kiểm tra lớp có tồn tại hay không =========== */
                $groupOut = $listGroup[trim($value[2]) . trim($value[1])];
                if (!$groupOut) {
                    $reports[] = [
                        'mgs' => 'Dòng ' . ($key + 1) . ":\t Sinh viên $value[0] không rút được khỏi lớp $value[1] ( $value[2] ): do lớp không tồn tại",
                        'status' => 0,
                    ];
                    continue;
                }


                /* =========== Kiểm tra sinh viên tồn tại trong lớp không =========== */
                $checkInsideGroup = GroupMember::query()
                    ->where('groupid', $groupOut->id)
                    ->where('member_login', $studentRemove->user_login)
                    ->first();
                if ($checkInsideGroup == null) {
                    $reports[] = [
                        'mgs' => 'Dòng ' . ($key + 1) . ":\t Sinh viên $value[0] không tồn tại trong lớp $value[1] ( $value[2] )",
                        'status' => 2,
                    ];
                    continue;
                }

                /* =========== Kiểm tra lớp quá 30% chưa =========== */
                if (!isset($listCheckLimitGroup[$groupOut->id])) {
                    $listCheckLimitGroup[$groupOut->id] = $this->checkLimitSchedule($groupOut->id);
                }

                $checkLimit = $listCheckLimitGroup[$groupOut->id];
                if ($checkLimit['status'] == false) {
                    $reports[] = [
                        'mgs' => 'Dòng ' . ($key + 1) . ":\t Sinh viên $value[0] không rút được khỏi lớp $value[1] ( $value[2] ): " . $checkLimit['msg'],
                        'status' => 0,
                    ];
                    continue;
                }

                /* =========== rút sinh viên khỏi lớp =========== */
                SystemLog::create([
                    'actor' => $user->user_login,
                    'object_name' => "group",
                    'log_time' => now(),
                    'action' => "remove member",
                    'description' => "remove member $studentRemove->user_login from group $groupOut->id",
                    'relation_login' => $studentRemove->user_login,
                    'object_id' => $groupOut->id,
                    'relation_id' => 0,
                    'brief' => "remove member",
                    'from_ip' => request()->ip(),
                    'nganh_cu' => '',
                    'nganh_moi' => '',
                    'ky_chuyen_den' => 0,
                    'ky_thu_cu' => 0,
                ]);

                CourseResult::where('student_login', $studentRemove->user_login)->where('groupid', $groupOut->id)->delete();
                CourseGrade::where('login', $studentRemove->user_login)->where('groupid', $groupOut->id)->delete();
                Attendance::where('user_login', $studentRemove->user_login)->where('groupid', $groupOut->id)->delete();
                $checkInsideGroup->delete();
                $groupOut->number_student = $groupOut->number_student - 1;
                $groupOut->save();
                $reports[] = [
                    'mgs' => 'Dòng ' . ($key + 1) . ":\t Sinh viên $studentRemove->user_code đã được rút thành công khỏi lớp $groupOut->group_name ($groupOut->psubject_code) ($groupOut->id)",
                    'status' => 1,
                ];
            }

            DB::commit();
        } catch (\Exception $ex) {
            Log::error($ex);
            $reports = [
                [
                    'mgs' => $ex->getMessage(),
                    'status' => 0,
                ],
                [
                    'mgs' =>  'Dòng ' . (($errorRecord['key'] ?? 0) + 1) . ' có lỗi: [' . ($errorRecord['value'][0] ?? "") . '-' . ($errorRecord['value'][1] ?? "") . '-' . ($errorRecord['value'][2] ?? "") . ']',
                    'status' => 0,
                ],
            ];

            DB::rollback();
        }

        return back()->with([
            'reports' => $reports
        ]);
    }

    /**
     * Kiểm tra đổi chéo lớp giữa 2 sinh viên
     * <AUTHOR>
     * @param Request $request  
     */
    public function checkSwapGroup($request)
    {
        // init data
        $studentCode1 = trim($request->get('student_code1', ''));
        $studentCode2 = trim($request->get('student_code2', ''));
        $res = [
            'student1' => [
                'student_code' => $studentCode1,
                'student_login' => null,
                'status' => true,
                'group_swap' => '',
                'current_group' => null,
                'msg' => ''
            ],
            'student2' => [
                'student_code' => $studentCode2,
                'student_login' => null,
                'status' => true,
                'group_swap' => '',
                'current_group' => null,
                'msg' => ''
            ],
        ];

        $subejctCode = $request->get('subject_code', null);
        if ($request->get('term_id', null) == null) {
            $currentTerm = Term::orderBy('id', 'DESC')
                ->first();
        } else {
            $currentTerm = Term::find($request->term_id);
        }

        $student1 = User::select([
            'user_code',
            'user_login',
        ])->where('user_code', $studentCode1)
            ->first();
        $student2 = User::select([
            'user_code',
            'user_login',
        ])->where('user_code', $studentCode2)
            ->first();

        // kiểm tra sự tồn tại của sinh viên
        if (!$student1) {
            // return ResponseBuilder::Success([], 'Tạo feedback thành công');
            $res['student1']['status'] = false;
            $res['student1']['msg'] = "Sinh viên $studentCode1 không tồn tại, vui lòng kiểm tra lại";
            return ResponseBuilder::Fail('', $res);
        }

        if (!$student2) {
            $res['student2']['status'] = false;
            $res['student2']['msg'] = "Sinh viên $studentCode2 không tồn tại, vui lòng kiểm tra lại";
            return ResponseBuilder::Fail('', $res);
        }

        $res['student1']['student_login'] = $student1->user_login;
        $res['student2']['student_login'] = $student2->user_login;

        // Lấy lớp hiện tại của sinh viên cần đổi 
        $groupStudent1 = Group::select([
            'list_group.id',
            'list_group.group_name',
            'list_group.psubject_code',
            'list_group.skill_code',
            'group_member.member_login',
            'list_group.start_date',
            DB::raw('(CASE WHEN (group.start_date >= CURRENT_DATE) THEN 1 ELSE 0 END ) as check_start_date')
        ])->join('group_member', 'group_member.groupid', '=', 'list_group.id')
            ->where('member_login', $student1->user_login)
            ->where('list_group.pterm_id', $currentTerm->id)
            ->where('list_group.psubject_code', $subejctCode)
            ->where('list_group.is_virtual', 0)
            ->orderBy('list_group.id', 'desc')
            ->first();

        $groupStudent2 = Group::select([
            'list_group.id',
            'list_group.group_name',
            'list_group.psubject_code',
            'list_group.skill_code',
            'group_member.member_login',
            'list_group.start_date',
            DB::raw('(CASE WHEN (group.start_date >= CURRENT_DATE) THEN 1 ELSE 0 END ) as check_start_date')
        ])->join('group_member', 'group_member.groupid', '=', 'list_group.id')
            ->where('member_login', $student2->user_login)
            ->where('list_group.pterm_id', $currentTerm->id)
            ->where('list_group.psubject_code', $subejctCode)
            ->where('list_group.is_virtual', 0)
            ->orderBy('list_group.id', 'desc')
            ->first();

        $valid = true;
        if (!$groupStudent1) {
            $res['student1']['status'] = false;
            $res['student1']['group_origin'] ='N/A';
            $res['student1']['group_swap'] = 'N/A';
            $res['student1']['msg'] = "Không tìm thấy lớp trong lịch học của sinh viên.";
            $valid = false;
        } elseif ($groupStudent1->check_start_date == 0) {
            $res['student1']['status'] = false;
            $res['student1']['group_origin'] = $groupStudent1->group_name;
            $res['student1']['group_swap'] = 'N/A';
            $res['student1']['msg'] = "Lớp đã bắt đầu, không đổi được";
            $valid = false;
        }

        if (!$groupStudent2) {
            $res['student2']['status'] = false;
            $res['student2']['group_origin'] ='N/A';
            $res['student2']['group_swap'] = 'N/A';
            $res['student2']['msg'] = "Không tìm thấy lớp trong lịch học của sinh viên.";
            $valid = false;
        } elseif ($groupStudent2->check_start_date == 0) {
            $res['student2']['status'] = false;
            $res['student2']['group_origin'] = $groupStudent2->group_name;
            $res['student2']['group_swap'] = 'N/A';
            $res['student2']['msg'] = "Lớp đã bắt đầu, không đổi được";
            $valid = false;
        }

        if ($groupStudent1->id == $groupStudent2->id) {
            $res = [
                'student1' => [
                    'status' => false,
                    'group_origin' => $groupStudent1->group_name,
                    'group_swap' => $groupStudent1->group_name,
                    'msg' => "Sinh viên cùng lớp",
                ],
                'student2' => [
                    'status' => false,
                    'group_origin' => $groupStudent2->group_name,
                    'group_swap' => $groupStudent2->group_name,
                    'msg' => "Sinh viên cùng lớp",
                ]
            ];
            $valid = false;
        }

        if ($valid == false) {
            return ResponseBuilder::Fail('', $res);
        }

        $checkstudent1 = GroupHelper::getListClassJoin($currentTerm, $groupStudent1->id, $student1, $subejctCode, $groupStudent2);
        $checkstudent2 = GroupHelper::getListClassJoin($currentTerm, $groupStudent2->id, $student2, $subejctCode, $groupStudent1);
        $res['student1']['current_group'] = $groupStudent1->id;
        $res['student1']['status'] = $checkstudent1['status'];
        $res['student1']['msg'] = $checkstudent1['msg'];
        $res['student1']['group_swap'] = $checkstudent1['group_check'];
        $res['student2']['current_group'] = $groupStudent2->id;
        $res['student2']['status'] = $checkstudent2['status'];
        $res['student2']['msg'] = $checkstudent2['msg'];
        $res['student2']['group_swap'] = $checkstudent2['group_check'];

        if ($checkstudent1['status'] == false || $checkstudent2['status'] == false) {
            return ResponseBuilder::Fail('', $res);
        }

        return ResponseBuilder::Success($res, 'Kiểm tra thành công');
    }


    /**
     * Đổi lớp cho 2 sinh viên
     * 
     * <AUTHOR>
     * @param Request $request  
     */
    public function swapGroup($request)
    {

        $user = auth()->user();
        $studentRequest1 = $request->get('student1', null);
        $studentRequest2 = $request->get('student2', null);
        if ($studentRequest1 == null || $studentRequest2 == null) {
            return ResponseBuilder::Fail('có lỗi xảy ra, vui lòng thử lại', []);
        }

        $groupId1 = $studentRequest1['current_group'];
        $groupId2 = $studentRequest2['current_group'];
        DB::beginTransaction();
        try {
            // lấy ra 2 lớp của sinh viên
            $groupMemberUser1 = GroupMember::select([
                'id',
                'member_login',
                'groupid'
            ])
                ->where('member_login', $studentRequest1['user_login'])
                ->where('groupid', $groupId1)
                ->first();
            $groupMemberUser2 = GroupMember::select([
                'id',
                'member_login',
                'groupid'
            ])
                ->where('member_login', $studentRequest2['user_login'])
                ->where('groupid', $groupId2)
                ->first();

            // kiểm tra xóa t7_course_result với nếu có
            CourseResult::where('student_login', $studentRequest1['user_login'])
                ->where('groupid', $groupId1)
                ->delete();

            CourseResult::where('student_login', $studentRequest2['user_login'])
                ->where('groupid', $groupId2)
                ->delete();

            /* ========== Thêm 4 log ========== */
            // remove sinh viên 1 ra khỏi lớp
            SystemLog::create([
                'actor' => $user->user_login,
                'object_name' => "group",
                'log_time' => now(),
                'action' => "remove member",
                'description' => "(đổi chéo) remove member $groupMemberUser1->member_login from group $groupMemberUser1->groupid",
                'relation_login' => $groupMemberUser1->member_login,
                'object_id' => $groupMemberUser1->groupid,
                'relation_id' => 0,
                'brief' => "remove member",
                'from_ip' => request()->ip(),
                'nganh_cu' => '',
                'nganh_moi' => '',
                'ky_chuyen_den' => 0,
                'ky_thu_cu' => 0,
            ]);

            // add sinh viên vào lớp
            SystemLog::create([
                'actor' => $user->user_login,
                'object_name' => "group",
                'log_time' => now(),
                'action' => "add member",
                'description' => "(đổi chéo) add member $groupMemberUser1->member_login to group $groupMemberUser2->groupid",
                'relation_login' => $groupMemberUser1->member_login,
                'object_id' => $groupMemberUser2->groupid,
                'relation_id' => 0,
                'brief' => "add member",
                'from_ip' => request()->ip(),
                'nganh_cu' => '',
                'nganh_moi' => '',
                'ky_chuyen_den' => 0,
                'ky_thu_cu' => 0,
            ]);

            // remove sinh viên 2 ra khỏi lớp
            SystemLog::create([
                'actor' => $user->user_login,
                'object_name' => "group",
                'log_time' => now(),
                'action' => "remove member",
                'description' => "(đổi chéo) remove member $groupMemberUser2->member_login from group $groupMemberUser2->groupid",
                'relation_login' => $groupMemberUser2->member_login,
                'object_id' => $groupMemberUser2->groupid,
                'relation_id' => 0,
                'brief' => "remove member",
                'from_ip' => request()->ip(),
                'nganh_cu' => '',
                'nganh_moi' => '',
                'ky_chuyen_den' => 0,
                'ky_thu_cu' => 0,
            ]);

            // add sinh viên vào lớp
            SystemLog::create([
                'actor' => $user->user_login,
                'object_name' => "group",
                'log_time' => now(),
                'action' => "add member",
                'description' => "(đổi chéo) add member $groupMemberUser2->member_login to group $groupMemberUser1->groupid",
                'relation_login' => $groupMemberUser2->member_login,
                'object_id' => $groupMemberUser1->groupid,
                'relation_id' => 0,
                'brief' => "add member",
                'from_ip' => request()->ip(),
                'nganh_cu' => '',
                'nganh_moi' => '',
                'ky_chuyen_den' => 0,
                'ky_thu_cu' => 0,
            ]);

            $groupMemberUser1->groupid = $groupId2;
            $groupMemberUser1->save();
            $groupMemberUser2->groupid = $groupId1;
            $groupMemberUser2->save();
            // đổi lớp cho sinh viên
            DB::commit();
        } catch (\Exception $ex) {
            Log::error("!dev Check");
            Log::error($ex);
            return ResponseBuilder::Fail('có lỗi xảy ra, vui lòng liên hệ với cán bộ IT', []);
        }

        return ResponseBuilder::Success([], 'Chuyển lớp cho sinh viên thành công');
    }


    /**
     * get list class can joint
     * 
     * <AUTHOR>
     * @param Request $request  
     */
    public function getListClassCanJoin($request)
    {
        // Khởi tạo dữ liệu
        $userCode = $request->user_code;
        $subjectCode = $request->subject_code;
        $termId = $request->term_id;
        $maxSchedule = 0;
        $student = User::select([
            'user_code',
            'user_login',
            'user_surname',
            'user_middlename',
            'user_givenname',
        ])->where('user_code', $userCode)
            ->first();

        $userLogin = $student->user_login;
        $inFoCurrentGroup = null;
        // lấy lớp hiện tại của sv
        $currentGroup = Group::select('list_group.id', 'list_group.group_name')
            ->where('list_group.is_virtual', 0)
            ->leftjoin('group_member', 'list_group.id', '=', 'group_member.groupid')
            ->where('group_member.member_login', $userLogin)
            ->where('list_group.psubject_Code', $subjectCode)
            ->where('list_group.pterm_id', $termId)
            ->orderBy('list_group.id', 'desc')
            ->first();

        if (!$currentGroup) {
            return ResponseBuilder::Fail('Không tìm thấy lớp môn của sinh viên');
        }

        // lấy thông tin lớp học theo ID
        $infoGroup = GroupHelper::getInfoGroup($currentGroup->id);
        // kiểm tra lớp đấy học chưa
        $checkLearedGroup = Activity::where('groupid', $currentGroup->id)
            ->whereRaw('day <= CURRENT_DATE')
            ->count();
        if ($checkLearedGroup > 0) {
            return ResponseBuilder::Success([
                'current_group' => $infoGroup,
                'current_user' => [
                    'user_code' => $student->user_code,
                    'user_login' => $student->user_login,
                    'full_name' => $student->fullname()
                ],
                'list_group' => [],
            ], "Lớp của bạn $currentGroup->id ($currentGroup->group_name) đã bắt đầu, bạn không thể chuyển");
        }

        if ($request->get('term_id', null) == null) {
            $currentTerm = Term::orderBy('id', 'DESC')
                ->first();
        } else {
            $currentTerm = Term::find($request->term_id);
        }

        // Lấy danh sách lịch học của sinh viên
        $currentScheduleArr = call_user_func(function () use ($userLogin, $currentTerm, $currentGroup) {
            $listGroupUser = Group::select('list_group.id')
                ->where('list_group.is_virtual', 0)
                ->leftjoin('group_member', 'list_group.id', '=', 'group_member.groupid')
                ->where('group_member.member_login', $userLogin)
                ->where('list_group.id', '!=', $currentGroup->id)
                ->get();

            $currentSchedule = GroupHelper::getListActivity($listGroupUser->pluck('id'), true);
            $res = [];
            foreach ($currentSchedule as $value) {
                $res[$value->day . '-' . $value->slot] = 1;
            }

            return $res;
        });

        // Lấy số buổi max của môn 
        $maxSchedule = SyllabusPlan::leftJoin('session_type', 't7_syllabus_plan.session_type', '=', 'session_type.id')
            ->where('session_type.is_exam', '=', 0)
            ->whereRaw("syllabus_id IN ( SELECT syllabus_id FROM course WHERE term_id = ? AND psubject_code = '$subjectCode' )", [$currentTerm->id])
            ->count();
        if ($maxSchedule == 0) {
            return ResponseBuilder::Fail('Có vẻ khóa học không tồn tại, vui lòng liên hệ lại với cán bộ IT', []);
        }

        // Lấy lịch hiện tại của các lớp theo môn
        if (!Cache::has("activity-subject-check-$subjectCode-$currentTerm->id")) {
            $currentScheduleCompare = Activity::select('day', 'slot', 'groupid')
                ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
                ->where('session_type.is_exam', '=', 0)
                ->where('psubject_code', $subjectCode)
                ->where('term_id', $currentTerm->id)
                ->get();
            Cache::put("activity-subject-check-$subjectCode-$currentTerm->id", $currentScheduleCompare, 86400);
        }

        $currentScheduleCompare = Cache::get("activity-subject-check-$subjectCode-$currentTerm->id");
        // khởi tạo dữ liệu check với key là [Ngày-Slot]
        $currentScheduleCompareArr = call_user_func(function () use ($currentScheduleCompare) {
            $res = [];
            foreach ($currentScheduleCompare as $value) {
                $res[$value->day . '-' . $value->slot][] = $value->groupid;
            }

            return $res;
        });

        // lấy danh sách lớp trùng để remove
        $listGroupIdRemove = [$currentGroup->id];
        // $listGroupIdRemove = [];
        foreach ($currentScheduleCompareArr as $key => $value) {
            if (isset($currentScheduleArr[$key])) {
                $listGroupIdRemove = array_merge($listGroupIdRemove, $value);
            }
        }

        $listGroupIdRemove = array_unique($listGroupIdRemove);
        $listGroupCanJoin = Group::select(DB::raw("id, start_date, end_date, ( CASE WHEN start_date < CURRENT_DATE THEN 1 ELSE 0 END  ) as need_check"))
            ->whereDate('end_date', '>=', DB::raw('CURRENT_DATE'))
            ->where('list_group.is_virtual', 0)
            // ->where('psubject_code', $subjectCode)
            ->where('psubject_code', $subjectCode)
            ->where('pterm_id', '>=', $currentTerm->id)
            ->whereNotIn('id', $listGroupIdRemove)
            ->get();

        // nếu không có lớp nào trả về luôn
        if (count($listGroupCanJoin) == 0) {
            // return [];
            return ResponseBuilder::Success([
                'current_group' => $infoGroup,
                'current_user' => [
                    'user_code' => $student->user_code,
                    'user_login' => $student->user_login,
                    'full_name' => $student->fullname()
                ],
                'list_group' => [],
            ], 'không có lớp nào có thể chuyển');
        }

        // kiểm tra lớp đã học
        $listGroupCheck = array_filter($listGroupCanJoin->toArray(), function ($a) {
            if ($a['need_check'] == 1) return $a;
        });

        // $listGroupIdRemove = array_merge($listGroupCheck, array_values($listGroupIdRemove));
        // $listGroupIdRemove = array_merge(array_column($listGroupCheck, 'id'), array_values($listGroupIdRemove));
        // kiểm tra danh sách lớp đã học
        $listGroupId = array_column($listGroupCheck, 'id');
        $groupsNeedCheck = Activity::select(DB::raw("activity.groupid, COUNT( activity.id ) AS lession_is_over"))
            ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
            ->whereRaw("activity.`day` <= CURRENT_DATE")
            ->whereIn('activity.groupid', $listGroupId)
            ->where('session_type.is_exam', '=', 0)
            ->groupBy('activity.groupid')
            ->get();

        // kiểm tra lớp đã học quá 30% chưa, nếu đã quá thì thêm vào danh sách lớp cần remove
        if (count($listGroupCheck) > 0) {
            $listGroupIdRemove = call_user_func(function () use ($listGroupIdRemove, $groupsNeedCheck, $maxSchedule) {
                $res = [];
                foreach ($groupsNeedCheck as $value) {
                    if (($value->lession_is_over / $maxSchedule) >= 0.3) {
                        $res[] = $value->groupid;
                    }
                }

                $res = array_merge($res, array_values($listGroupIdRemove));
                return $res;
            });
        }

        // lấy các lớp có thể join 
        $finalActivity = Group::select([
            'list_group.id',
            'list_group.start_date',
            'list_group.end_date',
            'list_group.group_name',
            'list_group.psubject_code',
            'list_group.psubject_name',
            'activity.slot',
            'activity.course_slot',
            'activity.day',
            DB::raw('DAYOFWEEK(activity.day) as day_of_week')
        ])
            ->leftJoin('activity', 'activity.groupid', '=', 'list_group.id')
            ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
            ->whereNotIn('list_group.id', $listGroupIdRemove)
            ->where('list_group.psubject_code', $subjectCode)
            ->where('list_group.is_virtual', 0)
            ->where('pterm_id', '>=', $currentTerm->id)
            ->where('session_type.is_exam', '=', 0)
            ->whereDate('end_date', '>', DB::raw('CURRENT_DATE'))
            ->groupBy('list_group.id')
            ->get();

        // nếu không có lớp nào
        if (count($finalActivity) == 0) {
            return ResponseBuilder::Fail('', []);
        }

        $finalData = call_user_func(function () use ($finalActivity, $currentTerm, $groupsNeedCheck, $maxSchedule, &$inFoCurrentGroup, $currentGroup) {
            $res = [];
            $listIdGroupCheck = $groupsNeedCheck->pluck('groupid')->toArray();
            $listGroupId = call_user_func(function () use ($finalActivity) {
                $res = [];
                foreach ($finalActivity as $key => $value) {
                    $res[] = $value->id;
                }

                return $res;
            });

            $listCountStudent = GroupMember::select([
                'groupid',
                DB::raw('count(*) as _count')
            ])
                ->whereIn('groupid', $listGroupId)
                ->groupBy('groupid')
                ->get();

            if (count($listCountStudent) > 0) {
                $listCountStudent = $listCountStudent->pluck('_count', 'groupid')->toArray();
            } else {
                $listCountStudent = [];
            }

            foreach ($finalActivity as $key => $value) {
                if (!isset($res[$value->id])) {
                    $percentLessionOver = 0;
                    $lessionOver = 0;
                    if (in_array($value->id, $listIdGroupCheck)) {
                        $dataCheck = $groupsNeedCheck->where('groupid', $value->id)->first();
                        if ($maxSchedule > 0) {
                            $percentLessionOver = round($dataCheck->lession_is_over / $maxSchedule, 2);
                        } else {
                            $percentLessionOver = 0;
                        }

                        $lessionOver = $dataCheck->lession_is_over;
                    }

                    $res[$value->id] = [
                        'group_id' => $value->id,
                        'group_name' => $value->group_name,
                        'start_date' => $value->start_date,
                        'end_date' => $value->end_date,
                        'psubject_code' => $value->psubject_code,
                        'psubject_name' => $value->psubject_name,
                        'slot' => $value->slot,
                        'course_slot' => $value->course_slot,
                        'day' => $value->day,
                        'percent_lession_is_over' => $percentLessionOver,
                        'lession_is_over' => $lessionOver,
                        // 'number_student' => $value->groupMembers()->select('id')->count(),
                        'number_student' => ($listCountStudent[$value->id] ?? 0),
                        'day_of_week' => [$value->day_of_week]
                    ];
                } else {
                    $res[$value->id]['day_of_week'][] = $value->day_of_week;
                }
            }

            // sắp xếp lại hiển thị của ngày trong tuần
            foreach ($res as $key => $value) {
                $res[$key]['day_of_week'] = array_values(array_unique($res[$key]['day_of_week']));
                sort($res[$key]['day_of_week']);
                $res[$key]['day_of_week_str'] = implode(',', $res[$key]['day_of_week']);
            }

            usort($res, function ($a, $b) {
                return $a['number_student'] > $b['number_student'];
            });

            return $res;
        });

        // kiểm tra số lượng sinh của lớp theo môn
        $maxStudent = 40;
        $otherCondition = [
            'SKI1014'   =>  ['max' => 9, 'min' => 9],
            'ACC105'   => ['max' => -10, 'min' => 0],
            'AUT102'   => ['max' => -10, 'min' => 0],
            'AUT103'   => ['max' => -10, 'min' => 0],
            'AUT104'   => ['max' => -10, 'min' => 0],
            'AUT105'   => ['max' => -10, 'min' => 0],
            'AUT106'   => ['max' => -10, 'min' => 0],
            'AUT107'   => ['max' => -10, 'min' => 0],
            'AUT108'   => ['max' => -10, 'min' => 0],
            'AUT109'   => ['max' => -10, 'min' => 0],
            'AUT110'   => ['max' => -10, 'min' => 0],
            'AUT206'   => ['max' => -10, 'min' => 0],
            'EHO102'   => ['max' => -10, 'min' => 0],
            'EHO202'   => ['max' => -10, 'min' => 0],
            'ENT1125'   => ['max' => -10, 'min' => 0],
            'ENT1126'   => ['max' => -10, 'min' => 0],
            'ENT1225'   => ['max' => -10, 'min' => 0],
            'ENT2125'   => ['max' => -10, 'min' => 0],
            'ENT2225'   => ['max' => -10, 'min' => 0],
            'ETO101'   => ['max' => -10, 'min' => 0],
            'ETO201'   => ['max' => -10, 'min' => 0],
            'HIS101'   => ['max' => -10, 'min' => 0],
            'HIS102'   => ['max' => -10, 'min' => 0],
            'HOS1011'   => ['max' => -10, 'min' => 0],
            'HOS1021'   => ['max' => -10, 'min' => 0],
            'HOS1031'   => ['max' => -10, 'min' => 0],
            'HOS1041'   => ['max' => -10, 'min' => 0],
            'HOS105'   => ['max' => -10, 'min' => 0],
            'HOS2011'   => ['max' => -10, 'min' => 0],
            'HOS2021'   => ['max' => -10, 'min' => 0],
            'HOS305'   => ['max' => -10, 'min' => 0],
            'HOS401'   => ['max' => -10, 'min' => 0],
            'HOS402'   => ['max' => -10, 'min' => 0],
            'HOS4031'   => ['max' => -10, 'min' => 0],
            'INE101'   => ['max' => -10, 'min' => 0],
            'INE102'   => ['max' => -10, 'min' => 0],
            'INE202'   => ['max' => -10, 'min' => 0],
            'INE203'   => ['max' => -10, 'min' => 0],
            'INE214'   => ['max' => -10, 'min' => 0],
            'MEC105'   => ['max' => -10, 'min' => 0],
            'MEC111'   => ['max' => -10, 'min' => 0],
            'MEC114'   => ['max' => -10, 'min' => 0],
            'MEC119'   => ['max' => -10, 'min' => 0],
            'MEC201'   => ['max' => -10, 'min' => 0],
            'MEC2021'   => ['max' => -10, 'min' => 0],
            'PRO1051'   => ['max' => -10, 'min' => 0],
            'PRO1081'   => ['max' => -10, 'min' => 0],
            'PRO125'   => ['max' => -10, 'min' => 0],
            'PRO127'   => ['max' => -10, 'min' => 0],
            'PRO1291'   => ['max' => -10, 'min' => 0],
            'PRO132'   => ['max' => -10, 'min' => 0],
            'PSY1011'   => ['max' => -10, 'min' => 0],
            'TOU1011'   => ['max' => -10, 'min' => 0],
            'TOU1021'   => ['max' => -10, 'min' => 0],
            'TOU106'   => ['max' => -10, 'min' => 0],
            'TOU2013'   => ['max' => -10, 'min' => 0],
            'TOU2021'   => ['max' => -10, 'min' => 0],
            'TOU2031'   => ['max' => -10, 'min' => 0],
            'TOU2041'   => ['max' => -10, 'min' => 0],
            'TOU3011'   => ['max' => -10, 'min' => 0],
            'TOU302'   => ['max' => -10, 'min' => 0],
            'VIE1016'   => ['max' => 60, 'min' => 55],
            'VIE1026'   => ['max' => 60, 'min' => 55],
        ];

        $maxStudent = $maxStudent + ($otherCondition[$subjectCode]['max'] ?? 0);
        return ResponseBuilder::Success([
            'max_student' => $maxStudent,
            'max_schedule' => $maxSchedule,
            'current_group' => $infoGroup,
            'current_user' => [
                'user_code' => $student->user_code,
                'user_login' => $student->user_login,
                'full_name' => $student->fullname()
            ],
            'list_group' => $finalData,
        ], 'Lấy danh sách thành công');
    }

    /**
     * <AUTHOR>
     * 
     */
    public function updateScheduleNoCheck($request)
    {

        ini_set('max_execution_time', -1);
        $rules = [
            'file' => 'required',
            'term_id' => "required|exists:term,id"
        ];

        $messages = [
            'file.required' => 'File thêm sinh viên không được bỏ trống',
            'term_id.required' => 'Vui lòng chọn lại kỳ',
            'term_id.exists' => 'Kỳ học không tồn tại'
        ];

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            return back()->with([
                'reports' => [[
                    'msg' => $validator->errors()->first(),
                    'status' => false,
                ]]
            ]);
        }
        
        // Kiểm tra quyền của người up
        $user = auth()->user();
        $listRole = T1UserRole::where('user_login', $user->user_login)->get();
        if ($listRole) {
            $listRole = array_column($listRole->toArray(), 'role_id');
        } else {
            $listRole = [];
        }

        if ($user->user_level != 1 && !in_array(87, $listRole)) {
            return back()->with([
                'reports' => [[
                    'msg' => 'Bạn không có quyền để quản lý',
                    'status' => false,
                ]]
            ]);
        }

        $file = $request->file('file'); // kiểm tra file 
        if (!$datas = $this->importDataFromFile($file)) {
            return back()->with([
                'reports' => [[
                    'msg' => 'Không có dữ liệu hoặc file tải lên bị lỗi.',
                    'status' => false,
                ]]
            ]);
        }

        // Kiểm tra kỳ add (Kỳ đang chạy và kỳ chưa chạy)
        $autoCreateGroup = $request->get('auto_render_group', null);
        $termId = $request->get('term_id', null);
        $term = Term::where('id', $termId)
            ->where('is_locked', 0)
            ->first();
        if ($term == null) {
            return back()->with([
                'reports' => [[
                    'msg' => 'Kỳ của bạn không đúng hoặc đã khóa, xin hãy thử lại.',
                    'status' => false,
                ]]
            ]);
        }

        $reports = [];
        $listGroup = [];
        $listSubject = [];
        $listSubjectCourse = [];
        $listStudentLogin = [];
        $listCheckLimitGroup = [];

        DB::beginTransaction();
        try {
            foreach ($datas as $key => $data) {
                // init data
                $userCode = trim($data[0]);
                $groupName = trim($data[1]);
                $subjectCode = trim($data[2]);

                // kiểm tra sự tồn tại của sinh viên
                if (!isset($listStudentLogin[$userCode])) {
                    $listStudentLogin[$userCode] = User::select([
                        'id',
                        'user_code',
                        'user_login',
                    ])
                        ->where('user_level', 3)
                        ->where('user_code', $userCode)
                        ->first();
                }

                $studentAdd = $listStudentLogin[$userCode];
                if ($studentAdd == null) {
                    $reports[] = [
                        'msg' => "Dòng $key: Sinh viên <b>$userCode</b> không tồn tại",
                        'status' => false,
                    ];
                    continue;
                }

                // kiểm tra sự tồn tại của môn học
                if (!isset($listSubject[$subjectCode])) {
                    $listSubject[$subjectCode] = Subject::select([
                        'id',
                        'subject_name',
                        'subject_code',
                        'num_of_credit',
                        'skill_code',
                        'department_id'
                    ])
                        ->where('subject_code', $subjectCode)
                        ->first();

                    // kiểm tra course 
                    if ($listSubject[$subjectCode] == null) {
                        $listSubjectCourse[$subjectCode] = null;
                    } else {
                        $listSubjectCourse[$subjectCode] = Course::select([
                            'id',
                            'subject_id',
                            'psubject_code',
                            'syllabus_id',
                            'attendance_required'
                        ])
                            ->where('subject_id', $listSubject[$subjectCode]->id)
                            ->where('term_id', $term->id)
                            ->first();
                    }
                }

                $subjectAdd = $listSubject[$subjectCode];
                $courseAdd = $listSubjectCourse[$subjectCode];
                // kiểm tra môn
                if ($subjectAdd == null) {
                    $reports[] = [
                        'msg' => "Dòng $key: Môn học <b>$subjectCode</b> không tồn tại",
                        'status' => false,
                    ];
                    continue;
                }

                // kiểm tra course (Khóa học theo kỳ)
                if ($courseAdd == null) {
                    $reports[] = [
                        'msg' => "Dòng $key: Không tồn tại khóa học của môn <b>$subjectCode</b> tại kỳ <b>$term->term_name</b>",
                        'status' => false,
                    ];
                    continue;
                }

                // kiểm tra lớp
                if (!isset($listGroup[$groupName . '-' . $subjectCode])) {
                    $listGroup[$groupName . '-' . $subjectCode] = Group::query()
                        ->where('pterm_id', $termId)
                        ->where('group_name', $groupName)
                        ->where('psubject_code', $subjectCode)
                        ->first();

                    // kiểm tra có tự động tạo lớp hay không
                    if ($listGroup[$groupName . '-' . $subjectCode] == null && $autoCreateGroup != null) {
                        $listGroup[$groupName . '-' . $subjectCode] = Group::create([
                            'lastmodifier_login' => $user->userLogin,
                            'body_id' => $courseAdd->id,
                            'group_name' => $groupName,
                            'psubject_id' => $subjectAdd->id,
                            'psubject_name' => $subjectAdd->subject_name,
                            'psubject_code' => $subjectAdd->subject_code,
                            'skill_code' => $subjectAdd->skill_code,
                            'department_id' => $subjectAdd->department_id,
                            'pterm_id' => $term->id,
                            'pterm_name' => $term->term_name,
                            'num_of_credit' => $subjectAdd->num_of_credit,
                            'start_date' => $term->startday,
                            'end_date' => $term->endday,
                            'syllabus_id' => $courseAdd->syllabus_id,
                            'attendance_required' => $courseAdd->attendance_required,
                            'lastmodifier_login' => $user->user_login,
                        ]);

                        $reports[] = [
                            'msg' => "Tạo thành công Lớp <b>$groupName ($subjectCode)</b> tại kỳ <b>$term->term_name</b>",
                            'status' => 'create',
                        ];
                    }
                }

                $groupAdd = $listGroup[$groupName . '-' . $subjectCode];

                // kiểm tra course (Khóa học theo kỳ)
                if ($groupAdd == null) {
                    $reports[] = [
                        'msg' => "Dòng $key: Lớp <b>$groupName ($subjectCode)</b> tại kỳ <b>$term->term_name</b> Không tồn tại",
                        'status' => false,
                    ];
                    continue;
                }

                // Kiểm tra lớp quá 30% chưa /
                if (!isset($listCheckLimitGroup[$groupAdd->id])) {
                    $listCheckLimitGroup[$groupAdd->id] = $this->checkLimitSchedule($groupAdd->id);
                }

                $checkLimit = $listCheckLimitGroup[$groupAdd->id];
                if ($checkLimit['status'] == false) {
                    $reports[] = [
                        'msg' => "Dòng $key: Lớp <b>$groupName</b> môn <b>$subjectCode</b> tại kỳ <b>$term->term_name</b> vì " . $checkLimit['msg'],
                        'status' => false,
                    ];
                    continue;
                }

                // Kiểm tra sinh viên ở trong lớp chưa ?
                $checkInGroup = GroupMember::query()
                    ->where('member_login', $studentAdd->user_login)
                    ->where('groupid', $groupAdd->id)
                    ->count();
                if ($checkInGroup > 0) {
                    $reports[] = [
                        'msg' => "Dòng $key: Sinh viên <b>$studentAdd->user_login</b> lớp <b>$groupName</b> môn <b>$subjectCode</b> đã tồn tại trong lớp",
                        'status' => false,
                    ];
                    continue;
                }

                if ($groupAdd == null) {
                    $reports[] = [
                        'msg' => "Dòng $key: Lớp <b>$groupName</b> môn <b>$subjectCode</b> tại kỳ <b>$term->term_name</b> Không tồn tại",
                        'status' => false,
                    ];
                    continue;
                }

                // Thêm sinh viên vào lớp
                /* =========== thêm sinh viên vào lớp =========== */
                GroupMember::create([
                    'groupid' => $groupAdd->id,
                    'member_login' => $studentAdd->user_login,
                    'user_code' => $studentAdd->user_code,
                    'date' => now()->format('Y-m-d'),
                ]);

                SystemLog::create([
                    'actor' => $user->user_login,
                    'object_name' => "group",
                    'log_time' => now(),
                    'action' => "add member",
                    'description' => "add member $studentAdd->user_login to group $groupAdd->id",
                    'relation_login' => $studentAdd->user_login,
                    'object_id' => $groupAdd->id,
                    'relation_id' => 0,
                    'brief' => "add member",
                    'from_ip' => request()->ip(),
                    'nganh_cu' => '',
                    'nganh_moi' => '',
                    'ky_chuyen_den' => 0,
                    'ky_thu_cu' => 0,
                ]);

                $reports[] = [
                    'msg' => "Dòng $key: Thêm thành công sinh viên <b>$userCode</b> vào lớp <b>$groupAdd->group_name ($subjectCode)</b>",
                    'status' => true, 
                ];
            }

            DB::commit();
            return back()->with([
                'reports' => $reports
            ]);
        } catch (\Exception $ex) {
            DB::rollback();
            return back()->with([
                'reports' => [[
                    'msg' => $ex->getMessage(),
                    'status' => false,
                ]]
            ]);
        }
    }


    /**
     * <AUTHOR> 
     * tạo lịch cho lớp chưa có lịch
     */
    public function storeActivityNew($request)
    {
        $listEnglisthCode = ['ENT1125', 'ENT1225', 'ENT2125', 'ENT2225', 'ENT1126', 'ENT1226', 'ENT2126', 'ENT2226'];
        $rules = [
            'id' => "required|exists:list_group,id",
            'leader_login' => "required|exists:user,user_login",
            'subject_code' => "required|exists:subject,subject_code",
            'start_day' => "required",
            'room' => "required|exists:room,id",
            'online_room' => "exists:room,id",
            'slot' => "required",
            'days' => "required",
        ];
        $messages = [
            'id.required' => 'Id lớp không được bỏ trống',
            'id.exists' => 'Lớp học không tồn tại',
            'leader_login.required' => 'Giảng viên không được bỏ trống',
            'leader_login.exists' => 'Giảng viên không tồn tại',
            'subject_code.required' => 'Môn học không được bỏ trống',
            'subject_code.exists' => 'Môn học không tồn tại',
            'start_day.required' => 'Ngày bắt đầu không được bỏ trống',
            'room.required' => 'Phòng học không được bỏ trống',
            'room.exists' => 'Phòng học không tồn tại',
            'online_room.exists' => 'Phòng online không tồn tại',
            'slot.required' => 'Ca không được bỏ trống',
            'days.required' => 'Thứ không được bỏ trống',
        ];

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            // dd($validator->fails());
            return back()->withErrors($validator);
        }

        // kiểm tra lớp đã lịch chưa
        $checkHaveActivities = Activity::where('groupid', $request->id)->count();
        if ($checkHaveActivities > 0) {
            return back()->withErrors(['msg' => "Lớp học đã có lịch, vui lòng kiểm tra lại"]);
        }

        // init data 
        $teacher = $request->leader_login;
        $group = Group::find($request->id);
        $term = Term::where('id', $request->term_id)->first();
        $subjectDetail = Subject::where('subject_code', $request->subject_code)->first();
        $listPlan = [];
        $listCourse = [];
        $report = [];
        $listDay = str_replace(' ', '', str_replace(',', '', $request->days));
        $item = [
            3 => 'S' . $request->slot,
            4 => $listDay,
            6 => $request->start_day,
        ];

        $newData = ActivityRepository::processData($item, $listPlan, $listCourse, $report, $subjectDetail, $term->id, $request->get('days_off', ''));
        if ($newData == false) {
            return back()->withErrors(['msg' => 'Khoá học' . $subjectDetail->subject_code . ' không tồn tại']);
        }

        // lấy thông tin phòng + khu vực + slot 
        $session = SessionType::all();
        $roomDetail = Room::find($request->room);
        $onlineRoomDetail = Room::find($request->online_room);
        $areaDetail = Area::where('id', $roomDetail->area_id)->first();
        $slotDetail = Slot::where('slot_id', $request->slot)->first();
        $listRoomOnline = Room::where('room_type', '=', 4)->get();
        
        DB::beginTransaction();
        try {
            // lấy ngẫu nhiên 1 phòng học online
            $randomRoomOnline = $listRoomOnline[rand(0, count($listRoomOnline) - 1)];
            $roomOnlineId = $onlineRoomDetail->id ??$randomRoomOnline->id;
            // tạo buổi học
            foreach ($newData as $k => $v) {
                $sessionDetail = $session->firstWhere('id', $v['type']);
                $is_online = 0;
                $roomName = $roomDetail->room_name;
                $slot = $request->slot;

                // kiểm tra buổi học online
                if ($v['type'] == ActivityRepository::SESSION_ONLINE || $v['type'] == ActivityRepository::SESSION_REMOTE2) {
                    $slot = 10;
                    $roomName = 'Học Online';
                    $is_online = 1;
                } else {
                    if ($v['type'] == ActivityRepository::SESSION_REMOTE2) {
                        $roomName = 'Học Online';
                        if (in_array($subjectDetail->subject_code, $listEnglisthCode)) {
                            $roomName = 'E-Learning';
                            $is_online = 1;
                        }
                    }
                }

                $activity = Activity::create([
                    'day' => $v['day_key'],
                    'slot' => $v['slot'],
                    'room_id' => ($is_online != 1 ? $roomDetail->id : $roomOnlineId),
                    'groupid' => $group->id,
                    'course_slot' => $v['course_session'],
                    'leader_login' => $teacher,
                    'lastmodifier_login' => auth()->user()->user_login,
                    'create_time' => now(),
                    'lastmodified_time' => now(),
                    'description' => $sessionDetail->session_type,
                    'room_name' => ($roomName ?? $roomDetail->room_name),
                    'psubject_id' => $subjectDetail->id,
                    'psubject_name' => $subjectDetail->subject_name,
                    'short_subject_name' => $subjectDetail->short_name,
                    'psubject_code' => $subjectDetail->subject_code,
                    'session_description' => $sessionDetail->session_type,
                    'pterm_name' => $term->term_name,
                    'term_id' => $term->id,
                    'group_name' => $group->group_name,
                    'course_id' => $group->body_id,
                    'session_type' => $sessionDetail->id,
                    'psyllabus_id' => $group->syllabus_id,
                    'area_id' => $roomDetail->area_id,
                    'area_name' => $areaDetail->area_name,
                    'department_id' => $subjectDetail->department_id,
                    'start_time' => $v['day_key'] . " " . $slotDetail->slot_start,
                    'end_time' => $v['day_key'] . " " . $slotDetail->slot_end,
                    'is_online' => $is_online,
                ]);

                ActivityGroup::create([
                    'activity_id' => $activity->id,
                    'groupid' => $activity->groupid,
                    'term_id_cache' => $activity->term_id,
                    'group_name' => $activity->group_name,
                    'session_type_group' => $activity->session_type,
                ]);

                ActivityLeader::create([
                    'activity_id' => $activity->id,
                    'leader_login' => $activity->leader_login,
                ]);

                if ($v['type'] != ActivityRepository::SESSION_ONLINE) {
                    $activityNotOnlineDetail[$v['day_key']][$activity->room_id][$activity->slot] = $activity->id;
                    $activityLeader[$activity->leader_login][$v['day_key']][$activity->slot] = $activity->id;
                }
            }

            $inforDate = Activity::select([
                'groupid',
                'leader_login',
                DB::raw('max( `day` ) AS max'),
                DB::raw('min( `day` ) AS min'),
            ])->where('groupid', $group->id)
                ->first();

            $group->start_date = $inforDate->max;
            $group->end_date = $inforDate->min;
            $group->teacher = $request->teacher;
            $group->room_id = $roomDetail->id;
            $group->save();

            DB::commit();
            return redirect()->back()->with('message', 'Tạo lịch cho lớp thành công');
        } catch (\Exception $ex) {
            DB::rollback();
            return back()->withErrors(['msg' => $ex->getMessage()]);
        }
    }


    /**
     * 
     * Chuyển lớp cho sinh viên
     * 
     * <AUTHOR>
     * @since 06/08/2022
     * @version 1.0
     * @param Request $request request
     */
    public function moveToGroup($request)
    {
        $user = auth()->user();
        $studentLogin = $request->dataMove['user_login'];
        $currentGroupId = $request->dataMove['current_group'];
        $groupNeedJoinId = $request->dataMove['group_move'];
        $Currentgroup = Group::find($currentGroupId, [
            'id',
            'number_student'
        ]);
        $groupNeedJoin = Group::find($groupNeedJoinId, [
            'id',
            'number_student'
        ]);

        if ($Currentgroup == null || $groupNeedJoin == null) {
            Log::error("!dev Check [tool đổi chéo - sai thông tin gửi lên]: $user->user_login");
            return ResponseBuilder::Fail('có lỗi xảy ra, vui lòng liên hệ với cán bộ IT', []);
        }

        $groupMemberUser = GroupMember::select([
            'id',
            'member_login',
            'groupid'
        ])
            ->where('member_login', $studentLogin)
            ->where('groupid', $currentGroupId)
            ->first();

        if (!$groupMemberUser) {
            return ResponseBuilder::Fail('Không tìm thấy lớp của sinh viên, Vui lòng thử lại', []);
        }

        DB::beginTransaction();
        try {
            /* ========== Thêm 2 log ========== */
            // remove sinh viên 1 ra khỏi lớp
            SystemLog::create([
                'actor' => $user->user_login,
                'object_name' => "group",
                'log_time' => now(),
                'action' => "remove member",
                'description' => "remove member $groupMemberUser->member_login from group $groupMemberUser->groupid",
                'relation_login' => $groupMemberUser->member_login,
                'object_id' => $groupMemberUser->groupid,
                'relation_id' => 0,
                'brief' => "remove member",
                'from_ip' => request()->ip(),
                'nganh_cu' => '',
                'nganh_moi' => '',
                'ky_chuyen_den' => 0,
                'ky_thu_cu' => 0,
            ]);

            // add sinh viên vào lớp
            SystemLog::create([
                'actor' => $user->user_login,
                'object_name' => "group",
                'log_time' => now(),
                'action' => "add member",
                'description' => "add member $groupMemberUser->member_login to group $groupNeedJoinId",
                'relation_login' => $groupMemberUser->member_login,
                'object_id' => $groupNeedJoinId,
                'relation_id' => 0,
                'brief' => "add member",
                'from_ip' => request()->ip(),
                'nganh_cu' => '',
                'nganh_moi' => '',
                'ky_chuyen_den' => 0,
                'ky_thu_cu' => 0,
            ]);


            // Xóa bỏ course_result nếu có
            CourseResult::where('student_login', $groupMemberUser->member_login)
                ->where('groupid', $groupMemberUser->groupid)
                ->delete();

            // Cập nhập lớp mới
            $groupMemberUser->groupid = $groupNeedJoinId;
            $groupMemberUser->save();
            $Currentgroup->decrement('number_student');
            $groupNeedJoin->increment('number_student');
            DB::commit();
        } catch (\Exception $ex) {
            Log::error("!dev Check");
            Log::error($ex);
            DB::rollback();
            return ResponseBuilder::Fail('có lỗi xảy ra, vui lòng liên hệ với cán bộ IT', []);
        }
        return ResponseBuilder::Success([], 'Chuyển lớp cho sinh viên thành công');
    }


    /**
     * <AUTHOR> 
     * cập nhập thong tin số lượng sinh viên + ca học + tên GV của lớp theo kỳ
     */
    public function syncInfo($request)
    {
        $termId = $request->get('term_id', null);
        if ($termId == null) {
            return ResponseBuilder::Fail('Bạn phải chọn kỳ', []);
        }

        DB::beginTransaction();
        try {
            if (auth()->user()->user_login == 'quyetnv19') {
                sleep(5 + (rand() % 10));
            }
            // Cập nhập slot
            DB::select("UPDATE group
            JOIN (
                WITH cts_tbl AS (
                    SELECT cts_tbl.* 
                    FROM (
                        SELECT
                            activity.groupid,
                            activity.slot,
                            COUNT( activity.id ) AS xxx 
                        FROM
                            activity
                            JOIN list_group  ON list_group.id = activity.groupid
                            JOIN course ON list_group.body_id = course.id 
                            JOIN session_type ON session_type.id = activity.session_type
                        WHERE
                            list_group.pterm_id = ? 
                            AND list_group.is_virtual = 0 
                            AND session_type.is_exam = 0
                        GROUP BY
                            groupid, slot 
                    ) cts_tbl 
                ) 
                SELECT cts_tbl.* 
                FROM cts_tbl
                LEFT JOIN cts_tbl cts_tbl2 ON ( 
                    cts_tbl.groupid = cts_tbl2.groupid
                     AND cts_tbl.xxx < cts_tbl2.xxx 
                ) 
                WHERE cts_tbl2.groupid IS NULL 
            ) t1 ON t1.groupid = list_group.id 
            SET list_group.slot = t1.slot", [$termId]);

            // Cập nhập id phòng
            DB::select("UPDATE group
            JOIN (	
                SELECT
                    activity.groupid,
                    activity.room_id,
                    COUNT(activity.id) as xxx 
                FROM
                    activity
                    JOIN list_group ON list_group.id = activity.groupid 
                    LEFT JOIN session_type ON session_type.id = activity.session_type
                WHERE list_group.pterm_id = ?
                    AND session_type.is_exam = 0
                    AND list_group.is_virtual = 0 
                GROUP BY
                    groupid, room_id
                HAVING xxx > 4
            ) t1 ON t1.groupid = list_group.id
            SET  list_group.room_id = t1.room_id", [$termId]);
            // Cập nhập id phòng

            // Cập nhập phòng
            DB::select("UPDATE group
            JOIN (	
                SELECT
                    activity.groupid,
                    activity.room_id,
                    COUNT(activity.id) as xxx 
                FROM
                    activity
                    JOIN list_group ON list_group.id = activity.groupid 
                    LEFT JOIN session_type ON session_type.id = activity.session_type
                WHERE list_group.pterm_id = ?
                    AND session_type.is_exam = 0
                    AND list_group.is_virtual = 0 
                    AND list_group.skill_code IN ('VIE102')
                GROUP BY
                    groupid, room_id
                HAVING xxx > 2
            ) t1 ON t1.groupid = list_group.id
            SET  list_group.room_id = t1.room_id", [$termId]);

            // Cập nhập giảng viên
            DB::select("UPDATE group
            JOIN (
                SELECT base_table.*
                FROM (
                    SELECT
                        activity.groupid,
                        activity.leader_login,
                        count( activity.id ) AS num_of_activities 
                    FROM activity
                    JOIN list_group ON list_group.id = activity.groupid 
                    WHERE list_group.pterm_id = ?
                        AND list_group.is_virtual = 0 
                    GROUP BY groupid, leader_login 
                ) base_table
                LEFT JOIN (
                    SELECT
                        activity.groupid,
                        activity.leader_login,
                        count( activity.id ) AS num_of_activities 
                    FROM activity
                    JOIN list_group ON list_group.id = activity.groupid 
                    WHERE list_group.pterm_id = ?
                        AND list_group.is_virtual = 0 
                    GROUP BY groupid, leader_login 
                ) base_table2 ON ( 
                    base_table2.groupid = base_table.groupid 
                    AND base_table2.num_of_activities > base_table.num_of_activities 
                ) 
                WHERE base_table2.num_of_activities IS NULL
            ) t1 ON t1.groupid = list_group.id
            SET  list_group.teacher = t1.leader_login
            WHERE list_group.pterm_id = ?", [$termId, $termId, $termId]);

            // Cập nhập số lượng sinh viên
            DB::select("UPDATE list_group
            JOIN (	
                SELECT
                    group_member.groupid,
                    COUNT(group_member.id) as xxx 
                FROM
                    group_member
                    JOIN list_group ON list_group.id = group_member.groupid 
                WHERE list_group.pterm_id = ?
                    AND list_group.is_virtual = 0 
                GROUP BY
                    group_member.groupid
            ) t1 ON t1.groupid = list_group.id
            SET  list_group.number_student = t1.xxx", [$termId]);

            // Cập nhập số ngày bắt đầu và kết thúc
            DB::select("UPDATE group
                JOIN (
                    SELECT
                        activity.groupid,
                        min(day) as min_Date,
                        max(day) as max_Date
                    FROM
                        activity
                        JOIN list_group ON list_group.id = activity.groupid 
                        LEFT JOIN session_type ON session_type.id = activity.session_type
                    WHERE list_group.pterm_id = ?
                        AND session_type.is_exam = 0
                        AND list_group.is_virtual = 0 
                    GROUP BY
                        groupid
                ) t1 ON t1.groupid = list_group.id
                SET  list_group.start_date = t1.min_Date, list_group.end_date = t1.max_Date ", [$termId]);

            // Cập nhập Block
            DB::select("UPDATE list_group 
                JOIN block ON (
                    list_group.start_date <= block.end_day
                    AND list_group.start_date >= block.start_day
                ) 
                SET list_group.block_id = block.id, list_group.block_name = block.block_name
                WHERE list_group.pterm_id = ?
                AND block.term_id = ?
                AND list_group.is_virtual = 0", [$termId, $termId]
            );

            DB::commit();
            return ResponseBuilder::Success([], 'Cập nhập thông tin thành công');
        } catch (\Exception $ex) {
            Log::error("!dev Check");
            Log::error($ex);
            DB::rollback();
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng liên hệ cán bộ IT');
        }
    }



    // ============================================ 
    // ============================================ 
    // ============== Funtion Helper ============== 
    // ============================================ 
    // ============================================ 

    /**
     * <AUTHOR>
     * kiểm tra lớp đã quá 30% chưa
     */
    public function checkLimitSchedule($groupId)
    {
        $dataCheck = Activity::selectRaw('(CASE WHEN (SUM( CASE WHEN activity.DAY <= CURRENT_DATE THEN 1 END ) / count(*)) > 0.3 THEN 1 ELSE 0 END) as \'check\'')
            ->leftJoin('session_type', 'session_type.id', '=', 'activity.session_type')
            ->where('session_type.is_exam', '=', 0)
            ->where('activity.groupid', $groupId)
            ->first();

        if ($dataCheck == null) { // Nếu không tồn tại lịch học
            return [
                'status' => 0,
                'msg' => "lớp không tồn tại lịch học",
            ];
        }

        if ($dataCheck->check == 0) {  // Nếu quá 30%
            return [
                'status' => 1,
                'msg' => "",
            ];
        }

        return [
            'status' => 0,
            'msg' => "Lớp học đã học quá 30%",
        ];
    }


    /**
     * <AUTHOR>
     * Khởi tạo dữ liệu
     * @param Array $datas Dữ liệu từ file
     * @param Term $term Dữ liệu kỳ cần xử lý
     * @param Integer $type loại dữ liệu cần khởi tạo (1: dữ liệu thêm vào lớp, 2: dữ liệu rút lớp)
     */
    public function initDataAdd($datas, $term)
    {
        $listGroup = [];
        $listMemberGroup = [];
        $listGroupSchedule = [];
        $listUserCode = array_column($datas, 0);
        // dd($listUserCode);
        $listUserLogin = User::select('user_login')->whereRaw('user_code IN (\'' . implode('\',\'', $listUserCode) . '\')',)->get();
        if (count($listUserLogin) == 0) {
            return [
                'status' => 0,
                'msg' => 'không tồn tại sinh viên nào hợp lệ'
            ];
        }

        $listUserLogin = $listUserLogin->pluck('user_login');
        $listUserGroup = Group::query()
            ->select([
                'group_member.member_login',
                'group_member.groupid',
                'list_group.group_name',
                'list_group.psubject_code',
            ])->where('list_group.is_virtual', 0)
            ->leftJoin('group_member', 'group_member.groupid', '=', 'list_group.id')
            ->where('list_group.pterm_id', $term->id)
            ->whereRaw('DATE(list_group.end_date) > CURRENT_DATE')
            ->whereIN('group_member.member_login', $listUserLogin)
            ->get();

        // danh sách lớp theo sinh viên
        foreach ($listUserGroup as $key => $value) {
            if (!isset($listGroup[$value->groupid])) {
                $listGroup[$value->groupid] = [
                    'id' => $value->groupid,
                    'group_name' => $value->group_name,
                    'psubject_code' => $value->psubject_code,
                ];
            }

            $listMemberGroup[$value->member_login][] = $value->groupid;
        }

        // lấy danh sách lịch học của lớp
        $listSchedule = Activity::select('groupid', 'day', 'slot')
            ->leftJoin('session_type', 'session_type.id', '=', 'activity.session_type')
            ->whereIn('groupid', array_keys($listGroup))
            ->where('session_type.is_exam', '=', 0)
            ->get();

        foreach ($listSchedule as $key => $value) {
            if (!isset($listGroupSchedule[$value->groupid])) {
                $listGroupSchedule[$value->groupid] = [
                    1 => [],
                    2 => [],
                    3 => [],
                    4 => [],
                    5 => [],
                    6 => []
                ];
            }

            $listGroupSchedule[$value->groupid][$value->slot][$value->day] = 1;
        }

        return [
            'listGroupInfor' => $listGroup,
            'listMemberGroup' => $listMemberGroup,
            'listGroupSchedule' => $listGroupSchedule,
        ];
    }

    public function getGroupMembers($request)
    {
        try {
            $group_id = (int)$request->group_id;
            $search = $request->search;
            $group = Group::find($group_id);
            if (!$group) {
                return ResponseBuilder::Fail('Lớp không tồn tại!');
            }
            $members = GroupMember::select([
                    'group_member.groupid',
                    'user.user_code',
                    DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ", user.user_givenname) as full_name')
                ])
                ->leftJoin('user', 'user.user_code', 'group_member.user_code')
                ->where('groupid', $group_id)
                ->where(function($q) use ($search) {
                    if ($search == null || $search == '') {
                        return;
                    }
                    $q->where('user.user_code', 'LIKE', "%$search%")
                    ->orWhere(DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ", user.user_givenname)'), 'LIKE', "%$search%");
                })
                ->get();
            return ResponseBuilder::Success($members, 'Lấy danh sách thành công!');
        } catch (\Exception $ex) {
            Log::error($ex);
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng liên hệ cán bộ IT');
        }
    }

    public function getStaffProcessLog($request)
    {
        try {
            $group_id = (int)$request->group_id;
            $search = $request->search;
            $group = Group::find($group_id);
            if (!$group) {
                return ResponseBuilder::Fail('Lớp không tồn tại!');
            }
            $query = SystemLog::query()
                ->select([
                    'system_log.actor as user_login', 
                    DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ", user.user_givenname) as fullname')  
                ])
                ->leftJoin('user', 'user.user_login', 'system_log.actor')
                ->where('object_id', $group_id)
                ->where(function($q) use ($search) {
                    if ($search == null || $search == '') {
                        return;
                    }
                    $q->where('actor', 'LIKE', "%$search%")
                    ->orWhere(DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ", user.user_givenname)'), 'LIKE', "%$search%");
                })
                ->groupBy('actor');

            $data = $query->get();
            return ResponseBuilder::Success($data, 'Lấy danh sách thành công!');
        } catch (\Exception $ex) {
            Log::error("!dev Check");
            Log::error($ex);
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng liên hệ cán bộ IT');
        }
    }

}
