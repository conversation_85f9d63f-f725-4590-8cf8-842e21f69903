<?php
namespace App\Repositories;

use App\Models\Fu\User;
use App\Repositories\BaseRepository;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class PluginRepository extends BaseRepository
{

    /**
     * get model
     * @return string
     */
    public function getModel()
    {
        return User::class;
    }
    /**
     * upload file theo file_dir custom
     *
     * @param  mixed $request {file, dir}
     * @return mixed {file_name, error}
     */
    public function uploadFileToSource(Request $request)
    {
        try {
            $result = $this->uploadFileGetInfo($request->file, $request->dir, false, 'custom-editor-upload');
            if ($result == null) {
                return response([
                    'error' => true,
                    'file_name' => null,
                    'message' => 'Upload file error',
                    'url' => null
                ], 200);
            }
            return response([
                'error' => false,
                'file_name' => $result->file_name,
                'message' => 'Upload file success',
                'url' => env('AWS_URL') . "/" . "custom-editor-upload" . "/" . $result->url
            ], 200);
        } catch (\Throwable $th) {
            Log::error(`uploadFileToSource Repository error: ${$th}`);
            return response([
                'error' => true,
                'file_name' => null,
                'message' => 'Upload file error',
                'url' => null
            ], 200);
        }
    }

    /**
     * Xóa file trong thư mục theo file_dir custom
     *
     * @param  mixed $request {file}
     * @return mixed {file_name, error}
     */
    public function deleteFileFromSource(Request $request)
    {
        try {
            if (Storage::disk('custom-editor-upload')->exists($request->file)) {
                Storage::disk('custom-editor-upload')->delete($request->file);
            }
            return response([
                'error' => false,
                'file' => $request->file,
                'message' => 'Remove error'
            ], 200);
        } catch (\Throwable $th) {
            Log::error(`deleteFileFromSource Repository error: ${$th}`);
            return response([
                'error' => true,
                'file' => null,
                'message' => 'Remove error'
            ], 200);
        }
    }
}