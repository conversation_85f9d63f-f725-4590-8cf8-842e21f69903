<?php

namespace App\Repositories;

use App\Imports\BaseModelWithHeadingRowImport;
use App\Imports\DisciplineImport;
use App\Models\Dra\StudentHistory;
use App\Models\Dra\T1Permission;
use App\Models\Fu\ActionLog;
use App\Models\Fu\Term;
use App\Models\Fu\UserLevel;
use App\Models\Ho\IpWan;
use App\Models\LQueue;
use App\Models\SystemLog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Contracts\Support\Arrayable;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Exception;

abstract class BaseRepository
{
    const DA_XEP_LOP = 1;
    const CHUA_XEP_LOP = 0;
    const HUY_XEP_LOP = -1;

    const PAYMENT_WAITING = 0;
    const PAYMENT_DONE = 1;
    const id_phong_dao_tao = 78;
    const BAO_LUU = 1;
    const GIA_HAN_HOC_PHI = 3;
    const DANG_KY_HOC_LAI = 2;
    const DANG_KY_TAM_NGUNG = 1;
    const DANG_KY_CHUYEN_LOP = 4;
    const TAM_NGUNG_MON = 5;
    const DANG_KY_KHAC = 6;
    const CHUA_DAT = -3;
    const TRUOT_DIEM_DANH = -1;
    const DANG_KY_THI_LAI = 23;
    const CHUYEN_NGANH = 24;

    const action_process_service = 1;
    const action_cancel_service = 0;
    const action_change_user = 2;
    const action_change_role = 3;
    const action_update_note = 4;
    const FILE_TYPE_LIST_STUDENT_REALTIME = 'LIST_STUDENT_REALTIME';
    const FILE_TYPE_USER_UPLOAD = 'USER_UPLOAD';
    const FILE_TYPE_REMOVE_STUDENT_30 = 'remove-30-group';
    const FILE_TYPE_REMOVE_STUDENT_AP = 'remove-from-ap';
    const ROLE_ADMIN = 1;

    /**
     * @var \Illuminate\Database\Eloquent\Model
     */
    protected $_model;
    /**
     * EloquentRepository constructor.
     */
    public function __construct()
    {
        $this->setModel();
    }

    /**
     * get model
     * @return string
     */
    abstract public function getModel();

    /**
     * Set model
     */
    public function setModel()
    {
        $this->_model = app()->make(
            $this->getModel()
        );
    }

    /**
     * Get All
     * @return \Illuminate\Database\Eloquent\Collection|static[]
     */
    public function getAll()
    {
        return $this->_model::orderBy('id', 'desc')->get();
    }

    /**
     * Create
     * @param array $attributes
     * @return mixed
     */
    public function create(array $attributes)
    {
        return $this->_model->create($attributes);
    }

    /**
     * Update
     * @param $id
     * @param array $attributes
     * @return bool|mixed
     */
    public function update($id, array $attributes)
    {
        $result = $this->find($id);
        if ($result) {
            $result->update($attributes);

            return $result;
        }

        return false;
    }

    /**
     * Get one
     * @param $id
     * @return mixed
     */
    public function find($id)
    {
        $result = $this->_model->findOrfail($id);

        return $result;
    }

    /**
     * Delete
     *
     * @param $id
     * @return bool
     */
    public function delete($id)
    {
        $result = $this->find($id);
        if ($result) {
            $result->delete();

            return true;
        }

        return false;
    }

    public function responseMessage($status, $messages, $data = null)
    {
        return [
            'status' => $status,
            'messages' => trans("err_mes.$messages"),
            'data' => $data
        ];
    }

    public function getAllRole()
    {
        return UserLevel::all();
    }

    public function getAllStudyStatus()
    {
        return config('status')->trang_thai_hoc;
    }

    public function upload($dir_name = null, $file, $user_code)
    {
        return $file->storeAs("public/$dir_name", $user_code . '.jpg');
    }

    public function view($name_view, $data = [])
    {
        return view(env('THEME_ADMIN') . ".$name_view", $data);
    }

    public function mySelfCompareUserLogin($user_login)
    {
        if (Auth::user()->user_login == $user_login) {
            return true;
        }

        return false;
    }

    public function redirectWithStatus($type, $messages, $route = null)
    {
        return redirect($route ?? url()->previous())->with(['status' => ['type' => $type, 'messages' => $messages]]);
    }

    public function getAllPermission()
    {
        return T1Permission::orderBy('permission_title')->get();
    }

    public function isRoleAdmin()
    {
        return in_array(self::ROLE_ADMIN, session('roles'));
    }

    public function getTerms()
    {
        return Term::orderBy('id', 'desc')->get();
    }

    public function logDraStudentHistory($student_login, $action_name, $description, $student_code = '', $term_id = 0, $old_status = 0, $current_status = 0, $actor = null)
    {
        $actor = $actor ?? Auth::user()->user_login;
        return StudentHistory::create([
            'student_login' => $student_login,
            'action_name' => $action_name,
            'date_taken' => now(),
            'actor' => $actor,
            'description' => $description,
            'old_status' => $old_status,
            'current_status' => $current_status,
            'student_code' => $student_code,
            'term_id' => $term_id,
            'cusfield1' => '',
        ]);
    }

    public function uploadFile($dir_name = null, $file, $file_name)
    {
        return $file->storeAs("public/$dir_name", $file_name);
    }

    public function checkTypeFile($type)
    {
        switch ($type) {
            case 'pdf':
                return 'pdf';
            case 'jpg' || 'png' || 'jpeg' || 'gif':
                return 'images';
            default:
                return 'none';
        }
    }

    /**
     * base function lưu file
     *
     * @param  file $file
     * @param  string $dir
     * @return mixed {type, file_name, original_name}
     */
    public function uploadFileGetInfo($file, $dir, $date_path = false, $driver = null, $_file_name = null)
    {
        $original_name = $file->getClientOriginalName();
        $extension = $file->getClientOriginalExtension();
        if (!in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'zip', 'rar'])) {
            return null;
        }
        $date = now()->format('Y/' . 'm-d');
        $file_name = auth()->user()->user_login . '_' . uniqid() . '.' . $extension;
        if ($_file_name) {
            $file_name = $_file_name;
        }
        if ($date_path) {
            $date = now()->format('Y/' . 'm-d');
            $file_name = "$date" . DIRECTORY_SEPARATOR . $file_name;
        }
        $url = '';
        if ($driver) {
            if (Storage::disk($driver)->putFileAs($dir, $file, $file_name, 'public')) {
                $url = $dir . DIRECTORY_SEPARATOR . $file_name;
            } else {
                return null;
            }
        } else {
            $file->storeAs("public/$dir", $file_name);
            $url = "storage/$dir/$file_name";
        }
        $type = $this->checkTypeFile($extension);

        return (object) [
            'type' => $type,
            'original_name' => $original_name,
            'file_name' => $file_name,
            'url' => $url
        ];
    }

    public function systemLog($object_name, $action, $description, $relation_login, $object_id = 0, $relation_id = 0, $nganh_cu = '', $nganh_moi = '', $brief = '', $ky_chuyen_den = 0, $ky_thu_cu = 0,$staff_user_login = null)
    {
        $actor = $staff_user_login ?? Auth::user()->user_login;
        return SystemLog::create([
            'actor' => $actor,
            'object_name' => $object_name,
            'log_time' => now(),
            'action' => $action,
            'description' => $description,
            'relation_login' => $relation_login,
            'object_id' => $object_id,
            'relation_id' => $relation_id,
            'brief' => $brief,
            'from_ip' => request()->ip(),
            'nganh_cu' => $nganh_cu,
            'nganh_moi' => $nganh_moi,
            'ky_chuyen_den' => $ky_chuyen_den,
            'ky_thu_cu' => $ky_thu_cu,
        ]);
    }

    public function getTerm()
    {
        $terms = Term::orderBy('id', 'desc')->get();
        $today = now();
        $now_term = 0;
        if ($terms) {
            foreach ($terms as $term) {
                if ($today->greaterThanOrEqualTo($term->startday) && $today->lessThan($term->endday)) {
                    $now_term = $term->id;
                    break;
                }
            }
        }

        return $now_term;
    }

    public function createQueue($title, $description, $target = null)
    {
        return LQueue::create([
            'title' => $title,
            'description' => $description,
            'target' => $target,
            'created_by' => Auth::user()->user_login
        ]);
    }

    public function updateQueue($id, $target = null)
    {
        return LQueue::find($id)->update([
            'target' => $target,
            'status' => 1
        ]);
    }

    public function res($status = null, $messages = null, $data = null)
    {
        return [
            'status' => $status,
            'messages' => $messages,
            'data' => $data,
        ];
    }

    public function importDataFromFile($file)
    {
        $data = Excel::toArray(new DisciplineImport(), $file)[0];
        if (!count($data)) {
            return false;
        }
        unset($data[0]);
        $data = array_filter($data, function ($item) {
            return $item[0] !== null;
        });

        // Trim kỹ hơn với preg_replace
        foreach ($data as $key => $item) {
            $data[$key] = array_map(function ($value) {
                // Xóa mọi ký tự trắng Unicode ở đầu và cuối chuỗi
                return preg_replace('/^[\p{Z}\s\xA0]+|[\p{Z}\s\xA0]+$/u', '', $value);
            }, $item);
        }
        return $data;
    }

    /**
     * convert dữ liệu từ file excel thành mảng
     *
     * @param  mixed $file
     * @return array [keys - danh sách key, data - dữ liệu từ file excel]
     */
    public function fileToArrayData($file)
    {
        try {
            $theCollection = Excel::toArray(new BaseModelWithHeadingRowImport, $file);
            if (count($theCollection)) {
                $theCollection = $theCollection[0];
                // convert từ array sang collection
                $data = [];
                foreach ($theCollection as $row) {
                    if ($row['ma_sinh_vien']) {
                        $data[] = (array) $row;
                    }
                }
                return $data;
            } else {
                return [];
            }
        } catch (\Throwable $th) {
            Log::error('Controller@fileToArrayData: ' . $th->getLine() . ' - ' . $th->getMessage());
            throw new \Exception('Controller@fileToArrayData: ' . $th->getLine() . ' - ' . $th->getMessage());
            return [];
        }
    }

    public function checkIp()
    {
        $your_ip = request()->ip();
        if ($your_ip == '127.0.0.1') {
            return true;
        }
        $check = IpWan::where('ip_wan', $your_ip)->first();
        if ($check) {
            return true;
        }

        return false;
    }

    protected function setUnlimitedExecutionQuery()
    {
        ini_set("memory_limit", "-1");
        ini_set('max_execution_time', 360000);
    }
    
    public function getDay($day) {
        $daysInVietnamese = array(
            'Monday' => 'Thứ Hai',
            'Tuesday' => 'Thứ Ba',
            'Wednesday' => 'Thứ Tư',
            'Thursday' => 'Thứ Năm',
            'Friday' => 'Thứ Sáu',
            'Saturday' => 'Thứ Bảy',
            'Sunday' => 'Chủ Nhật'
        );
    
        return $daysInVietnamese[$day];
    }

    public function actionLog($object, $auth, $action, $description="", $object_id , $data_changed, $ip = null) {
        try {
            $auth = $auth ?? Auth::user()->user_login;
            $ip = $ip ?? request()->ip();
            ActionLog::create([
                'object'        => $object,
                'auth'          => $auth,
                'action'        => $action,
                'description'   => $description,
                'object_id'     => $object_id,
                'data_changed'  => $data_changed,
                'ip'            => $ip,
            ]);
            return true;
        } catch (Exception $e) {
            Log::error($e);
            return false;
        }

    }
        /**
     * Cập nhập actionlog
     * 
     * <AUTHOR>
     * @param Array $oldData Dữ liệu trước khi cập nhập
     * @param Array $newData Dữ liệu sau khi cập nhập
     * @param Array $dataCreate Dữ liệu để tạo ActionLog như object, auth, ...
     * @param int $typeSave 1: lưu kiểu string, 2 là lưu kiểu json
     */

     public function createActionLog($oldData = [], $newData = [], $dataCreate, $typeSave = 1) {
        if (count($oldData) == 0) {
            ActionLog::create([
                'object'        => ($dataCreate['object'] ?? ""),
                'auth'          => ($dataCreate['auth'] ?? auth()->user()->user_login),
                'action'        => ($dataCreate['action'] ?? ""),
                'description'   => ($dataCreate['description'] ?? ""),
                'object_id'     => ($dataCreate['object_id'] ?? ""),
                'data_changed'  => '',
                'ip'            => ($dataCreate['ip'] ?? ""),
            ]);
        } else {
            $strUpdate = '';
            $changeData = [];
            if ($typeSave == 1) {
                if (serialize($oldData) != serialize($newData)) {
                    foreach ($newData as $keyChange => $valuChange) {
                        if ($oldData[$keyChange] != $newData[$keyChange]) {
                            $changeData[] = "{ $keyChange: từ (" . $oldData[$keyChange] . ') thành (' . $newData[$keyChange] . ') }';
                        }
                    }
        
                    $strUpdate = implode(', ', $changeData);
                }
            } elseif ($typeSave == 2) {
                if (serialize($oldData) != serialize($newData)) {
                    foreach ($newData as $keyChange => $valuChange) {
                        if ($oldData[$keyChange] != $newData[$keyChange]) {
                            $changeData[$keyChange] = [
                                'old' => $oldData[$keyChange],
                                'new' => $newData[$keyChange],
                            ];
                        }
                    }
    
                    $strUpdate = json_encode($changeData);
                }
            }
    
            if (count($changeData) > 0) {
                ActionLog::create([
                    'object'        => ($dataCreate['object'] ?? ""),
                    'auth'          => ($dataCreate['auth'] ?? auth()->user()->user_login),
                    'action'        => ($dataCreate['action'] ?? ""),
                    'description'   => ($dataCreate['description'] ?? ""),
                    'object_id'     => ($dataCreate['object_id'] ?? ""),
                    'data_changed'  => $strUpdate,
                    'ip'            => ($dataCreate['ip'] ?? ""),
                ]);
            }
        }

    }
        /**
     * hàm xuất excel nhanh
     * 
     * <AUTHOR>
     */
    public function fastExport (string $fileName, Collection $collection, string $writerType = null, $withHeadings = false, $customStyles = null,  array $responseHeaders = [],$noDownload = false) {
        $export = new class($collection, $withHeadings, $customStyles) implements FromCollection, WithHeadings, WithStyles, ShouldAutoSize {
            use Exportable;

            private $withHeadings;
            private $collection;
            private $customStyles;
            public function __construct(Collection $collection, $withHeading = false, $customStyles = null) {
                $this->collection   = $collection->toBase();
                $this->withHeadings = $withHeading;
                $this->customStyles = $customStyles;
                
            }

            public function collection() {
                return $this->collection;
            }

            public function styles(Worksheet $sheet) {
                $sheet->getStyle($this->customStyles['header'] ?? "A1:C1")->getFont()->setBold(true);
                $sheet->getStyle($this->customStyles['form'] ?? "A1:C1")->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                        ],
                    ],
                ]);
                $sheet->getStyle($this->customStyles['form'] ?? "A1:C1")->getAlignment()->setWrapText(true);
            }

            public function headings(): array {
                if (!$this->withHeadings) {
                    return [];
                }
                
                if (is_array($this->withHeadings)) {
                    return $this->withHeadings;
                }

                $firstRow = $this->collection->first();

                if ($firstRow instanceof Arrayable || \is_object($firstRow)) {
                    return array_keys(\Maatwebsite\Excel\Sheet::mapArraybleRow($firstRow));
                }

                return $this->collection->collapse()->keys()->all();
            }
        };

        if ($noDownload == true) {
            return $export;
        }

        return $export->download($fileName, $writerType, $responseHeaders);
    }

}
