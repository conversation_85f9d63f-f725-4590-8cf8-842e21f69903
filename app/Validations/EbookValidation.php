<?php

namespace App\Validations;

class EbookValidation extends Validation
{

    /**
    * validate parameters
    */
    public function getValidator()
    {
        $this->rules = [
            'student_code' => 'required|string|min:3',
            'subject_code' => 'required|string|min:3',
            'code'         => 'required|string|min:3',
            'url'          => 'required|string|min:3',
        ];

        $this->messages = [
            'student_code.required' => 'Mã sinh viên không được để trống!',
            'subject_code.required' => 'Mã môn không được để trống!',
            'code.required'         => 'Mã code không được để trống!',
            'url.required'          => 'Mã url không được để trống!',
        ];

        return $this;
    }

    /**
    * validate upload File
    */
    public function uploadExcelFile()
    {
        $this->rules = [
            'file' => 'required|file|mimes:xlsx'
        ];

        $this->messages = [
            'file.required' => 'Vui lòng import file!',
            'file.file' => 'File không hợp lệ!',
            'file.mimes' => 'File không đúng định dạng!'
        ];

        return $this;
    }

    public function importExcelFile()
    {

        $this->rules = [
            'data' => 'required|array',
            'data.*.ma_sinh_vien' => 'required|string',
            'data.*.id_lop' => 'string',
            'data.*.ma_mon' => 'string',
            'data.*.ma_code' => 'string',
            'data.*.link_truy_cap' => 'required|string',
            'data.*.han_su_dung' => 'numeric',
            'data.*.ten_sach' => 'string',
            'data.*.ghi_chu' => 'string',
            'data.*.*' => 'not_regex:/=/'
        ];

        $this->messages = [
            'data.required' => 'Dữ liệu không được để trống!',
            'data.array' => 'Dữ liệu không hợp lệ!',
            'data.*.ma_sinh_vien.required' => 'Mã sinh viên không được để trống!',
            'data.*.ma_sinh_vien.string' => 'Mã sinh viên không hợp lệ!',
            'data.*.id_lop.string' => 'Mã lớp không hợp lệ!',
            'data.*.ma_mon.string' => 'Mã môn không hợp lệ!',
            'data.*.ma_code.string' => 'Mã code không hợp lệ!',
            'data.*.link_truy_cap.required' => 'Link truy cập không được để trống!',
            'data.*.link_truy_cap.string' => 'Link truy cập không hợp lệ!',
            'data.*.han_su_dung.numeric' => 'Hạn sử dụng phải là số!',
            'data.*.ten_sach.string' => 'Tên sách không hợp lệ!',
            'data.*.ghi_chu.string' => 'Ghi chú không hợp lệ!',
            'data.*.*.not_regex' => 'File import không được phép chứa hàm Excel, vui lòng kiểm tra lại.'
        ];
        return $this;
    }

    /**
     * Specify laravel style rules
     *
     * @return array
     */
    public function rules()
    {
        return count($this->rules) ? $this->rules : [];
    }
}
