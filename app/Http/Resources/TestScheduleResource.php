<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;

class TestScheduleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            "date" => $this->toDayOfWeek($this->day),
            "room_name" => $this->room_name,
            "area_name" => $this->area_name,
            "subject_name" => $this->psubject_name,
            "class_name" => $this->group_name,
            "leader_name" => $this->leader_login,
            "slot_and_time" => "Ca $this->slot (từ $this->slot_start đến $this->slot_end)",
            "type" => ($this->is_online == 0) ? "Offline" : "Online",
            "content" => $this->noi_dung,
            "duty_of_student" => $this->nv_sinh_vien,
            "document_of_subject" => $this->hoc_lieu_mon,
            "refer_documents" => $this->nv_giang_vien
        ];
    }

    private function toDayOfWeek(string $date)
    {
        $carbonDate = Carbon::createFromFormat('Y-m-d', date('Y-m-d', strtotime($date)));
        return ($carbonDate->dayOfWeek == 8)
            ? "Chủ nhật, ngày " . date('d/m/Y', strtotime($date))
            : "Thứ " . ($carbonDate->dayOfWeek + 1) . ", ngày " . date('d/m/Y', strtotime($date));
    }
}
