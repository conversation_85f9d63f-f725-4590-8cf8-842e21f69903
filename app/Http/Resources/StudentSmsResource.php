<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\Fu\Term;
use Carbon\Carbon;

class StudentSmsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        try {
            $active = [
                "value" => 1,
                "text" => "Đã kích hoat",
                "style" => "success"
            ];

            $now = Carbon::now()->format('Y-m-d');
            $term = Term::where('startday', '<=', $now)->orderBy('startday', 'DESC')->first(); //$database->select("select * from ap_hn.term order by startday DESC;")[0];
            if ($this->created_on === '0000-00-00 00:00:00' || $this->is_active === 0) {
                $active = [
                    "value" => 0,
                    "text" => "Vô hiệu hóa",
                    "style" => "danger"
                ];
                return [
                    "sms_table_id" => $this->id,
                    "phone" => $this->phone,
                    "student_login" => $this->student_login,
                    "student_code" => $this->student_code,
                    "student_name" => $this->student_name,
                    "owner_name" => $this->owner_name,
                    "owner_type" => $this->owner_type,
                    "is_active" => $active,
                    "created_on" => $this->created_on,
                ];
            }
            $createdPhoneDate = Carbon::createFromFormat('Y-m-d', date('Y-m-d', strtotime($this->created_on)));
            $termStartDate = Carbon::createFromFormat('Y-m-d', date('Y-m-d', strtotime($term->startday)));
            if ($createdPhoneDate->lt($termStartDate)) {
                $active = [
                    "value" => 0,
                    "text" => "Vô hiệu hóa",
                    "style" => "danger"
                ];
            }

            return [
                "sms_table_id" => $this->id,
                "phone" => $this->phone,
                "student_login" => $this->student_login,
                "student_code" => $this->student_code,
                "student_name" => $this->student_name,
                "owner_name" => $this->owner_name,
                "owner_type" => $this->owner_type,
                "is_active" => $active,
                "created_on" => $this->created_on,
            ];
        } catch (\Throwable $th) {
            error_log($th);
        }
    }
}
