<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class StudentSmsRole extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'user_login'=> $this->user_login,
            'import' => $this['import'] ?? 0,
            'tao_mau_tin_nhan'=> $this['tao-mau-tin-nhan'] ?? 0,
            'duyet_tin_nhan_lv1'=> $this['duyet-tin-nhan-lv1']?? 0,
            'gui_tin_nhan'=> $this['gui-tin-nhan'] ?? 0, 
            'duyet_tin_nhan_lv2'=> $this['duyet-tin-nhan-lv2'] ?? 0,
            'duyet_tin_cham_soc'=> $this['duyet-tin-cham-soc'] ?? 0,
        ];
    }
}
