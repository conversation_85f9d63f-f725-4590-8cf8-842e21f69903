<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class DisciplineRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'file' => 'required|mimes:pdf',
            'users' => 'required|mimes:xlsx',
            'decision_no' => 'required|alpha_num',
            'signee' => 'required',
            'date_of_decision' => 'required',
            'decision_name' => 'required',
            'date_affected' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'file.required' => 'Bạn chưa tải lên file quyết định',
            'decision_name.required' => 'Bạn chưa điền tên quyết định',
            'file.mimes' => 'File quyết định phải là định dạng pdf',
            'users.mimes' => 'File dữ liệu phải là định dạng xlsx',
            'users.required' => 'Bạn chưa tải lên file dữ liệu sinh viên',
            'decision_no.required' => 'Bạn chưa nhập số quyết định',
            'signee.required' => 'Bạn chưa chọn người ký',
            'date_of_decision.required' => 'Bạn chưa chọn ngày ký',
            'date_affected.required' => 'Bạn chưa chọn ngày có hiệu lực',
        ];
    }
}
