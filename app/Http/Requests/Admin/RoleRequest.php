<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class RoleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'role_name' => 'required',
            'role_description' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'role_name.required' => 'Tên không được để trống',
            'role_description.required' => 'Chú thích không được để trống',
        ];
    }
}
