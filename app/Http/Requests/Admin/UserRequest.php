<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'user_code' => [
                'required',
                Rule::unique('user')->ignore($this->id),
            ],
            'user_DOB' => [
                'required',
                'date',
                'before_or_equal:today',
            ],
            'user_email' => [
                'required',
                'email',
                Rule::unique('user')->ignore($this->id),
            ],
            'profile_avatar' => [
                'dimensions:width=120,height=160',
                'image',
            ],
        ];
    }

    public function messages()
    {
        return [
            'user_code.required' => 'Mã tài khoản không được để trống',
            'user_code.unique' => 'Mã tài khoản đã tồn tại',
            'user_DOB.required' => 'Ngày sinh không được để trống',
            'user_DOB.date' => 'Ngày sinh phải là ngày',
            'user_DOB.before_or_equal' => 'Ngày sinh phải nhỏ hơn hoặc bằng ngày hiện tại',
            'user_email.required' => 'Email không được để trống',
            'user_email.email' => 'Không đúng định dạng email',
            'user_email.unique' => 'Email đã tồn tại',
            'profile_avatar.dimensions' => 'Kích thước ảnh không đúng 120 x 160',
            'profile_avatar.image' => 'Tệp tin tải lên phải là ảnh jpeg, png, bmp, gif, svg, or webp',
        ];
    }
}
