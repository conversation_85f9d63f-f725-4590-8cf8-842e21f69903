<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class DropOutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'file' => 'mimes:pdf',
            'users' => 'required|mimes:xlsx',
            'date_affected' => 'required',
            'term_id' => 'required',
            'type' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'file.mimes' => 'Chỉ được phép tải lên .pdf',
            'users.mimes' => 'File dữ liệu phải là định dạng xlsx',
            'users.required' => 'Bạn chưa tải lên file dữ liệu sinh viên',
            'date_affected.required' => 'Bạn chưa chọn ngày có hiệu lực',
            'term_id.required' => 'Bạn chưa chọn học kỳ',
            'type.required' => 'Bạn chưa chọn loại quyết định',
        ];
    }
}
