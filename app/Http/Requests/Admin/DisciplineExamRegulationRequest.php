<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class DisciplineExamRegulationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'users' => 'required|mimes:xlsx',
            'decision_name' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'decision_name.required' => 'Bạn chưa điền tên quyết định',
            'users.mimes' => 'File dữ liệu phải là định dạng xlsx',
            'users.required' => 'Bạn chưa tải lên file dữ liệu sinh viên',
        ];
    }
}
