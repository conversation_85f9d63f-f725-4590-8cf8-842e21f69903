<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Route;

class CurriculumAddRequest extends FormRequest
{
     /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        switch (Route::getCurrentRoute()->getActionMethod()) {
            case 'curriculumAdd':
                return $this->getCustomRule();
            default:
                return [];
        }
    }

    public function getCustomRule()
    {
        if (Route::getCurrentRoute()->getActionMethod() == 'curriculumAdd') {
            return [
                "name" => 'string|required',
                "number_of_period" => 'integer|required',
                "description" => 'string|nullable',
                "program_id" => 'integer|required',
                "mod_of_study" => 'integer|required',
                "number_of_credit" => 'integer|required',
                "number_of_subject" => 'integer|required',
                "status" => 'integer|required',
                "number_compulsory_credit" => 'integer|required',
                "number_compulsory_subject" => 'integer|required',
                "compulsory_subject_list" => 'integer|required',
                "number_optional_credit"  => 'integer|required',
                "number_optional_subject" => 'integer|required',
                "optional_subject_list",
                "branch_object_id" => 'integer|required',
                "branch_object_code" => 'string|required',
                "branch_table_name" => 'string|required',
                "brand_code" => 'string|required',
                "khoa" => 'string|required',
                "nganh" => 'string|required',
                "chuyen_nganh" => 'string|required',
                "noi_dung" => 'string|required',
                "nganh_in_bang" => 'string|required',
                "nganh_in_bang_en" => 'string|required',
            ];
        }
    }

    public function messages()
    {
        return [
            'required' => ':attribute là bắt buộc',
            'exists' => ':attribute không tồn tại',
        ];
    }
}
