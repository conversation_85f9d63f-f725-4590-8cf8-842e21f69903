<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PermissionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'permission_title' => 'required',
            'permission_name' => [
                'required',
                Rule::unique('permission')->ignore($this->id),
            ],
            'permission_description' => 'required',
            'route_name' => [
                'required',
                Rule::unique('permission')->ignore($this->id),
            ],
        ];
    }

    public function messages()
    {
        return [
            'permission_title.required' => 'Tiêu đề không được bỏ trống',
            'permission_name.required' => 'Tên không được bỏ trống',
            'permission_name.unique' => 'Tên đã tồn tại',
            'permission_description.required' => 'Tóm tắt không được bỏ trống',
            'route_name.required' => 'Tên route không được bỏ trống',
            'route_name.unique' => 'Tên route đã tồn tại',
        ];
    }
}
