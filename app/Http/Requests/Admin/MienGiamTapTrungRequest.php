<?php

namespace App\Http\Requests\Admin;

use App\Models\Fu\User;
use Illuminate\Foundation\Http\FormRequest;

class MienGiamTapTrungRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'user_login' => ['required' , "exists:user,user_login"],
            'subject_code' => 'required',
            'subject_code_new' => 'required_if:type,1',
            // 'so_quyet_dinh' => 'required',
            'type' => 'required'
        ];
    }

    public function messages()
    {
        return [
            'user_login.required' => 'Tài khoản đăng nhập không được bỏ trống',
            'subject_code.required' => 'Mã môn không được để trống',
            'subject_code_new.required_if' => 'Mã môn mới không được bỏ trống',
            // 'so_quyet_dinh.required' => '<PERSON><PERSON> quyết định không được bỏ trống',
            'user_login.exists' => "Không tồn tại mã sinh viên này"
        ];
    }
}
