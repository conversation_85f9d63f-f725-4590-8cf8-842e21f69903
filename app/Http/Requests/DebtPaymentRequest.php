<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Fee\StudentDebt;
use App\Models\Fee\StudentWallet;

class DebtPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $debtId = $this->route('id');
        $debt = StudentDebt::find($debtId);
        
        if (!$debt) {
            return [];
        }

        $remainingAmount = $debt->amount - $debt->paid_amount;

        return [
            'payment_type' => 'required|in:wallet,direct',
            'amount' => [
                'required',
                'numeric',
                'min:1000', // Minimum 1,000 VND
                'max:' . $remainingAmount
            ],
            'payment_method' => 'required_if:payment_type,direct|in:cash,bank_transfer,card,other,wallet',
            'transaction_id' => 'nullable|string|max:255',
            'invoice_id' => 'nullable|string|max:255',
            'note' => 'nullable|string|max:1000'
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'payment_type.required' => 'Vui lòng chọn phương thức thanh toán',
            'payment_type.in' => 'Phương thức thanh toán không hợp lệ',
            'amount.required' => 'Vui lòng nhập số tiền thanh toán',
            'amount.numeric' => 'Số tiền phải là số',
            'amount.min' => 'Số tiền thanh toán tối thiểu là 1,000 VNĐ',
            'amount.max' => 'Số tiền thanh toán không được vượt quá số tiền còn lại',
            'payment_method.required_if' => 'Vui lòng chọn hình thức thanh toán',
            'payment_method.in' => 'Hình thức thanh toán không hợp lệ',
            'transaction_id.max' => 'Mã giao dịch không được quá 255 ký tự',
            'invoice_id.max' => 'Mã hóa đơn không được quá 255 ký tự',
            'note.max' => 'Ghi chú không được quá 1000 ký tự'
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $debtId = $this->route('id');
            $debt = StudentDebt::find($debtId);
            
            if (!$debt) {
                $validator->errors()->add('debt', 'Công nợ không tồn tại');
                return;
            }

            // Check debt status
            if (!$debt->canBePaid()) {
                $validator->errors()->add('debt', 'Công nợ này không thể thanh toán (trạng thái: ' . $debt->status_name . ')');
                return;
            }

            // Additional validation for wallet payment
            if ($this->payment_type === 'wallet') {
                $wallet = StudentWallet::where('user_code', $debt->user_code)->first();
                
                if (!$wallet) {
                    $validator->errors()->add('payment_type', 'Sinh viên chưa có ví điện tử');
                    return;
                }
                
                if ($wallet->is_locked) {
                    $validator->errors()->add('payment_type', 'Ví sinh viên đã bị khóa: ' . ($wallet->lock_reason ?? 'Không rõ lý do'));
                    return;
                }
                
                if ($wallet->balance < $this->amount) {
                    $validator->errors()->add('amount', 'Số dư ví không đủ. Số dư hiện tại: ' . number_format($wallet->balance, 0, ',', '.') . ' VNĐ');
                    return;
                }
            }

            // Validate amount precision (should be whole number for VND)
            if ($this->amount && fmod($this->amount, 1) !== 0.0) {
                $validator->errors()->add('amount', 'Số tiền phải là số nguyên (không có phần thập phân)');
            }
        });
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'payment_type' => 'phương thức thanh toán',
            'amount' => 'số tiền',
            'payment_method' => 'hình thức thanh toán',
            'transaction_id' => 'mã giao dịch',
            'invoice_id' => 'mã hóa đơn',
            'note' => 'ghi chú'
        ];
    }
}
