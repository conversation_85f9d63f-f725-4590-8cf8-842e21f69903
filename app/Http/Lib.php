<?php

namespace App\Http;

use App\Models\Fu\Term;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class Lib
{
    public static function res($status = null, $messages = null, $data = null)
    {
        return [
            'status' => $status,
            'messages' => $messages,
            'data' => $data,
        ];
    }

    public static function upload($dir_name = null, $file, $user_code)
    {
        return $file->storeAs("public/$dir_name", $user_code . '.jpg');
    }

    public static function loadPermission($user)
    {
        $roles = $user->roles;
        $array = [];
        $array_roles = [];
        foreach ($roles as $role) {
            $array_roles[] = $role->role_id;
            $permissions = $role->permissions;
            foreach ($permissions as $permission) {
                $array[$permission->permission_id] = $permission->permission_id;
            }
        }
        return session([
            'permissions' => $array,
            'roles' => $array_roles,
        ]);
    }

    public static function loadSession()
    {
        session(['color' => 'light']);
    }

    public static function log($status = null, $messages = null)
    {
        Log::{$status}($messages . '[id: ' . Auth::id() . ' , user_login: ' . Auth::user()->user_login) . ']';
    }

    public static function logDiscord($status = null, $messages = null)
    {
        $ip = \Illuminate\Support\Facades\Request::ip();
        $status = strtoupper($status);
        $client = new Client();
        $client->post(env('LOG_DISCORD_WEBHOOK_URL'), [
            'json' => [
                'content' => "[$status]" . $messages . ' [id: ' . Auth::id() . ' , user_login: ' . (Auth::user()->user_login ?? 'System') . ', date: ' . now() . ', ip: ' . $ip . ']',
            ]
        ]);
    }

    public static function getTermDetails()
    {
        $terms = Term::orderBy('id', 'desc')->get();
        $today = now();
        $now_term = ['term_name' => null, 'term_id' => null];
        if ($terms) {
            foreach ($terms as $term) {
                if ($today->greaterThanOrEqualTo($term->startday) && $today->lessThan($term->endday)) {
                    $now_term['term_name'] = $term->term_name;
                    $now_term['term_id'] = $term->id;
                    break;
                }
            }
        }
        if ($now_term['term_name'] === null) {
            $now_term['term_name'] = $terms->first()->term_name;
            $now_term['term_id'] = $terms->first()->id;
        }

        return $now_term;
    }
}
