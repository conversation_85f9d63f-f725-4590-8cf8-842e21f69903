<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckCampus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $listCampus = [
            "ho"
        ];
        try {
            if (!in_array($request->session()->has('campus_db'), $listCampus)) {
                return redirect('/');
            }
        } catch (\Throwable $th) {
            return redirect('/');
        }
        return $next($request);
    }
}
