<?php

namespace App\Http\Middleware;

use App\Models\Dra\T1Permission;
use App\Models\Dra\T1RolePermission;
use App\Models\Fu\User;
use Closure;
use Illuminate\Http\Request;

    /**
     * Check permission for accessing
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
/**
 * check permission for accessing
 */
class Permission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (!auth()->check()) {
            return  redirect()->route('login');
        } else {
            $roles = session('roles');
            // lấy quyền theo round
            $currentPermision = T1Permission::with('roles')->where('permission.route_name', '=', $request->route()->getName())->first();
            if(!$currentPermision) {
                return $next($request);
            }
            // kiểm tra xem user có quyền không
            $checkPermision = T1RolePermission::whereIn('role_id', $roles)->where('permission_id', $currentPermision->id)->count();
            if (in_array(User::ROLE_ADMIN, $roles) || $checkPermision > 0) {
                return $next($request);
            } else {
                $permission = T1Permission::where('permission.route_name', '=', $request->route()->getName())->first();
                $description = "";
                if(isset($permission->permission_description) && $permission->permission_description!== null){
                    $description = $permission->permission_description;
                }
                return redirect()->route('no_permission', ['url' =>  $request->url(), 'name' => mb_strtolower($description,'UTF-8')]);
            }
        }
    }
}
