<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class IpMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $ip = $request->ip();
        $accept_ip = ['127.0.0.1','*************'];
        if (in_array($ip, $accept_ip)) {
            return $next($request);
        }

        return abort(401);
    }
}
