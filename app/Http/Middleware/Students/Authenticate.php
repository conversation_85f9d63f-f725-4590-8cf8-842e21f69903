<?php

namespace App\Http\Middleware\Students;

use App\Http\Lib;
use App\Models\Fu\User;
use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Gate;

class Authenticate
{
    const TEACHER = 2;
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!auth()->check()) {
            if ($request->token) {
                $this->decryptToken($request->token);

            } else {
                session(['url' =>  $request->url()]);
                return redirect()->route('home');
            }
        } else {
            if ($request->token) {
                $this->decryptToken($request->token);
            }
        }
        if (auth()->user()->user_level !=  3 ) { //&& auth()->user()->user_level !=  2
            return $next($request);
        }
        if (Gate::denies($request->route()->getName()) && $request->route()->getName() != 'admin.profile') {
            if (auth()->user()->user_level == 1 /*self::TEACHER*/) {
                return $next($request);
            }
            return abort(401);
        }

        return $next($request);
    }

    function decryptToken($token) {
        $decrypt = $this->hash($token, 'd');
        $decrypt = explode(':', $decrypt);
        $user = $decrypt[0];
        $expire = $decrypt[2];

        if (now()->timestamp > $expire) {
            return abort(404, 'Token expire');
        }
        if (auth()->check()) {
            $user = User::where('user_login', $user)->firstOrFail();
            Auth::loginUsingId($user->id);
            Lib::loadPermission(Auth::user());
            Lib::loadSession();
        } else {
            $user = User::where('user_login', $user)->firstOrFail();
            Auth::loginUsingId($user->id);
            Lib::loadPermission(Auth::user());
            Lib::loadSession();
        }

        return 'Login success';
    }

    function hash( $string, $action = 'e' ) {
        // you may change these values to your own
        $secret_key = '';
        $secret_iv = 'my_simple_secret_iv';

        $output = false;
        $encrypt_method = "AES-256-CBC";
        $key = hash( 'sha256', $secret_key );
        $iv = substr( hash( 'sha256', $secret_iv ), 0, 16 );

        if( $action == 'e' ) {
            $output = base64_encode( openssl_encrypt( $string, $encrypt_method, $key, 0, $iv ) );
        }
        else if( $action == 'd' ){
            $output = openssl_decrypt( base64_decode( $string ), $encrypt_method, $key, 0, $iv );
        }

        return $output;
    }
}
