<?php

namespace App\Http\Middleware;

use App\Models\Dra\T1Permission;
use App\Models\Dra\T1RolePermission;
use App\Models\Fu\User;
use Closure;
use Illuminate\Http\Request;
use App\Utils\ResponseBuilder;

/**
 * check permission for accessing
 */
class ApiPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (!auth()->check()) {
            return  redirect()->route('login');
        } else {
            try {
                //code...
            } catch (\Throwable $th) {
                //throw $th;
            }
            $roles = session('roles');
            $permission = T1Permission::where('permission.route_name', '=', $request->route()->getName())->first();
            // lấy quyền theo round
            $currentPermision = T1Permission::with('roles')
            ->where('permission.route_name', '=', $request->route()->getName())
            ->first();
            if(!$currentPermision){
                return $next($request);
            }
            // kiểm tra xem user có quyền không
            $checkPermision = T1RolePermission::whereIn('role_id', $roles)
            ->where('permission_id', $currentPermision->id)
            ->count();
            if (in_array(User::ROLE_ADMIN, $roles) || $checkPermision > 0) {
                return $next($request);
            } else {
                $description = "";
                if(isset($permission->permission_description) && $permission->permission_description!== null){
                    $description = $permission->permission_description;
                }
                return ResponseBuilder::Fail($description, null, 418);
            }
        }
    }
}
