<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Models\Fu\ChangeScheduled;
use App\Repositories\Admin\ChangeScheduledRepository;
use Illuminate\Http\Request;

class ChangeScheduledController extends Controller
{
    protected $ChangeScheduledRepository;

    public function __construct(ChangeScheduledRepository $changeScheduledRepository)
    {
        $this->ChangeScheduledRepository = $changeScheduledRepository;
    }

    public function getListOrder(Request $request)
    {
       return $this->ChangeScheduledRepository->getListOrder($request);
    }

    public function getListDetailOrderMaGV(Request $request)
    {
       return $this->ChangeScheduledRepository->getListDetailOrderMaGV($request);
    }

    public function getDetailOrder(Request $request)
    {
        return $this->ChangeScheduledRepository->getAllInfoOrder($request);
    }

    public function handleRegisterOrder(Request $request)
    {
        return $this->ChangeScheduledRepository->handleRegisterOrder($request);
    }

    public function exportExcel(Request $request)
    {
        return $this->ChangeScheduledRepository->exportExcel($request);
    }

    public function  handleCancelOrder(Request $request)
    {
        return $this->ChangeScheduledRepository->handelCancelOrder($request);
    }

    public function  emailNoticeStaff(Request $request)
    {
        return $this->ChangeScheduledRepository->emailNoticeStaff($request);
    }
}
