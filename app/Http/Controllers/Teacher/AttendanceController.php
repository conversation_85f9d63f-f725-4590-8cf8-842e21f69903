<?php

namespace App\Http\Controllers\Teacher;

use App\Exports\TinhHinhDiHoc;
use App\Http\Controllers\Controller;
use App\Http\Lib;
use App\Models\Fu\Activity;
use App\Models\Fu\Attendance;
use App\Models\Fu\Block;
use App\Models\Fu\Course;
use App\Models\Fu\Term;
use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use App\Models\Fu\Subject;
use App\Models\Ho\IpWan;
use App\Models\T7\CourseResult;
use App\Models\T7\GradeSyllabus;
use App\Models\T7\SyllabusPlan;
use App\Models\Dra\CurriCulum;
use App\Models\Brand;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use App\Models\Dra\T1UserRole;
use Illuminate\Support\Arr;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Log;
use App\Models\SystemLog;
use Carbon\Carbon;

class AttendanceController extends Controller
{
    const CO_MAT = 1;
    const VANG_MAT = 0;
    const OVER_TIME = 15;
    const DA_DIEM_DANH = 1;
    const CHUA_DIEM_DANH = 0;
    const START = 1;
    const TRUOT_DIEM_DANH = -1;
    const DANG_HOC = 0;
    public function getGroup($id, Request $request)
    {
        $action = null;

        $checkAttendance = Attendance::where('activity_id', $id)->exists();
        if ($checkAttendance) {
            $action = 'edit';
            session(['get_group_action' => 'edit']);
        } else {
            $action = 'add';
            session(['get_group_action' => 'add']);
        }
        $action_req = $request->get("action", null);
        if ($action_req == 'view') {
            $action = 'view';
            session(['get_group_action' => 'view']);
        }
        // $group_id = $request->group_id;
        // $whoIs = auth()->user()->user_login;
        $activity = Activity::findOrFail($id);
        $group_id = $activity->groupid;
        if (!$this->checkIp()) {
            return redirect(url()->previous())->with(['status' => ['type' => 'danger', 'messages' => 'Địa chỉ IP này không được phép điểm danh']]);
        }
        // if (auth()->user()->user_level != 1) {
        //     if ($whoIs != $activity->leader_login) {
        //         return redirect(url()->previous())->with(['status' => ['type' => 'danger', 'messages' => 'Đây không phải lớp của bạn']]);
        //     }
        // }

        if ($action == 'view' || $action == 'edit') {
            // xử lý danh sách sinh viên thêm vào lớp muộn
            $attendancesDD = Attendance::with('user')->where('activity_id', $id)->get();
            $group = Group::find($group_id);

            $attendances = GroupMember::with('user')->where('groupid', $group_id)->orderBy('user_code', "ASC")->get();
            foreach ($attendances as $attendance) {
                $attendance->group_name = $group->group_name ?? '';
            }

            $attendances = $attendances->map(function ($item) use ($attendancesDD) {
                $attendance = $attendancesDD->where('user_login', $item->member_login)->first();
                if ($attendance) {
                    $item->val = $attendance->val;
                    $item->id = $attendance->id;
                    $item->description = $attendance->description;
                    $item->lastmodifier_login = $attendance->lastmodifier_login;
                    $item->lastmodified_time = $attendance->lastmodified_time;
                } else {
                    $item->isNew = true;
                }

                return $item;
            });
        } elseif ($action == 'add') {
            $group = Group::find($group_id);
            $attendances = GroupMember::with('user')->where('groupid', $group_id)->orderBy('user_code', 'ASC')->get();
            foreach ($attendances as $attendance) {
                $attendance->group_name = $group->group_name ?? '';
            }
        }

        return view('teacher_v1.attendance', [
            'attendances' => $attendances,
            'id' => $id,
            'activity' => $activity
        ]);
    }

    public function createAttendance(Request $request)
    {
        $attendances = $request->attendance;
        $group_id = null;
        $activity_id = $request->activity_id;
        $whoIs = auth()->user()->user_login;
        // $quyen_diem_danh_muon = Gate::allows('attendance_edit');
        $start = date("H:i:s");
        $end = date("H:i:s", time() - self::OVER_TIME * 60);
        $activity = Activity::findOrFail($activity_id);

        $group_id = $activity->groupid;
        $group = Group::findOrFail($group_id);
        $subject = Subject::findOrFail($group->psubject_id);
        $term = Lib::getTermDetails();
        $currentTerm = Term::whereRaw('startday <= CURRENT_DATE')
            ->whereRaw('endday >= CURRENT_DATE')
            ->first();

        $quyen_diem_danh_muon = T1UserRole::checkPermission(auth()->user(), 'attendance_edit');

        if (!$quyen_diem_danh_muon) {
            return redirect(url()->previous())->with(['status' => ['type' => 'danger', 'messages' => 'Bạn không có quyền điểm danh']]);
        }

        if (($currentTerm->id != $activity->term_id)
            // && !$quyen_diem_danh_muon
        ) {
            return redirect(url()->previous())->with(['status' => ['type' => 'success', 'messages' => 'Kỳ đã kết thúc không thể điểm danh']]);
        }
        $create = [];
        DB::beginTransaction();
        foreach ($attendances as $key => $attendance) {
            // kiểm tra để tránh trường hợp báo lỗi trùng unique khi thêm điểm danh
            $checkExistAttendance = Attendance::where('activity_id', $activity_id)
                ->where('user_login', $key)
                ->count();
            if ($checkExistAttendance > 0) {
                continue;
            }

            $status_diem_danh = isset($attendance['value']) ? self::CO_MAT : self::VANG_MAT;
            $comment_diem_danh = isset($attendance['comment']) ? $attendance['comment'] : null;
            $create = [
                'lastmodifier_login' => $whoIs,
                'activity_id' => $activity_id,
                'groupid' => $group_id,
                'group_name' => $group->group_name,
                'psubject_name' => $subject->subject_name,
                'short_subject_name' => $subject->short_name,
                'user_login' => $key,
                'val' => isset($attendance['value']) ? self::CO_MAT : self::VANG_MAT,
                'description' => isset($attendance['comment']) ? $attendance['comment'] : null,
                'day' => $activity->day,
                'psubject_code' => $subject->subject_code,
                'term_id' => $term['term_id'],
                'is_group_member' => 0,
            ];

            $creates[] = $create;

            $id = Attendance::insertGetId($create);
            $this->systemLog('group', 'add', "Add attendance id = " . $id . " for student $key with value: $status_diem_danh $comment_diem_danh", $key, $group_id, 0, '', '', 'attendance');
        }

        try {
            DB::commit();
            $this->syncData($group, $activity, $creates, $subject, $term);
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect(url()->previous())->with(['status' => ['type' => 'success', 'messages' => 'Đã xảy ra lỗi']]);
        }

        return redirect(route('teacher.attendance.get_group', [
            'id' => $activity->id,
            'action' => 'edit',
            'group_id' => $group_id,
        ]))->with(['status' => ['type' => 'success', 'messages' => 'Lưu điểm danh thành công']]);
    }

    public function updateAttendance(Request $request)
    {
        $attendances = $request->attendance;
        $activity_id = $request->activity_id;
        $whoIs = auth()->user()->user_login;
        // $quyen_diem_danh_muon = Gate::allows('attendance_edit');
        $start = date("H:i:s");
        $end = date("H:i:s", time() - self::OVER_TIME * 60);
        $activity = Activity::with('slotDetail')->findOrFail($activity_id);
        $term = Lib::getTermDetails();
        if (!$this->checkIp()) {
            return redirect(url()->previous())->with(['status' => ['type' => 'danger', 'messages' => 'Địa chỉ IP này không được phép điểm danh']]);
        }

        $quyen_diem_danh_muon = T1UserRole::checkPermission(auth()->user(), 'attendance_edit');

        if (!$quyen_diem_danh_muon) {
            return redirect(url()->previous())->with(['status' => ['type' => 'danger', 'messages' => 'Bạn không có quyền điểm danh']]);
        }

        $group_id = $activity->groupid;
        $group = Group::findOrFail($group_id);
        $subject = Subject::findOrFail($group->psubject_id);

        $create = [];
        $currentTerm = Term::whereRaw('startday <= CURRENT_DATE')
            ->whereRaw('endday >= CURRENT_DATE')
            ->first();

        if (($currentTerm->id != $activity->term_id)
            // && !$quyen_diem_danh_muon
        ) {
            return redirect(url()->previous())->with(['status' => ['type' => 'success', 'messages' => 'Kỳ đã kết thúc không thể điểm danh']]);
        }
        DB::beginTransaction();
        try {
            $filteredAttendances = array_filter($attendances, function ($attendance) {
                return !$attendance['isNew']; // Lọc ra các phần tử có isnew == false
            });

            $ids = array_column($filteredAttendances, 'id'); // Lấy danh sách ID từ các phần tử đã lọc

            $listAttendance = Attendance::whereIn('id', $ids)->get();

            foreach ($attendances as $key => $attendance) {
                $status_diem_danh = isset($attendance['value']) ? self::CO_MAT : self::VANG_MAT;
                $comment_diem_danh = isset($attendance['comment']) ? $attendance['comment'] : null;
                $isNew = $attendance['isNew'];

                if ($isNew) {
                    $attendance = null;
                    $status_diem_danh_old = null;
                } else {
                    $attendance = $listAttendance
                        ->where('id', $attendance['id'])
                        ->where('user_login', $key)
                        ->where('groupid', $group->id)
                        ->first();
                    $status_diem_danh_old = $attendance->val;
                }


                if ($isNew || !$attendance) {
                    $att =  Attendance::create([
                        'lastmodifier_login' => auth()->user()->user_login,
                        'activity_id' => $activity_id,
                        'groupid' => $group->id,
                        'group_name' => $group->group_name,
                        'psubject_name' => $subject->subject_name,
                        'short_subject_name' => $subject->short_name,
                        'user_login' => $key,
                        'val' => $status_diem_danh,
                        'description' => $comment_diem_danh,
                        'day' => $activity->day,
                        'psubject_code' => $subject->subject_code,
                        'term_id' => $term['term_id'],
                        'is_group_member' => 0,
                    ]);

                    $attendance['id'] = $att->id;
                } else {
                    $attendance->update([
                        'lastmodifier_login' => $whoIs,
                        'activity_id' => $activity_id,
                        'groupid' => $group_id,
                        'group_name' => $group->group_name,
                        'psubject_name' => $subject->subject_name,
                        'short_subject_name' => $subject->short_name,
                        'val' => $status_diem_danh,
                        'description' => $comment_diem_danh,
                        'psubject_code' => $subject->subject_code,
                        'term_id' => $term['term_id'],
                        'day' => $activity->day
                    ]);
                }


                if ($status_diem_danh_old !== $status_diem_danh) {
                    $create[] = [
                        'lastmodifier_login' => $whoIs,
                        'groupid' => $group_id,
                        'psubject_name' => $subject->subject_name,
                        'short_subject_name' => $subject->short_name,
                        'user_login' => $key,
                        'psubject_code' => $subject->subject_code,
                    ];

                    $this->systemLog('group', 'update', "Update attendance id = " . $attendance['id'] . " for student $key with value: $status_diem_danh $comment_diem_danh", $key, $group_id, 0, '', '', 'attendance');
                }
            }

            DB::commit();
            $this->syncData($group, $activity, $create, $subject, $term);
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect(url()->previous())->with(['status' => ['type' => 'success', 'messages' => 'Đã xảy ra lỗi']]);
        }

        return redirect(url()->previous())->with(['status' => ['type' => 'success', 'messages' => 'Lưu điểm danh thành công']]);
    }

    public function updateAttendanceFromCadres(Request $request)
    {
        try {
            $diem_danh = self::CHUA_DIEM_DANH;
            $co_the_diem_danh = false;
            $diem_danh_muon = false;
            $dong_diem_danh = false;
            $group_id = $request->group_id;
            $activity_id = $request->activity_id;
            $whoIs = auth()->user()->user_login;
            $attendanceRecord = Attendance::where('user_login', $request->user_login)->where('activity_id', $activity_id)->first();
            $attendance_id = $attendanceRecord->id;
            $quyen_diem_danh_muon = Gate::allows('attendance_edit');
            $start = date("H:i:s");
            $end = date("H:i:s", time() - self::OVER_TIME * 60);
            $activity = Activity::with('slotDetail')->findOrFail($activity_id);
            $term = Lib::getTermDetails();

            $group = Group::findOrFail($group_id);
            $subject = Subject::findOrFail($group->psubject_id);
            $create = [];
            DB::beginTransaction();
            $value = ($request['status'] == 'Present') ? self::CO_MAT : self::VANG_MAT;
            $description = isset($request['comment']) ? $request['comment'] : '';

            $res = Attendance::where('id', $attendance_id)->update([
                'lastmodifier_login' => $whoIs,
                'activity_id' => $activity_id,
                'groupid' => $group_id,
                'group_name' => $group->group_name,
                'psubject_name' => $subject->subject_name,
                'short_subject_name' => $subject->short_name,
                'val' => $value,
                'description' => $description,
                'psubject_code' => $subject->subject_code,
                'term_id' => $term['term_id'],
            ]);
            $systemLog = SystemLog::create([
                'object_name' => "group",
                'actor' => $whoIs,
                'log_time' => Carbon::now()->format('Y-m-d H:i:s'),
                'action' => "edit",
                'description' => "Edit attendance id = " . $attendance_id . " for student $request->user_login with value:  $value $description.",
                'object_id' => $group_id,
                'brief' => "attendance",
                'from_ip' => 0,
                'relation_login' => $request->user_login,
                'relation_id' => 0,
                'nganh_cu' => "",
                'nganh_moi' => "",
                'ky_chuyen_den' => 0,
                'ky_thu_cu' => 0
            ]);

            $create[] = [
                'lastmodifier_login' => $whoIs,
                'groupid' => $group_id,
                'psubject_name' => $subject->subject_name,
                'short_subject_name' => $subject->short_name,
                'user_login' => $request->user_login,
                'psubject_code' => $subject->subject_code,
            ];
            $this->syncData($group, $activity, $create, $subject, $term);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("---------giangkt------------");
            Log::error($e);
            Log::error("---------err end changeattendance ------------");
            return redirect(url()->previous())->with(['status' => ['type' => 'success', 'messages' => 'Đã xảy ra lỗi']]);
        }
        return response('Cập nhật điểm danh thành công', 200);
    }

    public function checkIp($check = false)
    {
        if ($check == true) return true;
        $currentIp = $_SERVER["HTTP_CF_CONNECTING_IP"] ?? request()->ip();
        $check = IpWan::where('ip_wan', $currentIp)->first();
        if ($check) {
            return true;
        }

        Log::error("[" . auth()->user()->user_login . "] sử dụng Ip $currentIp (không được phép) điểm danh");
        return false;
    }

    public function syncData($group, $activity, $create, $subject, $term)
    {
        $group->is_started = self::START;
        $group->save();
        Course::where('id', $group->body_id)->update([
            'is_started' => self::START,
        ]);
        $activity->done = self::START;
        $activity->short_subject_name = $subject->short_name;
        $activity->save();
        $this->createOrUpdateCourseResult($create, $subject, $group, $term);
    }

    public function createOrUpdateCourseResult($data, $subject, $group, $term)
    {
        $activity_count = [];
        $activity_detail = [];
        $activity_done = 0;
        $activity_not_done = 0;
        $block_name = 'Học phần';

        $syllabus_plan = SyllabusPlan::select('id')
            ->leftJoin('session_type', 't7_syllabus_plan.session_type', '=', 'session_type.id')
            ->where('syllabus_id', $group->syllabus_id)
            ->where('session_type.is_exam', 0)
            ->count();
        $syllabus = GradeSyllabus::findOrFail($group->syllabus_id);
        $block = Block::where('start_day', '<=', now()->format('Y-m-d'))->where('end_day', '>', now()->format('Y-m-d'))->orderBy('id', 'desc')->first();
        if ($block) {
            $block_name = $block->block_name;
        }

        $activities = Activity::select([
            'activity.*',
            'session_type.is_exam',
        ])
        ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
        ->where('groupid', $group->id)
        ->where('session_type.is_exam', 0)
        ->get();
        
        foreach ($activities as $activity) {
            if (now() > $activity->day) {
                $activity_done += 1;
            } else {
                $activity_not_done += 1;
            }
            $activity_count[] = $activity->id;
            $activity_detail[$activity->id] = [
                'course_slot' => $activity->course_slot,
                'slot' => $activity->slot,
            ];
        }

        foreach ($data as $item) {
            $attendance_details = [];
            $attendance_absent = 0;
            $diem_danh = 0;
            $attendances = Attendance::where('user_login', $item['user_login'])->where('groupid', $item['groupid'])->whereIn('activity_id', $activity_count)->get();
            foreach ($attendances as $attendance) {
                if ($attendance->val == 0) {
                    $attendance_absent += 1;
                }
                $attendance_details[] = "$attendance->day:" . $activity_detail[$attendance->activity_id]['course_slot'] . ':' . $activity_detail[$attendance->activity_id]['slot'] . ':' . "$attendance->lastmodifier_login:$attendance->val:$attendance->description";
            }

            $absent_progress = ($attendance_absent * 100 / $syllabus_plan) > (100 - $syllabus->attendance_cutoff);
            $absent_progress_doing = ($attendance_absent * 100 / $syllabus_plan) <= (100 - $syllabus->attendance_cutoff);

            if ($absent_progress) {
                $diem_danh = self::TRUOT_DIEM_DANH;
            }
            if ($diem_danh) {
                if ($absent_progress_doing) {
                    $diem_danh = self::DANG_HOC;
                }
            }
            $result = CourseResult::where('student_login', $item['user_login'])->where('groupid', $item['groupid'])->update([
                'syllabus_id' => $group->syllabus_id,
                'course_id' => $group->body_id,
                'student_login' => $item['user_login'],
                'val' => $diem_danh,
                'term_id' => $term['term_id'],
                'pterm_name' => $term['term_name'],
                'groupid' => $group->id,
                'pgroup_name' => $group->group_name,
                'subject_id' => $subject->id,
                'psubject_name' => $item['psubject_name'],
                'psubject_code' => $item['psubject_code'],
                'skill_code' => $subject->skill_code,
                'total_session' => $syllabus_plan,
                'attendance_detail' => implode(',', $attendance_details),
                'attendance_absent' => $attendance_absent,
                'done_activity' => $activity_done,
                'attendance_cutoff' => $syllabus->attendance_cutoff,
                'minimum_required' => $syllabus->minimum_required,
                'modifier_login' => $item['lastmodifier_login'],
                'start_date' => $group->start_date,
                'end_date' => $group->end_date,
                'not_done_activity' => $activity_not_done,
                'number_of_credit' => $subject->num_of_credit,
            ]);
            if ($result == 0) {
                CourseResult::insert([
                    'syllabus_id' => $group->syllabus_id,
                    'course_id' => $group->body_id,
                    'groupid' => $group->id,
                    'student_login' => $item['user_login'],
                    'val' => $diem_danh,
                    'term_id' => $term['term_id'],
                    'pterm_name' => $term['term_name'],
                    'subject_id' => $subject->id,
                    'pgroup_name' => $group->group_name,
                    'psubject_name' => $item['psubject_name'],
                    'psubject_code' => $item['psubject_code'],
                    'skill_code' => $subject->skill_code,
                    'total_session' => $syllabus_plan,
                    'attendance_detail' => implode(',', $attendance_details),
                    'attendance_absent' => $attendance_absent,
                    'done_activity' => $activity_done,
                    'attendance_cutoff' => $syllabus->attendance_cutoff,
                    'minimum_required' => $syllabus->minimum_required,
                    'block' => $block_name,
                    'grade' => 0,
                    'creator_login' => $item['lastmodifier_login'],
                    'modifier_login' => $item['lastmodifier_login'],
                    'short_subject_name' => $item['short_subject_name'],
                    'start_date' => $group->start_date,
                    'end_date' => $group->end_date,
                    'punishment' => 0,
                    'attendance' => 0,
                    'grade_detail' => '',
                    'attendance_fail' => '',
                    'not_done_activity' => $activity_not_done,
                    'is_finish' => 0,
                    'subject_finish' => 0,
                    'complete_id' => 0,
                    'source' => 1,
                    'note' => '',
                    'approved_by' => '',
                    'attendance_state' => 0,
                    'ctsv_note' => '',
                    'done_session' => 0,
                    'teacher_login' => '',
                    'temp' => 0,
                    'is_announced' => 0,
                    'lastupdated' => now(),
                    'total_exam' => 0,
                    'done_exam' => 0,
                    'total_grade' => 0,
                    'done_grade' => 0,
                    'final_date' => now(),
                    'final_result' => 0,
                    'resit_date' => now(),
                    'resit_result' => 0,
                    'final_taken' => 0,
                    'resit_taken' => 0,
                    'activity_id' => 0,
                    'skill_code_update' => '',
                    'study_status' => 0,
                    'taken_exam' => 0,
                    'number_of_credit' => $subject->num_of_credit,
                    'is_grade' => 0,
                    'lan_thu' => 0,
                    'transfer_sub' => 0,
                    'slot' => 0,
                    'loai_buoi_hoc' => 0,
                    'note_chuyen_diem' => '',
                ]);
            }
        }
    }

    public function selectDataListAttendance(Request $request)
    {
        $term = Term::orderBy('id', 'DESC')->get(['id', 'term_name']) ?? [];
        $block = Block::get(['id', 'term_id', 'block_name']) ?? [];
        $curriculum = CurriCulum::get(['id', 'name', 'name_en']) ?? [];
        return response([
            'term' => $term,
            'block' => $block,
            'curriculum' => $curriculum,
        ]);
    }

    public function listAttendance(Request $request)
    {
        return $this->list($request);
    }

    public function brandList(Request $request)
    {
        $groupBy = Arr::get($request->all(), 'group_by', true);
        if ($groupBy == false) {
            return Brand::get(['id', 'code', 'major']);
        }
        return Brand::groupBy('major')->get(['id', 'code', 'major']);
    }

    private function list(Request $request)
    {
        ini_set("memory_limit", "-1");
        set_time_limit(0);
        $per_page = Arr::get($request->all(), 'perPage', 20);
        $is_export = Arr::get($request->all(), 'is_export', false);
        $resultTemplate = [];
        $result = [];
        $query = CourseResult::query();
        $query->leftJoin('user', 'user.user_login', '=', 't7_course_result.student_login');
        $query->leftJoin('list_group', 'list_group.id', '=', 't7_course_result.groupid');
        $query->leftJoin('sna_sms_warning', function ($qr) {
            $qr->on('t7_course_result.student_login', '=', 'sna_sms_warning.student_login')
                ->on('t7_course_result.groupid', '=', 'sna_sms_warning.group_id')
                ->on('t7_course_result.attendance_absent', '=', 'sna_sms_warning.attendance_absent')
                ->limit(1);
        });
        // $query->where('list_group.is_virtual', '=', 0);
        $this->applyFilter($query, $request);
        $query->select([
            't7_course_result.id',
            'user.user_code',
            DB::raw("CONCAT(TRIM(user.user_surname),' ', TRIM(user.user_middlename),' ',TRIM(user.user_givenname)) AS student_name"),
            't7_course_result.groupid',
            't7_course_result.pgroup_name',
            't7_course_result.psubject_code',
            't7_course_result.psubject_name',
            't7_course_result.attendance_cutoff',
            't7_course_result.attendance_absent',
            't7_course_result.done_activity',
            't7_course_result.total_session',
            't7_course_result.student_login',
            't7_course_result.term_id',
            't7_course_result.start_date',
            't7_course_result.end_date',
            'list_group.block_id',
            'user.user_telephone',
            'user.user_email',
            'sna_sms_warning.notified'
        ]);
        $query->orderBy('t7_course_result.id', 'ASC');
        if ($is_export == "export") {
            $courseResult = $query->get();
        } else {
            $courseResult = $query->paginate($per_page);
        }
        foreach ($courseResult as $attendance) {
            $resultTemplate["id"] = $attendance->id;
            $resultTemplate["user_code"] = $attendance->user_code;
            $resultTemplate["student_login"] = $attendance->student_login;
            $resultTemplate["student_name"] = $attendance->student_name;
            $resultTemplate["class"] = $attendance->pgroup_name;
            $resultTemplate["subject_code"] = $attendance->psubject_code;
            $resultTemplate["subject_name"] = $attendance->psubject_name;
            $resultTemplate["maxium_absent_percentage"] = 100 - $attendance->attendance_cutoff;
            $resultTemplate["absent_per_actual_attended"] = $attendance->attendance_absent . "/" . $attendance->done_activity;
            $absent_percent = ($attendance->total_session > 0) ? round($attendance->attendance_absent * 100 / $attendance->total_session) : $attendance->total_session;
            $resultTemplate["percentate_of_absent_per_total_slot"] = ($attendance->total_session > 0) ? $absent_percent . "% (" . $attendance->attendance_absent . "/" . $attendance->total_session . ")" : $attendance->total_session;
            $resultTemplate["phone"] = $attendance->user_telephone; // user teletphjone user_telephone
            $resultTemplate["user_email"] = $attendance->user_email; // user teletphjone user_telephone
            $resultTemplate["block_id"] = $attendance->block_id;
            $resultTemplate["term_id"] = $attendance->term_id;
            $resultTemplate["start_date"] = $attendance->start_date;
            $resultTemplate["end_date"] = $attendance->end_date;
            $resultTemplate["block"] = $attendance->block;
            $resultTemplate["notified"] = $attendance->notified;
            $result[] = $resultTemplate;
        }
        if ($is_export == "export") {
            return Excel::download(new TinhHinhDiHoc($result), 'tinhhinhdihoc.xlsx');
        }
        $courseResult->setCollection(collect($result));
        return response([
            'data' => $courseResult
        ]);
    }

    private function applyFilter(&$query, $request)
    {
        // + bỏ filter khoá(curriculum) => (chuyên ngành)
        // + filter từ khoá: => MSSV
        // + check lại filter kì(cần filter kèm những đk khác - 0 thì lỗi)
        // + filter số buổi nghỉ <5, <10, <15
        // + data default được lấy ra (những sinh viên có môn trong kì mới nhất)
        // +  export (theo filter, nhưng lấy hết - k phân trang)
        // + lấy thông tin sđt, phụ huynh k care.
        $key_word = Arr::get($request->all(), 'key_word', null);
        $curriculum_id = Arr::get($request->all(), 'curriculum_id', null);
        $term_id = Arr::get($request->all(), 'term', null);
        // $block_id = Arr::get($request->all(), 'block', null);
        $absent_from = Arr::get($request->all(), 'from_date', null);
        $absent_to = Arr::get($request->all(), 'to_date', null);

        $absent_from = is_numeric($absent_from) ? (float)$absent_from : null;
        $absent_to = is_numeric($absent_to) ? (float)$absent_to : null;

        $notification_status = Arr::get($request->all(), 'notification_status', null);
        $show_all = Arr::get($request->all(), 'show_all', null);
        if ($key_word) {
            $query->where('user.user_code', '=', $key_word);
        }
        if ($curriculum_id) {
            $query->where('user.curriculum_id', '=', $curriculum_id);
        }
        if ($term_id) {
            $query->where('t7_course_result.term_id', $term_id);
        }
        // if ($block_id) {
        //     $query->where('list_group.block_id', $block_id);
        // }
        if ($absent_from) {
            $query->whereRaw("(t7_course_result.attendance_absent * 100 / t7_course_result.total_session) >= ?", [$absent_from]);
        }
        if ($absent_to) {
            $query->whereRaw("(t7_course_result.attendance_absent * 100 / t7_course_result.total_session) <= ?", [$absent_to]);
        }
        if (!is_null($notification_status)) {
            if ($notification_status == 1) {
                $query->where("sna_sms_warning.notified", 1);
            } elseif ($notification_status == 0) {
                $query->where(function ($q) {
                    $q->where("sna_sms_warning.notified", 0)
                        ->orWhereNull("sna_sms_warning.notified");
                });
            }
        }

        if ($show_all == "false") {
            $query->where('t7_course_result.start_date', '>', Carbon::now()->subYears(1)->format('Y-m-d'));
        }
    }
}
