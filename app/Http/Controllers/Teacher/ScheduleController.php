<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Models\Fu\Activity;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;

class ScheduleController extends Controller
{
    const OVER_TIME = 15;
    const DA_DIEM_DANH = 1;
    const CHUA_DIEM_DANH = 0;
    public static function getSchedule()
    {
        $whoIs = auth()->user()->user_login;
        //$whoIs = 'ngocntm12';
        $whoIs = 'lopt';
        $quyen_diem_danh_muon = Gate::allows('attendance_edit');
        //$quyen_diem_danh_muon = false;
        $schedules = Activity::where('leader_login', $whoIs)->whereDate('day', '>=', now())->whereDate('day', '<=', now()->addDays(7))->orderBy('day')->orderBy('slot')->get();
        //$schedules = Activity::with('slotDetail')->where('leader_login', $whoIs)->whereDate('day', '>=', '2020-03-01')->orderBy('day')->orderBy('slot')->get();
        foreach ($schedules as $schedule) {
            $diem_danh = self::CHUA_DIEM_DANH;
            $co_the_diem_danh = false;
            $diem_danh_muon = false;
            $dong_diem_danh = false;
            $start = date("H:i:s");
            $end = date("H:i:s", time() - self::OVER_TIME * 60);
            if ($schedule->done) {
                $diem_danh = self::DA_DIEM_DANH;
            }
            if ($schedule->slotDetail->slot_start <= $start && $schedule->slotDetail->slot_start >= $end) {
                if (!$diem_danh) {
                    $co_the_diem_danh = true;
                }
            } else {
                $diem_danh_muon = true;
            }
            if ($diem_danh_muon) {
                if (!$quyen_diem_danh_muon) {
                    $diem_danh_muon = false;
                }
            }
            if ($schedule->slotDetail->slot_end < now()->format('Y-m-d')) {
                $dong_diem_danh = true;
            }
            $schedule->co_the_diem_danh = $co_the_diem_danh;
            $schedule->diem_danh = $diem_danh;
            $schedule->diem_danh_muon = $diem_danh_muon;
            $schedule->dong_diem_danh = $dong_diem_danh;

        }
        return $schedules;
    }
}
