<?php

namespace App\Http\Controllers;

use App\Imports\DisciplineImport;
use App\Models\Ho\IpWan;
use App\Models\SystemLog;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    /**
     * @param null $status
     * @param null $messages
     * @param null $data
     * @return array
     */
    public function res($status = null, $messages = null, $data = null)
    {
        return [
            'status' => $status,
            'messages' => $messages,
            'data' => $data,
        ];
    }

    public function checkIp()
    {
        $your_ip = request()->ip();
        $check = IpWan::where('ip_wan', $your_ip)->first();
        if ($check) {
            return true;
        }

        return false;
    }

    public function redirectWithStatus($type, $messages, $route = null)
    {
        return redirect($route ?? url()->previous())->with(['status' => ['type' => $type, 'messages' => $messages]]);
    }

    public function rediect404(Request $request)
    {
        Log::channel('check-user-access')->error("user " . auth()->user()->user_login . " access " . $request->getUrl());
        return view('admin_v1.errors.lock_user');
    }

    public function systemLog($object_name, $action, $description, $relation_login, $object_id = 0, $relation_id = 0, $nganh_cu = '', $nganh_moi = '', $brief = '', $ky_chuyen_den = 0, $ky_thu_cu = 0)
    {
        return systemLog::create([
            'actor' => Auth::user()->user_login,
            'object_name' => $object_name,
            'log_time' => now(),
            'action' => $action,
            'description' => $description,
            'relation_login' => $relation_login,
            'object_id' => $object_id,
            'relation_id' => $relation_id,
            'brief' => $brief,
            'from_ip' => request()->ip(),
            'nganh_cu' => $nganh_cu,
            'nganh_moi' => $nganh_moi,
            'ky_chuyen_den' => $ky_chuyen_den,
            'ky_thu_cu' => $ky_thu_cu,
        ]);
    }
}
