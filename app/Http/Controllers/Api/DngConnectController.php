<?php

namespace App\Http\Controllers\Api;


use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
// use App\Repositories\Admin\DngConnectRepository;


class DngConnectController extends Controller
{
    const MODEL = '';
    public function __construct()
    {
        // $this->DngConnectRepository = $dngConnectRepository;
    }

    public function getDngConnection(Request $request){
        // return $this->DngConnectRepository->getDngConnection($request);
        return response()->json(['error' => 'DngConnectRepository not available'], 500);
    }

    public function getInvoiceControl(Request $request){
        // return $this->DngConnectRepository->getInvoiceControl($request);
        return response()->json(['error' => 'DngConnectRepository not available'], 500);
    }

}
