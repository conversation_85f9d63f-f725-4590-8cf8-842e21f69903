<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\TechAPI\SmsBase;
use Illuminate\Support\Facades\Cache;
use App\Repositories\Admin\OtpRepository;
use Illuminate\Support\Facades\Log;
class OtpController extends Controller
{
    protected $repository;
    function __construct(OtpRepository $repository) {
        $this->repository = $repository;
    }
    public function sendOtp(Request $request) {
        return $this->repository->sendOtp($request);
    }

    public function validOtp(Request $request) {
        return $this->repository->validOtp($request);
    }
}
