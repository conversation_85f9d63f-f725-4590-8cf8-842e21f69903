<?php

namespace App\Http\Controllers\Api;

use App\Http\Lib;
use App\Models\Fu\Subject;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Utils\ResponseBuilder;



class SubjectController extends Controller
{
    public function getAll(Request $request)
    {
        $term = Term::orderBy('id', 'desc')->get();

        return Lib::res('success', 'Get data successfully', $term);
    }

    public function getDatasByOption(Request $request)
    {
        $list = Subject::query();
        $term_id = $request->get('term_id', null);
        $limit = $request->get('limit', 20);
        $page = $request->get('page', 1);
        
        if ($decision_type != null && $decision_type != -1) {
            $list = $list->where('name', $decision_type);
        }

        
        $list = $list->offset($limit * ($page - 1))
        ->limit($limit)
        ->get();

        if ($request->get('select2', null) == null) {
            return ResponseBuilder::Success($list, 'Thành công');
        }

        $res[] = [
            'id' => -1,
            'text' => "Tất cả các kỳ",
        ];
        foreach ($list as $key => $value) {
            $res[] = [
                'id' => $value->id,
                'text' => ($value->decision_num . " - " . $value->name),
            ];
        }

        return response()->json($res, 200);
    }
}
