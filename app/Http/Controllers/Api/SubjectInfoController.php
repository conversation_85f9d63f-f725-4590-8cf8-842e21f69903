<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Dra\Period;
use App\Models\Fu\Subject;
use App\Utils\ResponseBuilder;

class SubjectInfoController extends Controller
{
    public function getListCurriculumPeriod(Request $request)
    {
        $curriculums = Period::join('curriculum', 'period.curriculum_id', '=', 'curriculum.id')
            ->select(
                'period.curriculum_id',
                'curriculum.name as curriculum_name',
                'curriculum.nganh as major',
                'curriculum.chuyen_nganh as specialized_major',
                'curriculum.noi_dung as narrow_specialized_major'
            )
            ->selectRaw('COUNT(*) as num_of_semester')
            ->groupBy('period.curriculum_id')
            ->orderBy('curriculum.id')
            ->get();

        $data = [];

        foreach ($curriculums as $curriculum) {
            $data[] = (object)[
                'curriculum_id' => $curriculum['curriculum_id'],
                'curriculum_name' => $curriculum['curriculum_name'],
                'major' => $curriculum['major'],
                'specialized_major' => $curriculum['specialized_major'],
                'narrow_specialized_major' => $curriculum['narrow_specialized_major'],
                'num_of_semester' => $curriculum['num_of_semester'],
            ];
        }

        return ResponseBuilder::Success($data, "Success", 200);
    }

    public function getListSubject(Request $request)
    {
        $subjects = Subject::get([
            'subject_code',
            'subject_name',
            'num_of_credit'
        ]);

        $data = [];

        foreach ($subjects as $subject) {
            $data[] = (object)[
                'subject_code' => $subject['subject_code'],
                'subject_name' => $subject['subject_name'],
                'num_of_credit' => $subject['num_of_credit']
            ];
        }

        return ResponseBuilder::Success($data, "Success", 200);
    }
}
