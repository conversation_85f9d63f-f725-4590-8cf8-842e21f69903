<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Repositories\Admin\StudentHelperRepository;
use Illuminate\Http\Request;

class StudentHelperController extends Controller
{
    public $HelpRepository;
    public function __construct(StudentHelperRepository $helpRepository)
    {
        $this->HelpRepository = $helpRepository;
    }

    
    /**
     * lấy danh sách câu hỏi và trả lời
     *
     * @param  mixed $request
     * @return void
     */
    public function getQnA(Request $request)
    {
        return $this->HelpRepository->getQnA($request);
    }
        
    /**
     * lấy danh sách danh mục
     *
     * @param  mixed $request
     * @return void
     */
    public function getCategory(Request $request)
    {
        return $this->HelpRepository->getCategory($request);
    }
        
    /**
     * tạo mới danh mục
     *
     * @param  mixed $request
     * @return void
     */
    public function createCategory(Request $request)
    {
        return $this->HelpRepository->createCategory($request);
    }
        
    /**
     * xóa danh mục
     *
     * @param  mixed $id
     * @return void
     */
    public function deleteCategory($id)
    {
        return $this->HelpRepository->deleteCategory($id);
    }
        
    /**
     * chỉnh sửa danh mục
     *
     * @param  mixed $request
     * @param  mixed $id
     * @return void
     */
    public function editCategory(Request $request, $id)
    {
        return $this->HelpRepository->editCategory($request, $id);
    }
        
    /**
     * tạo mới câu hỏi và trả lời
     *
     * @param  mixed $request
     * @return void
     */
    public function createQnA(Request $request)
    {
        return $this->HelpRepository->createQnA($request);
    }
        
    /**
     * chỉnh sửa câu hỏi và trả lời
     *
     * @param  mixed $request
     * @param  integer $id
     * @return void
     */
    public function editQnA(Request $request, $id)
    {
        return $this->HelpRepository->editQnA($request, $id);
    }
    
    /**
     * xóa câu hỏi và trả lời
     *
     * @param  mixed $id
     * @return void
     */
    public function deleteQnA($id)
    {
        return $this->HelpRepository->deleteQnA($id);
    }
}
