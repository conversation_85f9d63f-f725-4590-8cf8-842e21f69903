<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Repositories\PluginRepository;
use Illuminate\Http\Request;

class PluginController extends Controller
{
    public function __construct(PluginRepository $pluginRepository)
    {
        $this->pluginRepository = $pluginRepository;
    }

    public function uploadFileToSource(Request $request)
    {
        return $this->pluginRepository->uploadFileToSource($request);
    }

    public function deleteFileFromSource(Request $request)
    {
        return $this->pluginRepository->deleteFileFromSource($request);
    }
}
