<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Repositories\Admin\Report3PartyRepository;

class Report3PartyController extends Controller
{
    protected $report3PartyRepository;

    public function __construct(Report3PartyRepository $report3PartyRepository) {
        $this->report3PartyRepository = $report3PartyRepository;
    }

    public function getFu3PartiesConfirmation(Request $request) {
        return $this->report3PartyRepository->getFu3PartiesConfirmation($request);
    }

    public function getFu3PartiesConfirmationDetail($id) {
        return $this->report3PartyRepository->getFu3PartiesConfirmationDetail($id);
    }


    public function downloadFileListStudentReport(Request $request) {
        return $this->report3PartyRepository->downloadFileListStudentReport($request);
    }

    public function updateListStudentReport() {
        return $this->report3PartyRepository->updateListStudentReport();
    }

    public function changeConfirmType($type_room ,$id) {
        return $this->report3PartyRepository->changeConfirmType($type_room ,$id);
    }

    public function uploadFileToSource(Request $request) {
        return $this->report3PartyRepository->uploadFileToSource($request);
    }

    public function getInfoLog(Request $request) {
        return $this->report3PartyRepository->getInfoLog($request);
    }
}
