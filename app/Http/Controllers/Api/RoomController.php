<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Repositories\Admin\RoomRepository;
use Illuminate\Http\Request;

class RoomController extends Controller
{
    protected $roomRepository;

    function __construct(RoomRepository $roomRepository) {
        $this->roomRepository = $roomRepository;
    }

    public function getCheckRoomByDate(Request $request){
        return $this->roomRepository->getCheckRoomByDate($request);
    }

    public function postRegisterRoom(Request $request){
        return $this->roomRepository->postRegisterRoom($request);
    }

    public function deleteBookingRoom(Request $request){
        return $this->roomRepository->deleteBookingRoom($request);
    }

    public function putUpdateBookingRoom(Request $request){
        return $this->roomRepository->putUpdateBookingRoom($request);
    }
}
