<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Sms\Account;
use Illuminate\Http\Request;
use App\Models\Fu\User;
use App\Models\Sms\Telco;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StudentSmsController extends Controller
{
    public function getListPhoneNumber(Request $request)
    {
        try {
            $user_login = $request->user_login;
            $lists = Account::select([
                'account.id', 
                'account.phone', 
                'account.owner_type', 
                'account.owner_name', 
                'account.is_active', 
                'account.telco', 
                'account.balance', 
                'account.created_on',
                'telco.name as network_name',
            ])
                ->leftJoin('telco', 'account.telco', '=', 'telco.code')
                ->where('account.student_login', $user_login)
                ->orderBy('account.id', 'asc')
                ->get();
            
            foreach ($lists as &$list) {
                if ($list->owner_type === null) {
                    $list->owner_type_name = "Chưa rõ";
                } else if ($list->owner_type == 0) {
                    $list->owner_type_name = "Sinh viên";
                } else {
                    $list->owner_type_name = "Phụ huynh";
                }
                
                if($list->is_active == 1) {
                    $list->is_active = true;
                } else {
                    $list->is_active = false;
                }
            }
            $options_telco = Telco::select(['code as value', 'name as text'])->get();
            return response(['lists' => $lists, 'options_telco' => $options_telco], 200);
        } catch (\Throwable $th) {
            Log::error($th);
            return response()->json([
                'lists' => [],
                'options_telco' => [],
            ], 500);
        }
    }

    /** 
     * Change student phone number
     * @param number    $request->id            ID
     * @param tring     $request->telco         Nhà mạng
     * @param number    $request->phone_number  Số điện thoại đã thay đổi
     * request {
     *  id: ,
     *  telco: changeData.telco,
     *  phone_number: changeData.phone
     * }
     * @return bool
    */
    public function changePhoneNumber(Request $request)
    {
        try {
            # lấy tất cả sđt trong bảng sms account
            if(!$request->telco)        return response(['status' => 0, 'message' => "Không tìm thấy nhà mạng. Vui lòng kiểm tra lại!"], 500);
            if(!$request->phone_number) return response(['status' => 0, 'message' => "SĐT mới không đúng định dạng. Vui lòng kiểm tra lại!"], 500);

            $phone_user = Account::where('id', '=', $request->id)->first();
            if(!$phone_user) {
                return response(['status' => 0, 'message' => "Not found!"], 500);
            }
            $old_phone_number = $phone_user->phone;
            # ktra xem sv nhập sđt mới có bị trùng với sđt khác hay không?
            $check = Account::where('phone', '=', $request->phone_number)->where('id', '!=', $request->id)->where('is_active', 1)->first();
            if($check) {
                return response(['status' => 0, 'message' => "Số điện thoại này đã được SV $check->student_code sử dụng!\nVui lòng vô hiệu hoá số điện thoại bị trùng trước."], 200);
            }

            DB::beginTransaction();
            DB::beginTransaction();
            $phone_user->telco = $request->telco;
            $phone_user->phone = $request->phone_number;
            $phone_user->save();
            $object_name = 'phone_number';
            $action = 'update';
            $description = "Số điện thoại $old_phone_number đã được đổi thành $request->phone_number.";
            $relation_login = $phone_user->student_login;
            $log = $this->systemLog($object_name, $action, $description, $relation_login);
            if(!$log) {
                DB::rollBack();
                DB::rollBack();
                return response(['status' => 0, "message" => "Thất bại! Có lỗi xảy ra."], 500);
            }
            DB::commit();
            DB::commit();
            return response(['status' => 1, "message" => "Cập nhật số điện thoại thành công!"], 200);
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            DB::rollBack();
            return response()->json([
                'status' => 0,
                'message' => 'Có lỗi xảy ra!'
            ], 500);
        }
    }


    public function changeStatusPhoneNumber(Request $request)
    {
        try {
            $phone_user = Account::where('id', '=', $request->id)->first();
            if(!$phone_user) {
                return response(['status' => 0, 'message' => "Not found!"], 500);
            }

            DB::beginTransaction();
            DB::beginTransaction();
            if($phone_user->is_active == 1){
                $phone_user->is_active = 0;
                $description = "Đã vô hiệu hoá số điện thoại $phone_user->phone.";
            } else {
                $check = Account::where('phone', '=', $phone_user->phone)->where('is_active', 1)->first();
                if($check) {
                    return response(['status' => 0, 'message' => "Số điện thoại này đã được SV $check->student_code sử dụng!\nVui lòng vô hiệu hoá số điện thoại bị trùng trước."], 200);
                }
                $phone_user->is_active = 1;
                $description = "Đã kích hoạt số điện thoại $phone_user->phone.";
            }
            $phone_user->save();
            $object_name = 'phone_number';
            $action = 'update';
            $relation_login = $phone_user->student_login;
            $log = $this->systemLog($object_name, $action, $description, $relation_login);
            if(!$log) {
                DB::rollBack();
                DB::rollBack();
                return response(['status' => 0, "message" => "Thất bại! Có lỗi xảy ra."], 500);
            }
            DB::commit();
            DB::commit();
            return response(['status' => 1, 'message' => "Cập nhật thành công!"], 200);
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            DB::rollBack();
            return response()->json([
                'code' => 0,
                'message' => 'Có lỗi xảy ra!'
            ], 500);
        }
    }
    public function saveNewPhoneNumber(Request $request)
    {
        try {
            # lấy tất cả sđt trong bảng sms 
            if(!$request->student_code) return response(['status' => 0, 'message' => "Không tìm thấy sinh viên!"], 500);
            if(!$request->owner_name)   return response(['status' => 0, 'message' => "Tên chủ tài khoản không hợp lệ!"], 500);
            if(!$request->telco)        return response(['status' => 0, 'message' => "Không tìm thấy nhà mạng. Vui lòng kiểm tra lại!"], 500);
            if(!$request->phone)        return response(['status' => 0, 'message' => "SĐT mới không đúng định dạng. Vui lòng kiểm tra lại!"], 500);

            # ktra sinh viên viên
            $student = User::where('user_code', '=', $request->student_code)->where('user_level', '=', 3)->first();
            if(!$student) return response(['status' => 0, 'message' => "Không tìm thấy sinh viên!"], 500);
            # ktra xem sv nhập sđt mới có bị trùng với sđt khác hay không?
            $check = Account::where('phone', '=', $request->phone)->where('is_active', 1)->first();
            if($check) {
                return response(['status' => 0, 'message' => "Số điện thoại này đã được SV $check->student_code sử dụng!\nVui lòng vô hiệu hoá số điện thoại bị trùng trước."], 200);
            }

            DB::beginTransaction();
            DB::beginTransaction();
            $new_phone_number = Account::insert([
                'phone' => $request->phone,
                'student_login' => $student->user_login,
                'student_code' => $student->user_code,
                'student_name' => $student->user_login,
                'owner_type' => $request->owner_type,
                'owner_name' => $request->owner_name,
                'is_active' => 1,
                'telco' => $request->telco,
                'created_on' => Carbon::now()->toDateTimeString()
            ]);
            if(!$new_phone_number) {
                DB::rollBack();
                DB::rollBack();
                return response(['status' => 0, "message" => "Thất bại! Có lỗi xảy ra."], 500);
            }
            $object_name = 'phone_number';
            $action = 'update';
            $owner_type_name = "Sinh viên";
            if($request->owner_type == 1) {
                $owner_type_name = "Phụ huynh";
            }
            $description = "Thêm số điện thoại $request->phone là số $owner_type_name.";
            $relation_login = $student->user_login;
            $log = $this->systemLog($object_name, $action, $description, $relation_login);
            if(!$log) {
                DB::rollBack();
                DB::rollBack();
                return response(['status' => 0, "message" => "Thất bại! Có lỗi xảy ra."], 500);
            }
            DB::commit();
            DB::commit();
            return response(['status' => 1, "message" => "Cập nhật số điện thoại thành công!"], 200);
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            DB::rollBack();
            return response()->json([
                'status' => 0,
                'message' => 'Có lỗi xảy ra!'
            ], 500);
        }
    }
}
