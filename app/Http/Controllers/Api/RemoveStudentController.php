<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Repositories\Admin\RemoveStudentRepository;

class RemoveStudentController extends Controller
{
    protected $removeStudentRepository;

    public function __construct(RemoveStudentRepository $removeStudentRepository) {
        $this->removeStudentRepository = $removeStudentRepository;
    }

    /**
     * lấy danh sinh viên rút lớp 30%
     *
     * @param  mixed $request
     * @return void
     */
    public function getListStudentRemove_30(Request $request) {
        return $this->removeStudentRepository->getListStudentRemove_30($request);
    }

    /**
     * tạo danh sinh viên rút lớp 30%
     *
     * @param  mixed $request
     * @return void
     */
    public function createListStudentRemove_30(Request $request) {
        return $this->removeStudentRepository->createListStudentRemove_30($request);
    }

    /**
     * xóa 1 sinh viên khỏi lớp 30%
     *
     * @param  mixed $id
     * @return void
     */
    public function deleteStudentRemove_30(Request $request) {
        return $this->removeStudentRepository->deleteStudentRemove_30($request);
    }

    /**
     * xóa toàn sinh viên khỏi lớp 30%
     *
     * @param  mixed $id
     * @return void
     */
    public function deleteListStudentRemove_30() {
        return $this->removeStudentRepository->deleteListStudentRemove_30();
    }

        /**
     * lấy danh sinh viên rút lớp 30%
     *
     * @param  mixed $request
     * @return void
     */
    public function getListStudentRemoveAp(Request $request) {
        return $this->removeStudentRepository->getListStudentRemoveAp($request);
    }

    /**
     * tạo danh sinh viên rút lớp 30%
     *
     * @param  mixed $request
     * @return void
     */
    public function createListStudentRemoveAp(Request $request) {
        return $this->removeStudentRepository->createListStudentRemoveAp($request);
    }

    /**
     * xóa 1 sinh viên khỏi lớp 30%
     *
     * @param  mixed $id
     * @return void
     */
    public function deleteStudentRemoveAp(Request $request) {
        return $this->removeStudentRepository->deleteStudentRemoveAp($request);
    }

    /**
     * xóa toàn sinh viên khỏi lớp 30%
     *
     * @param  mixed $id
     * @return void
     */
    public function deleteListStudentRemoveAp() {
        return $this->removeStudentRepository->deleteListStudentRemoveAp();
    }
}
