<?php

namespace App\Http\Controllers\Api;

use App\Models\Fu\Course;
use App\Models\Fu\Group;
use App\Models\Fu\Subject;
use App\Models\Fu\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Lib;
use Illuminate\Support\Facades\DB;

class SearchController extends Controller
{
    public function advanceSearch(Request $request)
    {
        $text = $request->text;
        $term_id = $request->term_id;
        $check_user = $request->check_user;
        $check_course = $request->check_course;
        $check_group = $request->check_group;
        $check_subject = $request->check_subject;
        $data = [];
        if ($check_user) {
            $users = User::select('user_surname', 'user_middlename', 'user_givenname', 'user_login', 'user_code')
                ->where('user_login', 'like', "%$text%")
                ->orWhere(DB::raw('CONCAT(user_surname," ",user_middlename," ", user_givenname)'), 'like', "%$text%")
                ->orWhere('user_code', 'like', "%$text%")
                ->limit(10)
                ->orderBy('id', 'desc')
                ->get();
            foreach ($users as $user) {
                $data[] = [
                    'title' => $user->user_login,
                    'user_code' => $user->user_code,
                    'description' => $user->fullname(),
                    'icon' => 'flaticon2-user',
                    'link' => route('admin.profile', [
                        'user_code' => $user->user_code,
                    ])
                ];
            }
        }
        if ($check_course) {
            $course = Course::query();
            if ($term_id) {
                $course->where('term_id', $term_id);
            }
            $course = $course->where(function ($query) use ($text) {
                $query->where('psubject_name', 'like', "%$text%");
                $query->orWhere('psubject_code', 'like', "%$text%");
            })
                ->limit(20)
                ->orderBy('id', 'desc')
                ->get();
            foreach ($course as $item) {
                $data[] = ['title' => $item->psubject_name, 'description' => $item->nameSplit(), 'icon' => 'flaticon2-soft-icons-1'];
            }
        }
        if ($check_group) {
            $groups = Group::where('list_group.is_virtual', 0);
            if ($term_id) {
                $groups->where('pterm_id', $term_id);
            }
            $groups = $groups->select('id', 'group_name', 'psubject_code', 'psubject_name', 'pterm_name')
                    ->where(function ($query) use ($text) {
                    $query->where('psubject_name', 'like', "%$text%");
                    $query->orWhere('psubject_code', 'like', "%$text%");
                    $query->orWhere('group_name', 'like', "%$text%");
                })
                ->limit(20)
                ->orderBy('id', 'desc')
                ->get();
            foreach ($groups as $group) {
                $data[] = ['title' => $group->psubject_name, 'description' => $group->nameSplit(), 'icon' => 'flaticon2-group'];
            }
        }
        if ($check_subject) {
            $subject = Subject::where(function ($query) use ($text) {
                    $query->where('subject_name', 'like', "%$text%");
                    $query->orWhere('subject_code', 'like', "%$text%");
                })
                ->limit(20)
                ->orderBy('id', 'desc')
                ->get();
            foreach ($subject as $item) {
                $data[] = ['title' => $item->subject_name, 'description' => $item->subject_code, 'icon' => 'flaticon2-analytics-1'];
            }
        }


        return Lib::res('success', 'Query success', $data);
    }
}
