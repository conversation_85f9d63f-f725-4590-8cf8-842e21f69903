<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Admin\SystemController;
use App\Http\Lib;
use App\Repositories\Admin\FeeRepository;
use App\Models\Fu\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Fu\Course;
use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use App\Models\Fu\Subject;
use App\Models\Fu\Term;
use App\Models\Sna\TransferStudentSubject;
use App\Models\SystemLog;
use App\Models\T7\CourseGrade;
use Illuminate\Support\Facades\DB;
use App\Utils\ResponseBuilder;
use App\Models\TranferT7Course;
use App\Models\T7\CourseResult;
use App\Models\T7\Grade;
use App\Models\T7\GradeGroup;
use App\Models\T7\GradeSyllabus;
use App\Models\Transcript;
use App\Models\TranscriptDetail;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class UserApiController extends Controller
{
    public function countStatus(Request $request)
    {
        $status = config('status')->trang_thai_hoc;
        $sql = "SELECT COUNT(*) AS tong,study_status FROM user WHERE user_level = 3 GROUP BY study_status";
        $users = User::select(DB::raw('count(*) as value, study_status'))->where('user_level', 3)->where('study_status', '!=', 0)->groupBy('study_status')->get();
        $total = 0;
        foreach ($users as $user) {
            $user->label = $status[$user->study_status]['uid'];
            $user->title = $status[$user->study_status]['value'];
            $total += $user->value;
        }

        return Lib::res('success', 'done', ['users' => $users, 'total' => $total]);
    }

    public function syncDataBasic(Request $request)
    {
        $user_code = $request->user_code;
        $user = User::where('user_code', $user_code)->first();
        if (!$user) {
            return abort(500);
        }
        SystemController::analyzeEnglish($user);

        return true;
    }

    public function sendEmailWhenUpdateWallet(Request $request)
    {
        FeeRepository::sendEmailWhenUpdateWallet($request->data);
    }

    public function getDatasByOption(Request $request)
    {
        $limit = $request->get('limit', 10);
        $page = $request->get('page', 1);
        $keyWord = $request->get('key_word', null);
        $notStudent = $request->get('not_student', null);

        $list = User::select([
            "id",
            DB::Raw("concat(trim( user_surname ), ' ', trim( user_middlename ), ' ', trim( user_givenname )) as full_name"),
            "user_login",
            "user_code",
            "user_surname",
            "user_middlename",
            "user_givenname",
            "user_email",
            "user_address",
            "user_telephone"
        ]);

        if ($keyWord != null) {
            $list = $list->where(function ($q) use ($keyWord) {
                $q = $q->where('user_login', 'like', "%$keyWord%")
                    ->orWhere('user_telephone', 'like', "%$keyWord%")
                    ->orWhere(DB::raw("concat(trim( user_surname ), ' ', trim( user_middlename ), ' ', trim( user_givenname ))"), 'like', "%$keyWord%");
            });
        }

        if ($notStudent == 'true') {
            $list = $list->where('user_level', '!=', 3);
        }

        $list = $list->offset($limit * ($page - 1))
            ->limit($limit)
            ->orderBy('id', 'DESC')
            ->get();

        if ($request->get('select2', null) == null) {
            return ResponseBuilder::Success($list, 'Thành công');
        }

        $res[] = [
            'id' => -1,
            'text' => "Tất cả",
        ];

        foreach ($list as $key => $value) {
            $res[] = [
                'id' => $value->id,
                'text' => ($value->decision_num . " - " . $value->name),
            ];
        }

        return response()->json($res, 200);
    }

    /**
     *  Cập nhập thông tin sinh viên từ CRM
     *
     * <AUTHOR>
     * @since 23/02/2022
     */
    public function updateInforUserFromCrm(Request $request)
    {
        ini_set('max_execution_time', -1);
        try {
            $studentLogin = $request->get('student_login', null);
            if ($studentLogin == null) {
                return ResponseBuilder::Fail('Sinh viên không hợp lệ');
            }

            // Cập nhập thông tin sinh viên
            $currentUser = User::where('user_login', $request->student_login)->first();
            if (!$currentUser) {
                return ResponseBuilder::Fail('Không tồn tại sinh viên');
            } elseif ($currentUser->study_status == 8) {
                // return ResponseBuilder::Fail('Sinh viên đã tốt nghiệp không thể cập nhập');
            }

            DB::beginTransaction();
            $listDataUpdate = DB::select("SELECT
                pstudent_code as user_code,
                dob as user_DOB,
                cmt as cmt,
                cmt_provided_date as ngaycap,
                cmt_provided_where as noicap,
                ptelephone as user_telephone,
                race_name as dantoc,
                gender,
                pphone,
                last_name,
                first_name as user_givenname,
                old_user_code
            FROM people
            WHERE people.pstudent_code = ?", [$currentUser->user_code]);

            foreach ($listDataUpdate as $dataUpdate) {
                $currentUser = User::where('user_code', $dataUpdate->user_code)->first();
                $resUpdate = [];
                $dataLogUpdate = [];
                foreach ($dataUpdate as $key => $value) {
                    if ($key == 'last_name') {
                        $temp_name = explode(' ', $value);
                        if (count($temp_name) > 0) {
                            $first_name = trim($temp_name[0]);
                            unset($temp_name[0]);
                            $middle_name = trim(implode(' ', $temp_name));
                        } else {
                            $first_name = trim($temp_name[0]);
                            $middle_name = '';
                        }

                        if ($currentUser->user_surname != $first_name) {
                            $dataLogUpdate['user_surname'] = [
                                'old_val' => $currentUser->user_surname,
                                'new_val' => $first_name
                            ];

                            $currentUser->user_surname = $first_name;
                            $resUpdate['user_surname'] = $first_name;
                        }

                        if ($currentUser->user_middlename != $middle_name) {
                            $dataLogUpdate['user_middlename'] = [
                                'old_val' => $currentUser->user_middlename,
                                'new_val' => $middle_name
                            ];

                            $currentUser->user_middlename = $middle_name;
                            $resUpdate['user_middlename'] = $middle_name;
                        }
                    } elseif ($key != 'user_telephone') {
                        if ($key != 'pphone' && $value != $currentUser->{$key}) {
                            $dataLogUpdate[$key] = [
                                'old_val' => $currentUser->{$key},
                                'new_val' => $value
                            ];

                            $resUpdate[$key] = $value;
                            $currentUser->{$key} = $value;
                        }
                    } else {
                        $user_telephone = '';
                        if ($dataUpdate->pphone != '' && $value != '') {
                            $user_telephone = "$dataUpdate->pphone-$value";
                        } else if ($dataUpdate->pphone != '') {
                            $user_telephone = $dataUpdate->pphone;
                        } else if ($value != '') {
                            $user_telephone = $value;
                        }

                        if ($user_telephone != $currentUser->user_telephone) {
                            $dataLogUpdate['user_telephone'] = [
                                'old_val' => $currentUser->user_telephone,
                                'new_val' => $user_telephone
                            ];

                            $resUpdate[$key] = $user_telephone;
                        }
                    }
                }

                if (count($resUpdate) > 0) {
                    $logUpdate = [];
                    foreach ($dataLogUpdate as $key => $value) {
                        $logUpdate[] = "{ $key: từ (" . $value['old_val'] . ') thành (' . $value['new_val'] . ') }';
                    }

                    $strUpdate = implode(', ', $logUpdate);
                    SystemLog::create([
                        'actor' => auth()->user()->user_login,
                        'object_name' => "user",
                        'log_time' => now(),
                        'action' => "update info",
                        'description' => "Cập nhập thông tin $currentUser->user_login: " . $strUpdate,
                        'relation_login' => $currentUser->user_login,
                        'relation_id' => 0,
                        'brief' => "update info",
                        'from_ip' => request()->ip(),
                        'nganh_cu' => '',
                        'nganh_moi' => '',
                        'ky_chuyen_den' => 0,
                        'ky_thu_cu' => 0,
                    ]);
                    $currentUser->save();
                }
            }

            DB::commit();
            return ResponseBuilder::Success([], 'Cập nhập thông tin thành công');
        } catch (\Throwable $th) {dd($th);
            DB::rollback();
            return ResponseBuilder::Fail('Cập nhập thông tin thất bại');
        }
    }

    public function getCuriculumList()
    {
        $curriculum_list = DB::select("SELECT * FROM curriculum ORDER BY CAST( khoa AS DECIMAL( 3, 1 ) ) ASC");
        $result = [
            'curriculum_list' => $curriculum_list,
        ];

        return response()->json($result, 200);
    }

    public function getListStudentByCreatedAt(Request $request) {
        try {
            $rules = [
                'datetime_from' => 'required|date_format:Y-m-d H:i:s',
                'datetime_to' => 'date_format:Y-m-d H:i:s',
            ];

            $messages = [
                'datetime_from.required' => 'datetime_from không được bỏ trống',
                'datetime_from.date_format' => 'datetime_from không đúng định dạng Y-m-d H:i:s',
                'datetime_to.date_format' => 'datetime_to không đúng định dạng Y-m-d H:i:s',
            ];

            $validator = Validator::make($request->all(), $rules, $messages);

            if($validator->fails()) {
                $mess = $validator->errors()->first();
                return ResponseBuilder::fail($mess);
            }

            $user = User::select(
                    'user_code as student_code',
                    'user_login as student_login',
                    DB::raw("CONCAT(user_surname, ' ', user_middlename, ' ', user_givenname) as student_fullname"),
                    'user_email as student_email'
                );

            $user->where('user_level', 3);
            $user->orderBy('created_at');

            if (isset($request->datetime_to)) {
                $user->whereBetween('created_at' ,[$request->datetime_from, $request->datetime_to]);
            } else {
                $user->where('created_at' , '>=', $request->datetime_from);
            }

            $data = $user->get();

            if (count($data) == 0) {
                return ResponseBuilder::Fail("Không có sinh viên mã số hoặc email này!", []);
            }

            return ResponseBuilder::Success($data, "Success");
        } catch (\Throwable $th) {
            Log::error('getListStudentByCreatedAt: ' . $th->getFile() . ' - ' . $th->getLine() . ' - ' . $th->getMessage());
            return ResponseBuilder::Fail($th->getMessage());
        }
    }
}
