<?php

namespace App\Http\Controllers\Api;

use App\Http\Lib;
use App\Models\Fu\Block;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Utils\ResponseBuilder;



class BlockController extends Controller
{
    public function getDatasByOption(Request $request)
    {
        $list = Block::query()
        ->leftJoin('term', 'term.id', '=', 'block.term_id')
        ->select([
            'block.id',
            'block.block_name',
            'block.term_id',
            'term.term_name'
        ]);
        
        $term_id = $request->get('term_id', -1);
        $limit = $request->get('limit', 20);
        $page = $request->get('page', 1);
        if ($term_id != null && $term_id != -1) {
            $list = $list->where('term_id', $term_id);
        }
        
        $list = $list->offset($limit * ($page - 1))
        ->limit($limit)
        ->orderBy('block.term_id', 'DESC')
        ->orderBy('block.id', 'DESC')
        ->get();

        if ($request->get('select2', null) == null) {
            return ResponseBuilder::Success($list, 'Thành công');
        }

        $res[] = [
            'id' => -1,
            'text' => "Tất cả các block",
        ];
        foreach ($list as $key => $value) {
            $res[] = [
                'id' => $value->id,
                'text' => ($value->term_name . " - " . $value->block_name),
            ];
        }

        return response()->json($res, 200);
    }
}
