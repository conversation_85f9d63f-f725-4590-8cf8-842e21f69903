<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Repositories\Admin\OtherManageRepository;
use Illuminate\Support\Facades\Auth;

class OtherManageController extends Controller
{
    protected $otherManageRepository;

    public function __construct(OtherManageRepository $otherManageRepository) {
        $this->otherManageRepository = $otherManageRepository;
    }

    // Area controller

    /**
     * lấy danh sách khu
     *
     * @return void
     */
    public function getListArea() {
        return $this->otherManageRepository->getListArea();
    }

    /**
     * tạo mới khu
     *
     * @param  mixed $request
     * @return void
     */
    public function createdArea(Request $request) {
        return $this->otherManageRepository->createdArea($request);
    }

    // Room controller

    /**
     * lấy danh sách phòng
     *
     * @param  mixed $request
     * @return void
     */
    public function getListRoom(Request $request) {
        return $this->otherManageRepository->getListRoom($request);
    }

    /**
     * lấy danh sách phòng
     *
     * @param  mixed $request
     * @return void
     */
    public function getListRoomByType(Request $request) {
        return $this->otherManageRepository->getListRoomByType($request);
    }

    /**
     * tạo mới phòng
     *
     * @param  mixed $request
     * @return void
     */
    public function createdRoom(Request $request) {
        return $this->otherManageRepository->createdRoom($request);
    }

    /**
     * chỉnh sửa phòng
     *
     * @param  mixed $request
     * @param  integer $id
     * @return void
     */
    public function editRoom(Request $request, $id) {
        return $this->otherManageRepository->editRoom($request, $id);
    }

    /**
     * xóa phòng
     *
     * @param  mixed $id
     * @return void
     */
    public function deleteRoom($id) {
        return $this->otherManageRepository->deleteRoom($id);
    }

    // Term controller
    /**
     * lấy danh sách học kỳ
     *
     * @param  mixed $request
     * @return void
     */
    public function getListTerm(Request $request) {
        return $this->otherManageRepository->getListTerm($request);
    }

    /**
     * tạo mới block
     *
     * @param  mixed $request
     * @return void
     */
    public function createdBlock(Request $request) {
        return $this->otherManageRepository->createdBlock($request);
    }

    /**
     * chỉnh sửa block
     *
     * @param  mixed $request
     * @param  integer $id
     * @return void
     */
    public function editBlock(Request $request, $id) {
        return $this->otherManageRepository->editBlock($request, $id);
    }

    /**
     * xóa block
     *
     * @param  mixed $id
     * @return void
     */
    public function deleteBlock($id) {
        return $this->otherManageRepository->deleteBlock($id);
    }    

    // Slot controller
    /**
     * lấy danh sách ca học
     *
     * @return void
     */
    public function getListSlot() {
        return $this->otherManageRepository->getListSlot();
    }

    /**
     * chỉnh sửa ca học
     *
     * @param  mixed $request
     * @param  integer $id
     * @return void
     */
    public function editSlot(Request $request, $id) {
        return $this->otherManageRepository->editSlot($request, $id);
    }

        /**
     * chỉnh sửa ca học
     *
     * @param  mixed $request
     * @return void
     */
    public function createSlot(Request $request) {
        return $this->otherManageRepository->createSlot($request);
    }

    /**
     * xóa ca học
     *
     * @param  mixed $id
     * @return void
     */
    public function deleteSlot($id) {
        return $this->otherManageRepository->deleteSlot($id);
    }
    /**
     * Kiểm tra quyền của người dùng
     */
    public function checkUserPermission(Request $request) {
        $role_id = (int)$request->role_id;
        $roles = session('roles');
        if (in_array($role_id, $roles)) {
            return true;
        }
        return false;
    }
    
    /**
     * @todo Tạo kỳ
     * <AUTHOR>
     * @since 02/04/0205
     * 
     * @return void
     * 
     */
    public function createTerm(Request $request) {
        return $this->otherManageRepository->createTerm($request);
    }

    /**
     * @todo Sửa thông tin kỳ
     * @return void
     * 
     */
    public function saveTermChange(Request $request) {
        return $this->otherManageRepository->saveTermChange($request);
    }

    public function getListKhoaNhapHoc(Request $request) {
        return $this->otherManageRepository->getListKhoaNhapHoc($request);
    }

    public function createKhoaNhapHoc(Request $request) {
        return $this->otherManageRepository->createKhoaNhapHoc($request);
    }

    public function deleteKhoaNhapHoc(Request $request) {
        $id = $request->id;
        return $this->otherManageRepository->deleteKhoaNhapHoc($request, $id);
    }

    public function getListTeacher(Request $request) {
        return $this->otherManageRepository->getListTeacher($request);
    }
}
