<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Repositories\Admin\EducateRepository;
use Illuminate\Http\Request;

class GradeController extends Controller
{
    public function __construct(EducateRepository $educateRepository)
    {
        $this->EducateRepository = $educateRepository;
    }

    public function getInfoExportGradeBook(Request $request)
    {
        return $this->EducateRepository->getInfoExportGradeBook($request);
    }
    public function getExportGradeBook(Request $request)
    {
        return $this->EducateRepository->getExportGradeBook($request);
    }
    public function getListExistedFileGradebook(Request $request)
    {
        return $this->EducateRepository->getListExistedFileGradebook($request);
    }
    public function getDownloadExistedFileGradebook(Request $request)
    {
        return $this->EducateRepository->getDownloadExistedFileGradebook($request);
    }
}
