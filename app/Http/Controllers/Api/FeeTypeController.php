<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Fee\FeeType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class FeeTypeController extends Controller
{
    /**
     * Get all fee types with pagination
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $query = FeeType::query();
            
            // Log để debug
            Log::info('FeeType query started', ['request' => $request->all()]);
            
            // Search by keyword
            if ($request->has('keyword') && !empty($request->keyword)) {
                $keyword = $request->keyword;
                $query->where(function($q) use ($keyword) {
                    $q->where('code', 'like', "%{$keyword}%")
                      ->orWhere('name', 'like', "%{$keyword}%");
                });
            }
            
            // Filter by status
            if ($request->has('status') && $request->status !== '') {
                $query->where('is_active', $request->status == 'active' ? 1 : 0);
            }
            
            // Order by
            $orderBy = $request->order_by ?? 'display_order';
            $orderDir = $request->order_dir ?? 'asc';
            $query->orderBy($orderBy, $orderDir);
            
            $perPage = $request->per_page ?? 15;
            $feeTypes = $query->paginate($perPage);
            
            // Log để debug
            Log::info('FeeType query completed', ['count' => $feeTypes->count(), 'total' => $feeTypes->total()]);
            
            return response()->json($feeTypes);
        } catch (\Exception $e) {
            Log::error('Error fetching fee types: ' . $e->getMessage());
            return response()->json(['message' => 'Có lỗi xảy ra khi tải dữ liệu'], 500);
        }
    }

    /**
     * Get active fee types
     *
     * @return \Illuminate\Http\Response
     */
    public function getActive()
    {
        try {
            $feeTypes = FeeType::where('is_active', true)
                ->orderBy('display_order')
                ->get();
                
            return response()->json($feeTypes);
        } catch (\Exception $e) {
            Log::error('Error fetching active fee types: ' . $e->getMessage());
            return response()->json(['message' => 'Có lỗi xảy ra khi tải dữ liệu'], 500);
        }
    }

    /**
     * Get fee types as key-value pairs
     *
     * @return \Illuminate\Http\Response
     */
    public function getAsKeyValue()
    {
        try {
            $feeTypes = FeeType::where('is_active', true)
                ->orderBy('display_order')
                ->get(['id', 'code', 'name']);
                
            return response()->json($feeTypes);
        } catch (\Exception $e) {
            Log::error('Error fetching fee types as key-value: ' . $e->getMessage());
            return response()->json(['message' => 'Có lỗi xảy ra khi tải dữ liệu'], 500);
        }
    }

    /**
     * Get a specific fee type
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $feeType = FeeType::findOrFail($id);
            return response()->json($feeType);
        } catch (\Exception $e) {
            Log::error('Error fetching fee type: ' . $e->getMessage());
            return response()->json(['message' => 'Không tìm thấy loại phí'], 404);
        }
    }

    /**
     * Store a new fee type
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'code' => 'required|string|max:20|unique:fee_types',
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'display_order' => 'nullable|integer|min:0',
                'is_active' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            $feeType = new FeeType();
            $feeType->code = $request->code;
            $feeType->name = $request->name;
            $feeType->description = $request->description;
            $feeType->display_order = $request->display_order ?? 0;
            $feeType->is_active = $request->is_active ?? true;
            $feeType->save();

            return response()->json([
                'message' => 'Loại phí đã được tạo thành công',
                'data' => $feeType
            ], 201);
        } catch (\Exception $e) {
            Log::error('Error creating fee type: ' . $e->getMessage());
            return response()->json(['message' => 'Có lỗi xảy ra khi tạo loại phí'], 500);
        }
    }

    /**
     * Update a fee type
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $feeType = FeeType::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'display_order' => 'nullable|integer|min:0',
                'is_active' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            $feeType->name = $request->name;
            $feeType->description = $request->description;
            $feeType->display_order = $request->display_order ?? 0;
            $feeType->is_active = $request->is_active ?? true;
            $feeType->save();

            return response()->json([
                'message' => 'Loại phí đã được cập nhật thành công',
                'data' => $feeType
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating fee type: ' . $e->getMessage());
            return response()->json(['message' => 'Có lỗi xảy ra khi cập nhật loại phí'], 500);
        }
    }

    /**
     * Delete a fee type
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $feeType = FeeType::findOrFail($id);
            $feeType->delete();

            return response()->json([
                'message' => 'Loại phí đã được xóa thành công'
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting fee type: ' . $e->getMessage());
            return response()->json(['message' => 'Có lỗi xảy ra khi xóa loại phí'], 500);
        }
    }
}
