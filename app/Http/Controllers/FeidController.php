<?php

namespace App\Http\Controllers;

use App\Repositories\FeidRepository;
use Illuminate\Http\Request;

class FeidController extends Controller
{
    protected $feidRepository;
    public function __construct(FeidRepository $feidRepository)
    {
        $this->feidRepository = $feidRepository;
    }

    public function index(Request $request)
    {
        return $this->feidRepository->index($request);
    }

    public function importFileEmailStudents(Request $request)
    {
        return $this->feidRepository->importFileEmailStudents($request);
    }

    public function setSessionCampusCode(Request $request)
    {
        return $this->feidRepository->setSessionCampus($request);
    }

    public function getCampus()
    {
        return $this->feidRepository->listAllCampusCode();
    }

    public function storeAndCreateAccountFeid(Request $request)
    {
        try {
            return $this->feidRepository->storeAndCreateAccountFeid($request);
        } catch (\Throwable $th) {
            return $th->getMessage();
        }
    }

    public function getLoopTimes(Request $request)
    {
        return $this->feidRepository->getLoopTimes($request);
    }
    public function exportFileStudentsNonFeid(Request $request)
    {
        return $this->feidRepository->exportFileStudentsNonFeid($request);
    }

    public function createFeidForNonFeidAccountMongo(Request $request)
    {
        return $this->feidRepository->createFeidForNonFeidAccountMongo($request);
    }

    public function updateFeidForUpdatedFeidAccountMongo(Request $request)
    {
        return $this->feidRepository->updateFeidForUpdatedFeidAccountMongo($request);
    }

    public function getProcessLoopTime(Request $request)
    {
        return $this->feidRepository->getProcessLoopTime($request);
    }

    public function checkCurrentCampusOfUser (Request $request)
    {
        return $this->feidRepository->checkCurrentCampusOfUser($request);
    }

    public function updateUserOnMongoDB(Request $request) {
        return $this->feidRepository->updateUserOnMongoDB($request);
    }

}
