<?php

namespace App\Http\Controllers\Auth;

use App\Http\Lib;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\Log;
use App\Models\Dra\T1UserRole;
use App\Models\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class SocialController extends Controller
{
    public function redirectToProvider(Request $request)
    {
        return Socialite::driver('google')->redirect();
    }

    public function handleProviderCallback($email = null)
    {
        try {
            $user = Socialite::driver('google')->user();
            $email = $user->getEmail();

            $user = User::where('user_email', $email)->whereIn('user_level', [1, 4])->first();
            Auth::loginUsingId($user->id);
            Lib::loadPermission(Auth::user());
            Lib::loadSession();

            if (session()->has('url')) {
                return redirect(session('url'));
            }
            return redirect()->route('admin.home');
        } catch (\Throwable $th) {
            Log::error($th);
            return redirect(route('home'))->with('message', "Đăng nhập không thành công!");
        }
    }

    public function loginAsUser($user_id)
    {
        try {
            $user_login = Auth::user()->user_login;
            $checkAdmin = T1UserRole::where('user_login', $user_login)->where('role_id', 1)->firstOrFail();
            $user_login_as = User::where('id', $user_id)->whereNotIn('user_level', [2, 3, 11])->firstOrFail();
            return $this->handleProviderCallback($user_login_as->user_email);
        } catch (\Throwable $th) {
            Log::error($th);
            return redirect()->back()->with('message', "Đăng nhập vào tài khoản cán bộ không thành công!");
        }
    }

    public function loginByPassword(Request $request){
        try {
            $validator = Validator::make($request->all(), [
                'user_login' => 'required',
                'user_password' => 'required',
            ], [
                'required' => "Không được bỏ trống :attribute",
            ], [
                'user_login' => "Tên đăng nhập",
                'user_password' => "Mật khẩu",
            ]);

            if ($validator->fails()) {
                return redirect()->back()->with([
                    'message' => $validator->errors()->first()
                ]);
            }
            
            $account = Arr::get($request, 'user_login');
            $password = Arr::get($request, 'user_password');
            $user = User::where(function($query) use ($account){
                    $query->where('user_login', $account)
                    ->orWhere('user_email', $account);
                })
                ->whereIn('user_level', [1, 4])
                ->first();
            if(!$user){
                return redirect()->back()->with('message', "Tài khoản không tồn tại!");
            }
            if (Hash::check($password, $user->user_pass)) {
                Auth::login($user, $request->has('remember'));
                Lib::loadPermission(Auth::user());
                Lib::loadSession();
                if (session()->has('url')) {
                    return redirect(session('url'));
                }
                return redirect()->route('admin.home');
            }
            return redirect()->back()->with('message', "Đăng nhập không thành công!");
        } catch (\Throwable $th) {
            Log::error($th);
            return redirect()->back()->with('message', "Đăng nhập không thành công!");
        }
    }
}
