<?php

namespace App\Http\Controllers\Admin;

use App\Repositories\Admin\GraduationRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class GraduationController extends Controller
{
    protected $GraduationRepository;

    public function __construct(GraduationRepository $graduationRepository)
    {
        $this->GraduationRepository = $graduationRepository;
    }

    public function exportTotNghiep(Request $request)
    {
        return $this->GraduationRepository->exportTotNghiep($request);
    }

    public function exportTotNghiepAll(Request $request)
    {
        return $this->GraduationRepository->exportTotNghiepAll($request);
    }

    // public function exportBangDiemDoiSoat(Request $request)
    // {
    //     return $this->GraduationRepository->exportBangDiemDoiSoat($request);
    // }

    public function bangDiemDoiSoat()
    {
        return $this->GraduationRepository->bangDiemDoiSoat();
    }

    public function syncing()
    {
        return $this->GraduationRepository->syncing();
    }

    public function inBangDiem(Request $request)
    {
        return $this->GraduationRepository->inBangDiem($request);
    }

    public function exportSoGoc($campaign_id)
    {
        return $this->GraduationRepository->exportSoGoc($campaign_id);
    }

    public function campaign(Request $request)
    {
        return $this->GraduationRepository->campaign($request);
    }

    public function campaignCreate(Request $request)
    {
        return $this->GraduationRepository->campaignCreate($request);
    }

    public function campaignStore(Request $request)
    {
        return $this->GraduationRepository->campaignStore($request);
    }

    public function createTranscipt(Request $request)
    {
        return $this->GraduationRepository->createTranscipt($request);
    }
    
    public function campaignFindStudent($campaign_id, Request $request)
    {
        return $this->GraduationRepository->campaignFindStudent($campaign_id, $request);
    }

    public function campaignDetail($campaign_id, Request $request)
    {
        return $this->GraduationRepository->campaignDetail($campaign_id, $request);
    }

    public function exportBangDiemDoiSoatAll(Request $request)
    {
        return $this->GraduationRepository->exportBangDiemDoiSoatAll($request);
    }

    /**
     * 
     * Export Bảng điểm excel
     * 
     * <AUTHOR>
     * @since 04/10/2022
     * @version 1.0
     */
    public function exportBangDiemExcel(Request $request)
    {
        return $this->GraduationRepository->exportBangDiemExcel($request);
    }

    public function createCampaign(Request $request)
    {
        return $this->GraduationRepository->createCampaign($request);
    }

    /**
     * 
     * Thêm quyết định
     * 
     * <AUTHOR>
     * @since 28/02/2022
     * @version 1.0
     */
    public function addDecision(Request $request)
    {
        return $this->GraduationRepository->addDecision($request);
    }

    /**
     * Hạ loại xếp hạng tốt nghiệp
     * 28/9/2023
     */
    public function downRankGraduateStudent(Request $request)
    {
        return $this->GraduationRepository->downRankGraduateStudent($request);
    }
}
