<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\DataTables\SubjectUpdateAbleDataTable;
use Illuminate\Http\Request;

use App\Repositories\Admin\SubjectUpdateAbleRepository;

class SubjectUpdateAbleController extends Controller
{
    protected $SubjectUpdateAbleRepository;
    public function __construct(SubjectUpdateAbleRepository $SubjectUpdateAbleRepository)
    {
        $this->SubjectUpdateAbleRepository = $SubjectUpdateAbleRepository;
    }

    /**
     * Display a listing of the decision.
     */
    public function index(Request $request)
    {
        return $this->SubjectUpdateAbleRepository->index($request);
    }
    
    /**
     * Show the form for creating a new decision.
     */
    public function create(Request $request)
    {
        return $this->SubjectUpdateAbleRepository->createFrom($request);
    }

    /**
     * Store a newly created decision in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     */
    public function store(Request $request)
    {
        return $this->SubjectUpdateAbleRepository->store($request);
    }

    /**
     * Show the form for editing the specified resource.
     * 
     * @param  Integer $id
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function edit($old_subject_code, Request $request)
    {
        return $this->SubjectUpdateAbleRepository->edit($old_subject_code, $request);
    }

    /**
     * Update the specified resource in storage.
     *
     * 
     * @param  Integer $id
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update($id, Request $request)
    {
        return $this->SubjectUpdateAbleRepository->update($id, $request);
    }

    /**
     * Update the specified resource in storage.
     *
     * 
     * @param  Integer $id
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function removeSubjectAble(Request $request)
    {
        return $this->SubjectUpdateAbleRepository->removeSubjectAble($request);
    }
    
    // /**
    //  * Remove the specified resource from storage.
    //  *
    //  * @return \Illuminate\Http\Response
    //  */
    // public function destroy(Request $request)
    // {
        
    // }

    /**
     * Datatable ajax request
     *
     * @return \Illuminate\Http\Response
     */
    public function datatable(SubjectUpdateAbleDataTable $datatable)
    {
        return $datatable->build()->toJson();
    }


    // /**
    //  * export data Decision
    //  *
    //  * @return \Illuminate\Http\Response
    //  */
    // public function ExportData(Request $request)
    // {
    //     return $this->SubjectUpdateAbleRepository->ExportData($request);
    // }

    /**
     * load data loadSelect2Decision
     *
     * @return \Illuminate\Http\Response
     */
    public function loadSelectSubject(Request $request)
    {
        return $this->SubjectUpdateAbleRepository->loadSelectSubject($request);
    }

    public function import(Request $request) {
        return $this->SubjectUpdateAbleRepository->import($request);
    }

}
