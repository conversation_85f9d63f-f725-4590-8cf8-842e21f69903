<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\DataTables\GroupGraduatedataTable;
use App\Repositories\Admin\CalendarRepository;

class CalendarController extends Controller
{
    /**
     * @var CalendarRepository
     */
    var $calendarRepository;

    public function __construct(CalendarRepository $calendarRepository)
    {
        $this->calendarRepository = $calendarRepository;
    }

    public function check_php()
    {
        phpinfo();
    }

    public function test_export()
    {
        \Debugbar::disable();

        $writer = WriterEntityFactory::createXLSXWriter();

        $fileName = "Test_Export.xlsx";
        $writer->openToBrowser($fileName); // stream data directly to the browser

        $cells = [
            WriterEntityFactory::createCell('Carl'),
            WriterEntityFactory::createCell('is'),
            WriterEntityFactory::createCell('great!'),
        ];

        /** add a row at a time */
        $singleRow = WriterEntityFactory::createRow($cells);
        $writer->addRow($singleRow);

        /** add multiple rows at a time */
        $multipleRows = [
            WriterEntityFactory::createRow($cells),
            WriterEntityFactory::createRow($cells),
        ];
        $writer->addRows($multipleRows);

        /** Shortcut: add a row from an array of values */
        $values = ['Carl', 'is', 'great!'];
        $rowFromValues = WriterEntityFactory::createRowFromArray($values);
        $writer->addRow($rowFromValues);

        $writer->close();
    }

    public function danh_sach_thi(Request $request)
    {
        return $this->calendarRepository->danhSachThi($request);
    }

    public function export_danh_sach_thi(Request $request)
    {
        return $this->calendarRepository->exportDanhSachThi($request);
    }

    public function export_danh_sach_thi_new(Request $request)
    {
        return $this->calendarRepository->exportListStudentExam($request);
    }
    
    public function exportGroupsExamDetail(Request $request) 
    {
        return $this->calendarRepository->exportGroupsExamDetail($request);
    }

    /**
     * Datatable ajax request
     *
     * @return \Illuminate\Http\FeedbackdataTable
     */
    public function datatable(GroupGraduatedataTable $datatable)
    {
        return $datatable->build()->toJson();
    }

    function syncGraduateCalendar(Request $request)
    {
        return $this->calendarRepository->syncGraduateCalendar($request);
    }
}
