<?php

namespace App\Http\Controllers\Admin;

use App\Repositories\Admin\LectureLessonRepository;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;


class LectureLessonController extends Controller
{
    public function __construct(LectureLessonRepository $LectureLessonRepository) {
        $this->LectureLessonRepository = $LectureLessonRepository;
    }

    public function indexLectureLesson() 
    {
        return $this->LectureLessonRepository->indexLectureLesson();
    }

    public function getListLectureLesson(Request $request) {
        return $this->LectureLessonRepository->getListLectureLesson($request);
    }
}