<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests\Admin\DisciplineExamRegulationRequest;

use App\Repositories\Admin\DisciplineExamRegulationRepository;

class DisciplineExamRegulationController extends Controller
{
    protected $DisciplineExamRegulationRepository;
    public function __construct(DisciplineExamRegulationRepository $disciplineExamRegulationRepository)
    {
        $this->DisciplineExamRegulationRepository = $disciplineExamRegulationRepository;
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        return $this->DisciplineExamRegulationRepository->index($request);
    }

    public function create()
    {
        return $this->DisciplineExamRegulationRepository->add();
    }

    public function store(DisciplineExamRegulationRequest $request)
    {
        return $this->DisciplineExamRegulationRepository->store($request);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        return $this->DisciplineExamRegulationRepository->viewDiscipline($id);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        return $this->DisciplineExamRegulationRepository->updateDisciplineExam($request, $id);
    }

    public function export($id)
    {
        return $this->DisciplineExamRegulationRepository->exportExamFile($id);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
