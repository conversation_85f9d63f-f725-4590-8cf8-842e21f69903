<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Fu\User;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx as ReaderXlxs;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Facades\DB;
use App\Models\Sms\StudentSms;
use App\Models\Sms\StudentSmsList;
use App\Models\Sms\StudentSmsFormat;
use App\Models\Sms\StudentSmsCategory;
use App\Models\Sms\TelecoNumber;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class SmsController extends Controller
{
    public function view_sms()
    {
        return view(env('THEME_ADMIN') . '.sms.view_sms');
    }
}
