<?php

namespace App\Http\Controllers\Admin;

use App\Http\Lib;
use App\Http\Requests\Admin\PermissionRequest;
use App\Repositories\Admin\PermissionRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class PermissionController extends Controller
{
    public function __construct(PermissionRepository $permissionRepository)
    {
        $this->PermissionRepository = $permissionRepository;
    }

    public function index()
    {
        return $this->PermissionRepository->index();
    }

    public function store(PermissionRequest $request)
    {
        return $this->PermissionRepository->store($request);
    }

    public function edit($id)
    {
        return $this->PermissionRepository->edit($id);
    }

    public function delete(Request $request)
    {
        return $this->PermissionRepository->delete($request);
    }
}
