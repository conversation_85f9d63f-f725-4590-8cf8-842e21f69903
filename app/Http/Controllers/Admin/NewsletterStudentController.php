<?php

namespace App\Http\Controllers\Admin;

use App\Repositories\Admin\NewsletterStudentRepository;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;


class NewsletterStudentController extends Controller
{
    protected $NewsletterStudentRepository;

    public function __construct(NewsletterStudentRepository $NewsletterStudentRepository)
    {
        $this->NewsletterStudentRepository = $NewsletterStudentRepository;
    }

    public function indexNewsletterStudent()
    {
        return $this->NewsletterStudentRepository->indexNewsletterStudent();
    }

    public function getListNewsletterStudent(Request $request)
    {
        return $this->NewsletterStudentRepository->getListNewsletterStudent($request);
    }

    public function createEditNewsletterStudent(Request $request)
    {
        return $this->NewsletterStudentRepository->createEditNewsletterStudent($request);
    }

    public function deleteNewsletterStudent(Request $request)
    {
        return $this->NewsletterStudentRepository->deleteNewsletterStudent($request);
    }

    public function updateStateNewsletterStudent(Request $request)
    {
        return $this->NewsletterStudentRepository->updateStateNewsletterStudent($request);
    }
}
