<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\Admin\DropOutRequest;
use App\Models\T7\AcademicDecisionManagement;
use App\Repositories\Admin\DropOutRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class DropOutController extends Controller
{
    public $DropOutRepository;
    public function __construct(DropOutRepository $dropOutRepository)
    {
        $this->DropOutRepository = $dropOutRepository;
    }

    public function index()
    {
        return $this->DropOutRepository->index();
    }

    public function store(DropOutRequest $request)
    {
        return $this->DropOutRepository->store($request);
    }

    public function listDropOut(Request $request)
    {
        return $this->DropOutRepository->listDropOut($request);
    }

    public function viewDropOut($id)
    {
        return $this->DropOutRepository->viewDropOut($id);
    }
}
