<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\Admin\UserRequest;
use App\Repositories\Admin\UserRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\DataTables\RcmPulldataTable;
use Svg\Tag\Rect;

class UserController extends Controller
{
    protected $UserRepository;
    public function __construct(UserRepository $userRepository)
    {
        $this->UserRepository = $userRepository;
    }

    public function create(Request $request)
    {
        return $this->UserRepository->create($request);
    }

    public function createUser(Request $request)
    {
        return $this->UserRepository->createUser($request);
    }

    public function index(Request $request)
    {
        return $this->UserRepository->index($request);
    }

    public function edit($id)
    {
        return $this->UserRepository->edit($id);
    }

    public function store(UserRequest $request)
    {
        return $this->UserRepository->store($request);
    }

    public function lockUser($id)
    {
        return $this->UserRepository->lockUser($id);
    }

    public function export(Request $request)
    {
        return $this->UserRepository->export($request);
    }

    public function newExport(Request $request)
    {
        return $this->UserRepository->newExport($request);
    }

    public function profile(Request $request)
    {
        return $this->UserRepository->profile($request);
    }

    public function print_form()
    {
        return $this->UserRepository->print_form();
    }
    public function print_result(Request $request)
    {
        return $this->UserRepository->print_result($request);
    }

    public function getListStatusStudent3Ben(Request $request)
    {
        return $this->UserRepository->getListStatusStudent3Ben($request);
    }

    /**
     * <AUTHOR>
     * Lấy token đăng nhập
     */
    public function getTokenLogin(Request $request)
    {
        return $this->UserRepository->getTokenLogin($request);
    }

    // /**
    //  * <AUTHOR>
    //  * quản lý kéo sinh viên từ CRM
    //  */
    // public function managerPullUser(Request $request)
    // {
    //     return $this->UserRepository->managerPullUser($request);
    // }

    /**
     * <AUTHOR>
     * Thêm mới đợt chiến dịch
     */
    public function storePullUser(Request $request)
    {
        return $this->UserRepository->storePullUser($request);
    }

    /**
     * <AUTHOR>
     * datatable
     */
    public function rcmPullDatatable(RcmPulldataTable $datatable)
    {
        return $datatable->build()->toJson();
    }

    // /**
    //  * Tải file email
    //  */
    // public function rcmExport(Request $request)
    // {
    //     return $this->UserRepository->rcmExport($request);
    // }


    // /**
    //  * quản lý kéo sinh viên từ CRM
    //  */
    // public function updateDetailsUser(Request $request)
    // {
    //     return $this->UserRepository->updateDetailsUser($request);
    // }

    /**
     * Import thêm sinh viên vào chiến dịch
     */
    public function updatePullUser(Request $request)
    {
        return $this->UserRepository->updatePullUser($request);
    }

    /**
     * Import thêm sinh viên vào chiến dịch
     */
    public function updatePullUserNoId(Request $request)
    {
        return $this->UserRepository->updatePullUserNoId($request);
    }

    // public function syncStudent(Request $request)
    // {
    //     return $this->UserRepository->syncStudent($request);
    // }

    public function loginToUser($id)
    {
        return $this->UserRepository->loginToUser($id);
    }

    /**
     * Import users from excel file
     *
     * @param Request $request
     * @return mixed
     */
    public function importUsers(Request $request)
    {
        return $this->UserRepository->importUsers($request);
    }

    public function resetPassword(Request $request)
    {
        return $this->UserRepository->resetPassword($request);
    }

    public function changePassword(Request $request)
    {
        return $this->UserRepository->changePassword($request);
    }
}
