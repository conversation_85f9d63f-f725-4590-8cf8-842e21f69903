<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SessionType;
use App\Utils\ResponseBuilder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SessionTypeController extends Controller
{
    /**
     * Display a listing of session types
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $sessionTypes = SessionType::orderBy('id', 'asc')->get();
        return view('admin_v1.session_type.index', compact('sessionTypes'));
    }

    /**
     * Show the form for creating a new session type
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin_v1.session_type.create');
    }

    /**
     * Store a newly created session type in storage
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function createSessionType(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'order' => 'required|numeric',
                'session_type' => 'required|string|max:255|unique:session_type,session_type',
            ], [
                'order.required' => 'Vui lòng nhập thứ tự',
                'order.numeric' => 'Thứ tự phải là số',
                'session_type.required' => 'Vui lòng nhập loại buổi học',
                'session_type.string' => 'Loại buổi học phải là chuỗi',
                'session_type.max' => 'Loại buổi học không được vượt quá 255 ký tự',
                'session_type.unique' => 'Loại buổi học đã tồn tại'
            ]);
            if ($validator->fails()) {
                return ResponseBuilder::Fail($validator->errors()->first());
            }
    
            $sessionType = SessionType::create([
                'order' => $request->order,
                'session_type' => $request->session_type,
                'description' => $request->description,
            ]);
    
            return ResponseBuilder::Success($sessionType, 'Tạo thành công!');
        } catch (\Exception $e) {
            Log::error($e);
            return ResponseBuilder::Fail($e->getMessage());
        }
    }

    /**
     * Update the specified session type in storage
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function editSessionType(Request $request)
    {
        try {
            $sessionType = SessionType::find($request->id);
            if (!$sessionType) {
                return ResponseBuilder::Fail('Loại buổi học không tồn tại.');
            }

            $checkSessionType = SessionType::where('session_type', $request->session_type)->where('id', '!=', $request->id)->first();
            if ($checkSessionType) {
                return ResponseBuilder::Fail('Loại buổi học đã tồn tại.');
            }

            $sessionType->session_type = $request->session_type;
            $sessionType->description = $request->description;
            $sessionType->order = $request->order;
            $sessionType->save();
    
            return ResponseBuilder::Success($sessionType, 'Cập nhật thành công!');
        } catch (\Exception $e) {
            Log::error($e);
            return ResponseBuilder::Fail($e->getMessage());
        }
    }

    /**
     * Remove the specified session type from storage
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function deleteSessionType($id)
    {
        try {
            $sessionType = SessionType::findOrFail($id);
            $sessionType->delete();
            return ResponseBuilder::Success($sessionType, 'Xóa thành công!');
        } catch (\Exception $e) {
            Log::error($e);
            return ResponseBuilder::Fail($e->getMessage());
        }
    }

    /**
     * API endpoint to get all session types
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllSessionTypes()
    {
        $sessionTypes = SessionType::select([
            'id', 
            'session_type', 
            'description', 
            'order',
        ])->orderBy('order', 'asc')->get();
        return ResponseBuilder::success($sessionTypes);
    }
}
