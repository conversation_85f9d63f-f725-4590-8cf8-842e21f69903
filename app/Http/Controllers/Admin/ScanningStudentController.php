<?php

namespace App\Http\Controllers\Admin;

use App\Repositories\Admin\ScanningStudentRepository;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ScanningStudentController extends Controller
{
    protected $scanningStudentRepository;
    
    public function __construct(ScanningStudentRepository $scanningStudentRepository) {
        $this->scanningStudentRepository = $scanningStudentRepository;
    }

    public function getListScanningStudent(Request $request) {
        return $this->scanningStudentRepository->getListScanningStudent($request);
    }

    /**
     * importFee - dùng để get data phí sinh viên, phụ<PERSON> vụ chính cho tool quét trạng thái sinh viên
     *
     * @param  mixed $request
     * @return void
     */
    public function getDataImportFee(Request $request) {
        return $this->scanningStudentRepository->getDataImportFee($request);
    }

    /**
     * importFee - dùng để import phí sinh viên, ph<PERSON><PERSON> vụ chính cho tool quét trạng thái sinh viên
     *
     * @param  mixed $request
     * @return void
     */
    public function importFee(Request $request) {
        return $this->scanningStudentRepository->importFee($request);
    }
}
