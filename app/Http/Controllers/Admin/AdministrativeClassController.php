<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\Admin\AdministrativeClassRepository;
use Illuminate\Http\Request;

class AdministrativeClassController extends Controller
{
    public $administrativeClassRepository;
    public function __construct(AdministrativeClassRepository $administrativeClassRepository)
    {
        $this->administrativeClassRepository = $administrativeClassRepository;
    }

    public function index()
    {
        return view('admin_v1.administrative_class.index');
    }

    public function getListAdministrativeClass(Request $request)
    {
        return $this->administrativeClassRepository->getListAdministrativeClass($request);
    }

    public function createAdministrativeClass(Request $request)
    {
        return $this->administrativeClassRepository->createAdministrativeClass($request);
    }

    public function deleteAdministrativeClass(Request $request)
    {
        return $this->administrativeClassRepository->deleteAdministrativeClass($request);
    }
    public function importStudent(Request $request)
    {
        return $this->administrativeClassRepository->importStudent($request);
    }
    public function deleteStudent(Request $request)
    {
        return $this->administrativeClassRepository->deleteStudent($request);
    }
}
