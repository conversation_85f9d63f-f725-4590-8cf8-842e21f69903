<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class TrainingManagementController extends Controller
{
    public function group()
    {
        return view(env('THEME_ADMIN') . '.training_management.group');
    }

    public function schedule()
    {
        return view(env('THEME_ADMIN') . '.training_management.schedule');
    }

    public function attendance()
    {
        return view(env('THEME_ADMIN') . '.training_management.attendance');
    }

    public function teaching()
    {
        return view(env('THEME_ADMIN') . '.training_management.teaching');
    }

    public function learn_again()
    {
        return view(env('THEME_ADMIN') . '.training_management.learn_again');
    }

    public function academic()
    {
        return view(env('THEME_ADMIN') . '.training_management.academic');
    }

    public function graduate()
    {
        return view(env('THEME_ADMIN') . '.training_management.graduate');
    }

    public function other()
    {
        return view(env('THEME_ADMIN') . '.training_management.other');
    }
}
