<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Repositories\Admin\EBookRepository;

class EBookController extends Controller
{
    protected $EBookRepository;
    public function __construct(EBookRepository $EBookRepository) {
        $this->EBookRepository = $EBookRepository;
    }
    public function getlistTheEBook(Request $request){
        return $this->EBookRepository->getlistTheEBook($request);
    }

    public function createSVEBook(Request $request){
        return $this->EBookRepository->createSVEBook($request);
    }

    public function updateSVEBook(Request $request){
        return $this->EBookRepository->updateSVEBook($request);
    }

    public function deleteSVEBook(Request $request){
        return $this->EBookRepository->deleteSVEBook($request);
    }
}
