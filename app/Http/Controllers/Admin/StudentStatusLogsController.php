<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Repositories\Admin\StudentStatusLogsRepository;

class StudentStatusLogsController extends Controller
{
    protected $StudentStatusLogsRepository;

    public function __construct(StudentStatusLogsRepository $StudentStatusLogsRepository) {
        return $this->StudentStatusLogsRepository = $StudentStatusLogsRepository;
    }

    public function getListStudentStatusLogs(Request $request) {
        return $this->StudentStatusLogsRepository->getListStudentStatusLogs($request);
    }
}
