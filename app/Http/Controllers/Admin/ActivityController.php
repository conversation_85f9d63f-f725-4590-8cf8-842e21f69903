<?php

namespace App\Http\Controllers\Admin;

use App\Exports\Activity\DanhSachLichThi;
use App\Exports\Activity\DanhSachThi;
use App\Exports\CourseSessionByTermExamExport;
use App\Exports\ScheduleExamByTermExport;
use App\Models\Fu\Activity;
use App\Models\T7\Grade;
use App\Repositories\Admin\ActivityRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\TestScheduleResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

class ActivityController extends Controller
{
    const MODEL = 'activity';
    protected $ActivityRepository;
    public function __construct(ActivityRepository $activityRepository)
    {
        $this->ActivityRepository = $activityRepository;
    }

    public function checkActivityAvailable(Request $request)
    {
        $validation = Validator::make($request->all(), [
            'start_day' => 'required|date'
        ]);
        $activity_id = $request->activity_id;
        if ($validation->fails()) {
            return $this->res('error', 'Check data failed', '');
        }
        $room_id = $request->room_id;
        $slot = $request->slot;
        $day = $request->start_day;
        $check = true;
        $activity = Activity::where('day', $day)->where('room_id', $room_id)->where('slot', $slot)->first();
        if ($activity && $activity->id != $activity_id) {
            $check = false;
        }

        return $this->res('success', 'Check data success', $check);
    }

    public function index(Request $request)
    {
        return $this->ActivityRepository->index($request);
    }

    public function import(Request $request)
    {
        return $this->ActivityRepository->import($request);
    }

    public function importForm()
    {
        return $this->ActivityRepository->importForm();
    }

    public function importExam(Request $request)
    {
        return $this->ActivityRepository->importExam($request);
    }

    public function importExamForm()
    {
        return $this->ActivityRepository->importExamForm();
    }


    public function exportSchedule(Request $request)
    {
        ini_set('max_execution_time', -1);
        $datas = DB::select("SELECT
                list_group.id,
                list_group.block_name,
                list_group.group_name,
                list_group.skill_code,
                list_group.psubject_code,
                activity.day,
                activity.slot,
                activity.course_slot,
                activity.leader_login,
                activity.session_type,
                activity.description,
                activity.session_description,
                activity.room_id,
                activity.room_name
            FROM
                activity
                JOIN list_group ON list_group.id = activity.groupid 
            WHERE
                list_group.pterm_id = ?
                AND list_group.is_virtual = 0
                ORDER BY list_group.id, course_slot
        ", [$request->term_id]);

        $fileName = 'Danh_sach_diem_danh_' . time() . '.xlsx';

        return Excel::download(new class($datas) implements \Maatwebsite\Excel\Concerns\FromArray, \Maatwebsite\Excel\Concerns\WithHeadings, \Maatwebsite\Excel\Concerns\ShouldAutoSize {
            protected $datas;

            public function __construct($datas)
            {
                $this->datas = $datas;
            }

            public function array(): array
            {
                $result = [];
                foreach ($this->datas as $data) {
                    $result[] = (array)$data;
                }
                return $result;
            }

            public function headings(): array
            {
                return [
                    'Id lớp',
                    'Tên block',
                    'Tên lớp',
                    'Mã chuyển đổi',
                    'Mã môn',
                    'Ngày',
                    'Ca học',
                    'số thứ tự',
                    'Giảng viên',
                    'loại buổi học (số)',
                    'Mô tả',
                    'Mô tả (loại buổi học)',
                    'Id Phòng',
                    'Tên Phòng',
                ];
            }
        }, $fileName);
    }

    /**
     * Xuất dữ liệu điểm dnah theo ngày
     * <AUTHOR>
     */
    public function exportAttendance(Request $request)
    {
        ini_set('max_execution_time', -1);
        $datas = DB::select("SELECT
            CONCAT( user.user_surname, ' ', user.user_middlename, ' ', user.user_givenname ) AS full_name,
            user.user_code,
            attendance.user_login,
            -- activity.groupid,
            activity.group_name,
            activity.psubject_code,
            DATE_FORMAT(activity.day,'%d/%m/%Y') as day,
            activity.area_name,
            activity.room_name,
            activity.leader_login,
            activity.slot,
            attendance.val,
            attendance.description as attendance_description,
            activity.note as activity_note
        FROM
            attendance
            LEFT JOIN activity ON attendance.activity_id = activity.id
            LEFT JOIN user ON user.user_login = attendance.user_login
        WHERE
            activity.DAY >= DATE(?)
            AND activity.DAY <= DATE(?)
        ORDER BY activity.day
        ", [$request->start_date, $request->end_date]);

        $fileName = 'Danh_sach_diem_danh_' . time() . '.xlsx';

        return Excel::download(new class($datas) implements \Maatwebsite\Excel\Concerns\FromArray, \Maatwebsite\Excel\Concerns\WithHeadings, \Maatwebsite\Excel\Concerns\ShouldAutoSize {
            protected $datas;

            public function __construct($datas)
            {
                $this->datas = $datas;
            }

            public function array(): array
            {
                $result = [];
                foreach ($this->datas as $data) {
                    $result[] = (array)$data;
                }
                return $result;
            }

            public function headings(): array
            {
                return [
                    'Họ và Tên',
                    'Mã sinh viên',
                    'Mã đăng nhập',
                    'Tên lớp',
                    'Mã môn',
                    'Ngày học',
                    'Khu Vực',
                    'Tên phòng',
                    'Giảng viên',
                    'Ca học',
                    'Trạng Thái (1 là điểm danh, 0 là không điểm danh )',
                    'ghi chú',
                    'ghi chú buổi học',
                ];
            }
        }, $fileName);
    }


    public function exportConfigRoom(Request $request)
    {
        ini_set('max_execution_time', -1);
        $datas = DB::select("SELECT
            list_group.id,
            list_group.skill_code,
            list_group.psubject_code,
            list_group.group_name,
            activity.DAY,
            activity.slot,
            activity.room_id,
            activity.room_name
        FROM activity
        JOIN list_group ON activity.groupid = list_group.id
        JOIN (
                SELECT
                    count( activity.id ) AS _count,
                    activity.DAY,
                    activity.slot,
                    activity.room_id
                FROM
                    `list_group`
                    JOIN activity ON activity.groupid = list_group.id
                    LEFT JOIN session_type ON session_type.id = activity.session_type
                WHERE
                    list_group.pterm_id = ?
                    AND activity.slot > 0
                    AND session_type.is_exam = 0
                    GROUP BY activity.DAY, activity.slot, activity.room_id HAVING _count > 1
            ) tbl_config ON (
                tbl_config.day = activity.`day`
                AND tbl_config.slot = activity.`slot`
                AND tbl_config.room_id = activity.`room_id`
            )
        WHERE list_group.is_virtual = 0
        ORDER BY 
            activity.DAY,
            activity.slot,
            activity.room_id", [$request->term_id]);

        $fileName = 'Danh_sach_trung_phong_hoc_' . time() . '.xlsx';

        return Excel::download(new class($datas) implements \Maatwebsite\Excel\Concerns\FromArray, \Maatwebsite\Excel\Concerns\WithHeadings, \Maatwebsite\Excel\Concerns\ShouldAutoSize {
            protected $datas;

            public function __construct($datas)
            {
                $this->datas = $datas;
            }

            public function array(): array
            {
                $result = [];
                foreach ($this->datas as $data) {
                    $result[] = (array)$data;
                }
                return $result;
            }

            public function headings(): array
            {
                return [
                    'Id lớp',
                    'Mã chuyển đổi',
                    'Mã môn',
                    'Tên lớp',
                    'Slot trùng',
                    'Ngày trùng',
                    'ID Phòng trùng',
                    'Tên Phòng trùng'
                ];
            }
        }, $fileName);
    }

    /**
     * Xuất dữ liệu danh sách trùng lớp môn
     * <AUTHOR>
     */
    public function configSchedule(Request $request)
    {
        ini_set('max_execution_time', -1);
        $datas = DB::select("SELECT
                member_login AS 'Tên đăng nhập',
                psubject_code AS 'Môn trùng',
            -- 	lastmodifier_login,
                DATE_FORMAT(DAY, \"%d/%m/%Y\") AS 'Ngày trùng',
                slot AS 'Slot trùng'
            FROM
                (
                SELECT
                    count( activity.id ) AS _count,
                    activity.DAY,
                    activity.slot,
                    activity.lastmodifier_login,
                    list_group.psubject_code,
                    group_member.member_login
                FROM
                    list_group
                    RIGHT JOIN group_member ON group_member.groupid = list_group.id
                    INNER JOIN activity ON activity.groupid = list_group.id
                    LEFT JOIN session_type ON session_type.id = activity.session_type
                WHERE
                    list_group.pterm_id = ?
                    AND activity.slot > 0
                    AND session_type.is_exam = 0
                    AND list_group.is_virtual = 0
                    GROUP BY group_member.member_login, activity.DAY, activity.slot HAVING _count > 1
                ) xxx
            GROUP BY
                member_login,
                lastmodifier_login,
                psubject_code ", [$request->term_id]);

        $fileName = 'Danh_sach_trung_lich_hoc_' . time() . '.xlsx';

        return Excel::download(new class($datas) implements \Maatwebsite\Excel\Concerns\FromArray, \Maatwebsite\Excel\Concerns\WithHeadings, \Maatwebsite\Excel\Concerns\ShouldAutoSize {
            protected $datas;

            public function __construct($datas)
            {
                $this->datas = $datas;
            }

            public function array(): array
            {
                $result = [];
                foreach ($this->datas as $data) {
                    $result[] = (array)$data;
                }
                return $result;
            }

            public function headings(): array
            {
                return [
                    'Tên đăng nhập',
                    'Môn trùng',
                    'Ngày trùng',
                    'Slot trùng',
                ];
            }
        }, $fileName);
    }

    /**
     * Xuất dữ liệu danh sách trùng lịch thi
     * <AUTHOR>
     */
    public function configScheduleTest(Request $request)
    {
        ini_set('max_execution_time', -1);
        $datas = DB::select("SELECT
                member_login AS 'Tên đăng nhập',
                psubject_code AS 'Môn trùng',
            -- 	lastmodifier_login,
                DATE_FORMAT(DAY, \"%d/%m/%Y\") AS 'Ngày trùng',
                slot AS 'Slot trùng'
            FROM
                (
                SELECT
                    count( activity.id ) AS _count,
                    activity.DAY,
                    activity.slot,
                    activity.lastmodifier_login,
                    list_group.psubject_code,
                    group_member.member_login
                FROM
                    list_group
                    RIGHT JOIN group_member ON group_member.groupid = list_group.id
                    INNER JOIN activity ON activity.groupid = list_group.id
                    LEFT JOIN session_type ON session_type.id = activity.session_type
                WHERE
                    list_group.pterm_id = ?
                    AND activity.slot > 0
                    AND session_type.is_exam = 1
                    AND list_group.is_virtual = 0
                    GROUP BY group_member.member_login, activity.DAY, activity.slot HAVING _count > 1
                ) xxx
            GROUP BY
                member_login,
                lastmodifier_login,
                psubject_code ", [$request->term_id]);

        $fileName = 'Danh_sach_trung_lich_thi_' . time() . '.xlsx';

        return Excel::download(new class($datas) implements \Maatwebsite\Excel\Concerns\FromArray, \Maatwebsite\Excel\Concerns\WithHeadings, \Maatwebsite\Excel\Concerns\ShouldAutoSize {
            protected $datas;

            public function __construct($datas)
            {
                $this->datas = $datas;
            }

            public function array(): array
            {
                $result = [];
                foreach ($this->datas as $data) {
                    $result[] = (array)$data;
                }
                return $result;
            }

            public function headings(): array
            {
                return [
                    'Tên đăng nhập',
                    'Môn trùng',
                    'Ngày trùng',
                    'Slot trùng',
                ];
            }
        }, $fileName);
    }

    /**
     * Xuất dữ liệu danh sách xếp nhiều lớp 1 môn
     * <AUTHOR>
     */
    public function configGroup(Request $request)
    {
        ini_set('max_execution_time', -1);
        $datas = DB::select("SELECT 
                group_member.member_login,
                group_member.groupid,
                list_group.group_name,
                list_group.skill_code,
                list_group.psubject_code
            FROM list_group 
            JOIN group_member ON group_member.groupid = list_group.id
            JOIN (
                SELECT group_member.member_login, list_group.psubject_code
                FROM group_member 
                JOIN list_group ON group_member.groupid = list_group.id
                WHERE list_group.is_virtual = 0 
                AND pterm_id = ?
                GROUP BY group_member.member_login, list_group.psubject_code
                HAVING count(group_member.id) > 1
            ) tbl_scan ON tbl_scan.member_login = group_member.member_login AND tbl_scan.psubject_code = list_group.psubject_code
                WHERE list_group.pterm_id = ?
                AND list_group.is_virtual = 0 
            ORDER BY member_login, groupid", [$request->term_id, $request->term_id]);

        $fileName = 'Danh_sach_xep_nhieu_lop_1_mon_' . time() . '.xlsx';

        return Excel::download(new class($datas) implements \Maatwebsite\Excel\Concerns\FromArray, \Maatwebsite\Excel\Concerns\WithHeadings, \Maatwebsite\Excel\Concerns\ShouldAutoSize {
            protected $datas;

            public function __construct($datas)
            {
                $this->datas = $datas;
            }

            public function array(): array
            {
                $result = [];
                foreach ($this->datas as $data) {
                    $result[] = (array)$data;
                }
                return $result;
            }

            public function headings(): array
            {
                return [
                    'Tên đăng nhập',
                    'ID lớp',
                    'Tên lớp',
                    'Mã chuyển đổi',
                    'Mã môn'
                ];
            }
        }, $fileName);
    }

    /**
     * <AUTHOR>
     */
    public function importnewSchedule(Request $request)
    {
        return $this->ActivityRepository->importnewSchedule($request);
    }


    /**
     * <AUTHOR>
     */
    public function importNewForm()
    {
        return $this->ActivityRepository->importForm2();
    }

    public function lich_thi_giua_ky_cuoi_ky(Request $request)
    {
        $exportExcel = $request->get('export_excel', 0);
        $date = $request->date;
        $group_name = $request->keyword;
        if ($date) {
            $date = explode(' - ', $date);
        } else {
            $date[0] = Carbon::now()->toDateString();
            $date[1] = Carbon::now()->addMonths(2)->toDateString();
        }

        $days = [];
        $result_main = Activity::with(['group'])
            ->select('activity.*')
            ->join('list_group', 'list_group.id', '=', 'activity.groupid')
            ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
            ->when(is_array($date), function ($query) use ($date) {
                $query->where('activity.day', '>=', $date[0])->where('activity.day', '<=', $date[1]);
            })
            ->when($group_name, function ($query) use ($group_name) {
                $query->where('list_group.group_name', $group_name);
            })
            ->where('session_type.is_exam', 1)
            ->where('list_group.is_virtual', 0)
            ->orderBy('activity.day')
            ->orderBy('activity.slot')
            ->get();

        foreach ($result_main as $main_item) {
            $days[$main_item->day][] = $main_item;
            // $main_leader = $main_item->group ? $main_item->group->main_leader() : false;
            // $main_item->leader = !is_bool($main_leader) ? $main_leader->leader_login : '';
            $main_item->leader = $main_item->group->teacher ?? '';
        }

        if ($exportExcel == 1) {
            return Excel::download(new DanhSachLichThi($days), 'Danh_sach_lich_thi_' . time() . '.xlsx');
        }
        return view("admin_v1." . self::MODEL . '.' . __FUNCTION__, [
            'model' => self::MODEL,
            'result_main' => $result_main,
            'days' => $days,
            'date' => $date,
        ]);
    }

    public function danh_sach_thi(Request $request)
    {
        $date = $request->date;
        if ($request->export == 'excel') {
            return Excel::download(new DanhSachThi($date), 'Danh_sach_thi_' . time() . '.xlsx');
        }
        $result_main = Activity::with(['group', 'slotDetail'])
            ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
            ->where('activity.day', $date)
            ->where('session_type.is_exam', 1)
            ->orderBy('activity.day')
            ->orderBy('activity.slot')
            ->get();
        
        foreach ($result_main as $item_main) {
            $course_slot = $item_main->course_slot;
            $session_type = $item_main->session_type;
            // if ($course_slot == 250) { // trường hợp thi giữa kỳ thì hiện danh sách cả lớp
                $members = DB::select("Select * from user t1,t7_course_result t2 where t1.user_login=t2.student_login and t2.groupid in ($item_main->groupid) order by t1.user_code asc");
            // } else {
            //     $members = DB::select("Select * from user t1,t7_course_result t2 where t1.user_login=t2.student_login and t2.groupid in ($item_main->groupid) and t2.val=0 order by t1.user_code asc");
            // }
            // if (($course_slot >= 150 && $course_slot < 200) || $session_type == 9) {
            //     $grades = Grade::where('syllabus_id', $item_main->group->syllabus_id)->where('is_final_exam', 1)->where('grade_type', '<', 2)->get();
            // } else if (($course_slot > 200 || $session_type == 10) && $course_slot != 250) {
                $grades = Grade::where('syllabus_id', $item_main->group->syllabus_id)->where('is_final_exam', 1)->get();
                //                $old_point = DB::select("Select *, t1.val as grade_val from t7_course_grade t1, t7_course_result t2 where t1.groupid=t2.groupid and t1.login=t2.student_login and t2.val=0 and t2.groupid in ($item_main->groupid) and t1.is_final=1 and t1.is_resit!=1");
            // }
            $item_main->grades = $grades;
            $item_main->members = $members;
            $item_main->cam_thi = DB::select("Select * from user t1,t7_course_result t2 where t1.user_login=t2.student_login and t2.groupid in($item_main->groupid) and t2.val=-1 order by t1.user_code asc");;
        }

        return view("admin_v1." . self::MODEL . '.' . __FUNCTION__, [
            'model' => self::MODEL,
            'result_main' => $result_main,
        ]);
    }

    public function testSchedule(Request $request)
    {
        $now = Carbon::now()->format('Y-m-d');
        $filterDate = Arr::get($request->all(), 'date', null);
        $student_login = Arr::get($request->all(), 'student_login', null);
        $per_page = Arr::get($request->all(), 'per_page', 15);
        if ($filterDate) {
            $filterDate = $this->dateSelecter($filterDate);
            $activity = Activity::query();
            $activity  = $activity->distinct();
            $activity = $activity->join('activity_groups', 'activity.id', '=', 'activity_groups.activity_id');
            $activity = $activity->join('group_member', 'activity_groups.groupid', '=', 'group_member.groupid');
            $activity = $activity->join('slot', 'slot.slot_id', '=', 'activity.slot');
            $activity = $activity->join('session_type', 'activity.session_type', '=', 'session_type.id');
            $activity = $activity->where('group_member.member_login', $student_login);
            $activity = $activity->where('activity.slot', '>', 0);
            $activity = $activity->where('session_type.is_exam', 1);
            $activity = $activity->whereBetween('activity.day', [$filterDate['start'], $filterDate['end']]);
            $activity = $activity->orderBy('day', 'ASC')->orderBy('slot', 'ASC')->paginate($per_page, [
                "activity.id",
                "activity.groupid",
                "activity.group_name",
                "activity.area_name",
                "activity.day",
                "activity.course_slot",
                "activity.course_id",
                "activity.room_name",
                "activity.psubject_name",
                "activity.leader_login",
                "activity.slot",
                "slot.slot_start",
                "slot.slot_end",
                "activity.noi_dung",
                "activity.nv_sinh_vien",
                "activity.hoc_lieu_mon",
                "activity.nv_giang_vien",
                "activity.tai_lieu_tk",
                "activity.tu_hoc",
                "activity.tl_buoi_hoc",
                "activity.muc_tieu",
                "activity.is_online"
            ]);
            return TestScheduleResource::collection($activity);
        }
        return response()->json([], 200);
    }

    private function dateSelecter(int $date)
    {
        // 7 14 30 60 90 || -7 -14 -30 -60 -90
        $now = Carbon::now()->format('Y-m-d');
        $dateSelecter = date('Y-m-d', strtotime($now . ($date > 0) ? (" +$date days") : (" -$date days")));
        return [
            'start' => ($date > 0) ? $now : $dateSelecter,
            'end' => ($date > 0) ? $dateSelecter : $now
        ];
    }

    /**
     * <AUTHOR>
     */
    public function downloadCourseExam(Request $request)
    {
        // dowloadTemplate
        return Excel::download(new CourseSessionByTermExamExport($request->get('term_id', null)), 'course_exam.xlsx');
    }

    /**
     * <AUTHOR>
     */
    public function downloadExamClassList(Request $request)
    {
        $termId = $request->get('term_id', null);
        $blockId = $request->get('block_id', null);
        $subjectCode = $request->get('subject_code', null);
        // dowloadTemplate
        return Excel::download(new ScheduleExamByTermExport($termId, $blockId, $subjectCode), 'exam_class_list.xlsx');
    }

    function chi_tiet_hoat_dong(Request $request)
    {
        return $this->ActivityRepository->chi_tiet_hoat_dong($request);
    }

    /**
     * <AUTHOR>
     * Kiểu tra những môn học đã pass mà vẫn được xếp lớp theo kỳ
     * 
     * @param Request $request 
     * @return View 
     * 
     */
    public function checkPassedSubjectByTerm(Request $request)
    {
        return $this->ActivityRepository->checkPassedSubjectByTerm($request);
    }
}
