<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\Admin\PopupRepository;
use Illuminate\Http\Request;

class PopupController extends Controller
{
    public function __construct(PopupRepository $PopupRepository) 
    {
        $this->PopupRepository = $PopupRepository;
    }

    public function index() 
    {
        return $this->PopupRepository->index();
    }

    public function store(Request $request) 
    {
        return $this->PopupRepository->store($request);
    }

    public function edit($id) 
    {
        return $this->PopupRepository->edit($id);
    }

    public function delete($id) 
    {
        return $this->PopupRepository->delete($id);
    }

    public function getlistPopup(Request $request) 
    {
        return $this->PopupRepository->getlistPopup($request);
    }
}