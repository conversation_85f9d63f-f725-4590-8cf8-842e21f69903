<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\DataTables\HO\CurriculumDatatable;
use App\Repositories\Admin\CurriculumRepository;
use Illuminate\Http\Request;

class CurriculumController extends Controller
{
    protected $curriculumRepository;
    public function __construct()
    {
        $this->curriculumRepository = new CurriculumRepository();
    }

    /**
     * Datatable ajax request
     *
     * @return \Illuminate\Http\CurriculumDatatable
     */
    public function datatable(CurriculumDatatable $datatable)
    {
        return $datatable->build()->toJson();
    }

    /**
     * index
     */
    public function index(Request $request)
    {
        return $this->curriculumRepository->index($request);
    }

    /**
     * @todo Xuất danh sách khung
     */
    public function export(Request $request)
    {
        return $this->curriculumRepository->export($request);
    }

    /**
     * create
     */
    public function create(Request $request)
    {
        return $this->curriculumRepository->createCurriculum($request);
    }

    /**
     * storage
     */
    public function storage(Request $request)
    {
        return $this->curriculumRepository->storageCurriculum($request);
    }

    /**
     * update
     */
    public function update(Request $request)
    {
        return $this->curriculumRepository->updateCurriculum($request);
    }

    /**
     * update
     */
    public function delete(Request $request)
    {
        return $this->curriculumRepository->deleteCurriculum($request);
    }
    
    /**
     * Lấy template import môn học 
     */
    public function getPeridoSubjectTemplate(Request $request)
    {
        return $this->curriculumRepository->getPeridoSubjectTemplate($request);
    }

    /**
     * update
     */
    public function updatePeriodSubject(Request $request)
    {
        return $this->curriculumRepository->updatePeriodSubject($request);
    }


    /**
     * auto generate Code
     */
    public function autoGenerateCurriculumCode(Request $request)
    {
        return $this->curriculumRepository->autoGenerateCurriculumCode($request);
    }


    // =========== Hepler ===========
    public function getlistBrand(Request $request) {
        return $this->curriculumRepository->getlistBrand($request);
    }
    
    public function downloadCurriculum(Request $request) {
        $data = $this->curriculumRepository->downloadCurriculum($request);
        $file_name = 'danh-sach-khung-chuong-trinh.csv';
        $filePath = storage_path('app/' . $file_name);

        // Ghi dữ liệu vào file CSV
        $csvFile = fopen($filePath, 'w');

        // Thêm BOM UTF-8 để tránh lỗi font khi mở bằng Excel
        fprintf($csvFile, chr(0xEF) . chr(0xBB) . chr(0xBF));
        $header = [
            'ID',
            'Tên khung',
            'Số kỳ',
            'Mô tả',
            'Khoá',
            'Mã ngành',
            'Ngành',
            'Chuyên ngành',
            'Chuyên ngành hẹp',
        ];
        
        fputcsv($csvFile, $header);
        foreach ($data as $key => $line) {
            fputcsv($csvFile, [
                $line->id,
                $line->ten_khung,
                $line->so_ky,
                $line->mo_ta,
                $line->khoa,
                $line->ma_nganh,
                $line->nganh,
                $line->chuyen_nganh,
                $line->chuyen_nganh_hep,
            ]);
        }
        fclose($csvFile);

        return response()->download($filePath)->deleteFileAfterSend(true);
    }
}
