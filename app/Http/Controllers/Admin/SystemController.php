<?php

namespace App\Http\Controllers\Admin;

use App\Console\Commands\AnalyzeEnglish;
use App\Console\Commands\SendFeeMail;
use App\Http\Lib;
use App\Mail\OrderProcess;
use App\Models\EnglishDetail;
use App\Models\EnglishDetailLevel;
use App\Models\Fee\Fee;
use App\Models\Fee\FeeDetail;
use App\Models\Fee\FeeLog;
use App\Models\Fee\FeeMail;
use App\Models\Fee\Plan;
use App\Models\Fee\PlanDetail;
use App\Models\Dra\CurriCulum;
use App\Models\Fu\Activity;
use App\Models\Fu\Course;
use App\Models\MienGiamTapTrung;
use App\Models\RelearnOnline;
use App\Models\T7\CourseResult;
use App\Models\Fu\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\DataTables\HistoryDataTable;
use App\Models\Fu\Term;
use App\Models\TranferT7Course;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use function Complex\cot;

class SystemController extends Controller
{

    public function checkDoubleCourseResult()
    {
        $sql = "select COUNT(*) as total, student_login, skill_code, pterm_name, GROUP_CONCAT(val) as val, GROUP_CONCAT(is_finish) as finish from t7_course_result where skill_code != 'VIE103' and pterm_name = 'Fall 2019' GROUP BY pterm_name, skill_code, student_login HAVING COUNT(*) > 1 ORDER BY create_time DESC";
        $result = CourseResult::select(DB::raw('COUNT(*) as total'), 'student_login', 'skill_code', 'pterm_name', DB::raw('GROUP_CONCAT(val) as val'), DB::raw('GROUP_CONCAT(is_finish) as finish'))
            ->where('skill_code', '!=', 'VIE103')
            ->where('pterm_name', 'Fall 2019')
            ->groupBy('pterm_name','skill_code','student_login')
            ->havingRaw('total > 1')
            ->groupBy('create_time')
            ->get();
        $sinh_vien_pass_1 = [];
        $sinh_vien_not_pass_1 = [];
        $sinh_vien_truot_diem_danh_1 = [];
        foreach($result as $item) {
            $arr = [];
            $item->val_arr = explode(',', $item->val);
            $item->finish_arr = explode(',', $item->finish);
            for($i=0;$i<count($item->val_arr);$i++) {
                $val = $item->val_arr[$i];
                $isFinish = $item->finish_arr[$i];
                $status = 0;
                if ($val == 0) {
                    $status = 0;
                } else if ($val == -1) {
                    $status = -1;
                } else if (($val == 0 && $isFinish == 0) or ( $val == 0 && $isFinish == 2)) {
                    $status = 0;
                } else if ($val == 1) {
                    $status = 1;
                } else if ($val == -13) {
                    $status = -13;
                } else if ($val == 4) {
                    $status = 4;
                }
                $arr[] = $status;
            }
            $item->status = $arr;
            for($z=0;$z<count($arr);$z++) {
                if ($arr[0] == 1) {
                    $sinh_vien_pass_1[] = $item;
                    break;
                } else if ($arr[0] == -1) {
                    $sinh_vien_truot_diem_danh_1[] = $item;
                    break;
                } else if ($arr[0] === 0) {
                    $sinh_vien_not_pass_1[] = $item;
                    break;
                }
            }

        }
    }

    public static function analyzeEnglish($user)
    {
        $activities = collect([]);
        $total_relearn = 0;
        $english = EnglishDetail::where('user_code', $user->user_code)->first();
        $mien_giam = MienGiamTapTrung::where('student_login', $user->user_login)->where('type', 2)->get();
        $relearns = RelearnOnline::where('user_login', $user->user_login)->where('skill_code', 'like','ENT%')->get();
        if (!$english) {
            $english = EnglishDetail::create([
                'user_code' => $user->user_code,
                'user_login' => $user->user_login,
                'study_status' => $user->study_status,
                'curriculum_id' => $user->curriculum_id,
                'brand_code' => $user->brand_code,
            ]);
        } else {
            $english->user_code = $user->user_code;
            $english->user_login = $user->user_login;
            $english->study_status = $user->study_status;
            $english->curriculum_id = $user->curriculum_id;
            $english->brand_code = $user->brand_code;
        }
        
        foreach ($mien_giam as $item) {
            $english = AnalyzeEnglish::mienGiam('ENT111', $english, $item);
            $english = AnalyzeEnglish::mienGiam('ENT121', $english, $item);
            $english = AnalyzeEnglish::mienGiam('ENT211', $english, $item);
            $english = AnalyzeEnglish::mienGiam('ENT221', $english, $item);
        }

        $english_code = ['ENT111','ENT121','ENT211','ENT221'];
        $course_result = CourseResult::whereIn('skill_code', $english_code)
            ->where('student_login', $user->user_login)
            ->orderBy('create_time')
            ->get();
        foreach ($course_result as $item) {
            //chú thích: check ngày kết thúc của tiếng anh, tạo ra ngày kết thúc +35 ngày để xem có phải là môn của kỳ trước, nếu là môn
            // của kỳ trước vậy chắc chắn là đã kết thúc thì sẽ không query lấy ngày kết thúc nữa
            $activity = null;
            $end_date_pre = Carbon::createFromFormat('Y-m-d', $item->end_date)->addDays(35);
            if ($end_date_pre->greaterThan(now()) && !$activity = $activities->firstWhere('groupid', $item->groupid)) {
                $activity = Activity::where('groupid', $item->groupid)->select('groupid', DB::raw('min(day) as min'), DB::raw('max(day) as max'))->groupBy('groupid')->get();
                $activities = $activities->merge($activity);
                $activity = $activities->firstWhere('groupid', $item->groupid);
            }
            $end_date = Carbon::createFromFormat('Y-m-d', $activity->max ?? $item->end_date)->addDays(5);
            if ($item->val == 0 && ($item->start_date <= now()->format('Y-m-d') && $end_date >= now()->format('Y-m-d'))) {
                $status = -2;
            } else if ($item->val == 0) {
                $status = -3;
            } else {
                $status = $item->val;
            }
            $detail_level = AnalyzeEnglish::createOrUpdateDetailLevel($item,'ENT111',$english,$relearns,$status,$total_relearn);
            $english = $detail_level[0];
            $total_relearn = $detail_level[1];
            $detail_level = AnalyzeEnglish::createOrUpdateDetailLevel($item,'ENT121',$english,$relearns,$status,$total_relearn);
            $english = $detail_level[0];
            $total_relearn = $detail_level[1];
            $detail_level = AnalyzeEnglish::createOrUpdateDetailLevel($item,'ENT211',$english,$relearns,$status,$total_relearn);
            $english = $detail_level[0];
            $total_relearn = $detail_level[1];
            $detail_level = AnalyzeEnglish::createOrUpdateDetailLevel($item,'ENT221',$english,$relearns,$status,$total_relearn);
            $english = $detail_level[0];
            $total_relearn = $detail_level[1];
        }
        
        if (count($course_result) == 0) {
            $tranferT7Course = TranferT7Course::whereIn('skill_code', $english_code)
                ->where('student_login', $user->user_login)
                ->orderBy('id')
                ->get();
            foreach ($tranferT7Course as $item) {
                //chú thích: check ngày kết thúc của tiếng anh, tạo ra ngày kết thúc +35 ngày để xem có phải là môn của kỳ trước, nếu là môn
                // của kỳ trước vậy chắc chắn là đã kết thúc thì sẽ không query lấy ngày kết thúc nữa
                $activity = null;
                $termData = Term::where('term_name', $item->pterm_name)->first();
                if (!$termData) {
                    continue;
                }

                $item->term_id = $termData->id;
                $end_date_pre = Carbon::createFromFormat('Y-m-d', $item->end_date)->addDays(35);
                if ($end_date_pre->greaterThan(now()) && !$activity = $activities->firstWhere('groupid', $item->groupid)) {
                    $activity = Activity::where('groupid', $item->groupid)->select('groupid', DB::raw('min(day) as min'), DB::raw('max(day) as max'))->groupBy('groupid')->get();
                    $activities = $activities->merge($activity);
                    $activity = $activities->firstWhere('groupid', $item->groupid);
                }
                $end_date = Carbon::createFromFormat('Y-m-d', $activity->max ?? $item->end_date)->addDays(5);
                if ($item->val == 0 && ($item->start_date <= now()->format('Y-m-d') && $end_date >= now()->format('Y-m-d'))) {
                    $status = -2;
                } else if ($item->val == 0) {
                    $status = -3;
                } else {
                    $status = $item->val;
                }
                $detail_level = AnalyzeEnglish::createOrUpdateDetailLevel($item,'ENT111',$english,$relearns,$status,$total_relearn, true);
                $english = $detail_level[0];
                $total_relearn = $detail_level[1];
                $detail_level = AnalyzeEnglish::createOrUpdateDetailLevel($item,'ENT121',$english,$relearns,$status,$total_relearn, true);
                $english = $detail_level[0];
                $total_relearn = $detail_level[1];
                $detail_level = AnalyzeEnglish::createOrUpdateDetailLevel($item,'ENT211',$english,$relearns,$status,$total_relearn, true);
                $english = $detail_level[0];
                $total_relearn = $detail_level[1];
                $detail_level = AnalyzeEnglish::createOrUpdateDetailLevel($item,'ENT221',$english,$relearns,$status,$total_relearn, true);
                $english = $detail_level[0];
                $total_relearn = $detail_level[1];
            }
        }

        if ($english->level_1 >= 1 && $english->level_2 >= 1 && $english->level_3 >= 1 && $english->level_4 >= 1) {
            $english->finish = 4;
        } else if ($english->level_1 >= 1 && $english->level_2 >= 1 && $english->level_3 >= 1) {
            $english->finish = 3;
        } else if ($english->level_1 >= 1 && $english->level_2 >= 1) {
            $english->finish = 2;
        } else if ($english->level_1 >= 1) {
            $english->finish = 1;
        }

        $english->save();
        $last_english = EnglishDetailLevel::where('english_id', $english->id)->orderBy('create_time','desc')->first();
        $fee = Fee::where('user_login', $user->user_login)->first();
        if ($last_english && $last_english->payment_status == 0) {
            if ($last_english->fee_detail_id == 0) {
                $data = AnalyzeEnglish::calculatorEnglishFee($english,$last_english,$total_relearn,$fee,$user);
                if ($data) {
                    $last_english->amount = $data['amount'];
                    $last_english->discount = $data['discount'];
                    $last_english->note = $data['note'];
                    $last_english->save();
                }
            }
        }
    }

    public static function createOrUpdateFee($skip, $limit = 2000)
    {
        $users = User::select('user.*')
        ->leftJoin('fee_mails', 'user.user_login', '=', 'fee_mails.user_login')
        ->where('fee_mails.term_id', 51)
        ->where('user.user_level', 3)
        ->skip($skip)->limit($limit)->get();
        foreach ($users as $user) {
            if (!self::createOrUpdateFeeRow($user)) {
                continue;
            }
        }
    }

    public static function createOrUpdateFeeRow($user)
    {
        $fee = Fee::where('user_code', $user->user_code)->first();
        $curriCulum = CurriCulum::find($user->curriculum_id);

        if (!$curriCulum) {
            return false;
        }

        if ($curriCulum->brand_code == "DOM") {
            $curriCulum->brand_code = "DIG";
            $curriCulum->save();
        }
        
        $fee_plan = Plan::with('details')->where('curriculum_id', $user->curriculum_id)->first();
        if (!$fee_plan) {
            return false;
        }

        $fee_plan->details = PlanDetail::where('fee_plan_id', $fee_plan->id)->get();
        if (!$fee) {
            $fee = Fee::create([
                'user_code' => $user->user_code,
                'user_login' => $user->user_login,
                'study_status' => $user->study_status,
                'curriculum_id' => $user->curriculum_id,
                'brand_code' => $curriCulum->brand_code,
                'fee_plan_id' => $fee_plan->id,
                'ki_thu' => $user->kithu,
            ]);
            
            foreach ($fee_plan->details as $item) {
                if (Str::contains($user->user_code, 'PF') && $fee->pf_transfer == 0) {
                    if ($item->type_fee == 1) {
                        if ($curriCulum->brand_code == 'MA') {
                            $cost = 3400000;
                        } else {
                            $cost = 3600000;
                        }

                        FeeDetail::create([
                            'fee_id' => $fee->id,
                            'ki_thu' => $item->ki_thu,
                            'type_fee' => $item->type_fee,
                            'amount' => $cost,
                            'discount' => $item->discount,
                            'version' => $item->version,
                        ]);
                    }
                } else {
                    FeeDetail::create([
                        'fee_id' => $fee->id,
                        'ki_thu' => $item->ki_thu,
                        'type_fee' => $item->type_fee,
                        'amount' => $item->amount,
                        'discount' => $item->discount,
                        'version' => $item->version,
                    ]);
                }
            }
        } else {
            // Cập nhập thông tin phí mới
            $old_version = $fee->version;
            $new_version = $fee_plan->version;
            $old_brand_code = $fee->brand_code;
            $new_brand_code = $curriCulum->brand_code;
            $fee->user_code = $user->user_code;
            $fee->user_login = $user->user_login;
            $fee->study_status = $user->study_status;
            $fee->curriculum_id = $user->curriculum_id;
            $fee->brand_code = $curriCulum->brand_code;
            $fee->ki_thu = $user->kithu;
            if ($old_brand_code != $new_brand_code) {
                $fee->fee_plan_id = $fee_plan->id;
                $old_version = 0;
            }

            // Kiểm tra version của phí
            if ($old_version < $new_version) {
                $fee->version = $new_version;
                FeeDetail::whereIn('type_fee', [1,2])->where('fee_id', $fee->id)->delete();
                foreach ($fee_plan->details as $item) {
                    if (Str::contains($user->user_code, 'PF') && $fee->pf_transfer == 0) {
                        if ($item->type_fee == 1) {
                            if ($curriCulum->brand_code == 'MA') {
                                $cost = 3400000;
                            } else {
                                $cost = 3600000;
                            }

                            FeeDetail::create([
                                'fee_id' => $fee->id,
                                'ki_thu' => $item->ki_thu,
                                'type_fee' => $item->type_fee,
                                'amount' => $cost,
                                'discount' => $item->discount,
                                'version' => $item->version,
                            ]);
                        }
                    } else {
                        FeeDetail::create([
                            'fee_id' => $fee->id,
                            'ki_thu' => $item->ki_thu,
                            'type_fee' => $item->type_fee,
                            'amount' => $item->amount,
                            'discount' => $item->discount,
                            'version' => $item->version,
                        ]);
                    }
                }
            }
        }

        $fee->save();
        // Cập nhập lại fee log
        self::syncLogFeeDetailStatus($fee);
        return $fee;
    }

    public static function syncLogFeeDetailStatus($fee)
    {
        $logs = FeeLog::where('version', '!=', 999)->where('fee_id', $fee->id)->get();
        if ($logs->count()) {
            foreach ($logs as $log) {
                if ($log->brand_code == $fee->brand_code) {
                    FeeDetail::where('fee_id', $fee->id)
                        ->where('status', '!=', 1)
                        ->where('ki_thu', $log->ki_thu)
                        ->where('type_fee', $log->type_fee)
                        ->update([
                            'status' => 1,
                            'amount' => $log->amount,
                        ]);
                    if ($log->type_fee == 3 || $log->type_fee == 4 || $log->type_fee == 5) {
                        $log->version = 999;
                        $log->save();
                    }
                }
            }

            return true;
        }

        return false;
    }

    public static function sendFeeMail($term_name, $skip, $limit = 2000)
    {
        $fees = Fee::whereIn('study_status', [1, 3, 10])
            ->where('ki_thu', "!=", 0)
            ->skip($skip)->limit(1000)->get();
        $listNewBook = [];

        $feePlans = Plan::with('details')->get()->toArray();
        $count = 0;
        $listEnglishOnline = ['ENT2225', 'ENT2125', 'ENT1225', 'ENT1125'];
        foreach ($fees as $fee) {
            echo "$fee->user_code\n";
            $tieng_anh = 0;
            $type_fee = [1,2,5];
            $ki_thu = $fee->ki_thu + 0;
            $englishStatus = null;
            $version = 0;
            $english_level = 0;
            $note = null;
            if ($ki_thu == 1) {
                continue;
            }

            $user = User::where('user_code', $fee->user_code)->first();
            if (!$user) {
                continue;
            }

            if ($user->study_status == 4 || $user->study_status == 8) {
                echo "Sinh viên $fee->user_code BH hoặc TNG\n";
                continue;
            }
            
            $english = EnglishDetail::where('user_code', $fee->user_code)->first();
            $last_english = null;
            if ($english) {
                $last_english = EnglishDetailLevel::select('level', 'status', 'version', 'note', 'subject_code')
                    ->where('english_id', $english->id)
                    ->orderBy('level','desc')
                    ->orderBy('create_time','desc')
                    ->first();
                if ($last_english) {
                    $tieng_anh = ($last_english->level != 4 ? ($last_english->status == 1 ? 2600000 : 500000) : 0);
                    if (!in_array($last_english->subject_code, $listEnglishOnline)) {
                        $tieng_anh = 2600000;
                    }

                    $englishStatus = $last_english->status;
                    $version = $last_english->version + $version;
                    $note = $note . $last_english->note;
                    $english_level = $last_english->level;
                }
            }

            $fee->details = FeeDetail::where('ki_thu', $ki_thu-1)->whereIn('type_fee', $type_fee)->where('fee_id', $fee->id)->get();
            $feeNeedCheck = array_values(array_filter($feePlans, function ($a) use ($fee, $user) {
                return ($a['curriculum_id'] == $user->curriculum_id);
            }));
            
            $feeNeedCheck = $feeNeedCheck[0];
            if ($fee->details->count()) {
                $listFeeDetail = $feeNeedCheck['details'];
                $listFeeDetail = array_values(array_filter($listFeeDetail, function ($a) use ($user) {
                    return ($a['ki_thu'] == $user->kithu + 1);
                }));
            
                $hoc_ky = 0;
                $tien_sach = 0;
                $version = $version + $fee->details->sum('version');
                foreach ($listFeeDetail as $item) {
                    if ($item['type_fee'] == 1) {
                        if (in_array($user->user_code, ['PF15011', 'PF15013', 'PF15005', 'PF15019', 'PF15035', 'PF15032', 'PF15050', 'PF15028', 'PF15030', 'PF15051', 'PF15001', 'PF15029', 'PF15098', 'PF15086', 'PF15094', 'PF15115', 'PF15102', 'PF15055', 'PF15149', 'PF15142', 'PF15166', 'PF15056'])) {
                            $hoc_ky += $fee->brand_code == 'MA' ? 3400000 : 3600000;
                        } else {
                            $hoc_ky += $item['amount'];
                        }
                    }
                    if ($item['type_fee'] == 2) {
                        $tien_sach += $item['amount'];
                    }
                }

                $totalFee = [
                    'hoc_ky' => $hoc_ky,
                    'tien_sach' => $tien_sach,
                    'tieng_anh' => $tieng_anh,
                    'study_wallet' => $fee->study_wallet,
                ];

                if ($english_level < 4) {
                    $totalFee['tieng_anh'] = 2600000;
                    if ($englishStatus == '-1') {
                        $totalFee['tieng_anh'] = 500000;
                    }
                    
                    if ($fee->brand_code == 'HDDL' || $fee->brand_code == 'QTKS') {
                        if ($english_level < 2) {
                            $totalFee['tieng_anh'] = 0;
                            if ($englishStatus == '-1' || $englishStatus == '-3' || $englishStatus == '-2' || $englishStatus == '-4') {
                                $totalFee['tieng_anh'] = 500000;
                            }
                        } else {
                            $totalFee['tieng_anh'] = 0;
                        }
                    }

                    if ($englishStatus == '2' || $englishStatus == 2) {
                        if ($english_level < 4) {
                            $fee_end = 2600000;
                        } 
                        
                        if ($english_level >= 2 && ($fee->brand_code == 'HDDL' || $fee->brand_code == 'QTKS')) {
                            $fee_end = 0;
                        } 
                    }

                    if (isset($last_english)  && $last_english != null && !in_array($last_english->subject_code, $listEnglishOnline)) {
                        $totalFee['tieng_anh'] = 2600000;
                    
                        if ($english_level >= 2 && ($fee->brand_code == 'HDDL' || $fee->brand_code == 'QTKS')) {
                            $totalFee['tieng_anh'] = 0;
                        } 
                    }

                    if (in_array($user->user_code, ['PF15011', 'PF15013', 'PF15005', 'PF15019', 'PF15035', 'PF15032', 'PF15050', 'PF15028', 'PF15030', 'PF15051', 'PF15001', 'PF15029', 'PF15098', 'PF15086', 'PF15094', 'PF15115', 'PF15102', 'PF15055', 'PF15149', 'PF15142', 'PF15166', 'PF15056'])) {
                        // $hoc_ky += $user->user_code == 'MA' ? 3400000 : 3600000; 
                        if ($englishStatus != '1') {
                            $totalFee['tieng_anh'] = 500000;
                        } else {
                            $totalFee['tieng_anh'] = 0;
                        }
                    }
                } else {
                    $totalFee['tieng_anh'] = 0;
                }

                $detailFee = $totalFee;
                $tieng_anh = $detailFee['tieng_anh'];
                $so_tien = $tieng_anh;
                $check = FeeMail::where('user_code', $fee->user_code)
                    ->where('term_name', $term_name)
                    ->where('brand_code', $fee->brand_code)
                    ->where('ki_thu', $ki_thu)
                    ->first();
                if (!$check) {
                    $check = FeeMail::create([
                        'user_login' => $fee->user_login,
                        'user_code' => $fee->user_code,
                        'term_name' => $term_name,
                        'brand_code' => $fee->brand_code,
                        'ki_thu' => $ki_thu,
                        'amount' => $so_tien,
                        'hoc_ky' => 0,
                        'tien_sach' => 0,
                        'tieng_anh' => $tieng_anh,
                        'version' => $version,
                        'note' => $note,
                        'english_level' => $english_level,
                        'study_status' => $fee->study_status,
                    ]);
                } else {
                    if ($version != $check->version) {
                        $check->version = $version;
                        $check->amount = $so_tien;
                        $check->status = 0;
                    }
                    
                    $check->english_level = $english_level;
                    $check->tieng_anh = $tieng_anh;
                    $check->tien_sach = 0;
                    $check->hoc_ky = 0;
                    $check->note = $note;
                    $check->save();
                }

                // if ($check->status == 0) {
                    $so_thu = $check->amount - $fee->study_wallet;
                    if ($so_thu <= 0) {
                        $check->status = 1;
                        $check->save();
                        continue;
                    } else {
                        $full_name = $user->full_name;
                        $checkZero = $detailFee['hoc_ky'] + $detailFee['tien_sach'] + $detailFee['tieng_anh'];
                        if ($checkZero <= 0 && $check->ki_thu == 7) {
                            dump($check->amount, $fee->study_wallet);
                        } else {
                            Mail::to($check->user_login . "")->queue(new \App\Mail\FeeMail($check, $fee, $full_name, $detailFee));
                        }
                        $check->status = 1;
                        $check->save();
                    }

            }
        }
    }

    /**
     * Datatable ajax request
     *
     * @return \Illuminate\Http\Response
     */
    public function historyDataTable(HistoryDataTable $datatable)
    {
        return $datatable->build()->toJson();
    }
}