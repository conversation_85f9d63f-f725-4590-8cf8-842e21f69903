<?php

namespace App\Http\Controllers\Admin;

use App\Exports\Feedback\BlockReviewExport;
use Illuminate\Support\Facades\Log;
use App\Exports\UsersExport;
use App\Imports\UsersImport;
use App\Repositories\Admin\FeedbackRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\DataTables\FeedbackBlockDataTable;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use App\Models\Fu\Department;
use App\Models\Fu\Term;
use App\Models\Fu\Block;
use App\Http\DataTables\FeedbackdataTable;
use Carbon\Carbon;

class FeedbackController extends Controller
{
    const searchBySemester = 1;
    private FeedbackRepository $FeedbackRepository;

    public function __construct(FeedbackRepository $feedbackRepository)
    {
        $this->FeedbackRepository = $feedbackRepository;
    }

    public function index(Request $request)
    {
        return $this->FeedbackRepository->index($request);
    }

    public function getGroupByTerm(Request $request)
    {
        return $this->FeedbackRepository->getGroupByTerm($request);
    }

    public function getBlockNameByTerm(Request $request)
    {
        return $this->FeedbackRepository->getBlockNameByTerm($request);
    }

    public function getFeedback(Request $request)
    {
        return $this->FeedbackRepository->getFeedback($request);
    }

    public function viewFeedback($id)
    {
        return $this->FeedbackRepository->viewFeedback($id);
    }

    public function test(Request $request)
    {
        $file = $request->file('file');
        $rows = Excel::toArray(new UsersImport, $file);
        $array = [];
        foreach ($rows[0] as $key => $row) {

            if ($key > 3) {
                if ($row[2]) {
                    $array[] = $row[2];
                }
            }
        }
        //        sort($array);
        //        dd($array);

        return Excel::download(new UsersExport($array), 'users.xlsx');
    }

    public function updateStatusById(Request $request)
    {
        return $this->FeedbackRepository->updateStatusById($request);
    }

    public function updateHitById(Request $request)
    {
        return $this->FeedbackRepository->updateHitById($request);
    }

    public function synAllCurrentClass(Request $request)
    {
        return $this->FeedbackRepository->synAllCurrentClass($request);
    }

    public function feedbackRole(Request $request)
    {
        return $this->FeedbackRepository->feedbackRole($request);
    }

    public function feedbackRoleEdit($id)
    {
        return $this->FeedbackRepository->feedbackRoleEdit($id);
    }

    public function feedbackRoleStore(Request $request)
    {
        return $this->FeedbackRepository->feedbackRoleStore($request);
    }

    private function getResultFeedbackBlock($request)
    {
        $teacher_login = isset(request()->inputTeacher) ? request()->inputTeacher : null;
        $term_id = isset(request()->inputSemester) ? request()->inputSemester : null;
        $department_id = isset(request()->inputDepartment) ? request()->inputDepartment : null;
        $is_review = isset(request()->inputIsReviewChecked) ? request()->inputIsReviewChecked : null;

        $query_GroupMember = GroupMember::query();
        $query_GroupMember->leftjoin('list_group', 'list_group.id', '=', 'group_member.groupid');
        $query_GroupMember->leftjoin('user as teacher', 'teacher.user_login', '=', 'group_member.leader_login');
        $query_GroupMember->leftjoin('user as student', 'student.user_login', '=', 'group_member.member_login');
        $query_GroupMember->leftjoin('department', 'department.id', '=', 'list_group.department_id');
        
        if($teacher_login != null && $teacher_login != '') {
            $query_GroupMember->where('list_group.teacher', $teacher_login);
        }

        if($term_id) {
            $query_GroupMember->where('list_group.pterm_id', $term_id);
        }

        if($department_id) {
            $query_GroupMember->where('list_group.department_id', $department_id);
        }

        if ($is_review != null && $is_review != '') {
            switch ($is_review) {
                case 0:
                    $query_GroupMember->where('list_group.danh_gia', '=', 0);
                    break;
                case 1:
                    $query_GroupMember->where('list_group.danh_gia', '>', 0);
                    break;
                default:
                    break;
            }
        }

        $query_GroupMember = $query_GroupMember->orderBy('group_member.groupid', 'desc')
            ->get([
                'list_group.id as group_id',
                'group_member.member_login as student_login',
                'list_group.group_name as group_name',
                'list_group.danh_gia as is_reviewed',
                'list_group.pterm_name as term_name',
                'list_group.pterm_id as term_id',
                'list_group.block_id as block_id',
                'list_group.block_name as block_name',
                'list_group.start_date as start_date',
                'list_group.end_date as end_date',
                'list_group.psubject_name as subject_name',
                'list_group.psubject_code as subject_code',
                'group_member.loai as evaluation_norm',
                'list_group.teacher as teacher_login',
                'list_group.group_fb_comment as group_fb_comment',
                'teacher.user_surname as teacher_surname',
                'teacher.user_middlename as teacher_middlename',
                'teacher.user_givenname as teacher_givenname',
                'student.user_code as student_user_code',
                'student.user_surname as student_surname',
                'student.user_middlename as student_middlename',
                'student.user_givenname as student_givenname',
                'department.department_name',
                'group_member.note as note',
                'group_member.note_comment as evaluation_note',
                'group_member.time_evaluate as time_evaluate'
            ])
            ->toArray();
        return $query_GroupMember;
    }

    public function checkFeedbackBlock(Request $request)
    {
        $all_terms = Term::orderBy('id', 'desc')->limit(5)->get(['id', 'term_name']);
        $all_department = Department::all(['id', 'department_name'])->toArray();
        $all_blocks = [];
        $data = [];
        $data = $this->getResultFeedbackBlock($request);
        if ($request->input('action') === 'export') {
            try {
                return Excel::download(new BlockReviewExport($data), 'feedback-block.xlsx');
            } catch (\Throwable $th) {
                Log::error('Error exporting feedback block', [
                    'function' => 'checkFeedbackBlock',
                    'line'     => $th->getLine(),
                    'message'  => $th->getMessage(),
                ]);
            }
        }
        return view('admin_v1.feedback.check_feedback', [
            'all_terms' => $all_terms,
            'all_blocks' => $all_blocks,
            'all_groups_by_name' => [],
            'all_department' => $all_department,
        ]);
    }

    public function searchGroup(Request $request)
    {
        $query_GroupMember = [];
        $all_blocks = [];
        $list_department = Department::all(['id', 'department_name'])->toArray();
        if (isset($request->term)) {
            $choosen_term = $request->term;
        } else {
            $date = Carbon::now()->subDays()->toDateTimeString();
            $choosen_term = Term::whereDate('term.startday', '<=', $date)
                ->whereDate('term.endday', '>=', $date)->pluck('id')[0];
        }
        $all_blocks = Block::where('term_id', $choosen_term)->get(['id', 'block_name', 'start_day', 'end_day']);
        $all_terms = Term::orderBy('id', 'desc')->limit(5)->get(['id', 'term_name']);
        return view('admin_v1.feedback.check_feedback', [
            'data' => $query_GroupMember,
            'all_terms' => $all_terms,
            'all_department' => $list_department,
            'all_blocks' => $all_blocks,
        ]);
    }

    public function updateFeedback(Request $request)
    {
        return $this->FeedbackRepository->updateFeedback($request);
    }

    /**
     * Datatable ajax request
     *
     * @return \Illuminate\Http\FeedbackdataTable
     */
    public function datatable(FeedbackdataTable $datatable)
    {
        return $datatable->build()->toJson();
    }
    /**
     * Datatable ajax request
     *
     * @return \Illuminate\Http\FeedbackdataTable
     */
    public function datatableFeedbackBlock(FeedbackBlockDataTable $datatable)
    {
        return $datatable->build()->toJson();
    }
}
