<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Repositories\Admin\NewFeedbackRepository;


class NewFeedbackController extends Controller
{
    protected $NewFeedbackRepository;
    const searchBySemester = 1;

    public function __construct(NewFeedbackRepository $newFeedbackRepository)
    {
        $this->NewFeedbackRepository = $newFeedbackRepository;
    }

    public function index(Request $request)
    {
        
    }

    public function edit(Request $request)
    {
        
    }

    public function updateStatus(Request $request)
    {
        
    }

    public function sync(Request $request)
    {
        
    }

    public function openListFeedBack(Request $request)
    {
        
    }

    public function changeStatus(Request $request)
    {
        return $this->NewFeedbackRepository->changeStatus($request);
    }

    public function syncData(Request $request)
    {
        return $this->NewFeedbackRepository->syncData($request);
    }

    public function openFeedback(Request $request)
    {
        return $this->NewFeedbackRepository->openFeedback($request);
    }

    public function updateConfigTimeFeedback(Request $request)
    {
        return $this->NewFeedbackRepository->updateConfigTimeFeedback($request);
    }

    public function openFeedbackByActivity(Request $request)
    {
        return $this->NewFeedbackRepository->openFeedbackByActivity($request);
    }

    public function syncFeedback(Request $request)
    {
        return $this->NewFeedbackRepository->syncFeedback($request);
    }

    public function setPlanerUser(Request $request)
    {
        return $this->NewFeedbackRepository->setPlanerUser($request);
    }
    
    public function exportFeedback(Request $request)
    {
        return $this->NewFeedbackRepository->exportFeedback($request);
    }
    
    public function exportFeedbackTerm(Request $request)
    {
        return $this->NewFeedbackRepository->exportFeedbackTerm($request);
    }
}
