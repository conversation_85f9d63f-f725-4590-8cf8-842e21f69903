<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Teacher\ScheduleController;

use App\Models\Fu\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;

class HomeController extends Controller
{
    public function home()
    {
        if (env('APP_ENV') == 'local') {
            $routes = Route::getRoutes();
            $route_array = [];
            foreach ($routes as $route) {
                if (isset($route->action['as']) && $route->action['prefix'] == 'api/v1') {
                    $route_array[str_replace('.','_',$route->action['as'])] = [
                        'name' => $route->action['as'],
                        'url' => "/$route->uri",
                        'prefix' => $route->action['prefix'],
                    ];
                }
            }
            $route_array = json_encode($route_array);
            Storage::put('public/local.js', "var routes = " . $route_array);
        }
        return view(env('THEME_ADMIN') . '.home', []);
    }

    public function cropper()
    {
        return view(env('THEME_ADMIN') . '.cropper');
    }

    public function login(Request $request)
    {
        $user_id = $request->username;
        $password = $request->password;
        $password = md5($password);

        $user = null;
        if ($user) {

            return redirect()->route('student.home');
        }

        return redirect()->route('home')->with(['status' => 'error']);
    }

    public function logout()
    {
        session()->flush();
        auth()->logout();

        return redirect()->route('home');
    }

    public function changeColor()
    {
        if (session('color') == 'light') {
            session(['color' => 'dark']);
        } else {
            session(['color' => 'light']);
        }

        return redirect(url()->previous());
    }

    public function storeMac(Request $request)
    {
        return $request->all();
    }

    public function authenticator(Request $request)
    {
        $secret = $request->one_time_password;
        $secret_key = $request->secret_key;
        $route = $request->route;
        $authenticator = $request->authenticator;
        $google2fa = app('pragmarx.google2fa');
        if ($authenticator == 'verify') {
            $secret_key = Auth::user()->google2fa_secret;
            if ($google2fa->verifyKey($secret_key, $secret)) {
                session(['verify_2fa' => true]);

                return redirect($route);
            }

            return redirect(url()->previous())->with(['status' => ['type' => 'danger', 'messages' => 'Mã xác thực không đúng hoặc đã hết hạn']]);
        }
        if ($google2fa->verifyKey($secret_key, $secret)) {
            $user = User::find(auth()->id());
            $user->google2fa_secret = $secret_key;
            $user->save();
            session(['verify_2fa' => true]);

            return $this->res('success', 'Authenticator successfully', route($route));
        }

        return $this->res('error', 'Authenticator failed');
    }
}
