<?php

namespace App\Http\Controllers\Admin;

use App\Repositories\Admin\LectureTeacherRepository;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class LectureTeacherController extends Controller
{
    public function __construct(LectureTeacherRepository $LectureTeacherRepository) {
        $this->LectureTeacherRepository = $LectureTeacherRepository;
    }

    public function indexLectureTeacher() 
    {
        return $this->LectureTeacherRepository->indexLectureTeacher();
    }

    public function getListLectureTeacher(Request $request) {
        return $this->LectureTeacherRepository->getListLectureTeacher($request);
    }
}
