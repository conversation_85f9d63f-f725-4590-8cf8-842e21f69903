<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Repositories\Admin\SmsRoleRepository;

class SmsRoleController extends Controller
{
    public function __construct(SmsRoleRepository $SmsRoleRepositoy)
    {
        $this->SmsRoleRepositoy = $SmsRoleRepositoy;
    }

    public function index()
    {
        return $this->SmsRoleRepositoy->index();
    }
    public function getRoles(Request $request)
    {
        return $this->SmsRoleRepositoy->getRoles();
    }
    public function getUserPermissions(Request $request)
    {
        return $this->SmsRoleRepositoy->getUserPermissions($request);
    }
    public function saveChangePermissions(Request $request)
    {
        return $this->SmsRoleRepositoy->saveChangePermissions($request);
    }
}
