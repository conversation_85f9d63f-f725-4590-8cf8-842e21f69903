<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\Admin\StudentWalletRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WalletController extends Controller
{
    protected $walletRepository;

    public function __construct(StudentWalletRepository $walletRepository)
    {
        $this->walletRepository = $walletRepository;
    }

    public function listWallets(Request $request)
    {
        Log::channel('wallet')->info('Xem danh sách ví', [
            'user' => auth()->user()->user_login,
            'ip' => request()->ip()
        ]);

        return $this->walletRepository->getWalletList($request);
    }

    public function createWalletForm()
    {
        return $this->walletRepository->createWalletForm();
    }

    public function createWallet(Request $request)
    {
        try {
            $result = $this->walletRepository->createWallet($request);

            Log::channel('wallet')->info('Tạo ví mới thành công', [
                'user' => auth()->user()->user_login,
                'student' => $request->user_login,
                'ip' => request()->ip()
            ]);

            return $result;
        } catch (\Exception $e) {
            Log::channel('wallet')->error('Lỗi tạo ví mới', [
                'user' => auth()->user()->user_login,
                'error' => $e->getMessage(),
                'ip' => request()->ip()
            ]);

            return redirect()->back()->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    public function lockWalletForm($id)
    {
        return $this->walletRepository->lockWalletForm($id);
    }

    public function lockWallet(Request $request, $id)
    {
        return $this->walletRepository->lockWallet($request, $id);
    }

    public function unlockWallet(Request $request, $id)
    {
        return $this->walletRepository->unlockWallet($id);
    }

    public function depositForm($id)
    {
        return $this->walletRepository->depositForm($id);
    }

    public function deposit(Request $request, $id)
    {
        return $this->walletRepository->deposit($request, $id);
    }

    public function exportWallets(Request $request)
    {
        return $this->walletRepository->exportWallets($request);
    }

    /**
     * Bulk lock wallets
     */
    public function bulkLockWallets(Request $request)
    {
        $request->validate([
            'wallet_ids' => 'required|array|min:1',
            'wallet_ids.*' => 'integer|exists:student_wallets,id',
            'reason' => 'required|string|max:255'
        ]);

        Log::channel('wallet')->info('Bulk lock wallets', [
            'user' => auth()->user()->user_login,
            'wallet_ids' => $request->wallet_ids,
            'reason' => $request->reason,
            'ip' => request()->ip()
        ]);

        return $this->walletRepository->bulkLockWallets($request);
    }

    /**
     * Bulk unlock wallets
     */
    public function bulkUnlockWallets(Request $request)
    {
        $request->validate([
            'wallet_ids' => 'required|array|min:1',
            'wallet_ids.*' => 'integer|exists:student_wallets,id'
        ]);

        Log::channel('wallet')->info('Bulk unlock wallets', [
            'user' => auth()->user()->user_login,
            'wallet_ids' => $request->wallet_ids,
            'ip' => request()->ip()
        ]);

        return $this->walletRepository->bulkUnlockWallets($request);
    }

    /**
     * Auto create wallet for new student
     */
    public function autoCreateWallet(Request $request)
    {
        $request->validate([
            'user_code' => 'required|string|max:20',
            'user_login' => 'required|string|max:50',
            'initial_balance' => 'nullable|numeric|min:0'
        ]);

        $result = $this->walletRepository->autoCreateWalletForNewStudent(
            $request->user_code,
            $request->user_login,
            $request->initial_balance ?? 0
        );

        if ($result['success']) {
            Log::channel('wallet')->info('Auto create wallet success', [
                'user_code' => $request->user_code,
                'user_login' => $request->user_login,
                'initial_balance' => $request->initial_balance ?? 0,
                'ip' => request()->ip()
            ]);

            return response()->json($result, 200);
        } else {
            Log::channel('wallet')->error('Auto create wallet failed', [
                'user_code' => $request->user_code,
                'user_login' => $request->user_login,
                'error' => $result['message'],
                'ip' => request()->ip()
            ]);

            return response()->json($result, 400);
        }
    }

    /**
     * Show import wallets form
     */
    public function importWalletsForm()
    {
        return $this->walletRepository->importWalletsForm();
    }

    /**
     * Show import deposits form
     */
    public function importDepositsForm()
    {
        return $this->walletRepository->importDepositsForm();
    }

    /**
     * Download import template
     */
    public function downloadImportTemplate(Request $request)
    {
        $type = $request->get('type', 'create');
        return $this->walletRepository->downloadImportTemplate($type);
    }

    /**
     * Process bulk import
     */
    public function processBulkImport(Request $request)
    {
        try {
            $result = $this->walletRepository->processBulkImport($request);

            Log::channel('wallet')->info('Bulk import processed', [
                'user' => auth()->user()->user_login,
                'action' => $request->action,
                'ip' => request()->ip()
            ]);

            return $result;
        } catch (\Exception $e) {
            Log::channel('wallet')->error('Bulk import failed', [
                'user' => auth()->user()->user_login,
                'error' => $e->getMessage(),
                'ip' => request()->ip()
            ]);

            return redirect()->back()->with('error', 'Import thất bại: ' . $e->getMessage());
        }
    }

    /**
     * Show wallet transactions
     */
    public function transactions($id)
    {
        Log::channel('wallet')->info('Xem lịch sử giao dịch ví', [
            'user' => auth()->user()->user_login,
            'wallet_id' => $id,
            'ip' => request()->ip()
        ]);

        return $this->walletRepository->transactions($id);
    }
}
