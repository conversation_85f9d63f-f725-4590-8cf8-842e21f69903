<?php

namespace App\Http\Controllers\Admin;

use App\Models\Fu\Term;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Models\Fu\Block;
use Illuminate\Support\Facades\DB;
class TermController extends Controller
{
    public function all(Request $request)
    {
        $terms = Term::orderBy('id','desc')->get();
        return $this->res('success','Get data success', $terms);
    }

    public function termlist(Request $request) {
        return Term::orderBy('startday', 'ASC')->get(['id', 'term_name', 'startday', 'endday']);
    }

    public function create(Request $request) {
        $enwTerm = Term::create([
            "term_name" => $request->term_name,
            "ordering" => 0,
            "startday" => $request->startday,
            "endday" => $request->endday
        ]);

        if ($enwTerm) {
            return response()->json([
                'status' => 'succes',
                'message' => 'Thành công',
                'data' => $enwTerm
            ], 200);
        } else {
            return response()->json([
                'status' => 'error',
                'message' => 'Không thể khởi tạo kỳ mới'
            ], 200);
        }
    }

    public function update(Request $request, Term $term) {
        if ($term) {
            $term->term_name = $request->term_name;
            $term->startday = $request->startday;
            $term->endday = $request->endday;
            if ($term->save()) {
                return response()->json([
                    'status' => 'success',
                    'message' => 'Update thành công'
                ], 200);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Update không thành công'
                ], 200);
            }
        }
    }

    public function delete(Request $request, $id) {
        DB::beginTransaction();
        try {
            if (Block::where('term_id', $id)->delete()) {
                if (Term::where('id', $id)->delete()) {
                    DB::commit();
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Xoá kỳ thành công'
                    ], 200);
                }
            }
        }
        catch (\Exception $e) {
            DB::rollBack();
            return $e;
        }
    }

    public function blockUpdate(Request $request, $id) {
        if ($term = Term::findOrFail($id)) {
            $block = Block::create([
                'term_id' => $id,
                'block_name' => $request->block_name,
                'start_day' => $request->start_day,
                'end_day' => $request->end_day
            ]);
            if ($block) {
                return response()->json([
                    'status' => 'success',
                    'message' => 'Khởi tợi block thành công'
                ], 200);
            }
        }
    }

    public function termDetail(Request $request, Term $term) {
        if ($term) {
            return response()->json([
                "term" => $term,
                "blocks_in_term" => Block::where('term_id', $term->id)->get(['id', 'term_id', 'block_name', 'start_day', 'end_day'])
            ], 200);
        }
    }
}
