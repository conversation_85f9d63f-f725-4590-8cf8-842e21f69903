<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Fu\Activity;
use App\Models\Fu\Attendance;
use App\Models\Fu\Course;
use App\Models\Fu\Department;
use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use App\Models\Fu\Prerequisite;
use App\Models\Fu\Slot;
use App\Models\Fu\Subject;
use App\Models\Fu\Term;
use App\Models\SystemLog;
use App\Models\T7\CourseGrade;
use App\Models\T7\CourseResult;
use App\Models\T7\SyllabusPlan;
use App\Models\Transcript;
use App\Repositories\Admin\ActivityRepository;
use App\Repositories\Admin\GroupRepository;
use App\Http\DataTables\ManagementStudentDataTable;
use App\Models\Fu\GroupDelete;
use App\Models\Fu\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Models\Fu\Block;
use App\Models\Fu\Room;
use App\Models\T7\Grade;
use App\Models\T7\GradeGroup;
use App\Models\T7\GradeSyllabus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class GroupController extends Controller
{
    public function __construct(GroupRepository $groupRepository)
    {
        $this->groupRepository = $groupRepository;
    }

    const MODEL = 'group';
    protected $groupRepository;


    /**
     * Datatable ajax request
     *
     * @return \App\Http\DataTables\ManagementStudentDataTable
     */
    public function datatable(ManagementStudentDataTable $datatable)
    {
        return $datatable->build()->toJson();
    }

    /**
     * <AUTHOR>
     * Quản lý rút, thêm sinh viên
     */
    public function managerStudents(Request $request)
    {
        return $this->groupRepository->managerStudents($request);
    }

    /**
     * <AUTHOR>
     * Quản lý chuyển lớp cho sinh viên
     */
    public function suitableSchedule(Request $request)
    {
        return $this->groupRepository->suitableSchedule($request);
    }

    /**
     * <AUTHOR>
     * Quản lý chuyển lớp cho sinh viên
     */
    public function managerSchedule(Request $request)
    {
        return $this->groupRepository->managerSchedule($request);
    }

    /**
     * <AUTHOR>
     * lấy danh sách lớp có thể chuyển
     */
    public function getListClassCanJoin(Request $request)
    {
        return $this->groupRepository->getListClassCanJoin($request);
    }



    /**
     * <AUTHOR>
     * rút sinh viên ra khỏi lớp bằng file
     */
    public function removeStudentFile(Request $request)
    {
        return $this->groupRepository->removeStudentFile($request);
    }

    /**
     * <AUTHOR>
     * Thêm sinh viên vào lớp bằng file
     */
    public function addStudentFile(Request $request)
    {
        return $this->groupRepository->addStudentFile($request);
    }


    /**
     * <AUTHOR>
     * Thêm sinh viên vào lớp bằng file (danh cho cán bộ quản lý lớp)
     */
    public function updateScheduleNoCheck(Request $request)
    {
        return $this->groupRepository->updateScheduleNoCheck($request);
    }

    /**
     * <AUTHOR> 
     * tạo lịch cho lớp chưa có lịch
     */
    public function storeActivityNew(Request $request)
    {
        return $this->groupRepository->storeActivityNew($request);
    }

    /**
     * <AUTHOR> 
     * lấy danh sách lớp theo kỳ
     */
    public function syncInfo(Request $request)
    {
        ini_set('max_execution_time', -1);
        return $this->groupRepository->syncInfo($request);
    }

    /**
     * <AUTHOR> 
     * lấy danh sách lớp theo kỳ
     */
    public function export(Request $request)
    {

        ini_set('max_execution_time', -1);

        $datas = Group::where('is_virtual', 0)
            ->where('pterm_id', $request->term_id)
            ->leftJoin('room', 'room.id', '=', 'list_group.room_id')
            ->select([
                'list_group.id',
                'list_group.group_name',
                'list_group.block_name',
                'list_group.skill_code',
                'list_group.psubject_code',
                'list_group.psubject_name',
                'list_group.slot',
                'list_group.start_date',
                'list_group.end_date',
                'list_group.number_student',
                'list_group.teacher',
                'room.id as room_id',
                'room.room_name',
                'room.description',
            ])
            ->get();

        // Định nghĩa tiêu đề cho file CSV
        $header = [
            'id lớp',
            'Tên lớp',
            'Học phần',
            'Mã Chuyển đổi',
            'Mã môn',
            'Tên môn',
            'Ca học',
            'Ngày bắt đầu',
            'Ngày kết thúc',
            'Số lượng sinh viên',
            'Giảng viên',
            'ID phòng',
            'Tên phòng',
            'Mô tả phòng'
        ];

        $fileName = 'Danh_sach_lop_' . time() . '.csv';
        $filePath = storage_path('app/' . $fileName);

        // Ghi dữ liệu vào file CSV
        $csvFile = fopen($filePath, 'w');

        // Thêm BOM UTF-8 để tránh lỗi font khi mở bằng Excel
        fprintf($csvFile, chr(0xEF) . chr(0xBB) . chr(0xBF));

        // Ghi dòng tiêu đề
        fputcsv($csvFile, $header);

        // Ghi dữ liệu
        foreach ($datas as $line) {
            fputcsv($csvFile, $line->toArray());
        }

        fclose($csvFile);

        // Trả về file để tải xuống
        return response()->download($filePath)->deleteFileAfterSend(true);
    }


    public function index(Request $request)
    {
        $subject = [];
        $term_id = $request->term_id;
        $course_id = $request->course_id;
        $department_id = $request->department_id;
        $keyword = $request->get('keyword', null);
        $block_name = $request->get('block_name', null);

        $terms = Term::orderBy('id', 'desc')->get();
        if (!$term_id) {
            $term_id = $terms->first()->id;
        }

        if ($department_id) {
            $subject = Subject::where('department_id', $department_id)->get();
        }

        $department = Department::orderBy('department_name')->get();
        $course = Course::when($department_id, function ($query) use ($subject) {
            $query->whereIn('subject_id', $subject->pluck('id'));
        })->orderBy('psubject_code')->where('term_id', $term_id)->get();
        if ($course_id) {
            $course_search = $course->where('id', $course_id)->pluck('id');
        } else {
            $course_search = [];
        }

        $result_main = Group::with(['cam_thi_total:id,groupid'])
            ->select([
                'id',
                'group_name',
                'psubject_code',
                'psubject_name',
                'pterm_id',
                'block_id',
                'block_name',
                'teacher',
                'number_student',
                'start_date',
                'end_date',
            ])
            ->where('list_group.is_virtual', 0);
        if ($keyword != '' || $keyword != null) {
            $result_main = $result_main->when($keyword, function ($query, $keyword) {
                $query->where('group_name', 'like', "%$keyword%");
            });
        }

        if ($block_name != '' || $block_name != null) {
            $result_main = $result_main->when($block_name, function ($query, $block_name) {
                $query->where('block_name', $block_name);
            });
        }

        if (count($course_search) > 0) {
            $result_main = $result_main->whereIn('body_id', $course_search);
        }

        $result_main = $result_main->where('pterm_id', $term_id)
            ->orderBy('id', 'desc')
            ->paginate(20);

        $listGroupId = [];
        foreach ($result_main as $item_main) {
            $listGroupId[] = $item_main->id;
        }

        $allActivities = Activity::select(['leader_login', 'slot', 'groupid'])
            ->whereIn('groupid', $listGroupId)
            ->get();

        foreach ($result_main as $item_main) {
            $leader_login = [];
            $slots = [];
            $activities = $allActivities->where('groupid', $item_main->id);
            foreach ($activities as $activity) {
                $item_leader_login = strtolower($activity->leader_login);
                if (isset($leader_login[$item_leader_login])) {
                    $leader_login[strtolower($activity->leader_login)]++;
                } else {
                    $leader_login[$item_leader_login] = 1;
                }

                if (isset($slots[$activity->slot])) {
                    $slots[$activity->slot]++;
                } else {
                    $slots[$activity->slot] = 1;
                }
            }
            // $item_main->block_name = $item_main->block_name == 'block 1' ? 'Học phần 1' : 'Học phần 2';
            // dd($item_main);
            $item_main->leader_logins = $leader_login;
            $item_main->slots = $slots;
        }
        return view('admin_v1.group.index', [
            'model' => self::MODEL,
            'result_main' => $result_main,
            'terms' => $terms,
            'course' => $course,
            'department' => $department,
        ]);
    }

        /**
         * Hiển thị chi tiết của lớp
         * @param Request $request
         * @param $id
         * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
         */
    public function edit($id, Request $request)
    {
        $tab_active = $request->tab_active;
        $tab_data = [];
        $group = Group::with(['course'])->find($id);
        if (!$group) {
            return redirect()->route('admin.group.index');
        }

        $slots = Slot::all();
        $terms = Term::orderBy('id', 'desc')->get();
        $department = Department::all();
        $group_deparment = Department::find($group->department_id);
        $group->department_name = $group_deparment->department_name;
        switch ($tab_active) {
            case 'thanh_vien':
                $group->load(['groupMembers' => function ($query) {
                    $query->with(['user', 'my_phone']);
                }]);
                Group::where('id', $id)->update([
                    'number_student' => $group->groupMembers->count()
                ]);
                $group = $this->mutantGroupMember($group);
                break;
            case 'ke_hoach':
                $this->activities($group);
                $temp = $group->syllabus_plan;
                foreach ($temp as $key => $plan) {
                    $temp[$key]['date_l'] = $plan['date'] ? '- ' . Carbon::createFromDate($plan['date'])->format('l') : '';
                }
                $group->syllabus_plan = $temp;
                break;
            case 'bang_diem':
                $this->gradeScore($group, $group->body_id);
                // return redirect()->route('admin.edu.so_diem', [
                //     'term_id' => $group->pterm_id,
                //     'department_id' => $group->department_id,
                //     'course_id' => $group->body_id,
                //     'group_id' => $group->id,
                // ]);
                break;
            case 'diem_danh':
                $group->load(['groupMembers' => function ($query) {
                    $query->with(['user', 'my_phone']);
                }]);
                $group = $this->mutantGroupMember($group);
                $this->activities($group);
                $this->attendance($group);
                break;
            case 'lop_khac':
                $group->another_class = Group::where('psubject_code', $group->psubject_code)->where('list_group.is_virtual', 0)->where('pterm_id', $group->pterm_id)->orderBy('block_id')->get();
                break;
            case 'lich_su':
                $group->logs = SystemLog::where('object_name', 'group')->where('object_id', $group->id)->orderBy('id', 'desc')->paginate(100);
            default:
                $group->load(['groupMembers' => function ($query) {
                    $query->with(['user', 'my_phone']);
                }]);
                $group = $this->mutantGroupMember($group);
        }

        $rooms = Room::all();
        // Loại bỏ phòng họp.
        $nomalRooms = $rooms->where('room_type', '!=', 5);
        $onlineRooms = $rooms->where('room_type', 4);
        $teachers = User::where('user_level', '!=', 3)->get();

        return view('admin_v1.group.edit', [
            'model' => self::MODEL,
            'tab_data' => $tab_data,
            'group' => $group,
            'slots' => $slots,
            'rooms' => $rooms,
            'terms' => $terms,
            'department' => $department,
            'nomalRooms' => $nomalRooms,
            'onlineRooms' => $onlineRooms,
            'teachers' => $teachers,
        ]);
    }

    public function getBlocksByTerm(Request $request)
    {
        $blocks = \App\Models\Fu\Block::where('term_id', $request->term_id)->get();
        return response()->json($blocks);
    }

    public function gradeScore(&$group, $course_id)
    {
        try {
            $history_status = config('status')->history_status;
            $group_grade_id = [];
            $grade_filter = [];
            $grade_temp = [];
            $grade_lock_list = $group->grade_lock_list;
            if ($grade_lock_list != '') {
                $grade_lock_list = explode(',', $grade_lock_list);
            } else {
                $grade_lock_list = [];
            }

            $students = GroupMember::where('groupid', $group->id)
                ->orderBy('member_login')
                ->get();
            $group_grades = GradeGroup::query()
                ->where('syllabus_id', $group->syllabus_id)
                ->where('subject_id', $group->psubject_id)
                ->orderBy('id')
                ->get();
            $grades = Grade::select('id', 'grade_name', 'grade_group_id', 'weight', 'minimum_required', 'is_final_exam', 'bonus_type', 'max_point')
                ->where('subject_id', $group->psubject_id)
                ->where('syllabus_id', $group->syllabus_id)
                ->orderBy('grade_name')
                ->orderBy('grade_group_id')
                ->get();

            $checkReload = false;
            if (sizeof($group_grades) == 0) {
                $checkReload = true;
                GradeGroup::where('subject_id', 0)->where('syllabus_id', $group->syllabus_id)->update(['subject_id' => $group->psubject_id]);
            }
            if (sizeof($grades) == 0) {
                $checkReload = true;
                Grade::where('subject_id', 0)->where('syllabus_id', $group->syllabus_id)->update(['subject_id' => $group->psubject_id]);
            }
            if ($checkReload) return back();

            $syllabus = GradeSyllabus::find($group->syllabus_id);
            $tb_temp_grade = [];
            foreach ($group_grades as $item) {
                $group_grade_id[] = $item->id;
                $tb_temp_grade[$item->id]['weight'] = $item->weight;
                $tb_temp_grade[$item->id]['tb_grade'] = 0;
                $tb_temp_grade[$item->id]['minimum_required'] = $item->minimum_required ?? 0;

                foreach ($grades as $grade) {
                    if ($item->id == $grade->grade_group_id) {
                        $grade_filter[] = $grade;
                    }
                }
            }
            foreach ($grade_filter as $item) {
                $item->locked = in_array($item->id, $grade_lock_list) ? 1 : 0;

                $grade_temp[$item->id] = [
                    'point' => null,
                    'weight' => $item->weight,
                    'minimum_required' => $item->minimum_required,
                    'is_final_exam' => $item->is_final_exam,
                    'grade_group_id' => $item->grade_group_id,
                    'pass' => false,
                    'bonus_type' => $item->bonus_type,
                    'locked' => 0,
                    'max_point' => $item->max_point,
                ];
            }
            foreach ($students as $student) {
                $student->load('user');
                if (!isset($student->user->user_code)) {
                    throw new \Exception("User code not found");
                }
                $student->user_code = $student->user->user_code;
                $tong = 0;
                $grades_temp = CourseGrade::where('login', $student->member_login)->where('groupid', $student->groupid)->whereIn('grade_group_id', $group_grade_id)->get();
                $lich_su = CourseResult::select('val', 'grade')->where('student_login', $student->member_login)->where('groupid', $student->groupid)->where('course_id', $course_id)->where('subject_id', $group->psubject_id)->first();
                if ($lich_su) {
                    $status_subject = $lich_su->val;
                } else {
                    $status_subject = 0;
                }

                $temp = $grade_temp;
                $final_score = 0;
                $countPointIsNull = count($grades_temp) == count($grade_filter);
                $danger_point = 0;
                $idGradeGroup = 0;
                $tbGrade = 0;
                foreach ($grades_temp as $item) {

                    if (isset($temp[$item->grade_id])) {
                        $temp[$item->grade_id]['point'] = $item->val;
                        $temp[$item->grade_id]['locked'] = $item->locked;
                        if ($temp[$item->grade_id]['point'] >= $temp[$item->grade_id]['minimum_required']) {
                            $temp[$item->grade_id]['pass'] = true;
                        }

                        // check + diem final 1 hay final 2nd vao tong
                        if ($item->is_final === 1) {
                            if ($item->is_resit < 1) {
                                $final_score = ($temp[$item->grade_id]['point'] * $temp[$item->grade_id]['weight']) / 100;
                            } else {
                                if ($temp[$item->grade_id]['point'] !== null) {
                                    $tong -= $final_score;
                                }
                            }
                        }
                        $tong += ($temp[$item->grade_id]['point'] * $temp[$item->grade_id]['weight']) / 100;
                        // Kiểm tra sinh viên có trượt do nhóm điểm thành phần không
                        if ($status_subject == 0) {
                            // loai điểm, nhóm điểm kết thúc môn, điểm bonus
                            if ($temp[$item->grade_id]['is_final_exam'] == 1) {
                                if (isset($temp[$item->grade_id]['grade_group_id'])) {
                                    unset($temp[$item->grade_id]['grade_group_id']);
                                }
                                continue;
                            }
                            if ($temp[$item->grade_id]['bonus_type'] == 1) {
                                continue;
                            }
                            // kiểm tra có đầu điểm nào trượt trong nhóm không phải nhóm điểm kết thúc môn
                            if (!$temp[$item->grade_id]['pass']) {
                                $danger_point++;
                            }

                            $idGradeGroup = $item['grade_group_id'];
                            if ($tb_temp_grade[$idGradeGroup]['weight'] != 0) {
                                $tbGrade += floatval($temp[$item->grade_id]['point']) * ($temp[$item->grade_id]['weight'] / $tb_temp_grade[$idGradeGroup]['weight']);
                            }
                            $tb_temp_grade[$item->id]['tb_grade'] = 0;
                        }
                    }
                }
                if ($idGradeGroup != 0) {
                    if ($tbGrade < $tb_temp_grade[$idGradeGroup]['minimum_required']) {
                        $danger_point++;
                    }
                    if ($danger_point > 0) {
                        $status_subject = -2;
                    }
                }
                $student->grade_point = $temp;
                $student->tong = round($lich_su->grade ?? 0, 1);
                $student->status_subject = $history_status[$status_subject];
                $student->enough_point = $countPointIsNull;
            }

            $group_grade_id_text = implode(',', $group_grade_id);
            $group->grades = $grades ?? [];
            $group->syllabus = $syllabus ?? [];
            $group->students = $students ?? [];
            $group->group_grades = $group_grades ?? [];
            $group->grade_filter = $grade_filter ?? [];
            $group->group_grade_id_text = $group_grade_id_text;
        } catch (\Exception $e) {
            Log::error($e);
            $group->grades = [];
            $group->syllabus = [];
            $group->students = [];
            $group->group_grades = [];
            $group->grade_filter = [];
            $group->group_grade_id_text = [];
        }
    }

    public function mutantGroupMember($group)
    {
        $listUserLogin = $group->groupMembers->pluck('member_login')->toArray();
        // dd($listUserLogin);
        $listCount = CourseResult::select([
            'student_login',
            DB::raw('count(id) as data')
        ])
            ->whereIn('student_login', $listUserLogin)
            ->where('skill_code', $group->skill_code)
            ->groupBy('student_login')
            ->pluck('data', 'student_login');

        foreach ($group->groupMembers as $member) {
            $member->luot_hoc = $listCount[$member->user->user_login ?? 'x'] ?? 1;
        }
        return $group;
    }

    public function deleteMember($id)
    {
        $member = GroupMember::find($id);
        if (!$member) {
            return redirect(route('admin.group.index'));
        }

        $activity_check = Activity::where('groupid', $member->groupid)->where('done', 1)->count();
        $activity_all =  Activity::where('groupid', $member->groupid)->count();
        $activity_all = ($activity_all > 0) ? $activity_all : 1;
        if ((($activity_check / $activity_all) * 100) > 25) {
            return $this->redirectWithStatus('danger', "Lớp đã học quá 30% không thể xoá sinh viên", route('admin.group.index.edit', [
                'id' => $member->groupid,
            ]));
        }

        DB::beginTransaction();
        try {
            $group = Group::find($member->groupid);
            SystemLog::create([
                'actor' => Auth::user()->user_login,
                'object_name' => "group",
                'log_time' => now(),
                'action' => "remove member",
                'description' => "remove member $member->member_login from group $member->groupid",
                'relation_login' => $member->member_login,
                'object_id' => $member->groupid,
                'relation_id' => 0,
                'brief' => "remove member",
                'from_ip' => request()->ip(),
                'nganh_cu' => '',
                'nganh_moi' => '',
                'ky_chuyen_den' => 0,
                'ky_thu_cu' => 0,
            ]);

            CourseResult::where('student_login', $member->member_login)->where('groupid', $member->groupid)->delete();
            CourseGrade::where('login', $member->member_login)->where('groupid', $member->groupid)->delete();
            Attendance::where('user_login', $member->member_login)->where('groupid', $member->groupid)->delete();
            $member->delete();
            $group->number_student = $group->number_student - 1;
            $group->save();

            DB::commit();
            return $this->redirectWithStatus('success', "Xoá thành viên $member->member_login thành công", route('admin.group.index.edit', ['id' => $member->groupid]));
        } catch (\Exception $exception) {
            DB::rollback();
            return $this->redirectWithStatus('danger', "Đã có lỗi xảy ra", route('admin.group.index.edit', ['id' => $member->groupid]));
        }
    }

    public function activities(&$group)
    {
        $data = [];
        $data_activity = [];
        $syllabus_id = $group->syllabus_id;
        $syllabus = SyllabusPlan::select([
            't7_syllabus_plan.*',
            'session_type.is_exam',
        ])
            ->leftJoin('session_type', 't7_syllabus_plan.session_type', '=', 'session_type.id')
            ->where('syllabus_id', $syllabus_id)
            ->orderBy('course_session')
            ->get();
        foreach ($syllabus as $item_syllabus) {
            $data[$item_syllabus->course_session] = [
                'description' => $item_syllabus->description,
                'date' => '',
                'leader_login' => '',
                'room_name' => '',
                'activity_id' => '',
                'is_online' => '',
                'is_done' => 0,
                'slot' => 0,
                'attendance' => [],
                'is_exam' => $item_syllabus->is_exam,
            ];
        }

        $activities = Activity::select([
            'activity.*',
            'session_type.is_exam',
        ])
            ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
            ->where('groupid', $group->id)
            ->get();
        foreach ($activities as $activity) {
            $data_activity[$activity->id] = $activity->day;
            if (isset($data[$activity->course_slot])) {
                $data[$activity->course_slot]['date'] = $activity->day;
                $data[$activity->course_slot]['leader_login'] = $activity->leader_login;
                $data[$activity->course_slot]['room_name'] = $activity->room_name;
                $data[$activity->course_slot]['activity_id'] = $activity->id;
                $data[$activity->course_slot]['is_online'] = $activity->is_online ? "<b>Online</b>" : "Offline";
                $data[$activity->course_slot]['is_done'] = $activity->done;
                $data[$activity->course_slot]['slot'] = $activity->slot;
                $data[$activity->course_slot]['is_exam'] = $activity->is_exam;
            }
        }
        $group->syllabus_plan = $data;
        $group->data_activity = $data_activity;
    }

    public function storeActivity(Request $request)
    {
        try {
            $old_id = [];
            $data_update = [];
            $data_create = [];
            $report_create = [];
            $report_update = [];
            $id = $request->id;
            $action = $request->action;
            $term_id = $request->term_id;
            $group_name = $request->group_name;
            $activities = $request->activities;
            $subject_code = $request->subject_code;

            $group = Group::where('group_name', $group_name)
                ->where('psubject_code', $subject_code)
                ->where('pterm_id', $term_id)
                ->first();
    
            if ($action == 'generate') {
                $activity_check = Activity::where('groupid', $id)->where('done', 1)->count();
                if ($activity_check > 0) {
                    return $this->redirectWithStatus('danger', "Lớp đã bắt đầu không thể tạo lịch tự động", route('admin.group.index.edit', [
                        'id' => $id,
                        'tab_active' => 'ke_hoach',
                    ]));
                }
    
                $activities = $this->generate($request);
                if (is_string($activities)) {
                    return $this->redirectWithStatus('danger', $activities, route('admin.group.index.edit', [
                        'id' => $id,
                        'tab_active' => 'ke_hoach',
                    ]));
                }
            }
    
            foreach ($activities as $key => $activity) {
                if (!$activity['date'] || (!$activity['slot'] && !is_numeric($activity['slot'])) || !$activity['room_name'] || !$activity['leader_login']) {
                    continue;
                }
    
                if ($activity['is_done'] == 1) {
                    continue;
                }
    
                $action_handle = $activity['activity_id'] ? 'data_update' : 'data_create';
                if ($activity['activity_id']) {
                    $old_id[] = $activity['activity_id'];
                }
    
                ${$action_handle}[$key] = [
                    $activity['date'],
                    $activity['slot'],
                    $activity['room_name'],
                    $activity['leader_login'],
                    $subject_code,
                    $key,
                    $group_name,
                ];
            }
    
            if (count($data_create)) {
                $report_create = ActivityRepository::process($data_create, $term_id, [
                    'action' => 'create',
                    'old_id_activity' => [],
                    'group' => $group
                ]);
            }
    
            if (count($data_update)) {
                $report_update = ActivityRepository::process($data_update, $term_id, [
                    'action' => 'update',
                    'old_id_activity' => $old_id,
                    'group' => $group
                ]);
            }
    
            $report = array_merge($report_create, $report_update);
    
            return $this->redirectWithStatus(is_array($report) ? 'success' : 'danger', is_array($report) ? implode('<br>', $report) : $report, route('admin.group.index.edit', [
                'id' => $id,
                'tab_active' => 'ke_hoach',
            ]));
        } catch (\Throwable $th) {
            Log::error($th);
            return $this->redirectWithStatus('danger', 'Có lỗi xảy ra', route('admin.group.index.edit', [
                'id' => $id,
                'tab_active' => 'ke_hoach',
            ]));
        }
    }

    public function generate($request)
    {
        $number_slots = $request->number_slots;
        $leader_login = $request->leader_login;
        $start_day = $request->start_day;
        $lt_room = $request->lt_room;
        $lt_slot = $request->lt_slot;
        $th_room = $request->th_room;
        $th_slot = $request->th_slot;
        $id = $request->id;
        $slot_max = 10;
        $number_slots = explode(',', $number_slots);
        $i_day_of_week = 0;
        $day_of_week_temp = [];
        $group = Group::find($id);
        $holiday = [
            '2020-10-3'
        ];
        $data_day = [
            2 => 1,
            3 => 2,
            4 => 3,
            5 => 4,
            6 => 5,
            7 => 6,
            8 => 0,
        ];
        $last_day_temp = null;
        $last_i_temp = null;
        $duplicate = false;
        $activities = self::activities($group);
        if ($activities && count($activities->syllabus_plan) == 0) {
            return "Không có kế hoạch ở syllabus";
        }

        foreach ($number_slots as $number) {
            if ($number < 2 || $number > 8) {
                continue;
            }
            $day_of_week_temp[] = $data_day[$number];
        }

        if (count($day_of_week_temp) == 0) {
            return "Dữ liệu ca học không đúng";
        }

        sort($day_of_week_temp);
        $start_day = Carbon::createFromDate($start_day);
        $i_temp = 0;
        $length_temp = count($activities->syllabus_plan) - 1;
        $swap = 0;
        $syllabus_plan_temp = $activities->syllabus_plan;
        foreach ($syllabus_plan_temp as $key => $activity) {
            if ($activity['is_exam'] == 1) {
                continue;
            }

            $day_of_week = $day_of_week_temp[$i_day_of_week];
            while ($day_of_week != $start_day->dayOfWeek) {
                $start_day->addDay();
            }

            if ($duplicate) {
                if ($syllabus_plan_temp[$last_i_temp]['slot'] == $slot_max) {
                    $slot = $syllabus_plan_temp[$last_i_temp]['slot'];
                } else {
                    $slot = $syllabus_plan_temp[$last_i_temp]['slot'] + 1;
                }
                $room = $syllabus_plan_temp[$last_i_temp]['room_name'];
            } else {
                $slot = $swap ? $lt_slot : $th_slot;
                $room = $swap ? $lt_room : $th_room;
            }

            $syllabus_plan_temp[$key]['slot'] = $slot;
            $syllabus_plan_temp[$key]['room_name'] = $room;
            $syllabus_plan_temp[$key]['date'] = $start_day->format('Y-m-d');
            $syllabus_plan_temp[$key]['leader_login'] = $leader_login;

            if ($i_day_of_week == count($day_of_week_temp) - 1) {
                $i_day_of_week = 0;
            } else {
                $i_day_of_week++;
            }
            $last_i_temp = $key;
            if ($i_temp != $length_temp && $day_of_week == $day_of_week_temp[$i_day_of_week]) {
                $start_day->subDay();
                $duplicate = true;
            } else {
                $start_day->addDay();
                $duplicate = false;
                $swap = $swap ? 0 : 1;
            }
            $i_temp++;
        }

        return $syllabus_plan_temp;
    }

    public function attendance(&$group)
    {
        $temp_activity = $group->syllabus_plan;
        foreach ($group->groupMembers as $member) {
            $array = [];
            $absent = 0;
            $total_session = 0;
            $attendance = Attendance::where('user_login', $member->member_login)->where('groupid', $group->id)->get();
            $course_result = CourseResult::where('student_login', $member->member_login)->where('groupid', $group->id)->first();
            if ($course_result) {
                $total_session = $course_result->total_session;
                $absent_percent = $course_result->total_session ? round(($course_result->attendance_absent * 100) / $course_result->total_session) : 0;
            }
            foreach ($attendance as $item) {
                $array[$item->activity_id][$item->day] = $item->val;
                if ($item->val === 0) {
                    $absent += 1;
                }
            }
            $temp_attendance = [];
            foreach ($temp_activity as $course_slot => $activity) {
                if ($activity['is_done']) {
                    if (isset($array[$activity['activity_id']][$activity['date']]) && $array[$activity['activity_id']][$activity['date']] == 0) {
                        $type = 0;
                    } else {
                        if (isset($array[$activity['activity_id']][$activity['date']])) {
                            $type = 1;
                        } else {
                            $type = -2;
                        }
                    }
                } else {
                    $type = -1;
                }
                $temp_attendance[$course_slot] = $type;
            }
            $member->attendance = $temp_attendance;
            $member->absent = $absent;
            $member->absent_percent = $absent_percent ?? '-';
            $member->total_session = $total_session;
        }

        // return $group;
    }

    public function getCourse(Request $request)
    {
        $subjects = Subject::select('id')->where('department_id', $request->department_id)->get();
        $subjects = $subjects->pluck('id');
        $course = Course::where('term_id', $request->term_id)->whereIn('subject_id', $subjects)->get();

        return $this->res('success', 'get course success', $course);
    }

    public function store(Request $request)
    {
        $action = $request->action;
        $reports = [];
        switch ($action) {
            case "create":
                // Validate input
                $term = Term::find($request->pterm_id);
                $block = Block::find($request->block_id);
                $course = Course::find($request->body_id);
                $subject = Subject::find($course->subject_id);

                $request->merge([
                    'lastmodifier_login' => auth()->user()->user_login,
                    'psubject_id' => $subject->id,
                    'skill_code' => $subject->skill_code,
                    'psubject_code' => $subject->subject_code,
                    'psubject_name' => $subject->subject_name,
                    'pterm_name' => $term->term_name,
                    'num_of_credit' => $subject->num_of_credit,
                    'syllabus_id' => $course->syllabus_id,
                    'attendance_required' => $course->attendance_required,
                    'start_date' => $term->startday,
                    'end_date' => $term->endday,
                    'block_id' => $block->id,
                    'block_name' => $block->block_name,
                    'department_id' => $subject->department_id,
                ]);
                Group::create($request->except(['action', '_token']));

                return redirect()->route('admin.group.index');
            case "update":
                $check_activity = Activity::where('groupid', $request->id)->where('done', 1)->count();
                if ($check_activity > 0 &&  auth()->user()->user_level != 1) {
                    return $this->redirectWithStatus('warning', 'Lớp đã học không thể chỉnh sửa', route('admin.group.index.edit', ['id' => $request->id]));
                }

                $currentGroup = Group::where('id', $request->id)->first();
                $oldData = [
                    'pterm_id' => (int)$currentGroup->pterm_id,
                    'block_id' => (int)$currentGroup->block_id,
                    'body_id' => (int)$currentGroup->body_id,
                    'group_name' => $currentGroup->group_name,
                    'teacher' => $currentGroup->teacher,
                    'start_date' => $currentGroup->start_date,
                    'end_date' => $currentGroup->end_date,
                ];

                $currentGroup->pterm_id = $request->get('pterm_id', $currentGroup->pterm_id);
                $currentGroup->block_id = $request->get('block_id', $currentGroup->block_id);

                // Lấy thông tin block mới nếu có thay đổi
                if ($request->has('block_id') && $request->block_id != $currentGroup->block_id) {
                    $block = Block::find($request->block_id);
                    if ($block) {
                        $currentGroup->block_name = $block->block_name;
                    }
                }

                $currentGroup->body_id = $request->get('body_id', $currentGroup->body_id);
                $currentGroup->group_name = $request->get('group_name', $currentGroup->group_name);
                $currentGroup->teacher = $request->get('teacher', $currentGroup->teacher);

                // Xử lý các trường ngày tháng
                if ($request->has('start_date')) {
                    $startDate = \DateTime::createFromFormat('d/m/Y', $request->start_date);
                    if ($startDate) {
                        $currentGroup->start_date = $startDate->format('Y-m-d');
                    }
                }

                if ($request->has('end_date')) {
                    $endDate = \DateTime::createFromFormat('d/m/Y', $request->end_date);
                    if ($endDate) {
                        $currentGroup->end_date = $endDate->format('Y-m-d');
                    }
                }

                if (request('users')) {
                    $student_codes = explode(',', $request->users);
                    foreach ($student_codes as $index => $code) {
                        $student_codes[$index] = trim($code);
                    }
                    foreach ($student_codes as $code) {
                        $student = User::where('user_level', 3)->where('user_code', $code)->first();
                        if (!$student) {
                            $reports[] = "Không tìm thấy sinh viên $code.";
                            continue;
                        }
                        $check = self::checkDataBeforeAddInGroup($code, $currentGroup);
                        if (is_bool($check)) {
                            $result = self::addUserInGroup($currentGroup, $student);
                            if ($result) {
                                $reports[] = "Sinh viên $code được thêm vào lớp thành công.";
                                continue;
                            } else {
                                $reports[] = "Không thêm được sinh viên $code vào lớp.";
                                continue;
                            }
                        }
                        $reports[] = $check;
                    }
                }

                $newData = [
                    'pterm_id' => (int)$currentGroup->pterm_id,
                    'block_id' => (int)$currentGroup->block_id,
                    'body_id' => (int)$currentGroup->body_id,
                    'group_name' => $currentGroup->group_name,
                    'teacher' => $currentGroup->teacher,
                    'start_date' => $currentGroup->start_date,
                    'end_date' => $currentGroup->end_date,
                ];

                $currentGroup->save();
                if (serialize($oldData) != serialize($newData)) {
                    $changeData = [];
                    foreach ($newData as $keyChange => $valuChange) {
                        if ($oldData[$keyChange] != $newData[$keyChange]) {
                            $changeData[] = "{ $keyChange: từ (" . $oldData[$keyChange] . ') thành (' . $newData[$keyChange] . ') }';
                        }
                    }

                    if ($oldData['group_name'] != $newData['group_name']) {
                        Activity::where('groupid', $request->id)->update([
                            'group_name' => $newData['group_name']
                        ]);
                    }

                    $strUpdate = implode(', ', $changeData);
                    SystemLog::create([
                        'actor' => auth()->user()->user_login,
                        'object_name' => "group",
                        'log_time' => now(),
                        'action' => "update group info",
                        'description' => auth()->user()->user_login . " Đổi thông tin lớp học: $strUpdate",
                        'relation_login' => '',
                        'object_id' => $currentGroup->id,
                        'relation_id' => 0,
                        'brief' => "update group info",
                        'from_ip' => request()->ip(),
                        'nganh_cu' => '',
                        'nganh_moi' => '',
                        'ky_chuyen_den' => 0,
                        'ky_thu_cu' => 0,
                    ]);
                }
                if (!empty($reports)) {
                    session()->flash('reports', $reports);
                }

                return $this->redirectWithStatus(
                    'success',
                    'Cập nhật lớp thành công',
                    route('admin.group.index.edit', [
                        'id' => $request->id
                    ])
                );
            case "add_user":
                $user_temp = [];
                $users = explode(',', $request->users);
                foreach ($users as $user) {
                    $user = trim($user);
                    $user = strtoupper($user);
                    if ($user) {
                        $user_temp[] = $user;
                    }
                }

                if (count($user_temp) == 0) {
                    return $this->redirectWithStatus('warning', 'Không có sinh viên nào để thêm', route('admin.group.index.edit', ['id' => $request->id]));
                }

                $group = Group::with(['groupMembers', 'activities'])->find($request->id);
                foreach ($user_temp as $temp) {
                    $check = self::checkDataBeforeAddInGroup($temp, $group);
                    if (is_bool($check)) {
                        $user = User::where('user_code', $temp)->first();
                        self::addUserInGroup($group, $user);
                        $reports[] = "$temp thêm vào lớp thành công";
                        continue;
                    }

                    $reports[] = $check;
                }

                if (!empty($reports)) {
                    session()->flash('reports', $reports);
                }

                return $this->redirectWithStatus('info', implode('<br>', $reports), route('admin.group.index.edit', [
                    'id' => $request->id,
                ]));
        }
    }

    public function create()
    {
        $terms = Term::orderBy('id', 'desc')->get();
        $department = Department::all();

        return view('admin_v1.group.create', [
            'model' => self::MODEL,
            'terms' => $terms,
            'department' => $department,
        ]);
    }

    public function delete(Request $request)
    {
        $id = $request->id;
        $checkMember = GroupMember::where('groupid', $id)->count();
        if ($checkMember > 0) {
            return $this->redirectWithStatus('warning', 'Lớp đang có sinh viên không thể xoá', route('admin.group.index.edit', ['id' => $id]));
        }

        $check_activity = Activity::where('groupid', $id)->where('done', 1)->count();
        if ($check_activity > 0 && auth()->user()->user_level != 1) {
            return $this->redirectWithStatus('warning', 'Lớp đã học không thể xoá', route('admin.group.index.edit', ['id' => $id]));
        }

        DB::beginTransaction();
        try {
            // Lấy thông tin lớp bị xóa và thêm bảng xóa lớp
            $currentGroup = Group::find($id);
            $listActivity = Activity::where('groupid', $id)->get();
            $listActivity = count($listActivity) > 0 ? $listActivity->toArray() : [];
            $listActivity = json_encode($listActivity);
            $groupDelete = $currentGroup->toArray();
            $groupDelete['activities'] = $listActivity;
            GroupDelete::create($groupDelete);

            // Thêm log
            SystemLog::create([
                'actor' => Auth::user()->user_login,
                'object_name' => "group",
                'log_time' => now(),
                'action' => "Delete Group",
                'description' => "Delete Group",
                'relation_login' => '',
                'object_id' => $id,
                'relation_id' => 0,
                'brief' => "Delete Group",
                'from_ip' => request()->ip(),
                'nganh_cu' => '',
                'nganh_moi' => '',
                'ky_chuyen_den' => 0,
                'ky_thu_cu' => 0,
            ]);

            // Xóa lớp 
            Group::find($id)->delete();
            // Xóa buổi học
            Activity::where('groupid', $id)->delete();
            DB::commit();
            // DB::rollback();
            return $this->redirectWithStatus('success', 'Xoá lớp thành công', route('admin.group.index'));
        } catch (\Exception $exception) {
            DB::rollback();
            return $this->redirectWithStatus('danger', 'Đã xảy ra lỗi xoá thất bại', route('admin.group.index.edit', ['id' => $id]));
        }
    }

    public static function checkDataBeforeAddInGroup($user_code, $group)
    {
        $study_status_expire = [1, 3, 10, 11, 15, 16, 17];
        $user = User::where('user_code', $user_code)->first();
        // kiểm tra thông tin sinh viên
        if (!$user) {
            return "$user_code không tồn tại";
        }

        // kiểm tra trạng thái add lớp
        if (!in_array($user->study_status, $study_status_expire)) {
            return "Trạng thái học của sinh viên $user_code không hợp lệ (" . config('status')->trang_thai_hoc[$user->study_status]['value'] . ")";
        }

        // kiểm tra tồn tại trong lớp
        if (!$group->groupMembers->where('member_login', $user->user_login)) {
            return "Sinh viên $user_code đã có trong lớp.";
        }

        $groups = $groupObj = Group::select('list_group.id')
            ->where('list_group.is_virtual', 0)
            ->join('group_member', 'group_member.groupid', 'list_group.id')
            ->where('group_member.member_login', $user->user_login)
            ->where('pterm_id', $group->pterm_id)
            ->get();
        // kiểm tra xem có add với vòa lớp trùng môn không
        if ($groups->count()) {
            if ($temp_group = $groups->where('block_id', $group->block_id)->firstWhere('psubject_code', $group->psubject_code)) {
                return "$user_code đang học cùng môn trong $temp_group->block_name";
            }
            $groups = $groups->pluck('id')->toArray();
        }

        // lấy ngày học
        $activities = Activity::whereIn('groupid', $groups)
            ->where('slot', '<', '7')
            ->where('slot', '>', 0)
            ->get();
        if ($activities->count()) {
            foreach ($activities as $ac) {
                if ($ac->groupid != $group->id) {
                    $listCheck[$ac->day][$ac->slot] = 1;
                }
            }

            $activities = $activities->where('is_online', 0)->pluck('day', 'day')->toArray();
        }

        foreach ($group->activities as $activity) {
            if (isset($listCheck[$activity->day][$activity->slot]) && $listCheck[$activity->day][$activity->slot] == 1) {
                return "Sinh viên $user_code trùng lịch học ngày $activity->day, ca $activity->slot, lớp " . $activity->group->group_name;
            }
        }

        $prerequisite = Prerequisite::where('subject_code', $group->psubject_code)->get();
        $check_prerequisite = true;
        $check_prerequisite_msg = '';
        if ($prerequisite->count()) {
            $transcript = Transcript::with(['details'])->where('user_code', $user->user_code)->first();
            if ($transcript->details->count()) {
                $course_result_array = $transcript->details->pluck('status', 'subject_code_pass');
            } else {
                $course_result_array = [];
            }

            foreach ($prerequisite as $pre) {
                if ($pre->passed == 1) {
                    if (isset($course_result_array[$pre->subject_code_require])) {
                        if ($course_result_array[$pre->subject_code_require] != 1) {
                            $check_prerequisite = false;
                            $check_prerequisite_msg = 'chưa đạt';
                        }
                    } else {
                        $check_prerequisite = false;
                        $check_prerequisite_msg = 'chưa học';
                    }
                } else {
                    if (!isset($course_result_array[$pre->subject_code_require])) {
                        $check_prerequisite = false;
                        $check_prerequisite_msg = 'chưa học';
                    }
                }
            }

            if (!$check_prerequisite) {
                return "Sinh viên $user_code còn môn bắt buộc chưa học hoặc $check_prerequisite_msg";
            }
        }

        return true;
    }

    public static function addUserInGroup($group, $user)
    {
        try {
            DB::beginTransaction();
            GroupMember::create([
                'groupid' => $group->id,
                'member_login' => $user->user_login,
                'user_code' => $user->user_code,
                'date' => now()->format('Y-m-d'),
            ]);
            $group->increment('number_student');
            $textLog = "add member $user->user_login to group $group->id";
            SystemLog::create([
                'actor' => $user->user_login,
                'object_name' => "group",
                'log_time' => now(),
                'action' => "add member",
                'description' => $textLog,
                'relation_login' => $user->user_login,
                'object_id' => $group->id,
                'relation_id' => 0,
                'brief' => "add member",
                'from_ip' => request()->ip(),
                'nganh_cu' => '',
                'nganh_moi' => '',
                'ky_chuyen_den' => 0,
                'ky_thu_cu' => 0,
            ]);
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return false;
        }
    }

    /**
     * 
     */
    public function changeSchedule(Request $request)
    {
        // PH18005, PH28912, PH21765
        $strListUser = $request->get('list_user_code', '');
        $listUser = explode(',', $strListUser);
        if (sizeof($listUser) < 1) {
            return back()->withErrors([
                'Danh sách bị lỗi vui lòng nhập lại'
            ]);
        }

        $listUser = array_map(function ($a) {
            return trim($a);
        }, $listUser);

        $listUser = array_slice($listUser, 0, 10);
        $listUserObj = User::select(['user_code', 'user_login'])
            ->whereIn('user_code', $listUser)
            ->get();
        if (sizeof($listUserObj) < 1) {
            return back()->withErrors([
                'Không tồn tại sinh viên nào'
            ]);
        }

        $listUserLogin = call_user_func(function () use ($listUserObj) {
            $res = [];
            foreach ($listUserObj as $value) {
                $res[] = $value->user_login;
            }

            return $res;
        });

        $lastTerm = Term::orderBy('id', 'DESC')
            ->first();
        $listDatas = Group::select([
            'list_group.id',
            'list_group.psubject_code',
            'member_login'
        ])
            ->join('group_member', 'group_member.groupid', '=', 'list_group.id')
            ->where('pterm_id', $lastTerm->id)
            ->get();

        // dd($listDatas);
        // dd($listUserLogin);
        // lấy danh sách lớp của sinh viên

        return view('admin_v1.group.change_schedule', [
            'datas' => []
        ]);
        // $listGroupSubject = 
    }


    /**
     * <AUTHOR>
     * kiểm tra đổi chéo
     */
    public function checkSwapGroup(Request $request)
    {
        return $this->groupRepository->checkSwapGroup($request);
    }


    /**
     * <AUTHOR>
     * đổi chéo lớp cho sinh viên
     */
    public function swapGroup(Request $request)
    {
        return $this->groupRepository->swapGroup($request);
    }


    /**
     * <AUTHOR>
     * Chuyển lớp cuối cho sinh viên
     */
    public function moveToGroup(Request $request)
    {
        return $this->groupRepository->moveToGroup($request);
    }

    public function getGroupMembers(Request $request)
    {
        return $this->groupRepository->getGroupMembers($request);
    }
    public function getStaffProcessLog(Request $request)
    {
        return $this->groupRepository->getStaffProcessLog($request);
    }
}
