<?php

namespace App\Http\Controllers\Admin;

use App\Repositories\Admin\DecisionRepository;
use App\Http\DataTables\DecisionUserDataTable;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class DecisionUserController extends Controller
{
    protected $decisionRepository;
    public function __construct(DecisionRepository $decisionRepository)
    {
        $this->decisionRepository = $decisionRepository;
    }

    /**
     * Display a listing of the decision.
     */
    public function index(Request $request)
    {
        return $this->decisionRepository->index($request);
    }
    
    /**
     * Show the form for creating a new decision.
     */
    public function create(Request $request)
    {
        return $this->decisionRepository->createFrom($request);
    }

    /**
     * Store a newly created decision in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     */
    public function store(Request $request)
    {
        return $this->decisionRepository->store($request);
    }

    /**
     * Show the form for editing the specified resource.
     * 
     * @param  Integer $id
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function edit($id, Request $request)
    {
        return $this->decisionRepository->edit($id, $request);
    }

    /**
     * Update the specified resource in storage.
     *
     * 
     * @param  Integer $id
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update($id, Request $request)
    {
        return $this->decisionRepository->update($id, $request);
    }

    /**
     * Update the specified resource in storage.
     *
     * 
     * @param  Integer $id
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function removeUser(Request $request)
    {
        return $this->decisionRepository->removeUser($request);
    }
    
    /**
     * Remove the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        
    }

    /**
     * Datatable ajax request
     *
     * @return \Illuminate\Http\Response
     */
    public function datatable(DecisionUserDataTable $datatable)
    {
        return $datatable->build()->toJson();
    }

    /**
     * load data loadSelect2Decision
     *
     * @return \Illuminate\Http\Response
     */
    public function loadSelect2Decision(Request $request)
    {
        return $this->decisionRepository->loadSelect2Decision($request);
    }

    public function uploadFileDecisionPdf($file, $no, $termName)
    {
        $fileName = $termName . '-' . $no . '-' . now()->format('d_m_y_h_i_s') . '.' . $file->getClientOriginalExtension();
        $this->uploadFile(('decision'), $file, $fileName);

        return $fileName;
    }

    /**
     * export data Decision User
     *
     * @return \Illuminate\Http\Response
    */
    public function exportDecision(Request $request)
    {
        return $this->decisionRepository->exportDecision($request);
    }

}
