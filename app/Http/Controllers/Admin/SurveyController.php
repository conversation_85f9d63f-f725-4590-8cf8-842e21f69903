<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\DataTables\SurveydataTable;
use App\Http\DataTables\SurveyDetaildataTable;
use App\Repositories\Admin\SurveyRepository;
use Illuminate\Http\Request;

class SurveyController extends Controller
{
    protected $SurveyRepository;
    public function __construct(SurveyRepository $surveyRepository)
    {
        $this->SurveyRepository = $surveyRepository;
    }

    public function managerSurvey(Request $request)
    {
        return $this->SurveyRepository->managerSurvey($request);
    }

    public function detailSurvey(Request $request)
    {
        return $this->SurveyRepository->detailSurvey($request);
    }

    public function storeSurvey(Request $request)
    {
        return $this->SurveyRepository->storeSurvey($request);
    }

    public function updateSurvey(Request $request)
    {
        return $this->SurveyRepository->updateSurvey($request);
    }

    public function surveyDatatable(SurveydataTable $datatable)
    {
        return $datatable->build()->toJson();
    }

    public function surveyDetailDatatable(SurveyDetaildataTable $datatable)
    {
        return $datatable->build()->toJson();
    }

    public function changeStatusSurvey(Request $request)
    {
        return $this->SurveyRepository->changeStatusSurvey($request);
    }
}