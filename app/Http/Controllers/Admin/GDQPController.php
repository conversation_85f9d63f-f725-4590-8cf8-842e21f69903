<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\DataTables\GDQPDataTable;
use App\Repositories\Admin\GDQPRepository;
use App\Repositories\Admin\VHPTManagerRepository;
use Illuminate\Http\Request;

class GDQPController extends Controller
{
    protected $GDQPRepository;
    public function __construct(GDQPRepository $GDQPRepository)
    {
        $this->GDQPRepository = $GDQPRepository;
    }

    public function index(Request $request)
    {
        return $this->GDQPRepository->index($request);
    }

    /**
     * Datatable ajax request
     *
     * @return \Illuminate\Http\FeedbackdataTable
     */
    public function datatable(GDQPDataTable $datatable)
    {
        return $datatable->build()->toJson();
    }

    public function import(Request $request)
    {
        return $this->GDQPRepository->import($request);
    }

    public function export(Request $request)
    {
        return $this->GDQPRepository->export($request);
    }
}
