<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

use App\Exports\Activity\DanhSachThi;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Fu\Activity;
use App\Models\T7\Grade;
use App\Models\Fu\Term;
use App\Models\Fu\Decision;
use App\Models\Fu\DecisionUser;

use App\Http\DataTables\DecisionDataTable;
use App\Repositories\Admin\DecisionRepository;

class DecisionController extends Controller
{
    protected $decisionRepository;
    public function __construct(DecisionRepository $decisionRepository)
    {
        $this->decisionRepository = $decisionRepository;
    }

    /**
     * Display a listing of the decision.
     */
    public function index(Request $request)
    {
        return $this->decisionRepository->index($request);
    }
    
    /**
     * Show the form for creating a new decision.
     */
    public function create(Request $request)
    {
        return $this->decisionRepository->createFrom($request);
    }

    /**
     * Store a newly created decision in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     */
    public function store(Request $request)
    {
        return $this->decisionRepository->store($request);
    }

    /**
     * Show the form for editing the specified resource.
     * 
     * @param  Integer $id
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function edit($id, Request $request)
    {
        return $this->decisionRepository->edit($id, $request);
    }

    /**
     * Update the specified resource in storage.
     *
     * 
     * @param  Integer $id
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update($id, Request $request)
    {
        return $this->decisionRepository->update($id, $request);
    }

    /**
     * Update the specified resource in storage.
     *
     * 
     * @param  Integer $id
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function removeUser(Request $request)
    {
        return $this->decisionRepository->removeUser($request);
    }
    
    /**
     * Remove the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        return $this->decisionRepository->destroy($request);
    }

    /**
     * Datatable ajax request
     *
     * @return \Illuminate\Http\Response
     */
    public function datatable(DecisionDataTable $datatable)
    {
        return $datatable->build()->toJson();
    }

    /**
     * load data loadSelect2Decision
     *
     * @return \Illuminate\Http\Response
     */
    public function loadSelect2Decision(Request $request)
    {
        return $this->decisionRepository->loadSelect2Decision($request);
    }

    public function uploadFileDecisionPdf($file, $no, $termName)
    {
        $fileName = $termName . '-' . $no . '-' . now()->format('d_m_y_h_i_s') . '.' . $file->getClientOriginalExtension();
        $this->uploadFile(('decision'), $file, $fileName);

        return $fileName;
    }

    /**
     * export data Decision User
     *
     * @return \Illuminate\Http\Response
    */
    public function exportDecision(Request $request)
    {
        return $this->decisionRepository->exportDecision($request);
    }

}
