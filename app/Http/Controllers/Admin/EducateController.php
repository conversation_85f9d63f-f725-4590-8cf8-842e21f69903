<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\Admin\EducateRepository;
use Illuminate\Http\Request;

class EducateController extends Controller
{
    protected $EducateRepository;

    public function __construct(EducateRepository $educateRepository)
    {
        $this->EducateRepository = $educateRepository;
    }

    public function thieuNoMon(Request $request)
    {
        $is_export_excel = $request->export_excel ?? false;
        $is_export_csv = $request->export_excel_new ?? false;
        if($is_export_excel != false || $is_export_csv != false){
            $filter_status = $request->status ?? false;
            $filter_major = $request->major ?? false;
            $filter_khoa_hoc = $request->khoa_hoc ?? false;
            $filter_list_subject = $request->list_subject ?? false;
            $filter_keyword = $request->keyword ?? false;

            if ($filter_status == false && $filter_major == false && $filter_khoa_hoc == false && $filter_list_subject == false && $filter_keyword ==false){
                return redirect()->back()->with('message', 'Vui lòng lọc dữ liệu trước khi export.');
            }
        }
        return $this->EducateRepository->thieuNoMon($request);
    }

    public function soDiem(Request $request)
    {
        return $this->EducateRepository->soDiem($request);
    }

    public function soDiemSave(Request $request)
    {
        return $this->EducateRepository->soDiemSave($request);
    }

    public function gradeLockStore(Request $request)
    {
        return $this->EducateRepository->gradeLockStore($request);
    }

    public function importForm(Request $request)
    {
        return $this->EducateRepository->importForm($request);
    }

    public function importStore(Request $request)
    {
        return $this->EducateRepository->importStore($request);
    }

    public function downloadFileImportExample($course_id)
    {
        return $this->EducateRepository->downloadFileImportExample($course_id);
    }

    public function exportForm(Request $request)
    {
        return $this->EducateRepository->exportForm($request);
    }

    public function exportDownload(Request $request)
    {
        return $this->EducateRepository->exportDownload($request);
    }

    public function exportCSVInfoPoint(Request $request)
    {
        return $this->EducateRepository->exportCSVInfoPoint($request);
    }

    public function exportCSVInfoPoint2(Request $request)
    {
        return $this->EducateRepository->exportCSVInfoPoint2($request);
    }

    public function indexGradeFUGE(Request $request)
    {
        return $this->EducateRepository->indexGradeFUGE($request);
    }

    public function importGradeFUGE(Request $request)
    {
        ini_set('upload_max_filesize', '90M');
        return $this->EducateRepository->importGradeFUGE($request);
    }
    
    public function gradeView(Request $request) {
        return $this->EducateRepository->gradeView($request);
    }
    
    public function bangDiemKyView(Request $request) {
        $id = $request->get('id');
        return $this->EducateRepository->bangDiemKyView($request, $id);
    }

    public function bangDiemKy(Request $request)
    {
        $term_id = trim($request->term_id);
        $page = trim($request->page);
        $block = trim($request->block);
        return $this->EducateRepository->bangDiemKy($term_id, $page, $block);
    }

    public function bangDiemKyApi(Request $request) {
        $term_id = trim($request->term_id);
        $page = trim($request->page ?? 1);
        $block = trim($request->block);
        $userCode = trim($request->userCode);
        return $this->EducateRepository->bangDiemKy($term_id, $page, $block, $userCode);
    }

    public function chotKy(Request $request)
    {
        return $this->EducateRepository->chotKy($request);
    }

    public function gradeByTerm(Request $request)
    {
        return $this->EducateRepository->gradeByTerm($request);
    }

    public function gradeByTermExport(Request $request)
    {
        return $this->EducateRepository->gradeByTermExport($request);
    }

    public function getListExistedFileThieuNoMon(Request $request)
    {
        return $this->EducateRepository->getListExistedFileThieuNoMon($request);
    }
    public function getExportThieuNoMon(Request $request)
    {
        return $this->EducateRepository->getFileExportThieuNoMon($request);
    }
    public function getDownloadExistedFileThieuNoMon(Request $request)
    {
        return $this->EducateRepository->getDownloadExistedFileThieuNoMon($request);
    }
    public function showGradeReport(Request $request)
    {
        return $this->EducateRepository->showGradeReport($request);
    }
}
