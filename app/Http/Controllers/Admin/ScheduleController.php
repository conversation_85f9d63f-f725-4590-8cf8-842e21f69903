<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\DataTables\schedule\ManagerSchangeDataTable;
use Illuminate\Http\Request;
use App\Repositories\Admin\ScheduleRepository;
use Illuminate\Support\Arr;
use App\Imports\BaseModelImport;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class ScheduleController extends Controller
{
    protected $scheduleRepository;

    public function __construct(ScheduleRepository $scheduleRepository)
    {
        $this->scheduleRepository = $scheduleRepository;
    }

    public function indexDataMakeSchedule(Request $request)
    {
        return $this->scheduleRepository->indexDataMakeSchedule($request);
    }

    public function exportDataMakeSchedule(Request $request, $type)
    {
        return $this->scheduleRepository->exportDataMakeSchedule($request, $type);
    }

    public function CurriculumList(Request $request)
    {
        $result = $this->scheduleRepository->curriculumList(
            Arr::get($request->all(), 'per_page', 30),
            [
                "nganh" => Arr::get($request->all(), 'nganh', null),
                "chuyen_nganh" => Arr::get($request->all(), 'chuyen_nganh', null),
                "curriculum_name" => Arr::get($request->all(), 'curriculum_name', null)
            ]
        )->toArray();
        $result['number_of_student_without_class'] = $this->scheduleRepository->numberOfStudentWithoutClass();
        return $result;
    }

    public function CurriculumPeriod(Request $request)
    {
        $result = $this->scheduleRepository->curriculumPeriod(
            Arr::get($request->all(), 'c_id'),
        )->toArray();

        return $result;
    }

    public function curriculumStudent(Request $request)
    {
        return $this->scheduleRepository->curriculumStudent(
            Arr::get($request->all(), 'c_id'),
            Arr::get($request->all(), 'per_page', 30),
            Arr::get($request->all(), 'study_status', null),
            Arr::get($request->all(), 'user_level'),
            Arr::get($request->all(), 'user_code'),
        );
    }

    public function curriculumDetail(Request $request)
    {
        return $this->scheduleRepository->curriculumDetail($request->id);
    }

    public function listChuyenNganh(Request $request)
    {
        return $this->scheduleRepository->listChuyenNganh($request->brand_code);
    }

    public function curriculumAdd(Request $request)
    {
        if ($curr = $this->scheduleRepository->curriculumAdd($request->all())) {
            return response()->json([
                "status" => 'success',
                "message" => 'Tạo khung thành công',
                "data" => $curr
            ]);
        } else {
            return response()->json([
                "status" => 'fail',
                "message" => 'Thất bại'
            ]);
        }
    }

    public function curriculumEdit(Request $request, $id)
    {
        if ($curr = $this->scheduleRepository->curriculumEdit($request->all(), $id)) {
            return $curr;
        } else {
            return response()->json([
                "status" => 'faild',
                "message" => 'Thất bại'
            ]);
        }
    }

    public function electiveGroupAdd(Request $request)
    {
        return $this->scheduleRepository->electiveGroupAdd($request);
    }

    public function electiveGroupEdit(Request $request, $id)
    {
        return $this->scheduleRepository->electiveGroupEdit($request->all(), $id);
    }

    public function getlistSubject(Request $request)
    {
        // return Subject::get(['id', 'subject_name', 'subject_code', 'skill_code']);
        return $this->scheduleRepository->getlistSubject($request);
    }

    public function getElectiveGroupsInCurriculum($curriculum)
    {
        return $this->scheduleRepository->getElectiveGroupsInCurriculum($curriculum);
    }

    public function getElectiveSubject(Request $request)
    {
        return $this->scheduleRepository->getElectiveSubject($request);
    }

    public function PeriodAdd(Request $request)
    {
        return $this->scheduleRepository->PeriodAdd($request);
    }

    public function SubjectPeriodAdd(Request $request)
    {
        return $this->scheduleRepository->SubjectPeriodAdd($request->all());
    }

    public function SubjectPeriodDelete(Request $request)
    {
        $curr_id = Arr::get($request->all(), 'curriculum_id', null);
        $id  = Arr::get($request->all(), 'id', null);
        $status = $this->scheduleRepository->SubjectPeriodDelete($curr_id, $id);
        return response()->json([
            'message' => ($status) ? "Thành Công" :  "Thất bại",
            'status' => ($status) ? 200 : 500
        ]);
    }

    public function listStudentInCurriculum(Request $request)
    {
        $curriculum = Arr::get($request->all(), 'curriculum', null);
        $user_code = Arr::get($request->all(), 'user_code', null);
        $study_status = Arr::get($request->all(), 'study_status', null);
        return $this->scheduleRepository->listStudentInCurriculum($curriculum, $study_status, $user_code);
    }

    public function updateCurriculumStudent(Request $request)
    {
        try {
            $file = $request->file;
            $theCollection = Excel::toArray(new BaseModelImport, $file);
            $student_list_array_object = $theCollection[0];
            $student_list = [];
            foreach ($student_list_array_object as $key => $value) {
                $student_list[] = $value['mssv'];
            }
            $updated = $this->scheduleRepository->updateCurriculumStudent($request->curriculum_id, $student_list);
            return response()->json([
                "updated" => $updated,
                "error" => 0,
                "message" => 'Cập nhật thành công ' . $updated . ' sinh viên'
            ]);
        } catch (\Throwable $th) {
            Log::error($th);
            return response()->json([
                "updated" => 0,
                "error" => 1,
                "message" => 'thất bại ' . $th->getMessage()
            ]);
        }
    }

    public function updateElectiveSubjects(Request $request)
    {
        return $this->scheduleRepository->updateElectiveSubjects($request);
    }

    public function updateElectiveGroup(Request $request)
    {
        return $this->scheduleRepository->updateElectiveGroup($request);
    }

    public function deleteElectiveGroup(Request $request)
    {
        return $this->scheduleRepository->deleteElectiveGroup($request);
    }

    /**
     * Quản lý đổi lịch của sinh viên
     */
    public function scheduleMangement(Request $request)
    {
        return $this->scheduleRepository->scheduleMangement($request);
    }

    /**
     *
     * Datatable ajax request Quản lý đổi lịch của sinh viên
     * 
     * @return \Illuminate\Http\Response
     */
    public function scheduleMangementDatatable(ManagerSchangeDataTable $datatable)
    {
        return $datatable->build()->toJson();
    }


    public function updateTimeChangeGroup(Request $request)
    {
        return $this->scheduleRepository->updateTimeChangeGroup($request);
    }

    public function deleteCurriculum($id)
    {
        return $this->scheduleRepository->deleteCurriculum($id);
    }

    /**
     * Datatable ajax request
     *
     * @return \Illuminate\Http\Response
     */
    public function getGroupFilter(Request $request)
    {
        return $this->scheduleRepository->getGroupFilter($request);
    }

    public function downloadTemplate(Request $request)
    {
        return $this->scheduleRepository->downloadTemplate($request);
    }

    public function uploadFileExcel(Request $request)
    {
        return $this->scheduleRepository->uploadFileExcel($request);
    }

    public function postGroupSchedule(Request $request)
    {
        return $this->scheduleRepository->postGroupSchedule($request);
    }
}
