<?php

namespace App\Http\Controllers\Admin;

use App\Repositories\Admin\QueueRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class QueueController extends Controller
{
    public function __construct(QueueRepository $queueRepository)
    {
        $this->QueueRepository = $queueRepository;
    }

    public function getQueueByType(Request $request)
    {
        return $this->QueueRepository->getQueueByType($request);
    }
}
