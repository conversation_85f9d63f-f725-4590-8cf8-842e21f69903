<?php

namespace App\Http\Controllers\Admin;

use App\Http\Lib;
use App\Models\Brand;
use App\Models\Dra\CurriCulum;
use App\Models\Dra\Period;
use App\Models\Dra\PeriodSubject;
use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use App\Models\RelearnOnline;
use App\Models\SubjectTransfer;
use App\Models\T7\CourseGrade;
use App\Models\T7\CourseResult;
use App\Models\T7\Grade;
use App\Models\T7\GradeGroup;
use App\Models\TranferT7Course;
use App\Models\Fu\ElectiveSubject;
use App\Models\Fu\ElectiveGroup;
use App\Models\Fu\Block;
use App\Models\Fu\Subject;
use App\Models\Transcript;
use App\Models\TranscriptDetail;
use App\Models\Fu\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\Fu\Term;
use Illuminate\Support\Facades\DB;
use mysql_xdevapi\Exception;
use Illuminate\Support\Facades\Log;
use niklasravnsborg\LaravelPdf\Facades\Pdf as PDF2;

class TranscriptController extends Controller
{
    public function getByUserLogin($user_login)
    {
        $result = Transcript::where('user_login', $user_login)->first();
        if ($result) {
            $result->load('details');

            return $this->res('success', 'get transcript success', $result);
        }

        return $this->res('error', 'not found', null);
    }

    public function syncFromCourseResult($id, Request $request)
    {
        $term = Lib::getTermDetails();
        $a = [];
        $temp_curriculum_id = null;
        $user = Transcript::where('user_login', $id)->first();

        $student = User::where('user_login', $id)->first();
        if (!$user) {
            $user = Transcript::create([
                'user_login' => $student->user_login,
                'user_code' => $student->user_code,
                'curriculum_id' => $student->curriculum_id,
                'version' => 1,
                'study_status' => $student->study_status,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } else {
            $temp_curriculum_id = $user->curriculum_id;
            $user->curriculum_id = $student->curriculum_id;
            $user->study_status = $student->study_status;
            $user->save();
        }

        $vie1031 = 0;
        $vie1032 = 0;
        $vie1033 = 0;
        $vie103 = 0;
        $curriculum = CurriCulum::select('version')->find($user->curriculum_id);
        if ($temp_curriculum_id != $user->curriculum_id) {
            $user->version = 0;
            Transcript::where('id', $user->id)->update(['version' => 0]);
        }

        if ($user->getTable() != 'transcripts') {
            $user = Transcript::where('user_login', $student->user_login)
                ->where('user_login', $student->user_login)
                ->first();
        }

        // kiểm tra những môn học trong khung và môn trong bảng điểm
        $checkSubjectInCurriculum = self::compareSubjectTranscript($user, $student);
        $checkCount = TranscriptDetail::where('transcript_id', $user->id)->count();
        if ($user->version < $curriculum->version || $temp_curriculum_id != $user->curriculum_id || $checkSubjectInCurriculum == false || $checkCount == 0) {
            TranscriptDetail::where('transcript_id', $user->id)->delete();
            $subjects = PeriodSubject::where('curriculum_id', $user->curriculum_id)->get();
            $insert_bang_diem_chi_tiet = [];
            foreach ($subjects as $subject) {
                $subject->period = Period::find($subject->period_id);
                $insert_bang_diem_chi_tiet[] = [
                    'transcript_id' => $user->id,
                    'subject_code' => $subject->subject_code,
                    'subject_name' => $subject->subject_name,
                    'subject_id' => $subject->subject_id,
                    'skill_code' => $subject->skill_code,
                    'type' => 0,
                    'status' => 0,
                    'num_of_credit' => $subject->number_of_credit,
                    'period_subject_id' => $subject->id,
                    'ki_thu' => $subject->period->ordering,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            TranscriptDetail::insert($insert_bang_diem_chi_tiet);
            $user->version = $curriculum->version;
            // $user->save();
            Transcript::where('id', $user->id)->update(['version' => $curriculum->version]);
        }

        TranscriptDetail::where('transcript_id', $user->id)
            ->update([
                'subject_code_pass' => null,
                'subject_name_pass' => null,
                'status' => 0,
                'type' => 0,
                'subject_code_replace' => null,
                'skill_code_replace' => null,
                'point' => 0,
                'luot_hoc' => 0,
                'term_name' => null
            ]);

        // DB::select("update transcripts a, transcript_details b, mien_giam_tap_trung c, subject d set b.type = 1, b.subject_code_replace = c.subject_code_new, b.skill_code_replace = c.skill_code_new, b.subject_name_replace = d.subject_name, b.updated_at = NOW() where c.type = 1 and a.id = b.transcript_id and a.user_login = c.student_login and b.skill_code = c.skill_code and c.subject_code_new = d.subject_code and a.id = $user->id");
        DB::select("UPDATE
            transcripts a,
            transcript_details b,
            mien_giam_tap_trung c,
            subject d
        SET
            b.type = 1,
            b.subject_code_replace = c.subject_code_new,
            b.skill_code_replace = c.skill_code_new,
            b.subject_name_replace = d.subject_name,
            b.updated_at = NOW()
        WHERE
            c.type = 1
            AND a.id = b.transcript_id
            AND a.user_login = c.student_login
            AND b.skill_code = c.skill_code
            AND c.subject_code_new = d.subject_code
            AND a.id = $user->id
            AND (
                EXISTS ( SELECT id FROM t7_course_result t7 WHERE t7.`skill_code` = c.`skill_code_new` AND t7.student_login = c.student_login)
                OR EXISTS ( SELECT id FROM tranfer_t7_course t72 WHERE t72.`skill_code` = c.`skill_code_new` AND t72.student_login = c.student_login)
            )");
        DB::select("update transcripts a, transcript_details b, mien_giam_tap_trung c set b.type = 2, b.subject_code_pass = b.subject_code, b.subject_name_pass = b.subject_name, b.status = 1, b.updated_at = NOW() where c.type = 2 and a.id = b.transcript_id and a.user_login = c.student_login and b.skill_code = c.skill_code and a.id = $user->id");
        DB::select("update transcripts a, transcript_details b, mien_giam_tap_trung c set b.type = 3, b.status = 1, b.updated_at = NOW() where c.type = 3 and a.id = b.transcript_id and a.user_login = c.student_login and b.skill_code = c.skill_code and a.id = $user->id");
        // $lich_su_hoc = CourseResult::where('student_login', $user->user_login)->orderBy('groupid', 'ASC')->get();
        $lich_su_hoc = CourseResult::select('t7_course_result.*', 'list_group.is_virtual')
            ->join('list_group', 'list_group.id', '=', 't7_course_result.groupid')
            // ->where('list_group.is_virtual', 0)
            ->where('student_login', $user->user_login)
            ->orderBy('groupid', 'ASC')
            ->get();
        $temp_details = TranscriptDetail::where('transcript_id', $user->id)->get();
        //        $mon_tuong_duong = [];
        //        $data_tuong_duong = SubjectTransfer::with('details')->where('type', 0)->get();
        //        foreach ($data_tuong_duong as $item) {
        //            if ($item->details->count()) {
        //                $mon_tuong_duong[$item->skill_code] = $item->skill_code;
        //                foreach ($item->details as $sub) {
        //                    $mon_tuong_duong[$item->skill_code] = [
        //                        'skill_code' => $sub->skill_code,
        //                        'subject_code' => $sub->subject_code,
        //                        'subject_name' => $sub->subject_name,
        //                    ];
        //                    $mon_tuong_duong[$sub->skill_code] = [
        //                        'skill_code' => $item->skill_code,
        //                        'subject_code' => $item->subject_code,
        //                        'subject_name' => $item->subject_name,
        //                    ];
        //                }
        //            }
        //        }
        //        foreach ($temp_details as $detail) {
        //            if (isset($mon_tuong_duong[$detail->skill_code])) {
        //                if ($lich_su_hoc->contains('skill_code', $mon_tuong_duong[$detail->skill_code]['skill_code'])) {
        //                    $detail->type = 1;
        //                    $detail->subject_code_replace = $mon_tuong_duong[$detail->skill_code]['subject_code'];
        //                    $detail->subject_name_replace = $mon_tuong_duong[$detail->skill_code]['subject_name'];
        //                    $detail->skill_code_replace = $mon_tuong_duong[$detail->skill_code]['skill_code'];
        //                    $detail->save();
        //                }
        //            }
        //        }

        $user->details = TranscriptDetail::where('transcript_id', $user->id)->get();
        if (count($user->details) < 10) {
            TranscriptDetail::where('transcript_id', $user->id)->delete();
            $subjects = PeriodSubject::where('curriculum_id', $user->curriculum_id)->get();
            $insert_bang_diem_chi_tiet = [];
            foreach ($subjects as $subject) {
                $subject->period = Period::find($subject->period_id);
                $insert_bang_diem_chi_tiet[] = [
                    'transcript_id' => $user->id,
                    'subject_code' => $subject->subject_code,
                    'subject_name' => $subject->subject_name,
                    'subject_id' => $subject->subject_id,
                    'skill_code' => $subject->skill_code,
                    'type' => 0,
                    'status' => 0,
                    'num_of_credit' => $subject->number_of_credit,
                    'period_subject_id' => $subject->id,
                    'ki_thu' => $subject->period->ordering,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            TranscriptDetail::insert($insert_bang_diem_chi_tiet);
            $user->version = $curriculum->version;
            Transcript::where('id', $user->id)->update(['version' => $curriculum->version]);
            $user->details = TranscriptDetail::where('transcript_id', $user->id)->get();
        }
        $mon_tu_lich_su = [];
        $mon_da_pass = [];
        //update lớp đang học
        $subject_on_learn = [];
        $groups = GroupMember::join('list_group', 'list_group.id', 'group_member.groupid')
            ->where('list_group.pterm_id', $term['term_id'])
            ->where('group_member.member_login', $user->user_login)
            ->whereRaw('list_group.start_date <= CURRENT_DATE')
            ->get();
        foreach ($groups as $group) {
            $subject_on_learn[$group->skill_code] = $group->skill_code;
        }

        if (count($subject_on_learn) > 0) {
            TranscriptDetail::where('transcript_id', $user->id)->where('type', '!=', 2)->whereIn('skill_code', $subject_on_learn)->update(['status' => -2]);
            TranscriptDetail::where('transcript_id', $user->id)->where('type', '!=', 2)->whereIn('skill_code_replace', $subject_on_learn)->update(['status' => -2]);
        }

        //update diem tu bang diem chuyen co so ve bang diem
        $hasGradeTransfer = TranferT7Course::where('student_login', $user->user_login)
            ->count();

        if ($hasGradeTransfer > 0) {
            $lich_su_hoc_chuyen_co_so = TranferT7Course::where('student_login', $user->user_login)
                ->orderBy('start_date')->get();
            foreach ($lich_su_hoc_chuyen_co_so as $lich_su) {
                if (isset($mon_tu_lich_su[$lich_su->skill_code])) {
                    if ($mon_tu_lich_su[$lich_su->skill_code] == 1) {
                        $mon_tu_lich_su[$lich_su->skill_code]['luot_hoc'] += 1;
                        continue;
                    }
                }

                $mon_tu_lich_su[$lich_su->skill_code] = [
                    'status' => $lich_su->val == 0 ? -3 : $lich_su->val,
                    'point' => $lich_su->grade,
                    'luot_hoc' => isset($mon_tu_lich_su[$lich_su->skill_code]['luot_hoc']) ? $mon_tu_lich_su[$lich_su->skill_code]['luot_hoc'] += 1 : 1,
                    'term_name' => $lich_su->pterm_name,
                    'subject_name_pass' => $lich_su->psubject_name,
                    'subject_code_pass' => $lich_su->psubject_code,
                ];
            }
        }

        //update diem tu lich su hoc ve bang diem
        foreach ($lich_su_hoc as $lich_su) {
            if (now()->format('Y-m-d') > $lich_su->end_date && $lich_su->is_finish != 2) {
                $lich_su->is_finish = 2;
                $lich_su->save();
            }

            if (isset($mon_tu_lich_su[$lich_su->skill_code])) {
                if ($mon_tu_lich_su[$lich_su->skill_code]['status'] == 1) {
                    if ($lich_su->val == 1) {
                        $mon_tu_lich_su[$lich_su->skill_code]['point'] = $lich_su->grade;
                        $a[] = [
                            'user' => $user->user_login,
                            'mon' => $lich_su->skill_code,
                        ];
                    }

                    if ($lich_su->is_virtual == 0) {
                        $mon_tu_lich_su[$lich_su->skill_code]['luot_hoc'] += 1;
                    }
                    continue;
                }
            }

            if ($lich_su->is_virtual == 0) {
                $luot_hoc = isset($mon_tu_lich_su[$lich_su->skill_code]['luot_hoc']) ? $mon_tu_lich_su[$lich_su->skill_code]['luot_hoc'] += 1 : 1;
            }

            $status = $lich_su->val;

            if ($lich_su->is_finish === 2 && $lich_su->val === '0') {
                $status = -3;
            } else if ($lich_su->val === '0' && ($lich_su->is_finish === 0 || $lich_su->is_finish === 1)) {
                $status = -2;
            }

            $mon_tu_lich_su[$lich_su->skill_code] = [
                'status' => $status == 0 ? -3 : $status,
                'point' => $lich_su->grade,
                'luot_hoc' => $luot_hoc,
                'term_name' => $lich_su->pterm_name,
                'subject_name_pass' => $lich_su->psubject_name,
                'subject_code_pass' => $lich_su->psubject_code,
            ];
        }

        // Cập nhập môn tự chọn của sinh viên
        $electiveGroups = ElectiveGroup::where('curriculum_id', $user->curriculum_id)->where('status', 1)->with('electiveSubject')->get();
        $listDataElective = call_user_func(function () use ($electiveGroups) {
            $res = [];
            foreach ($electiveGroups as $key => $value) {
                $dataAdd = [
                    "id" => $value->id,
                    "name" => $value->name,
                    "curriculum_id" => $value->curriculum_id,
                    "ki_thu" => $value->ki_thu,
                    "note" => $value->note,
                ];

                $electiveSubject = ElectiveSubject::where('elective_group_id', $value->id)->get();
                foreach ($electiveSubject as $v) {
                    if ($v->main_subject == 0) {
                        $dataAdd['subjects'][] = [
                            "subject_id" => $v->subject_id,
                            "skill_code" => $v->skill_code,
                            "subject_code" => $v->subject_code,
                            "subject_name" => $v->subject_name
                        ];
                    } else {
                        $dataAdd["main_subject_id"] = $v->subject_id;
                        $dataAdd["main_skill_code"] = $v->skill_code;
                        $dataAdd["main_subject_code"] = $v->subject_code;
                        $dataAdd["main_subject_name"] = $v->subject_name;
                    }
                }

                $res[] = $dataAdd;
            }

            return $res;
        });

        foreach ($user->details as $item) {
            if ($item->skill_code_replace) {
                if (isset($mon_tu_lich_su[$item->skill_code_replace])) {
                    TranscriptDetail::where('skill_code_replace', $item->skill_code_replace)
                        ->where('transcript_id', $user->id)
                        ->where('subject_code_replace', $item->subject_code_replace)
                        ->whereNotIn('type', [3, 2])
                        ->update($mon_tu_lich_su[$item->skill_code_replace]);
                    if ($mon_tu_lich_su[$item->skill_code_replace]['status'] == 1) {
                        unset($mon_tu_lich_su[$item->skill_code_replace]);
                        continue;
                    }

                    unset($mon_tu_lich_su[$item->skill_code_replace]);
                }
            } else {
                if (isset($mon_tu_lich_su[$item->skill_code])) {
                    TranscriptDetail::where('skill_code', $item->skill_code)
                        ->where('transcript_id', $user->id)
                        ->where('subject_code', $item->subject_code)
                        ->whereNotIn('type', [3, 2])
                        ->update($mon_tu_lich_su[$item->skill_code]);
                    if ($mon_tu_lich_su[$item->skill_code]['status'] == 1) {
                        $mon_da_pass[$item->skill_code] = $mon_tu_lich_su[$item->skill_code]['term_name'];
                    }

                    if ($mon_tu_lich_su[$item->skill_code]['status'] == 1) {
                        unset($mon_tu_lich_su[$item->skill_code]);
                        continue;
                    }

                    unset($mon_tu_lich_su[$item->skill_code]);
                }
            }

            $keySearch = array_search($item->subject_code, array_column($listDataElective, 'main_subject_code'));
            // if ($keySearch === false) {
            //     $keySearch = array_search($item->subject_code, array_column($listDataElective, 'main_subject_code'));
            // }

            // if ($user->user_login == 'sondhph08473') {
            //     dd($electiveGroups);
            // }

            if ($keySearch !== false) {
                if ($item->status != 1 && sizeof($listDataElective[$keySearch]['subjects']) > 0) {
                    $listSubjectId = array_column($listDataElective[$keySearch]['subjects'], 'skill_code');
                    $union = TranferT7Course::select([
                            'val',
                            'is_finish',
                            'skill_code',
                            'psubject_code',
                            'psubject_name',
                            'grade',
                            'pterm_name'
                        ])
                        ->where('student_login', $user->user_login)
                        ->whereIn('skill_code', $listSubjectId);

                    $listCours = CourseResult::select([
                            'val',
                            'is_finish',
                            'skill_code',
                            'psubject_code',
                            'psubject_name',
                            'grade',
                            'pterm_name'
                        ])
                        ->where('student_login', $user->user_login)
                        ->whereIn('skill_code', $listSubjectId)
                        ->union($union)
                        ->get();

                    $checkNext = false;
                    foreach ($listCours as $v) {
                        $checkUpdate = TranscriptDetail::where('transcript_id', $user->id)
                            ->where('subject_code', $item->subject_code)
                            ->whereIn('type', [3, 2])
                            ->count();
                        if ($checkUpdate > 0) {
                            continue;
                        }
                        // update status -3 -2
                        $statusCheck = $v->val;
                        if ($v->is_finish === 2 && $v->val === '0') {
                            $statusCheck = -3;
                        } else if ($v->val === '0' && ($v->is_finish === 0 || $v->is_finish === 1)) {
                            $statusCheck = -2;
                        }

                        TranscriptDetail::where('transcript_id', $user->id)
                            ->where('type', '!=', 1)
                            ->where('subject_code', $item->subject_code)->update([
                                'status' => $statusCheck == 0 ? -3 : $statusCheck,
                                'point' => $v->grade,
                                'subject_name_pass' => $v->psubject_name . " (Học phần tự chọn Thay thế môn: $item->subject_code - $item->subject_name)",
                                'subject_code_pass' => $v->psubject_code,
                                // 'skill_code' => $v->skill_code,
                                'skill_code_replace' => $v->skill_code,
                                'subject_code_replace' => $v->psubject_code,
                                'luot_hoc' => 1,
                                'term_name' => $v->pterm_name,
                            ]);

                        $checkNext = true;
                    }

                    if ($checkNext) continue;
                } else {
                    continue;
                }
            }
        }

        if (count($mon_tu_lich_su) > 0) {
            if (isset($mon_tu_lich_su['ENT211']) && isset($mon_tu_lich_su['ENT221']) && $mon_tu_lich_su['ENT211']['status'] == 1 && $mon_tu_lich_su['ENT221']['status'] == 1) {
                $point = ($mon_tu_lich_su['ENT211']['point'] + $mon_tu_lich_su['ENT211']['point']) / 2;
                TranscriptDetail::where('skill_code', 'ENT201')->where('transcript_id', $user->id)->where('subject_code', 'ENT201')->whereNotIn('type', [3, 2])->update([
                    'status' => 1,
                    'point' => $point,
                    'luot_hoc' => 0,
                    'term_name' => null,
                ]);
            }
            if (isset($mon_tu_lich_su['ENT212']) && isset($mon_tu_lich_su['ENT222']) && $mon_tu_lich_su['ENT212']['status'] == 1 && $mon_tu_lich_su['ENT222']['status'] == 1) {
                $point = ($mon_tu_lich_su['ENT212']['point'] + $mon_tu_lich_su['ENT222']['point']) / 2;
                TranscriptDetail::where('skill_code', 'ENT201')->where('transcript_id', $user->id)->where('subject_code', 'ENT201')->whereNotIn('type', [3, 2])->update([
                    'status' => 1,
                    'point' => $point,
                    'luot_hoc' => 0,
                    'term_name' => null,
                ]);
            }
            if ($vie1031 && $vie1032) {
                $point = ($vie1031 + $vie1032) / 2;
                TranscriptDetail::where('skill_code', 'VIE103')->where('transcript_id', $user->id)->where('subject_code', 'VIE103')->whereNotIn('type', [3, 2])->update([
                    'status' => 1,
                    'point' => $point,
                    'luot_hoc' => 0,
                    'term_name' => null,
                ]);
            }

            if ($vie1033 || $vie103) {
                $point = $vie1033 > 0 ? $vie1033 : $vie103;
                TranscriptDetail::where('skill_code', 'VIE1031')->where('transcript_id', $user->id)->where('subject_code', 'VIE1031')->whereNotIn('type', [3, 2])->update([
                    'status' => 1,
                    'point' => $point,
                    'luot_hoc' => 0,
                    'term_name' => null,
                ]);
                TranscriptDetail::where('skill_code', 'VIE1032')->where('transcript_id', $user->id)->where('subject_code', 'VIE1032')->whereNotIn('type', [3, 2])->update([
                    'status' => 1,
                    'point' => $point,
                    'luot_hoc' => 0,
                    'term_name' => null,
                ]);
            }
        }

        // update những môn học chưa đủ điểm thành phần được tính là đang học
        $subjectsInSuspected = TranscriptDetail::whereIn('status', [-3, -4])->where('transcript_id', $user->id)->get();
        $listSubjectRelearn = [];
        if (!empty($subjectsInSuspected)) {
            foreach ($subjectsInSuspected as $item) {
                $lich_su = CourseResult::where('student_login', $user->user_login)
                    ->where('skill_code', $item->skill_code)->orderBy('groupid', 'DESC')->first();
                if (!isset($lich_su->groupid)) {
                    continue;
                }

                $group_of_lich_su = Group::find($lich_su->groupid);
                $block_0f_history = Block::find($group_of_lich_su->block_id);
                if (isset($lich_su)) {
                    $grade_list = (isset($lich_su->grade_detail)) ? explode("$", $lich_su->grade_detail) : [];
                    $t7GradeDetail = Grade::where('syllabus_id',  $lich_su->syllabus_id)->where('master_grade', '=', 0)->get();
                    if (!isset($t7GradeDetail)) {
                        continue;
                    }

                    if (count($grade_list) < count($t7GradeDetail)) {
                        TranscriptDetail::where('transcript_id', $user->id)->where('subject_id', $item->subject_id)->update(['status' => -2]);
                    }
                }
            }
        }

        self::checkSubjectRelearn($user, $listSubjectRelearn);
        $user_code = $user->user_code;
        $relearns = RelearnOnline::where('user_code', $user_code)->whereNull('term_name_pass')->get();
        $subject_code_relearn = [];
        if (count($relearns) > 0) {
            foreach ($relearns as $relearn) {
                $subject_code_relearn[$relearn->skill_code] = $relearn->skill_code;
                if (isset($mon_da_pass[$relearn->skill_code])) {
                    RelearnOnline::where('id', $relearn->id)->whereNull('term_name_pass')->update([
                        'term_name_pass' => $term['term_name'],
                        'luot_hoc' => $relearn->luot_hoc + 1,
                    ]);
                }
            }

            TranscriptDetail::where('transcript_id', $user->id)->where('type', '!=', 2)->whereIn('skill_code', $subject_code_relearn)->update(['status' => -4]);
            TranscriptDetail::where('transcript_id', $user->id)->where('type', '!=', 2)->whereIn('skill_code_replace', $subject_code_relearn)->update(['status' => -4]);
        }
    }

    public function exportPdfBangDiemTichLuy($id, Request $request)
    {
        $tttn = [
            'PRO106', 'PRO109', 'PRO110', 'PRO115', 'PRO116', 'PRO117', 'PRO118', 'PRO119', 'PRO120', 'PRO121', 'PRO122',
            'PR0105', 'PRO130', 'PRO128', 'PRO123', 'PRO126', 'PRO134', 'PRO135', 'PRO136', 'PRO137',
            'VIE103', 'VIE104'
        ];
        $subject_done = 0;
        $dtb_point = 0;
        $dtb_tc = 0;
        $transcript = Transcript::where('user_login', $id)->firstOrFail();
        if ($transcript) {
            $transcript->details = TranscriptDetail::where('transcript_id', $transcript->id)->get();
            $transcript->user = User::where('user_code', $transcript->user_code)->first();
            $transcript->user->user_DOB = Carbon::createFromDate($transcript->user->user_DOB)->format('d/m/Y');
            $brand = Brand::where('code', $transcript->user->brand_code)->first();
            $transcript->user->brand_name = ($brand->name ?? $transcript->user->brand_name);
            foreach ($transcript->details as $item) {
                if ($item->status == 1) {
                    $subject_done += 1;
                }
                // Tính toán điểm tích lũy và tín chỉ đã đạt
                // Chỉ tính các môn đã đạt (status = 1) và không phải môn miễn giảm (type != 2) hoặc thực tập (type != 3)
                if ($item->status == 1 && $item->type != 2 && $item->type != 3) {
                    if (!in_array($item->subject_code, $tttn)) {
                        $dtb_point += ($item->point * $item->num_of_credit);
                        $dtb_tc += $item->num_of_credit;
                    }
                }
            }
            $transcript->subject_done = $subject_done;
            $transcript->subject_not_done = count($transcript->details) - $subject_done;
            if ($dtb_tc != 0) {
                $transcript->dtb = round($dtb_point / $dtb_tc, 1);
            } else {
                $transcript->dtb = 0;
            }
            $pdf = new PDF2();
            $pdf = $pdf::loadView('pdf.bang_diem_tich_luy', [
                'title' => $id,
                'item' => $transcript,
            ]);

            return $pdf->stream("bdtl_" . $id . "_.pdf");
        }
    }

    public static function syncFromCourseResultSchedule($skip, $limit = 2000)
    {
        ini_set("pcre.backtrack_limit", "20000000");
        ini_set('max_execution_time', '7200');
        ini_set('memory_limit', '-1');
        $temp_curriculum_id = null;
        $users = User::where('user_level', 3)
            ->whereIn('study_status', [1, 3, 10, 11, 13])
            ->skip($skip)
            ->limit($limit)
            ->get();
        $hoc_cai_thien = self::syncGradePoint($users);

        return count($hoc_cai_thien);
    }

    public static function syncGradePoint($users)
    {
        try {
            $term = Lib::getTermDetails();
            $hoc_cai_thien = [];
            foreach ($users as $user) {
                try {
                    $student = User::where('user_login', $user->user_login)->first();
                    if ($student) {
                        if ($student->curriculum_id == 0) {
                            continue;
                        }
                    } else {
                        continue;
                    }

                    if ($user->getTable() != 'transcripts') {
                        $user = Transcript::where('user_login', $student->user_login)
                            // ->where('user_login', $student->user_login)
                            ->first();
                    }

                    if (!$user) {
                        $user = Transcript::create([
                            'user_login' => $student->user_login,
                            'user_code' => $student->user_code,
                            'curriculum_id' => $student->curriculum_id,
                            'version' => 1,
                            'study_status' => $student->study_status,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);

                        $temp_curriculum_id = $student->curriculum_id;
                    } else {
                        $temp_curriculum_id = $user->curriculum_id;
                        $user->curriculum_id = $student->curriculum_id;
                        $user->study_status = $student->study_status;
                        $user->save();
                    }

                    //            $user->details = TranscriptDetail::where('transcript_id', $user->id)->get();
                    $vie1031 = 0;
                    $vie1032 = 0;
                    $vie1033 = 0;
                    $vie103 = 0;
                    $curriculum = CurriCulum::select('version')->find($user->curriculum_id);
                    if (!$curriculum) {
                        continue;
                    }

                    if ($temp_curriculum_id != $user->curriculum_id) {
                        $user->version = 0;
                        Transcript::where('id', $user->id)->update(['version' => 0]);
                    }

                    // kiểm tra những môn học trong khung và môn trong bảng điểm
                    $checkSubjectInCurriculum = self::compareSubjectTranscript($user, $student);
                    // $checkSubjectInCurriculum = true;

                    $checkCount = TranscriptDetail::where('transcript_id', $user->id)->count();
                    if ($user->version < $curriculum->version || $temp_curriculum_id != $user->curriculum_id || $checkSubjectInCurriculum == false || $checkCount == 0) {
                        TranscriptDetail::where('transcript_id', $user->id)->delete();
                        $subjects = PeriodSubject::where('curriculum_id', $user->curriculum_id)->get();
                        $insert_bang_diem_chi_tiet = [];
                        foreach ($subjects as $subject) {
                            $subject->period = Period::find($subject->period_id);
                            $insert_bang_diem_chi_tiet[] = [
                                'transcript_id' => $user->id,
                                'subject_code' => $subject->subject_code,
                                'subject_name' => $subject->subject_name,
                                'subject_id' => $subject->subject_id,
                                'skill_code' => $subject->skill_code,
                                'type' => 0,
                                'status' => 0,
                                'num_of_credit' => $subject->number_of_credit,
                                'period_subject_id' => $subject->id,
                                'ki_thu' => $subject->period->ordering,
                                'created_at' => now(),
                                'updated_at' => now(),
                            ];
                        }

                        TranscriptDetail::insert($insert_bang_diem_chi_tiet);
                        $user->version = $curriculum->version;
                        // $user->save();
                        Transcript::where('id', $user->id)->update(['version' => $curriculum->version]);
                    }

                    TranscriptDetail::where('transcript_id', $user->id)
                        ->update([
                            'subject_code_pass' => null,
                            'subject_name_pass' => null,
                            'status' => 0,
                            'type' => 0,
                            'subject_code_replace' => null,
                            'skill_code_replace' => null,
                            'point' => 0,
                            'luot_hoc' => 0,
                            'term_name' => null
                        ]);

                    // DB::select("update transcripts a, transcript_details b, mien_giam_tap_trung c, subject d set b.type = 1, b.subject_code_replace = c.subject_code_new, b.skill_code_replace = c.skill_code_new, b.subject_name_replace = d.subject_name, b.updated_at = NOW() where c.type = 1 and a.id = b.transcript_id and a.user_login = c.student_login and b.skill_code = c.skill_code and c.subject_code_new = d.subject_code and a.id = $user->id");
                    DB::select("UPDATE
                        transcripts a,
                        transcript_details b,
                        mien_giam_tap_trung c,
                        subject d
                    SET
                        b.type = 1,
                        b.subject_code_replace = c.subject_code_new,
                        b.skill_code_replace = c.skill_code_new,
                        b.subject_name_replace = d.subject_name,
                        b.updated_at = NOW()
                    WHERE
                        c.type = 1
                        AND a.id = b.transcript_id
                        AND a.user_login = c.student_login
                        AND b.skill_code = c.skill_code
                        AND c.subject_code_new = d.subject_code
                        AND a.id = $user->id
                        AND (
                            EXISTS ( SELECT id FROM t7_course_result t7 WHERE t7.`skill_code` = c.`skill_code_new` AND t7.student_login = c.student_login)
                            OR EXISTS ( SELECT id FROM tranfer_t7_course t72 WHERE t72.`skill_code` = c.`skill_code_new` AND t72.student_login = c.student_login)
                        )");
                    DB::select("update transcripts a, transcript_details b, mien_giam_tap_trung c set b.type = 2, b.status = 1, b.subject_code_pass = b.subject_code, b.subject_name_pass = b.subject_name, b.updated_at = NOW() where c.type = 2 and a.id = b.transcript_id and a.user_login = c.student_login and b.skill_code = c.skill_code and a.id = $user->id");
                    DB::select("update transcripts a, transcript_details b, mien_giam_tap_trung c set b.type = 3, b.status = 1, b.updated_at = NOW() where c.type = 3 and a.id = b.transcript_id and a.user_login = c.student_login and b.skill_code = c.skill_code and a.id = $user->id");

                    // $lich_su_hoc = CourseResult::where('student_login', $user->user_login)->orderBy('groupid', 'ASC')->get();
                    $lich_su_hoc = CourseResult::select('t7_course_result.*', 'list_group.is_virtual')
                        ->join('list_group', 'list_group.id', '=', 't7_course_result.groupid')
                        // ->where('list_group.is_virtual', 0)
                        ->where('student_login', $user->user_login)
                        ->orderBy('start_date', 'ASC')
                        ->get();

                    // $temp_details = TranscriptDetail::where('transcript_id', $user->id)->get();
                    //            $mon_tuong_duong = [];
                    //            $data_tuong_duong = SubjectTransfer::with('details')->where('type', 0)->get();
                    //            foreach ($data_tuong_duong as $item) {
                    //                if ($item->details->count()) {
                    //                    $mon_tuong_duong[$item->skill_code] = $item->skill_code;
                    //                    foreach ($item->details as $sub) {
                    //                        $mon_tuong_duong[$item->skill_code] = [
                    //                            'skill_code' => $sub->skill_code,
                    //                            'subject_code' => $sub->subject_code,
                    //                            'subject_name' => $sub->subject_name,
                    //                        ];
                    //                        $mon_tuong_duong[$sub->skill_code] = [
                    //                            'skill_code' => $item->skill_code,
                    //                            'subject_code' => $item->subject_code,
                    //                            'subject_name' => $item->subject_name,
                    //                        ];
                    //                    }
                    //                }
                    //            }
                    //            foreach ($temp_details as $detail) {
                    //                if (isset($mon_tuong_duong[$detail->skill_code])) {
                    //                    if ($lich_su_hoc->contains('skill_code', $mon_tuong_duong[$detail->skill_code]['skill_code'])) {
                    //                        $detail->type = 1;
                    //                        $detail->subject_code_replace = $mon_tuong_duong[$detail->skill_code]['subject_code'];
                    //                        $detail->subject_name_replace = $mon_tuong_duong[$detail->skill_code]['subject_name'];
                    //                        $detail->skill_code_replace = $mon_tuong_duong[$detail->skill_code]['skill_code'];
                    //                        $detail->save();
                    //                    }
                    //                }
                    //            }
                    $user->details = TranscriptDetail::where('transcript_id', $user->id)->get();
                    $mon_tu_lich_su = [];
                    $mon_da_pass = [];
                    //update môn đang học chưa bắt đầu
                    $subject_on_learn = [];
                    $groups = GroupMember::join('list_group', 'list_group.id', 'group_member.groupid')
                        ->where('list_group.pterm_id', $term['term_id'])
                        ->where('group_member.member_login', $user->user_login)
                        ->whereRaw('list_group.start_date <= CURRENT_DATE')
                        ->get();
                    foreach ($groups as $group) {
                        $subject_on_learn[$group->skill_code] = $group->skill_code;
                    }

                    if (count($subject_on_learn) > 0) {
                        TranscriptDetail::where('transcript_id', $user->id)->where('type', '!=', 2)->whereIn('skill_code', $subject_on_learn)->update(['status' => -2]);
                        TranscriptDetail::where('transcript_id', $user->id)->where('type', '!=', 2)->whereIn('skill_code_replace', $subject_on_learn)->update(['status' => -2]);
                    }
                    //update diem tu bang diem chuyen co so ve bang diem
                    $hasGradeTransfer = TranferT7Course::where('student_login', $user->user_login)
                        ->count();

                    if ($hasGradeTransfer > 0) {
                        $lich_su_hoc_chuyen_co_so = TranferT7Course::select('tranfer_t7_course.*')
                            ->leftJoin(DB::raw('tranfer_t7_course b'), function ($q) {
                                return $q->on('tranfer_t7_course.skill_code', '=', 'b.skill_code')
                                    ->on('tranfer_t7_course.student_login', '=', 'b.student_login')
                                    ->on('tranfer_t7_course.id', '<', 'b.id');
                            })
                            ->where('tranfer_t7_course.student_login', $user->user_login)
                            ->whereNull('b.id')
                            ->get();

                        foreach ($lich_su_hoc_chuyen_co_so as $lich_su) {
                            $skillCodeCheck = $lich_su->skill_code;
                            if ($skillCodeCheck == "" || $skillCodeCheck == null) {
                                $subjectCheck = Subject::where('subject_code', $lich_su->psubject_code)->first();
                                $skillCodeCheck = $subjectCheck->skill_code;
                            }

                            if (isset($mon_tu_lich_su[$skillCodeCheck])) {
                                if ($mon_tu_lich_su[$skillCodeCheck] == 1) {
                                    $mon_tu_lich_su[$skillCodeCheck]['luot_hoc'] += 1;
                                    continue;
                                }
                            }

                            $mon_tu_lich_su[$skillCodeCheck] = [
                                'status' => $lich_su->val === 0 ? -3 : $lich_su->val,
                                'point' => $lich_su->grade,
                                'luot_hoc' => isset($mon_tu_lich_su[$skillCodeCheck]['luot_hoc']) ? $mon_tu_lich_su[$skillCodeCheck]['luot_hoc'] += 1 : 1,
                                'term_name' => $lich_su->pterm_name,
                                'subject_name_pass' => $lich_su->psubject_name,
                                'subject_code_pass' => $lich_su->psubject_code,
                            ];
                        }
                    }


                    //update diem tu lich su hoc ve bang diem
                    //            dd($lich_su_hoc);
                    foreach ($lich_su_hoc as $lich_su) {
                        if (now()->format('Y-m-d') > $lich_su->end_date && $lich_su->is_finish != 2) {
                            $lich_su->is_finish = 2;
                            $lich_su->save();
                        }
                        if (isset($mon_tu_lich_su[$lich_su->skill_code])) {
                            if ($mon_tu_lich_su[$lich_su->skill_code]['status'] == 1) {
                                if ($lich_su->val == 1) {
                                    $mon_tu_lich_su[$lich_su->skill_code]['point'] = $lich_su->grade;
                                    $hoc_cai_thien[] = [
                                        'user' => $user->user_login,
                                        'mon' => $lich_su->skill_code,
                                    ];
                                    $mon_tu_lich_su[$lich_su->skill_code]['luot_hoc'] += 1;
                                    continue;
                                } else {
                                    $mon_tu_lich_su[$lich_su->skill_code]['point'] = $lich_su->grade;
                                    $mon_tu_lich_su[$lich_su->skill_code]['luot_hoc'] += 1;
                                }
                            }
                        }
                        $luot_hoc = isset($mon_tu_lich_su[$lich_su->skill_code]['luot_hoc']) ? $mon_tu_lich_su[$lich_su->skill_code]['luot_hoc'] += 1 : 1;
                        $status = $lich_su->val;
                        if ($lich_su->is_finish === 2 && $lich_su->val === '0') {
                            $status = -3;
                        } else if ($lich_su->val === '0' && ($lich_su->is_finish === 0 || $lich_su->is_finish === 1)) {
                            $status = -2;
                        } else if ($lich_su->val == 0 && ($lich_su->is_finish == 0 || $lich_su->is_finish == 1)) {
                            $status = -2;
                        }

                        $mon_tu_lich_su[$lich_su->skill_code] = [
                            'status' => $status == 0 ? -3 : $status,
                            'point' => $lich_su->grade,
                            'luot_hoc' => $luot_hoc,
                            'term_name' => $lich_su->pterm_name,
                            'subject_name_pass' => $lich_su->psubject_name,
                            'subject_code_pass' => $lich_su->psubject_code,
                        ];
                    }


                    $electiveGroups = ElectiveGroup::where('curriculum_id', $user->curriculum_id)->where('status', 1)->with('electiveSubject')->get();
                    $listDataElective = call_user_func(function () use ($electiveGroups) {
                        $res = [];
                        foreach ($electiveGroups as $key => $value) {
                            $dataAdd = [
                                "id" => $value->id,
                                "name" => $value->name,
                                "curriculum_id" => $value->curriculum_id,
                                "ki_thu" => $value->ki_thu,
                                "note" => $value->note,
                            ];

                            $electiveSubject = ElectiveSubject::where('elective_group_id', $value->id)->get();
                            foreach ($electiveSubject as $v) {
                                if ($v->main_subject == 0) {
                                    $dataAdd['subjects'][] = [
                                        "subject_id" => $v->subject_id,
                                        "skill_code" => $v->skill_code,
                                        "subject_code" => $v->subject_code,
                                        "subject_name" => $v->subject_name
                                    ];
                                } else {
                                    $dataAdd["main_subject_id"] = $v->subject_id;
                                    $dataAdd["main_skill_code"] = $v->skill_code;
                                    $dataAdd["main_subject_code"] = $v->subject_code;
                                    $dataAdd["main_subject_name"] = $v->subject_name;
                                }
                            }

                            $res[] = $dataAdd;
                        }

                        return $res;
                    });

                    foreach ($user->details as $item) {
                        if ($item->skill_code_replace) {
                            if (isset($mon_tu_lich_su[$item->skill_code_replace])) {
                                TranscriptDetail::where('skill_code_replace', $item->skill_code_replace)->where('transcript_id', $user->id)->where('subject_code_replace', $item->subject_code_replace)->whereNotIn('type', [3, 2])->update($mon_tu_lich_su[$item->skill_code_replace]);

                                if ($mon_tu_lich_su[$item->skill_code_replace]['status'] == 1) {
                                    unset($mon_tu_lich_su[$item->skill_code_replace]);
                                    continue;
                                }

                                unset($mon_tu_lich_su[$item->skill_code_replace]);
                                // continue;
                            }
                        } else {
                            if (isset($mon_tu_lich_su[$item->skill_code])) {
                                TranscriptDetail::where('skill_code', $item->skill_code)->where('transcript_id', $user->id)->where('subject_code', $item->subject_code)->whereNotIn('type', [3, 2])->update($mon_tu_lich_su[$item->skill_code]);
                                if ($mon_tu_lich_su[$item->skill_code]['status'] == 1) {
                                    $mon_da_pass[$item->skill_code] = $mon_tu_lich_su[$item->skill_code]['term_name'];
                                }

                                if ($mon_tu_lich_su[$item->skill_code]['status'] == 1) {
                                    unset($mon_tu_lich_su[$item->skill_code]);
                                    continue;
                                }

                                unset($mon_tu_lich_su[$item->skill_code]);
                                // continue;
                            }
                        }

                        $keySearch = array_search($item->subject_code, array_column($listDataElective, 'main_subject_code'));
                        // if ($keySearch === false) {
                        //     $keySearch = array_search($item->subject_code, array_column($listDataElective, 'main_subject_code'));
                        // }


                        if ($keySearch !== false) {
                            if ($item->status != 1 && sizeof($listDataElective[$keySearch]['subjects']) > 0) {
                                $listSubjectId = array_column($listDataElective[$keySearch]['subjects'], 'skill_code');
                                $union = TranferT7Course::select([
                                        'val',
                                        'is_finish',
                                        'skill_code',
                                        'psubject_code',
                                        'psubject_name',
                                        'grade',
                                        'pterm_name'
                                    ])
                                    ->where('student_login', $user->user_login)
                                    ->whereIn('skill_code', $listSubjectId);

                                $listCours = CourseResult::select([
                                        'val',
                                        'is_finish',
                                        'skill_code',
                                        'psubject_code',
                                        'psubject_name',
                                        'grade',
                                        'pterm_name'
                                    ])
                                    ->where('student_login', $user->user_login)
                                    ->whereIn('skill_code', $listSubjectId)
                                    ->union($union)
                                    ->get();

                                $checkNext = false;
                                foreach ($listCours as $v) {
                                    $checkUpdate = TranscriptDetail::where('transcript_id', $user->id)
                                        ->where('subject_code', $item->subject_code)
                                        ->whereIn('type', [3, 2])
                                        ->count();
                                    if ($checkUpdate > 0) {
                                        continue;
                                    }

                                    $statusCheck = $v->val;
                                    if ($v->is_finish === 2 && $v->val === '0') {
                                        $statusCheck = -3;
                                    } else if ($v->val === '0' && ($v->is_finish === 0 || $v->is_finish === 1)) {
                                        $statusCheck = -2;
                                    }

                                    TranscriptDetail::where('transcript_id', $user->id)
                                        ->where('type', '!=', 1)
                                        ->where('subject_code', $item->subject_code)->update([
                                            'status' => $statusCheck == 0 ? -3 : $statusCheck,
                                            'point' => $v->grade,
                                            'subject_name_pass' => $v->psubject_name . " (Học phần tự chọn Thay thế môn: $item->subject_code - $item->subject_name)",
                                            'subject_code_pass' => $v->psubject_code,
                                            // 'skill_code' => $v->skill_code,
                                            'skill_code_replace' => $v->skill_code,
                                            'subject_code_replace' => $v->psubject_code,
                                            'luot_hoc' => 1,
                                            'term_name' => $v->pterm_name,
                                        ]);

                                    $checkNext = true;
                                }

                                if ($checkNext) continue;
                            } else {
                                continue;
                            }
                        }
                    }

                    if (count($mon_tu_lich_su) > 0) {
                        if (isset($mon_tu_lich_su['ENT211']) && isset($mon_tu_lich_su['ENT221']) && $mon_tu_lich_su['ENT211']['status'] == 1 && $mon_tu_lich_su['ENT221']['status'] == 1) {
                            $point = ($mon_tu_lich_su['ENT211']['point'] + $mon_tu_lich_su['ENT211']['point']) / 2;
                            TranscriptDetail::where('skill_code', 'ENT201')->where('transcript_id', $user->id)->where('subject_code', 'ENT201')->whereNotIn('type', [3, 2])->update([
                                'status' => 1,
                                'point' => $point,
                                'luot_hoc' => 0,
                                'term_name' => null,
                            ]);
                        }
                        if (isset($mon_tu_lich_su['ENT212']) && isset($mon_tu_lich_su['ENT222']) && $mon_tu_lich_su['ENT212']['status'] == 1 && $mon_tu_lich_su['ENT222']['status'] == 1) {
                            $point = ($mon_tu_lich_su['ENT212']['point'] + $mon_tu_lich_su['ENT222']['point']) / 2;
                            TranscriptDetail::where('skill_code', 'ENT201')->where('transcript_id', $user->id)->where('subject_code', 'ENT201')->whereNotIn('type', [3, 2])->update([
                                'status' => 1,
                                'point' => $point,
                                'luot_hoc' => 0,
                                'term_name' => null,
                            ]);
                        }
                        if ($vie1031 && $vie1032) {
                            $point = ($vie1031 + $vie1032) / 2;
                            TranscriptDetail::where('skill_code', 'VIE103')->where('transcript_id', $user->id)->where('subject_code', 'VIE103')->whereNotIn('type', [3, 2])->update([
                                'status' => 1,
                                'point' => $point,
                                'luot_hoc' => 0,
                                'term_name' => null,
                            ]);
                        }

                        if ($vie1033 || $vie103) {
                            $point = $vie1033 > 0 ? $vie1033 : $vie103;
                            TranscriptDetail::where('skill_code', 'VIE1031')->where('transcript_id', $user->id)->where('subject_code', 'VIE1031')->whereNotIn('type', [3, 2])->update([
                                'status' => 1,
                                'point' => $point,
                                'luot_hoc' => 0,
                                'term_name' => null,
                            ]);
                            TranscriptDetail::where('skill_code', 'VIE1032')->where('transcript_id', $user->id)->where('subject_code', 'VIE1032')->whereNotIn('type', [3, 2])->update([
                                'status' => 1,
                                'point' => $point,
                                'luot_hoc' => 0,
                                'term_name' => null,
                            ]);
                        }
                    }

                    // update những môn học chưa đủ điểm thành phần được tính là đang học
                    $subjectsInSuspected = TranscriptDetail::whereIn('status', [-3, -4])->where('transcript_id', $user->id)->get();
                    $listSubjectRelearn = [];

                    if (!empty($subjectsInSuspected)) {
                        foreach ($subjectsInSuspected as $item) {
                            $mostRecentCourseResult = CourseResult::where('student_login', $user->user_login)
                                ->where('skill_code', $item->skill_code)->orderBy('groupid', 'DESC')->first();
                            if (!isset($mostRecentCourseResult->groupid)) {
                                continue;
                            }

                            $groupOfMostRecentCourseResult = Group::find($mostRecentCourseResult->groupid);
                            if (isset($groupOfMostRecentCourseResult->block_id)) {
                                $block0fHistory = Block::find($groupOfMostRecentCourseResult->block_id);
                            } else {
                                continue;
                            }

                            if (isset($mostRecentCourseResult)) {
                                $gradeListOfMostRecentCourseResult = (isset($mostRecentCourseResult->grade_detail)) ? explode("$", $mostRecentCourseResult->grade_detail) : [];
                                $t7GradeDetail = Grade::where('syllabus_id',  $mostRecentCourseResult->syllabus_id)->where('master_grade', '=', 0)->get();
                                if (!isset($t7GradeDetail)) {
                                    continue;
                                }

                                if (count($gradeListOfMostRecentCourseResult) < count($t7GradeDetail)) {
                                    TranscriptDetail::where('transcript_id', $user->id)->where('subject_id', $item->subject_id)->update(['status' => -2]);
                                }
                            }
                        }
                    }

                    self::checkSubjectRelearn($user, $listSubjectRelearn);
                    $user_code = $user->user_code;
                    $relearns = RelearnOnline::where('user_code', $user_code)->whereNull('term_name_pass')->get();
                    $subject_code_relearn = [];
                    if (count($relearns) > 0) {
                        foreach ($relearns as $relearn) {
                            if (str_contains($relearn->skill_code, 'ENT')) {
                                continue;
                            }
                            $subject_code_relearn[$relearn->skill_code] = $relearn->skill_code;
                            if (isset($mon_da_pass[$relearn->skill_code])) {
                                RelearnOnline::where('id', $relearn->id)->whereNull('term_name_pass')->update([
                                    'term_name_pass' => $term['term_name'],
                                    'luot_hoc' => $relearn->luot_hoc + 1,
                                ]);
                            }
                        }

                        TranscriptDetail::where('status', '!=', 1)->where('transcript_id', $user->id)->whereIn('skill_code', $subject_code_relearn)->update(['status' => -4]);
                        TranscriptDetail::where('status', '!=', 1)->where('transcript_id', $user->id)->whereIn('skill_code_replace', $subject_code_relearn)->update(['status' => -4]);
                    }
                } catch (\Throwable $th) {
                    Log::channel('transcript-sync')->error('---- Lỗi khi cập nhật bảng điểm sinh viên ----');
                    Log::channel('transcript-sync')->error(json_encode($user));
                    Log::channel('transcript-sync')->error($th->getFile() . ' - ' . __METHOD__ . ' - ' . $th->getLine() . ' - ' . $th->getMessage());
                    Log::channel('transcript-sync')->error('---------------------------------------------');
                }
            }
        } catch (\Throwable $th) {
            Log::channel('transcript-sync')->error('Lỗi khi cập nhật bảng điểm: ' . $th->getFile() . ' - ' . __METHOD__ . ' - ' . $th->getLine() . ' - ' . $th->getMessage());
            Log::channel('transcript-sync')->error(json_encode($users));
            Log::channel('transcript-sync')->error('---------------------------------------------');
        }

        return $hoc_cai_thien;
    }

    public static function compareSubjectTranscript($transcript, $user)
    {
        if ($user->curriculum_id == '' || $user->curriculum_id == null) return true;

        // Lấy danh sách môn trong khung
        $subjects = PeriodSubject::select(['id', 'skill_code', 'subject_code'])->where('curriculum_id', $user->curriculum_id)->orderBy('skill_code')->get();
        if (!$subjects) {
            return false;
        }

        // Lấy danh sách môn trong bảng điểm hiện tại của sunh viên
        $transcriptDetail = $transcript->details()
            ->select(['id', 'skill_code', 'subject_code', 'status', 'point', 'term_name', 'type'])
            ->orderBy('skill_code')
            ->get();
        $subjectByCurriculumn = array_column($subjects->toArray(), 'skill_code');

        if (count($transcriptDetail) > (count($subjects) + 5)) {
            return false;
        }

        // Kiểm Lấy số lượng không có trong khung của sinh viên
        $countCheckDelete = 0;
        foreach ($transcriptDetail as $value) {
            if (!in_array($value->skill_code, $subjectByCurriculumn))
                $countCheckDelete++;
        }

        // kiểm tra trong bảng điểm có các môn khác trong khung thì trả false
        if ($countCheckDelete > 2) {
            return false;
        }

        /* Kiểm tra các môn chưa học nhưng lại đánh là học */
        $countCheckDelete = 0;
        foreach ($transcriptDetail as $value) {
            if ($value->status != 0 && $value->type != 2 && $value->type != 1) {
                // Kiểm tra theo lượt xếp lớp "Của cơ sở hiện tại"
                $checkData = DB::select("SELECT
                    count( group_member.id ) as checked
                FROM
                    group_member
                    LEFT JOIN list_group ON list_group.id = group_member.groupid
                WHERE
                    list_group.skill_code = ?
                    AND list_group.pterm_name = ?
                    AND group_member.member_login = ?", [$value->skill_code, $value->term_name, $user->user_login]);


                // kiểm trả cả chuyển cơ sở
                $checkData2 = DB::select("SELECT
                    COUNT(id) as checked
                FROM
                    tranfer_t7_course
                WHERE
                    student_login = ?
                    AND pterm_name = ?
                    AND skill_code = ?", [
                    $user->user_login,
                    $value->term_name,
                    $value->skill_code
                ]);

                // kiểm tra theo chuyển cơ sở
                if ($checkData[0]->checked == 0 && $checkData2[0]->checked == 0) {
                    // echo "$value->skill_code , $value->status <br>";
                    $countCheckDelete++;
                }
            }
        }

        if ($countCheckDelete > 4) {
            return false;
        }

        return true;
    }

    /**
     * Kiểm tra môn học có thể thi lại
     * <AUTHOR> @since 11/07/2022
     * @version 1.1
     *
     */
    public static function checkSubjectRelearn($user, $listSubjectRelearn)
    {
        try {
            // Tìm kiếm những môn học không pass trên TranscriptDetail (-3 là fail, -4 là thi lại)
            $subject_online = array_keys(Subject::getListSubjectOnline());
            $arr_subject_online = Subject::getListSubjectOnline(true);
            $arr_subject_online[] = null;
            $union = TranscriptDetail::with('transcript')
                ->whereIn('status', [-3, -4])
                ->where('transcript_id', $user->id)
                ->whereIn('subject_code_pass', $arr_subject_online)
                ->whereIn('skill_code', $subject_online);
            $students = TranscriptDetail::with('transcript')
                ->whereIn('status', [-3, -4])
                ->where('transcript_id', $user->id)
                ->whereIn('subject_code_replace', $arr_subject_online)
                ->whereIn('skill_code_replace', $subject_online)
                ->union($union)
                ->get();

            if (count($students) < 1) {
                $students = TranscriptDetail::with('transcript')
                    ->whereIn('status', [-3, -4])
                    ->where('transcript_id', $user->id)
                    ->whereIn('subject_code', $arr_subject_online)
                    ->whereIn('skill_code', $subject_online)
                    ->get();
            }

            $course_id = null;
            $subject_id = null;
            $syllabus_id = null;
            $subject_code = null;
            $subject_name = null;

            $array = [];
            $array_lan_1 = [];
            $array_lan_2 = [];
            $grade_has_query = collect([]);
            // student
            foreach ($students as $student) {
                $grade_group_has_query = collect([]);
                $grades = [];
                $grade_group_id = [];
                $grade_group_id_distinct = [];
                $grade_groups = [];
                $is_final_exam = [];
                $skill_code = $student->skill_code;

                // kiểm tra môn học đủ đầu điểm hay chưa
                if (in_array($skill_code, $listSubjectRelearn)) {
                    TranscriptDetail::where('transcript_id', $user->id)->where('skill_code', $skill_code)->update(['status' => -2]);
                    RelearnOnline::where('user_login', $student->transcript->user_login)->where('skill_code', $skill_code)->delete();
                    continue;
                }

                // Trường hợp skill_code là những môn không phải online mà được thay thế bởi môn online
                if (!in_array($skill_code, $subject_online)) {
                    $skill_code = $student->skill_code_replace;
                }

                if (!$student->transcript) {
                    continue;
                }

                // Tìm kiếm môn học trong lịch sử học
                $lich_su = CourseResult::select('t7_course_result.*')
                    ->join('list_group', 'list_group.id', '=', 't7_course_result.groupid')
                    ->where('list_group.is_virtual', 0)
                    ->where('t7_course_result.skill_code', $skill_code)
                    ->where('student_login', $student->transcript->user_login)
                    ->orderBy('groupid', 'desc')
                    ->first();
                if (!$lich_su || $lich_su->punishment == 1) {
                    continue;
                }

                if ($lich_su->val == -1) {
                    RelearnOnline::where('user_login', $student->transcript->user_login)->where('skill_code', $skill_code)->whereNull('term_name_pass')->update([
                        'status' => -1
                    ]);
                    continue;
                }

                $subject_code = $lich_su->psubject_code;
                $subject_name = $lich_su->psubject_name;
                $course_id = $lich_su->course_id;
                $subject_id = $lich_su->subject_id;
                $syllabus_id = $lich_su->syllabus_id;
                $student['syllabus_id'] = $syllabus_id;
                $student['course_id'] = $course_id;
                $student->term_name = $lich_su->pterm_name;
                $student->group_id = $lich_su->groupid;

                // Lấy chi tiết từng đầu điểm của môn học đấy,để xem xét trượt môn do đâu
                if (!$grade_data_local = $grade_has_query->firstWhere('key_num', $subject_id . $syllabus_id)) {
                    $grade_has_query = $grade_has_query->add([
                        'key_num' => $subject_id . $syllabus_id,
                        'data' => Grade::select('id', 'grade_name', 'grade_group_id', 'weight', 'minimum_required', 'is_final_exam')->where('subject_id', $subject_id)->where('syllabus_id', $syllabus_id)->orderBy('grade_name')->get(),
                    ]);
                    $grade_data_local = $grade_has_query->firstWhere('key_num', $subject_id . $syllabus_id);
                }

                foreach ($grade_data_local['data'] as $item) {
                    if (!$grade_group_local = $grade_group_has_query->firstWhere('key_num', $item->grade_group_id)) {
                        $grade_group_has_query = $grade_group_has_query->add([
                            'key_num' => $item->grade_group_id,
                            'data' => GradeGroup::findOrFail($item->grade_group_id),
                        ]);

                        $grade_group_local = $grade_group_has_query->firstWhere('key_num', $item->grade_group_id);
                    }

                    $grade_group_id[$item->grade_group_id] = isset($grade_group_id[$item->grade_group_id]) ? $grade_group_id[$item->grade_group_id] += 1 : 1;
                    $grades[$item->id] = [
                        'grade_name' => $item->grade_name,
                        'grade_group_id' => $item->grade_group_id,
                        'weight' => $item->weight,
                        'minimum_required' => $item->minimum_required,
                        'point' => 0,
                        'is_final_exam' => $item->is_final_exam,
                        'pass' => $item->minimum_required ? 0 : 1,
                        'grade_group_minimum' => $grade_group_local['data']->minimum_required,
                        'grade_group_name' => $grade_group_local['data']->grade_group_name,
                        'have_point' => 0,
                    ];
                }

                $temp = $grades;
                $course_grade = CourseGrade::select('val', 'login', 'grade_id')
                    ->where('groupid', $lich_su->groupid)
                    // ->whereIn('grade_group_id', $grade_group_id_distinct)
                    ->where('login', $student->transcript->user_login)
                    ->get();

                $hasRelearn = 0;
                $total_grade = $grade_data_local['data']->count();
                $check_progress = 0;
                $check_final = 0;
                $phai_thi_lai = [];
                $thi_qua = [];

                foreach ($course_grade as $item) {
                    if ($temp[$item->grade_id]['minimum_required'] > $item->val) {
                        $hasRelearn += 0;
                    } else {
                        $hasRelearn += 1;
                        $temp[$item->grade_id]['pass'] = 1;
                    }

                    $temp[$item->grade_id]['point'] = $item->val;
                    $temp[$item->grade_id]['have_point'] = 1;
                }

                // kiểm tra các đầu điểm xem có đủ điều kiện thi lại không
                $noCheckSubject = false;
                foreach ($grade_group_has_query as $item) {
                    $listChildGrage = array_filter($temp, function ($a) use ($item) {
                        return $a['grade_group_id'] == $item['data']->id;
                    });

                    // Kiểm tra có phải đầu điểm cuối không, nếu phải thì bỏ qua không cần check
                    $checkFinalPoint = array_filter($listChildGrage, function ($a) {
                        return $a['is_final_exam'] == 1;
                    });

                    if (count($checkFinalPoint) > 0) {
                        continue;
                    }

                    // Kiểm tra các nhóm đầu điểm thành phần có đủ điều kiện đi thi hay không
                    $pointCheck = array_sum(array_map(function ($a) {
                        return $a['weight'] * $a['point'];
                    }, $listChildGrage));

                    $totalPointCheck = array_sum(array_map(function ($a) {
                        return $a['point'];
                    }, $listChildGrage));

                    if ($item['data']->weight == 0) {
                        $totalPointCheck = 0;
                    } else {
                        $totalPointCheck = $pointCheck / $item['data']->weight;
                    }

                    $totalPointCheck = round($totalPointCheck, 1);
                    if ($item['data']->minimum_required != 0 && $item['data']->minimum_required > $totalPointCheck) {
                        $noCheckSubject = true;
                        break;
                    }
                }

                // nếu nhóm đầu điểm không đủ điều kiện thì bỏ qua môn hiện tại
                if ($noCheckSubject == true) {
                    try {
                        if (RelearnOnline::where('user_login', $student->transcript->user_login)->where('subject_code', $subject_code)->where('created_at', '>=', '2022-09-12')->count()) {
                            RelearnOnline::where('user_login', $student->transcript->user_login)->where('subject_code', $subject_code)->where('created_at', '>=', '2022-09-12')->delete();
                        }
                    } catch (\Throwable $th) {
                        Log::channel('transcript-sync')->error('--------- Xóa relearn online bị lỗi --------');
                        Log::channel('transcript-sync')->error(json_encode($student));
                        Log::channel('transcript-sync')->error($th->getFile() . ' - ' . $th->getLine() . ' - ' . $th->getMessage());
                        Log::channel('transcript-sync')->error('--------------------------------------------');
                    }
                    //  xóa relearn online nếu có

                    continue;
                }

                if ($total_grade) {
                    if ($total_grade - $hasRelearn == 0) {
                        $student->not_pass = 0;
                    } else {
                        $student->not_pass = $total_grade - $hasRelearn;
                    }
                } else {
                    $student->not_pass = 999;
                }

                foreach ($temp as $key => $item) {
                    if (isset($grade_groups[$item['grade_group_id']])) {
                        $grade_groups[$item['grade_group_id']]['tong_dau_diem'] += 1;
                        $grade_groups[$item['grade_group_id']]['tong_so_diem'] += $item['point'];
                    } else {
                        $grade_groups[$item['grade_group_id']] = [
                            'tong_dau_diem' => 1,
                            'tong_so_diem' => $item['point'],
                            'grade_group_name' => $item['grade_group_name'],
                            'grade_group_minimum' => $item['grade_group_minimum'],
                        ];
                    }

                    // Xác định phải thi lại vì lí do gì
                    if (!$item['pass']) {
                        if ($item['is_final_exam']) {
                            $is_final_exam[$key] = $key;
                            $check_final += 1;
                            $type = 'unknown';
                            switch ($item['grade_name']) {
                                case 'Bảo vệ assignment':
                                case 'Bảo vệ Assignment':
                                    $type = 'Thi thực hành';
                                    break;
                                case 'Đánh giá Trắc nghiệm':
                                case 'General test':
                                case 'Final exam':
                                case 'Final Exam':
                                case 'Thi hết môn':
                                case 'Thi hết môn (2nd)':
                                case 'Trắc nghiệm':
                                case 'Thi trắc nghiệm':
                                case 'Final test':
                                    $type = 'Thi EOS';
                                    break;
                                case 'Speaking test':
                                case 'Oral test':
                                    $type = 'Thi nói';
                                    break;
                            }

                            $phai_thi_lai[] = [
                                'grade_id' => $key,
                                'minimum_required' => $item['minimum_required'],
                                'grade_group_id' => $item['grade_group_id'],
                                'grade_group_minimum' => $item['grade_group_minimum'],
                                'type' => $type,
                                'text' => $item['grade_name'],
                                'point' => $item['point'],
                            ];
                        } else {
                            $check_progress += 1;
                        }
                    } else {
                        if ($item['is_final_exam']) {
                            $is_final_exam[$key] = $key;
                            $type = 'unknown';
                            switch ($item['grade_name']) {
                                case 'Bảo vệ assignment':
                                case 'Bảo vệ Assignment':
                                    $type = 'Thi thực hành';
                                    break;
                                case 'Đánh giá Trắc nghiệm':
                                case 'General test':
                                case 'Final exam':
                                case 'Final Exam':
                                case 'Thi hết môn':
                                case 'Thi hết môn (2nd)':
                                case 'Trắc nghiệm':
                                case 'Thi trắc nghiệm':
                                case 'Final test':
                                    $type = 'Thi EOS';
                                    break;
                                case 'Speaking test':
                                case 'Oral test':
                                    $type = 'Thi nói';
                                    break;
                            }

                            $thi_qua[] = [
                                'grade_id' => $key,
                                'minimum_required' => $item['minimum_required'],
                                'grade_group_id' => $item['grade_group_id'],
                                'grade_group_minimum' => $item['grade_group_minimum'],
                                'type' => $type,
                                'text' => $item['grade_name'],
                                'point' => $item['point'],
                            ];
                        }
                    }

                    if ($subject_code == 'VIE1015' || $subject_code == 'VIE1025') {
                        if (count($phai_thi_lai) > 1) {
                            if ($phai_thi_lai[0]['text'] == 'Thi hết môn') {
                                unset($phai_thi_lai[0]);
                            }

                            if ($phai_thi_lai[1]['text'] == 'Thi hết môn') {
                                unset($phai_thi_lai[1]);
                            }
                        }
                    }
                }

                $grade_group_not_pass = [];
                // Tra cứu xét học lại, thi lại và lấy danh sách thi lại
                if (count($grade_groups)) {
                    foreach ($grade_groups as $key => $grade_group) {
                        $diem_trung_binh = $grade_group['tong_so_diem'] / $grade_group['tong_dau_diem'];
                        if ($diem_trung_binh < $grade_group['grade_group_minimum']) {
                            $grade_group_not_pass[$key] = $key;
                        }
                    }
                }

                if (count($grade_group_not_pass)) {
                    foreach ($thi_qua as $key => $tq) {
                        if (in_array($tq['grade_group_id'], $grade_group_not_pass)) {
                            if ($tq['minimum_required'] == 0) {
                                $phai_thi_lai[] = $tq;
                                if (in_array($tq['grade_id'], $is_final_exam)) {
                                    $check_final += 1;
                                }

                                unset($thi_qua[$key]);
                            }
                        }
                    }

                    $student->not_pass = $check_final;
                }

                if ($student->point < 5 && $check_progress == 0 && count($phai_thi_lai) == 0) {
                    if (count($thi_qua)) {
                        foreach ($thi_qua as $key => $tq) {
                            if (in_array($tq['grade_id'], $is_final_exam)) {
                                $phai_thi_lai[] = $tq;
                                unset($thi_qua[$key]);
                                $check_final += 1;
                            }
                        }
                    }

                    $student->not_pass = $check_final;
                }

                $student->grade_points = $temp;
                $student->total_grade = $total_grade;
                $student->check_progress = $check_progress;
                $student->check_final = $check_final;
                $student->phai_thi_lai = $phai_thi_lai;
                $student->thi_qua = $thi_qua;
                $student->relearn_temp = $hasRelearn;
                $student->grade_group_not_pass = $grade_group_not_pass;

                if ($student->status == -4) {
                    $relearns = RelearnOnline::where('user_login', $student->transcript->user_login)->where('skill_code', $skill_code)->whereNull('term_name_pass')->get();
                    if ($data = $student->thi_qua) {
                        foreach ($data as $thi_qua) {
                            $check_relearn_pass = $relearns->where('type', $thi_qua['type'])->where('skill_code', $skill_code)->first();
                            if ($check_relearn_pass) {
                                $check_relearn_pass->status = 1;
                                $check_relearn_pass->term_name_pass = "Pass by system";
                                $check_relearn_pass->save();
                            }
                        }
                    }
                }

                if ($student->not_pass) {
                    // kiem tra mon VIE101
                    // if ( $check_final > 0 && $student->skill_code == 'VIE101') {
                    //     $array[] = $student;
                    // }
                    // else
                    if ($check_progress == 0 && $check_final > 0) {
                        $array[] = $student;
                        $array_lan_2[] = $student;
                    } else {
                        $array_lan_2[] = $student;
                    }
                } else {
                    $array_lan_1[] = $student;
                }
            }

            $tong_insert = 0;
            // Tra soát môn học đã học lại hay chưa

            if (count($array) > 0) {
                foreach ($array as $item) {
                    foreach ($item->phai_thi_lai as $mon) {
                        $check = RelearnOnline::where('user_login', $item->transcript->user_login)
                            ->where('skill_code', $item->skill_code)
                            ->where('type', $mon['type'])
                            ->whereNull('term_name_pass')
                            ->first();

                        if (!$check) {
                            RelearnOnline::updateOrInsert(
                                [
                                    'user_code' => $item->transcript->user_code,
                                    'term_name' => $item->term_name,
                                    'type' => $mon['type'],
                                    'skill_code' => $item->skill_code,
                                ],
                                [
                                    'user_login' => $item->transcript->user_login,
                                    'point' => $mon['point'],
                                    'subject_id' => $item->subject_id,
                                    'subject_code' => $item->subject_code,
                                    'subject_name' => $item->subject_name,
                                    'created_by' => (isset(Auth::user()->user_login)) ? Auth::user()->user_login : 'System',
                                    'updated_by' => (isset(Auth::user()->user_login)) ? Auth::user()->user_login : 'System',
                                    'course_id' => $item->course_id,
                                    'syllabus_id' => $item->syllabus_id,
                                    'total_grade' => $item->total_grade,
                                    'check_progress' => $item->check_progress,
                                    'check_final' => $item->check_final,
                                    'grade_id' => $mon['grade_id'],
                                    'grade_name' => $mon['text'],
                                    'group_id' => $item->group_id,
                                    'created_at' => now(),
                                    'updated_at' => now(),
                                ]
                            );
                            $tong_insert++;
                        } else {
                            $check->skill_code = $item->skill_code;
                            $check->subject_code = $item->subject_code;
                            $check->subject_name = $item->subject_name;
                            $check->term_name = $item->term_name;
                            $check->subject_id = $item->subject_id;
                            $check->course_id = $item->course_id;
                            $check->syllabus_id = $item->syllabus_id;
                            $check->total_grade = $item->total_grade;
                            $check->check_progress = $item->check_progress;
                            $check->check_final = $item->check_final;
                            $check->grade_id = $mon['grade_id'];
                            $check->grade_name = $mon['text'];
                            $check->point = $mon['point'];
                            $check->group_id = $item->group_id;
                            $check->updated_by = (isset(Auth::user()->user_login)) ? Auth::user()->user_login : 'System';
                            $check->status = 0;
                            $check->save();
                        }
                    }
                }
            }

            //kiểm tra môn học trong relearn nhưng kp môn online - kiểm tra vết cũ
            $listSubjectInRelearn = RelearnOnline::where('user_login', $user->user_login)->whereNull('term_name_pass')->pluck('subject_id')->toArray();
            $listSubject = Subject::whereIn('id', $listSubjectInRelearn)->get();
            foreach ($listSubject as $item) {
                $haveSubjectCodePass = TranscriptDetail::with('transcript')
                    ->where('transcript_id', $user->id)
                    ->where('subject_id', $item->id)->first();
                $typeOfsubjectChecked = (!isset($haveSubjectCodePass->subject_code_pass))
                    ? $item->subject_type
                    : Subject::where('subject_code', $haveSubjectCodePass->subject_code_pass)->value('subject_type');

                if (strtolower($typeOfsubjectChecked) !== 'online') {
                    RelearnOnline::where('user_login', $user->user_login)
                        ->whereNull('term_name_pass')
                        ->where('subject_id', $item->id)
                        ->delete();
                }
            }
        } catch (\Exception $e) {
            Log::channel('transcript-sync')->error(" ---- Error Check relearn: -----");
            Log::channel('transcript-sync')->error('user : ' . json_encode($user));
            Log::channel('transcript-sync')->error('listSubjectRelearn : ' . json_encode($listSubjectRelearn));
            Log::channel('transcript-sync')->error(__METHOD__ . " - " . $e->getLine() . " err: " . $e->getMessage());
            Log::channel('transcript-sync')->error(" -------------------------------");
            return $e->getMessage();
        }
    }

    /**
     * Kiểm tra môn học có thể thi lại
     * <AUTHOR> @since 03/09/2022
     * @version 1.0
     *
     */
    public static function coveringZeroPointsInTheOldSemesterSubjects($user, $t7CoureResult)
    {
        try {
            // dump($t7CoureResult);
            // danh sach các id đầu điểm của môn học theo syllabus môn học
            $syllabus_id = $t7CoureResult->syllabus_id;
            $course_grade = Grade::where('syllabus_id', $syllabus_id)->pluck('id')->toArray();
            //danh sach diem chi tiet cua mon mà học sinh đã có
            $grade_list = (isset($t7CoureResult->grade_detail)) ? explode("$", $t7CoureResult->grade_detail) : [];
            // lấy danh sách id điểm của môn học đã có điểm
            if (!empty($grade_list)) {
                $arrGrade = [];
                for ($i = 0; $i < count($grade_list); $i++) {
                    $this_grade_detail = $grade_list[$i];
                    $arr = explode(":", $this_grade_detail);
                    if (isset($arr[3])) {
                        $arrGrade[] =  intval($arr[3]);
                    }
                }
                $resultEmptyPoints = array_diff($course_grade, $arrGrade);
                // lay id nhung grade con thieu
                $course_grade_empty = Grade::whereIn('id', $resultEmptyPoints)->get();
                for ($i = 0; $i < count($course_grade_empty); $i++) {
                    $pointInfo = $course_grade_empty[$i];
                    $has_point =  CourseGrade::where('grade_id', $pointInfo->id)->where('groupid', $t7CoureResult->groupid)->where('login', $user->user_login)->first();
                    if (!isset($has_point)) {
                        // lay group point cua diem
                        $syllabus_grade_info = GradeGroup::where('id', $course_grade_empty[$i]->grade_group_id)
                            ->first();
                        // phu diem 0
                        CourseGrade::create([
                            'course_id' => $t7CoureResult->course_id,
                            'grade_id' => $pointInfo->id,
                            'grade_minimum_required' => $pointInfo->minimum_required,
                            'grade_weight' => $pointInfo->weight,
                            'grade_name' => $pointInfo->grade_name,
                            'groupid' => $t7CoureResult->groupid,
                            'login' => $user->user_login,
                            'val' => 0,
                            'comment' => 'Systerm covering zero',
                            'creator_login' => (isset(Auth::user()->user_login)) ? Auth::user()->user_login : 'System',
                            'modifier_login' => (isset(Auth::user()->user_login)) ? Auth::user()->user_login : 'System',
                            'grade_group_id' => $pointInfo->grade_group_id,
                            'grade_group_name' => ($syllabus_grade_info->grade_group_name) ? $syllabus_grade_info->grade_group_name : 'Khong xac dinh',
                            'grade_group_weight' => $syllabus_grade_info->weight,
                            'grade_group_minimum_required' => $syllabus_grade_info['minimum_required'],
                            'syllabus_id' => $pointInfo['syllabus_id'],
                            'subject_id' => $pointInfo['subject_id'],
                            'is_used' => 0,
                            'subject_name' => $pointInfo['subject_name'],
                            'course_group_name' => '',
                            'subject_code' => '',
                            'term_id' => $t7CoureResult['term_id'],
                            'term_name' => $t7CoureResult['pterm_name'],
                            'is_final' => $pointInfo['is_final_exam'],
                            'is_resit' => $pointInfo['allow_resit'],
                            'master_grade' => $pointInfo['master_grade'],
                            'token' => substr(md5($pointInfo->grade_group_id . $syllabus_id), 0, 13),
                            'group_val' => 0,
                            'temp' => 0,
                            'locked' => 1,
                        ]);
                    }
                }
                // cap nhat grade detail cho course result
                $list_grade_after_covering = CourseGrade::where('groupid', $t7CoureResult->groupid)->where('login', $user->user_login)->get();
                $gradeDetailUpdate = [];
                for ($i = 0; $i < count($list_grade_after_covering); $i++) {
                    $gradeDetailUpdate[] = $list_grade_after_covering[$i]['grade_name'] . ':' . $list_grade_after_covering[$i]['grade_weight'] . ':' . $list_grade_after_covering[$i]['val'] . ':' . $list_grade_after_covering[$i]['grade_id'];
                }
                $gradeDetailUpdate = implode('$', $gradeDetailUpdate);
                $t7CoureResult->grade_detail = $gradeDetailUpdate;
                $t7CoureResult->save();
            }
        } catch (\Exception $e) {
            Log::error("Error Check covering zero points in the old term subjects: " . $e->getLine() . " err: " . $e->getMessage());
            return $e->getMessage();
        }
    }

    public function exportNotLearnOldSemester(Request $request)
    {
        ini_set('max_execution_time', -1);
        $datas = DB::select("SELECT
            user.user_code,
            user.kithu,
            period.ordering,
            period_subject.skill_code,
            period_subject.subject_code
        FROM
            user
            JOIN period ON ( period.curriculum_id = user.curriculum_id AND period.ordering < user.kithu )
            JOIN period_subject ON period.id = period_subject.period_id
            JOIN transcripts ON transcripts.user_login = user.user_login
            JOIN transcript_details ON transcript_details.transcript_id = transcripts.id AND period_subject.skill_code = transcript_details.skill_code
        WHERE
            user.user_level = 3
            AND user.study_status = 1
            AND transcript_details.`status` = 0
            ORDER BY user_code, period.ordering");

        header('Content-Type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename="Danh_sach_diem_danh_' . time() . '.csv"');
        $header = [
            'Mã sinh viên',
            'Tên đăng nhập',
            'Kỳ học',
            'Kỳ theo khung',
            'Mã chuyển đổi',
            'Mã môn'
        ];

        $f = fopen('php://output', 'w');
        fputcsv($f, $header);
        foreach ($datas as $key => $line) {
            $dataExport = (array)$line;
            fputcsv($f, $dataExport);
        }
    }
}
