<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Repositories\Admin\StudentWarningRepository;

class StudentWarningController extends Controller
{
    public $StudentWarningRepository;
    public function __construct(StudentWarningRepository $StudentWarningRepository) {
        return $this->StudentWarningRepository = $StudentWarningRepository;
    }
    public function index()
    {
        return $this->StudentWarningRepository->index();
    }
    public function getDataWarning(Request $request)
    {
        return $this->StudentWarningRepository->getDataWarning($request);
        
    }
    public function getTerm()
    {
        return $this->StudentWarningRepository->getTerm();
    }
    public function getBlock()
    {
        return $this->StudentWarningRepository->getBlock();
    }
    public function studentWarningUpdate()
    {
        return $this->StudentWarningRepository->studentWarningUpdate();
    }
    public function filterStudentWarning(Request $request)
    {
        return $this->StudentWarningRepository->filterStudentWarning($request);
    }
    public function filterParentWarning(Request $request)
    {
        return $this->StudentWarningRepository->filterParentWarning($request);
    }
    public function updateStatusSmsWarning(Request $request)
    {
        return $this->StudentWarningRepository->updateStatusSmsWarning($request);
    }
    public function getFormatSmsWarning()
    {
        return $this->StudentWarningRepository->getFormatSmsWarning();     
    }
    public function getAllStudentsWarning(Request $request)
    {
        return $this->StudentWarningRepository->getAllStudentsWarning($request);     
    }
    public function deleteStudentWaning(Request $request)
    {
        return $this->StudentWarningRepository->deleteStudentWaning($request->id);     
    }
}
