<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\Admin\RoleRequest;
use App\Repositories\Admin\RoleRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class RoleController extends Controller
{
    public function __construct(RoleRepository $roleRepository)
    {
        $this->RoleRepository = $roleRepository;
    }

    public function index(Request $request)
    {
        return $this->RoleRepository->index($request);
    }

    public function deleteUser(Request $request)
    {
        return $this->RoleRepository->deleteUser($request);
    }

    public function add(Request $request)
    {
        return $this->RoleRepository->add($request);
    }

    public function store(RoleRequest $request)
    {
        return $this->RoleRepository->store($request);
    }

    public function edit($id)
    {
        return $this->RoleRepository->edit($id);
    }

    public function checkPermission(Request $request)
    {
        return $this->RoleRepository->checkPermission($request);
    }
}
