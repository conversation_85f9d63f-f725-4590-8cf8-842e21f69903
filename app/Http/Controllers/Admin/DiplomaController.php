<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\DataTables\DiplomaDataTable;
use App\Repositories\Admin\DiplomaRepository;
use Illuminate\Http\Request;

class DiplomaController extends Controller
{
    protected $diplomaRepository;

    public function __construct()
    {
        $this->diplomaRepository = new DiplomaRepository();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        return $this->diplomaRepository->index($request);
    }

    /**
     * Datatable ajax request
     *
     * @return \Illuminate\Http\FeedbackdataTable
     */
    public function datatable(DiplomaDataTable $datatable)
    {
        return $datatable->build()->toJson();
    }
    public function import(Request $request) {
        return $this->diplomaRepository->import($request);
    }
   
    public function export(Request $request) {
        return $this->diplomaRepository->export($request);
    }

    public function update(Request $request) {
        return $this->diplomaRepository->updateVanBang($request);
    }   
}
