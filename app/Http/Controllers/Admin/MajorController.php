<?php

namespace App\Http\Controllers\Admin;

use App\Repositories\Admin\MajorRepository;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class MajorController extends Controller 
{
    protected $repository;

    public function __construct(MajorRepository $repository) {
        $this->repository = $repository;
    }

    public function list(Request $request)
    {
        return $this->repository->list($request);
    }
    
    public function store(Request $request)
    {
        return $this->repository->createMajor($request);
    }
    
    public function update(Request $request, $id)
    {
        return $this->repository->updateMajor($request, $id);
    }
    
    public function delete($id)
    {
        return $this->repository->delete($id);
    }
 
}