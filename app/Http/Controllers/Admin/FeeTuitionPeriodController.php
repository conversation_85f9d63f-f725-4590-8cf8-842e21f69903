<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\Admin\FeeTuitionPeriodRepository;
use Illuminate\Http\Request;

class FeeTuitionPeriodController extends Controller
{
    public $feeTuitionPeriodRepository;
    public function __construct(FeeTuitionPeriodRepository $feeTuitionPeriodRepository)
    {
        $this->feeTuitionPeriodRepository = $feeTuitionPeriodRepository;
    }
    public function getListFeeTuitionPeriod(Request $request)
    {
        return $this->feeTuitionPeriodRepository->getListFeeTuitionPeriod($request);
    }

    public function createFeeTuitionPeriod(Request $request)
    {
        return $this->feeTuitionPeriodRepository->createFeeTuitionPeriod($request);
    }
    public function updateFeeTuitionPeriod(Request $request)
    {
        return $this->feeTuitionPeriodRepository->updateFeeTuitionPeriod($request);
    }
    public function deleteFeeTuitionPeriod(Request $request)
    {
        return $this->feeTuitionPeriodRepository->deleteFeeTuitionPeriod($request);
    }
    public function getDetailFeeTuitionPeriod(Request $request)
    {
        return $this->feeTuitionPeriodRepository->getDetailFeeTuitionPeriod($request);
    }
    public function scanAllStudents(Request $request)
    {
        return $this->feeTuitionPeriodRepository->scanAllStudents($request);
    }
    public function deleteStudentTuitionPeriod(Request $request)
    {
        return $this->feeTuitionPeriodRepository->deleteStudentTuitionPeriod($request);
    }
    public function deleteStudentSubjectTuitionPeriod(Request $request)
    {
        return $this->feeTuitionPeriodRepository->deleteStudentSubjectTuitionPeriod($request);
    }

    public function approveFeeTuitionPeriod(Request $request)
    {
        return $this->feeTuitionPeriodRepository->approveFeeTuitionPeriod($request);
    }

    public function cancelFeeTuitionPeriod(Request $request)
    {
        return $this->feeTuitionPeriodRepository->cancelFeeTuitionPeriod($request);
    }
    public function exportFeeTuitionPeriod(Request $request)
    {
        return $this->feeTuitionPeriodRepository->exportFeeTuitionPeriod($request);
    }
    public function exportFeeTuitionPeriodSubject(Request $request)
    {
        return $this->feeTuitionPeriodRepository->exportFeeTuitionPeriodSubject($request);
    }
    public function cancelStudentSubjectTuition(Request $request)
    {
        return $this->feeTuitionPeriodRepository->cancelStudentSubjectTuition($request);
    }
    
}