<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\T7\GradeSyllabus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class SyllabusController extends Controller
{


    /**
     * <AUTHOR> 
     * l<PERSON>y danh sách lớp theo kỳ
     */
    public function export2(Request $request)
    {
        ini_set('max_execution_time', -1);
        $all = $request->get('all', 0);
        if ($all == 0) {
            $datas = DB::select("SELECT
                grade_syllabus.id,
                grade_syllabus.syllabus_code,
                subject.subject_type AS 'Loại hình môn học',
                department.department_name AS 'Bộ môn',
                grade_syllabus.syllabus_name,
                grade_syllabus.subject_code,
                grade_syllabus.subject_name,
                grade_syllabus.number_group as 'số nhóm đầu điểm',
                grade_syllabus.attendance_cutoff as 'phần trăm phải đi học',
                grade_syllabus.minimum_required as 'Điểm tối thiểu',
                t7_grade_group.id as 'id nhóm đầu điểm cha',
                t7_grade_group.grade_group_name as 'tên nhóm đầu điểm cha',
                t7_grade_group.weight as 'Trọng số nhóm đầu điểm cha',
                t7_grade_group.number_grade as 'số đầu điểm con',
                t7_grade_group.minimum_required as 'Điểm tối thiểu cha',
                t7_grade.id as 'id đầu điểm con',
                t7_grade.grade_name as 'tên đầu điểm con',
                t7_grade.weight as 'trọng số đầu điểm con',
                t7_grade.minimum_required as 'Điểm tối thiểu con',
                (CASE WHEN (t7_grade.is_final_exam = 1) THEN 'Điểm thi kết thúc môn' ELSE '-' END) AS 'Điểm thi kết thúc môn',
                (CASE WHEN (t7_grade.bonus_type = 1) THEN 'Đầu điểm thưởng' ELSE '-' END) AS 'Là điểm thưởng',
                t7_grade.max_point AS 'Điểm thưởng tối đa',
                (CASE WHEN (t7_grade.allow_resit = 1) THEN 'Có thi lại' ELSE '-' END) AS 'Có thi lại',
                t7_grade.substitute_grade as 'Đầu điểm thi lại con',
                t7_grade.master_grade as 'Đầu điểm thi lại cha'
            FROM
                grade_syllabus
                JOIN subject ON subject.subject_code = grade_syllabus.subject_code
                JOIN department ON department.id = subject.department_id 
                JOIN course ON course.syllabus_id =  grade_syllabus.id 
                JOIN t7_grade_group ON t7_grade_group.syllabus_id = grade_syllabus.id
                JOIN t7_grade ON t7_grade.grade_group_id = t7_grade_group.id 
            WHERE
                course.term_id = ?
            ORDER BY
            grade_syllabus.id,
            t7_grade_group.id,
            t7_grade.id", [$request->term_id]);
        } else {
            $datas = DB::select("SELECT
                grade_syllabus.id,
                grade_syllabus.syllabus_code,
                subject.subject_type AS 'Loại hình môn học',
                department.department_name AS 'Bộ môn',
                grade_syllabus.syllabus_name,
                grade_syllabus.subject_code,
                grade_syllabus.subject_name,
                grade_syllabus.number_group as 'số nhóm đầu điểm',
                grade_syllabus.attendance_cutoff as 'phần trăm phải đi học',
                grade_syllabus.minimum_required as 'Điểm tối thiểu',
                t7_grade_group.id as 'id nhóm đầu điểm cha',
                t7_grade_group.grade_group_name as 'tên nhóm đầu điểm cha',
                t7_grade_group.weight as 'Trọng số nhóm đầu điểm cha',
                t7_grade_group.number_grade as 'số đầu điểm con',
                t7_grade_group.minimum_required as 'Điểm tối thiểu cha',
                t7_grade.id as 'id đầu điểm con',
                t7_grade.grade_name as 'tên đầu điểm con',
                t7_grade.weight as 'trọng số đầu điểm con',
                t7_grade.minimum_required as 'Điểm tối thiểu con',
                (CASE WHEN (t7_grade.is_final_exam = 1) THEN 'Điểm thi kết thúc môn' ELSE '-' END) AS 'Điểm thi kết thúc môn',
                (CASE WHEN (t7_grade.bonus_type = 1) THEN 'Đầu điểm thưởng' ELSE '-' END) AS 'Là điểm thưởng',
                t7_grade.max_point AS 'Điểm thưởng tối đa',
                (CASE WHEN (t7_grade.allow_resit = 1) THEN 'Có thi lại' ELSE '-' END) AS 'Có thi lại',
                t7_grade.substitute_grade as 'Đầu điểm thi lại con',
                t7_grade.master_grade as 'Đầu điểm thi lại cha'
            FROM
                grade_syllabus
                JOIN subject ON subject.subject_code = grade_syllabus.subject_code
                JOIN department ON department.id = subject.department_id 
                JOIN t7_grade_group ON t7_grade_group.syllabus_id = grade_syllabus.id
                JOIN t7_grade ON t7_grade.grade_group_id = t7_grade_group.id 
            WHERE
                grade_syllabus.inactive = 0
            ORDER BY
            grade_syllabus.id,
            t7_grade_group.id,
            t7_grade.id");
        }

        header('Content-Type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename="Hau_kiem_dau_diem_syllabus_theo_ky_' . time() . '.csv";');
        $header = [
            'id',
            'syllabus_code',
            'Loại hình môn học',
            'Bộ môn',
            'syllabus_name',
            'subject_code',
            'subject_name',
            'số nhóm đầu điểm',
            'phần trăm phải đi học',
            'Điểm tối thiểu',
            'id nhóm đầu điểm cha',
            'tên nhóm đầu điểm cha',
            'Trọng số nhóm đầu điểm cha',
            'số đầu điểm con',
            'Điểm tối thiểu cha',
            'id đầu điểm con',
            'tên đầu điểm con',
            'trọng số đầu điểm con',
            'Điểm tối thiểu con',
            'Điểm thi kết thúc môn',
            'Là điểm thưởng',
            'Điểm thưởng tối đa',
            'Có thi lại',
            'Đầu điểm thi lại con',
            'Đầu điểm thi lại cha'
        ];

        $f = fopen('php://output', 'w');
        fputcsv($f, $header);
        foreach ($datas as $key => $line) {
            $dataExport = (array)$line;
            fputcsv($f, $dataExport);
        }
    }

    /**
     * <AUTHOR> 
     * lấy danh sách lớp theo kỳ
     */
    public function export1(Request $request)
    {
        ini_set('max_execution_time', -1);
        $all = $request->get('all', 0);
        if ($all == 0) {
            $datas = DB::select("SELECT
                grade_syllabus.id,
                grade_syllabus.syllabus_code,
                grade_syllabus.attendance_cutoff AS 'Phần trăm phải đi học',
                subject.subject_type AS 'Loại hình môn học',
                department.department_name AS 'Bộ môn',
                grade_syllabus.syllabus_name,
                subject.skill_code AS 'Mã chuyển đổi',
                grade_syllabus.subject_code 'Mã môn',
                grade_syllabus.subject_name as 'Tên môn',
                t7_syllabus_plan.course_session as 'Số thứ tự buổi học',
                t7_syllabus_plan.session_type as 'Loại buổi học (số)',
                session_type.session_type as 'Loại buổi học (chữ)',
                t7_syllabus_plan.noi_dung as 'Nội Dung'
            FROM
                grade_syllabus
                JOIN subject ON subject.subject_code = grade_syllabus.subject_code
                JOIN department ON department.id = subject.department_id 
                JOIN course ON course.syllabus_id =  grade_syllabus.id 
                JOIN t7_syllabus_plan ON t7_syllabus_plan.syllabus_id =  grade_syllabus.id 
                JOIN session_type ON t7_syllabus_plan.session_type = session_type.id
            WHERE
                course.term_id = ?
            ORDER BY
            grade_syllabus.id DESC,
            t7_syllabus_plan.course_session", [$request->term_id]);
        } else {
            $datas = DB::select("SELECT
                grade_syllabus.id,
                grade_syllabus.syllabus_code,
                grade_syllabus.attendance_cutoff AS 'Phần trăm phải đi học',
                subject.subject_type AS 'Loại hình môn học',
                department.department_name AS 'Bộ môn',
                grade_syllabus.syllabus_name,
                subject.skill_code AS 'Mã chuyển đổi',
                grade_syllabus.subject_code 'Mã môn',
                grade_syllabus.subject_name as 'Tên môn',
                t7_syllabus_plan.course_session as 'Số thứ tự buổi học',
                t7_syllabus_plan.session_type as 'Loại buổi học (số)',
                session_type.session_type as 'Loại buổi học (chữ)',
                t7_syllabus_plan.noi_dung as 'Nội Dung'
            FROM
                grade_syllabus
                JOIN subject ON subject.subject_code = grade_syllabus.subject_code
                JOIN department ON department.id = subject.department_id 
                JOIN t7_syllabus_plan ON t7_syllabus_plan.syllabus_id =  grade_syllabus.id 
                JOIN session_type ON t7_syllabus_plan.session_type = session_type.id
            WHERE
                grade_syllabus.inactive = 0
            ORDER BY
            grade_syllabus.id DESC,
            t7_syllabus_plan.course_session");
        }
        
        header('Content-Type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename="Hau_kiem_lich_trinh_syllabus_theo_ky_' . time() . '.csv";');
        $header = [
            'id',
            'syllabus_code',
            'Phần trăm phải đi học',
            'Loại hình môn học',
            'Bộ môn',
            'syllabus_name',
            'Mã chuyển đổi',
            'Mã môn',
            'Tên môn',
            'Số thứ tự buổi học',
            'Loại buổi học (số)',
            'Loại buổi học (chữ)',
            'Nội Dung'
        ];

        $f = fopen('php://output', 'w');
        fputcsv($f, $header);
        foreach ($datas as $key => $line) {
            $dataExport = (array)$line;
            fputcsv($f, $dataExport);
        }
    }

        /**
     * <AUTHOR> 
     * get api danh sách syllabus
     */

     public function getListSyllabus(Request $request) {
        $query = GradeSyllabus::query();
        $query->limit(15);
        if(isset($request->search)){
            $query->where('syllabus_name', 'like', "%{$request->search}%");
        }
        if(isset($request->subject_id)) {
            $query->where('subject_id', $request->subject_id);
        }
        $syllabus = $query->get();

        return response()->json([
            'data' => $syllabus,
        ], 200);
    }
}
