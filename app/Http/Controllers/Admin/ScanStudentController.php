<?php

namespace App\Http\Controllers\Admin;

use App\Repositories\Admin\ScanStudentRepository;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ScanStudentController extends Controller
{
    public function __construct(ScanStudentRepository $ScanStudentRepository) {
        $this->ScanStudentRepository = $ScanStudentRepository;
    }

    public function getListScanStudent(Request $request) {
        return $this->ScanStudentRepository->getListScanStudent($request);
    }
}
