<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\Admin\DisciplineRequest;
use App\Repositories\Admin\DisciplineRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class DisciplineController extends Controller
{
    public function __construct(DisciplineRepository $disciplineRepository)
    {
        $this->DisciplineRepository = $disciplineRepository;
    }

    public function index(Request $request)
    {
        return $this->DisciplineRepository->index($request);
    }

    public function viewDiscipline($id)
    {
        return $this->DisciplineRepository->viewDiscipline($id);
    }

    public function create()
    {
        return $this->DisciplineRepository->add();
    }

    public function store(DisciplineRequest $request)
    {
        return $this->DisciplineRepository->store($request);
    }
}
