<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\DataTables\MienGiamTapTrungDataTable;
use App\Http\Requests\Admin\MienGiamTapTrungRequest;
use App\Models\Dra\PeriodSubject;
use App\Models\Fu\Subject;
use App\Models\Fu\SubjectUpdateAble;
use App\Models\MienGiamTapTrung;
use App\Repositories\Admin\MienGiamTapTrungRepository;
use App\Models\Fu\User;
use App\Utils\ResponseBuilder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SubstituteSubjectsController extends Controller
{
    protected $mgtt_Repository;

    public function __construct()
    {
         $this->mgtt_Repository = new MienGiamTapTrungRepository();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        return $this->mgtt_Repository->index($request);
    }


    public function import (Request $request) {
        $request->validate([
            // 'so_quyet_dinh' => 'required',
            'file' => 'required|mimes:xlsx,xls,csv'
        ], [
            // 'so_quyet_dinh.required' => 'Vui lòng chọn quyết định',
            'file.required' => "Vui lòng chọn file",
            'file.mimes' => "Không đúng định dạng file (xlsx, xls, csv)"
        ]);
        return $this->mgtt_Repository->import($request);
    }


    public function downloadFileImport() {
        $file_path = public_path('example/import/file_mau_import_thay_the_mon.xlsx');
        return response()->download($file_path);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(MienGiamTapTrungRequest $request)
    {
        
        return $this->mgtt_Repository->store($request);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        return $this->mgtt_Repository->delete($id);
    }

    /**
     * Datatable ajax request
     *
     * @return \Illuminate\Http\Response
     */
    public function datatable(MienGiamTapTrungDataTable $datatable, Request $request)
    {
        $input = $request->all();
        return $datatable->build($input);
    }

    public function getSubjectCode(Request $request) {
        $input = $request->all();
         
        if(!empty($input['get-new-ss'])){
            $data = DB::table('subject_update_able')
                ->select([
                    'id',
                    'new_subject_code',
                    'new_skill_code',
                    'new_subject_name'
                ])
                ->when((!empty($input['old_subject_code'])), function($query) use ($input){
                    return $query->where('old_subject_code', $input['old_subject_code']);
                })
                ->get();   
        }else{
            $data = DB::table('subject_update_able')
                ->select([
                    'id',
                    'old_subject_code',
                    'new_subject_code',
                    'old_skill_code',
                    'new_skill_code',
                    'old_subject_name',
                    'new_subject_name'
                ])
                ->when((!empty($input['q'])), function($query) use ($input){
                    return $query->where('old_subject_code', 'like', "%{$input['q']}%");
                })
                ->groupBy('old_subject_code')
                ->limit('7')->get();   
        }
        return $this->res(200, "success",  $data);
    }

    public function getDecision(Request $request) {
        $input= $request->all();
        $data = DB::table('decision')
        ->when((!empty($input['q'])), function($query) use ($input){
            return $query->where('name', 'like', "%{$input['q']}%");
        })
        ->limit('7')->get();   

        return $this->res(200, "success", $data);
    }


    public function getSubjectCodeByStudent(Request $request) {
        // lấy mã sih viên
        $userLogin = $request->student_login;
        $typeSubject = $request->type;
        $subjectMienGiam = $request->get('subject_code_mg', null);
        $oldSubjectCode = $request->get('old_subject_code', null);
        $newSubjectCode = $request->get('new_subject_code', null);

        $user = User::where('user_login', $userLogin)->first();
        if (!$user) {
            return ResponseBuilder::Fail("Sinh viên không tồn tại", []);
        }

        if ($typeSubject == MienGiamTapTrung::TYPE_MIEN_GIAM) {
            // Nếu là môn miễn giảm
            /** 1. lấy danh sách môn trong khung của sinh viên */
            $listSubjectInCurriculumn = PeriodSubject::select([
                'period_subject.id',
                'period_subject.curriculum_id',
                'period_subject.skill_code',
                'period_subject.subject_code',
                'period_subject.subject_name',
            ])
            ->where('curriculum_id', $user->curriculum_id)
            ->get();

            return ResponseBuilder::Success($listSubjectInCurriculumn, "Lấy danh sách thành công");
        } elseif ($typeSubject == MienGiamTapTrung::TYPE_THAY_THE) {
            // Nếu là môn thay thế thì map cùng với ma trận
            // lấy danh sách môn trong khung
            $listSubjectInCurriculumn = PeriodSubject::select([
                'id',
                'curriculum_id',
                'skill_code',
                'subject_code',
                'subject_name',
            ])
            ->where('curriculum_id', $user->curriculum_id)
            ->get();
            
            $listSubjectCodeCanChange = $listSubjectInCurriculumn->pluck('subject_code');
            
            // Môn có thể thay thế 
            $subjectChange = SubjectUpdateAble::whereIn('old_subject_code', $listSubjectCodeCanChange)->get();
            $subjectChangeObj = call_user_func(function() use ($subjectChange) {
                $subjects = Subject::select([
                    'id',
                    'skill_code',
                    'subject_code',
                    'subject_name',
                ])
                ->whereIn('subject_code', $subjectChange->pluck('old_subject_code'))
                ->get();

                return $subjects;
            });

            if ($oldSubjectCode == null) {
                return ResponseBuilder::Success($subjectChangeObj, "Lấy danh sách thành công");
            }

            // Môn thay thế dựa vào 
            $subjectCanChange = SubjectUpdateAble::whereIn('old_subject_code', $subjectChangeObj->pluck('subject_code'))->get();
            $subjectCanChangeObj = call_user_func(function() use ($subjectCanChange) {
                $subjects = Subject::select([
                    'id',
                    'skill_code',
                    'subject_code',
                    'subject_name',
                ])
                ->whereIn('subject_code', $subjectCanChange->pluck('new_subject_code'))
                ->get();

                return $subjects;
            });

            return ResponseBuilder::Success($subjectCanChangeObj, "Lấy danh sách thành công");
        } else {
            return ResponseBuilder::Success([], "Lấy danh sách thành công");
        }
    }

    public function importMienGiam(Request $request) {
        return $this->mgtt_Repository->importMienGiam($request);
    }
    
}
