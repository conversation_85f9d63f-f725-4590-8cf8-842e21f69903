<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

class AdmissionController extends Controller
{
    /**
     * Get current user info for logging
     */
    private function getCurrentUserInfo()
    {
        $user = auth()->user();
        return [
            'user_id' => $user->id ?? null,
            'user_login' => $user->user_login ?? 'system',
            'user_name' => $user->user_name ?? 'System User',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ];
    }

    /**
     * Get admissions list for API
     */
    public function getAdmissions(Request $request)
    {
        try {
            $perPage = $request->get('per_page', 6);
            $search = $request->get('search');
            $status = $request->get('status');
            $grade = $request->get('grade'); // For compatibility

            $query = Admission::query()
                ->search($search)
                ->byStatus($status)
                ->orderBy('created_at', 'desc');

            $admissions = $query->paginate($perPage);

            // Log successful request
            $userInfo = $this->getCurrentUserInfo();
            Log::info('Admissions list retrieved', [
                'action' => 'GET_ADMISSIONS_LIST',
                'total_records' => $admissions->total(),
                'current_page' => $admissions->currentPage(),
                'filters' => compact('search', 'status', 'grade'),
                'performed_by' => $userInfo
            ]);

            return response()->json([
                'success' => true,
                'data' => $admissions->items(),
                'pagination' => [
                    'current_page' => $admissions->currentPage(),
                    'last_page' => $admissions->lastPage(),
                    'per_page' => $admissions->perPage(),
                    'total' => $admissions->total(),
                    'from' => $admissions->firstItem(),
                    'to' => $admissions->lastItem()
                ]
            ]);
        } catch (\Exception $e) {
            $userInfo = $this->getCurrentUserInfo();
            Log::error('Error retrieving admissions list', [
                'action' => 'GET_ADMISSIONS_LIST_FAILED',
                'error' => $e->getMessage(),
                'performed_by' => $userInfo
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải danh sách hồ sơ'
            ], 500);
        }
    }

    /**
     * Store new admission
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_surname' => 'required|string|max:255',
            'user_givenname' => 'required|string|max:255',
            'user_DOB' => 'required|date',
            'gender' => 'required',
            'user_address' => 'required|string|max:500',
            'province' => 'required|string|max:255',
            'district' => 'required|string|max:255',
            'ward' => 'required|string|max:255',
            'training_level' => 'required|string|max:255',
            'curriculum_id' => 'required|string|max:255',
            'education_level' => 'required|string|max:255',
            'admission_period' => 'required|string|max:255',
            // Optional fields
            'user_middlename' => 'nullable|string|max:255',
            'user_code' => 'nullable|string|max:50',
            'user_email' => 'nullable|email|max:255',
            'user_telephone' => 'nullable|string|max:20',
            'priority_object' => 'nullable|string|max:255',
            'note' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            Log::error('Admission store validation failed', [
                'request_data' => $request->all(),
                'validation_errors' => $validator->errors()->toArray()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            // Map form fields to database fields
            $data = [
                'user_surname' => $request->user_surname,
                'user_middlename' => $request->user_middlename ?: '',
                'user_givenname' => $request->user_givenname,
                'user_birthday' => $request->user_DOB,
                'user_gender' => (int) $request->gender,
                'user_code' => $request->user_code,
                'user_email' => $request->user_email,
                'user_telephone' => $request->user_telephone,
                'user_address' => $request->user_address,
                'user_province' => $request->province,
                'user_district' => $request->district,
                'user_ward' => $request->ward,
                'training_level' => $request->training_level,
                'training_program' => $request->curriculum_id,
                'cultural_level' => $request->education_level,
                'admission_period' => $request->admission_period,
                'priority_object' => $request->priority_object,
                'note' => $request->note
            ];

            // Clean optional fields
            $optionalFields = ['user_code', 'user_email', 'user_telephone', 'priority_object', 'note'];
            foreach ($optionalFields as $field) {
                if (isset($data[$field]) && $data[$field] === '') {
                    $data[$field] = null;
                }
            }

            // Set creator from authenticated user
            $createdBy = 'system';
            if (auth()->check() && auth()->user()) {
                $createdBy = auth()->user()->user_login ?? auth()->user()->name ?? 'system';
            }

            $admission = Admission::create(array_merge(
                $data,
                ['created_by' => $createdBy, 'status' => Admission::STATUS_PENDING]
            ));

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Tạo hồ sơ tuyển sinh thành công',
                'data' => $admission
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show admission details
     */
    public function show($id)
    {
        try {
            $admission = Admission::findOrFail($id);
            return response()->json([
                'success' => true,
                'data' => $admission
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy hồ sơ'
            ], 404);
        }
    }



    /**
     * Update admission
     */
    public function update(Request $request, $id)
    {
        // Validation rules - accept both old and new field names for flexibility
        $validator = Validator::make($request->all(), [
            'user_surname' => 'required|string|max:255',
            'user_middlename' => 'nullable|string|max:255',
            'user_givenname' => 'required|string|max:255',
            'user_birthday' => 'required|date|before:today',
            'user_gender' => 'required|integer|in:0,1',
            'user_code' => 'nullable|string|max:50|unique:admissions,user_code,' . $id,
            'user_email' => 'nullable|email|max:255|unique:admissions,user_email,' . $id,
            'user_telephone' => 'nullable|string|max:20',
            'user_address' => 'required|string|max:500',
            'user_province' => 'required|string|max:255',
            'user_district' => 'required|string|max:255',
            'user_ward' => 'required|string|max:255',
            'training_level' => 'required|string|max:255',
            'training_program' => 'required|string|max:255',
            'cultural_level' => 'required|string|max:255',
            'admission_period' => 'required|string|max:255',
            'priority_object' => 'nullable|string|max:255',
            'note' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $admission = Admission::findOrFail($id);

            // Map form fields to database fields
            $data = [
                'user_surname' => $request->user_surname,
                'user_middlename' => $request->user_middlename ?: '',
                'user_givenname' => $request->user_givenname,
                'user_birthday' => $request->user_birthday,
                'user_gender' => $request->user_gender,
                'user_code' => $request->user_code,
                'user_email' => $request->user_email,
                'user_telephone' => $request->user_telephone,
                'user_address' => $request->user_address,
                'user_province' => $request->user_province,
                'user_district' => $request->user_district,
                'user_ward' => $request->user_ward,
                'training_level' => $request->training_level,
                'training_program' => $request->training_program,
                'cultural_level' => $request->cultural_level,
                'admission_period' => $request->admission_period,
                'priority_object' => $request->priority_object,
                'note' => $request->note
            ];

            // Clean optional fields
            $optionalFields = ['user_code', 'user_email', 'user_telephone', 'priority_object', 'note'];
            foreach ($optionalFields as $field) {
                if (isset($data[$field]) && $data[$field] === '') {
                    $data[$field] = null;
                }
            }

            $admission->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Cập nhật hồ sơ thành công',
                'data' => $admission
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete admission
     */
    public function destroy($id)
    {
        try {
            $admission = Admission::findOrFail($id);
            $admission->delete();

            return response()->json([
                'success' => true,
                'message' => 'Xóa hồ sơ thành công'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Approve admission
     */
    public function approve($id)
    {
        try {
            $admission = Admission::findOrFail($id);
            $admission->update([
                'status' => Admission::STATUS_APPROVED,
                'approved_by' => auth()->user()->user_login ?? 'system',
                'approved_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Duyệt hồ sơ thành công',
                'data' => $admission
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject admission
     */
    public function reject(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'note' => 'required|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Lý do từ chối không được bỏ trống',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $admission = Admission::findOrFail($id);
            $admission->update([
                'status' => Admission::STATUS_REJECTED,
                'rejection_note' => $request->note,
                'rejected_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Từ chối hồ sơ thành công',
                'data' => $admission
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk approve admissions
     */
    public function bulkApprove(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'admission_ids' => 'required|array',
            'admission_ids.*' => 'integer|exists:admissions,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $updated = Admission::whereIn('id', $request->admission_ids)
                ->where('status', Admission::STATUS_PENDING)
                ->update([
                    'status' => Admission::STATUS_APPROVED,
                    'approved_by' => auth()->user()->user_login ?? 'system',
                    'approved_at' => now()
                ]);

            return response()->json([
                'success' => true,
                'message' => "Đã duyệt {$updated} hồ sơ thành công"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }



    /**
     * Import admissions
     */
    public function import(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:xlsx,xls,csv|max:10240' // Max 10MB
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'File không hợp lệ',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $file = $request->file('file');

            Log::info('Starting admission import', [
                'filename' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'extension' => $file->getClientOriginalExtension()
            ]);



            $import = new \App\Imports\AdmissionImportByIndex();

            Excel::import($import, $file);

            $userInfo = $this->getCurrentUserInfo();
            Log::info('Admission import completed', [
                'action' => 'IMPORT_ADMISSIONS',
                'success_count' => $import->getSuccessCount(),
                'error_count' => $import->getErrorCount(),
                'total_processed' => count($import->getResults()),
                'performed_by' => $userInfo
            ]);

            return response()->json([
                'success' => true,
                'message' => "Import hoàn tất. Thành công: {$import->getSuccessCount()}, Lỗi: {$import->getErrorCount()}",
                'results' => [
                    'success_count' => $import->getSuccessCount(),
                    'error_count' => $import->getErrorCount(),
                    'total_processed' => count($import->getResults()),
                    'errors' => $import->getErrors(),
                    'results' => $import->getResults()
                ]
            ]);
        } catch (\Exception $e) {
            $userInfo = $this->getCurrentUserInfo();
            Log::error('Admission import failed', [
                'action' => 'IMPORT_ADMISSIONS_FAILED',
                'error' => $e->getMessage(),
                'performed_by' => $userInfo
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download template
     */
    public function downloadTemplate()
    {
        try {
            $filename = 'mau_import_tuyen_sinh_' . date('Y_m_d_H_i_s') . '.xlsx';

            $userInfo = $this->getCurrentUserInfo();
            Log::info('Admission template downloaded', [
                'action' => 'DOWNLOAD_ADMISSION_TEMPLATE',
                'filename' => $filename,
                'performed_by' => $userInfo
            ]);

            return Excel::download(new \App\Exports\AdmissionTemplateExport, $filename);
        } catch (\Exception $e) {
            $userInfo = $this->getCurrentUserInfo();
            Log::error('Admission template download failed', [
                'action' => 'DOWNLOAD_ADMISSION_TEMPLATE_FAILED',
                'error' => $e->getMessage(),
                'performed_by' => $userInfo
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export admission data
     */
    public function exportData(Request $request)
    {
        try {
            // Set memory and time limits for large exports
            ini_set('memory_limit', '1024M'); // Increased memory limit
            ini_set('max_execution_time', 600); // 10 minutes

            // Validate request
            $request->validate([
                'search' => 'nullable|string|max:255',
                'status' => 'nullable|integer|in:0,1,2',
                'admission_period' => 'nullable|string|max:50',
                'training_level' => 'nullable|string|max:100',
                'training_program' => 'nullable|string|max:100',
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
            ]);

            // Get filters from request
            $filters = [
                'search' => $request->get('search'),
                'status' => $request->get('status'),
                'admission_period' => $request->get('admission_period'),
                'training_level' => $request->get('training_level'),
                'training_program' => $request->get('training_program'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
            ];

            // Remove empty filters
            $filters = array_filter($filters, function($value) {
                return $value !== null && $value !== '';
            });

            // Get count for logging
            $query = Admission::query()->orderBy('created_at', 'desc');

            // Apply same filters to get count
            if (!empty($filters['search'])) {
                $query->search($filters['search']);
            }
            if (isset($filters['status']) && $filters['status'] !== '') {
                $query->byStatus($filters['status']);
            }
            if (!empty($filters['admission_period'])) {
                $query->byAdmissionPeriod($filters['admission_period']);
            }
            if (!empty($filters['training_level'])) {
                $query->where('training_level', $filters['training_level']);
            }
            if (!empty($filters['training_program'])) {
                $query->where('training_program', $filters['training_program']);
            }
            if (!empty($filters['date_from'])) {
                $query->whereDate('created_at', '>=', $filters['date_from']);
            }
            if (!empty($filters['date_to'])) {
                $query->whereDate('created_at', '<=', $filters['date_to']);
            }

            $count = $query->count();

            $filename = 'danh_sach_tuyen_sinh_' . date('Y_m_d_H_i_s') . '.xlsx';

            $userInfo = $this->getCurrentUserInfo();
            Log::info('Admission data export started', [
                'action' => 'EXPORT_ADMISSION_DATA',
                'filename' => $filename,
                'filters' => $filters,
                'count' => $count,
                'performed_by' => $userInfo
            ]);

            // Use fast export for better performance
            $response = Excel::download(new \App\Exports\AdmissionDataExportFast($filters), $filename, \Maatwebsite\Excel\Excel::XLSX, [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
                'Cache-Control' => 'max-age=0',
            ]);

            // Add CORS headers for browser compatibility
            $response->headers->set('Access-Control-Allow-Origin', '*');
            $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
            $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
            $response->headers->set('Access-Control-Expose-Headers', 'Content-Disposition');

            return $response;
        } catch (\Exception $e) {
            $userInfo = $this->getCurrentUserInfo();
            Log::error('Admission data export failed', [
                'action' => 'EXPORT_ADMISSION_DATA_FAILED',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'performed_by' => $userInfo
            ]);

            // Return proper error response for AJAX requests
            if (request()->expectsJson() || request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Có lỗi xảy ra khi tạo file Excel: ' . $e->getMessage()
                ], 500);
            }

            // For direct browser requests, return a simple error page
            return response('Có lỗi xảy ra khi tạo file Excel: ' . $e->getMessage(), 500)
                ->header('Content-Type', 'text/plain');
        }
    }
}
