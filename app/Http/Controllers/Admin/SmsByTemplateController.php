<?php

namespace App\Http\Controllers\Admin;

use App\Repositories\Admin\SmsRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Sms\StudentSmsPermission;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SmsByTemplateController extends Controller
{
    protected $SmsRepository;
    public function __construct(SmsRepository $SmsRepository)
    {
        $this->SmsRepository = $SmsRepository;
    }

    public function index()
    {
        return $this->SmsRepository->index();
    }
    public function getPermissionSms()
    {
        return $this->SmsRepository->getPermissionSms();
    }
    public function getListRole()
    {
        return $this->SmsRepository->getListRole();
    }
    public function viewFormatSms()
    {
        return $this->SmsRepository->viewFormatSms();
    }
    public function getSmsCategory()
    {
        return $this->SmsRepository->getSmsCategory();
    }

    public function getSmsFormat()
    {
        return $this->SmsRepository->getSmsFormat();
    }
    public function getFormatList(Request $request)
    {
        return $this->SmsRepository->getFormatList($request);
    }
    public function getListTelco()
    {
        return $this->SmsRepository->getListTelco();
    }

    public function getDataSmsList(Request $request)
    {
        return $this->SmsRepository->getDataSmsList($request);
    }

    public function getSmsListDetail(Request $request)
    {
        return $this->SmsRepository->getSmsListDetail($request);
    }

    public function createFormat(Request $request)
    {
        return $this->SmsRepository->createFormat($request);
    }
    public function importDataSms(Request $request)
    {
        return $this->SmsRepository->importDataSms($request);
    }
    public function setSendTime(Request $request)
    {
        try{
            $now = Carbon::now();
            if($request->send_time != null && $request->send_time <= $now){
                return response(['status'  => 'fail', 'notification'=> 'Thời gian gửi không hợp lệ.'], 200);
            }
            $user_permission = StudentSmsPermission::leftJoin('student_sms_role', 'student_sms_role.id', '=', 'student_sms_permission.role_id')
            ->where('student_sms_permission.user_login', Auth::user()->user_login)
            ->where(function ($query) {
                $query->where('sms_role_code', '=', "gui-tin-nhan")
                      ->orWhere('sms_role_code', '=', "quan-tri");
            })->first();
            if(!$user_permission) return response(['status'  => 'fail', 'notification'=> 'Người dùng không có quyền gửi tin nhắn.'], 200);
            return $this->SmsRepository->setSendTime($request);
        } catch (\Exception $ex) {
            return response(['status'  => 'fail', 'notification'=> 'Có lỗi xảy ra!'], 500);
        }
    }

    public function reviewListSms(Request $request)
    {
        try{
            $user_permission = StudentSmsPermission::leftJoin('student_sms_role', 'student_sms_role.id', '=', 'student_sms_permission.role_id')
            ->select('student_sms_permission.*')
            ->where('student_sms_permission.user_login', Auth::user()->user_login)
            ->where('student_sms_role.sms_role_code', $request->role_require)->firstOrFail();
            return $this->SmsRepository->reviewListSms($request->id);
        } catch (\Exception $ex) {
            return response(['status'  => false, 'notification'=> 'Người dùng không có quyền xác nhận'], 200);
        }
    }
    public function acceptListSms(Request $request)
    {
        try{
            $user_permission = StudentSmsPermission::leftJoin('student_sms_role', 'student_sms_role.id', '=', 'student_sms_permission.role_id')
            ->select('student_sms_permission.*')
            ->where('student_sms_permission.user_login', Auth::user()->user_login)
            ->where('student_sms_role.sms_role_code', $request->role_require)->firstOrFail();
            return $this->SmsRepository->acceptListSms($request);      
        } catch (\Exception $ex) {
            return response(['status'  => false, 'notification'=> 'Người dùng không có quyền phê duyệt'], 200);
        }
    }
    public function cancelListSms(Request $request)
    {
        return $this->SmsRepository->cancelListSms($request);      
    }
    public function fillDataStudent(Request $request)
    {
        return $this->SmsRepository->fillDataStudent($request);     
    }
    public function saveFormatWarning(Request $request)
    {
        return $this->SmsRepository->saveFormatWarning($request);     
    }
    public function sendSms(Request $request)
    {
        try{
            $user_permission = StudentSmsPermission::leftJoin('student_sms_role', 'student_sms_role.id', '=', 'student_sms_permission.role_id')
            ->select('student_sms_permission.*')
            ->where('student_sms_permission.user_login', Auth::user()->user_login)
            ->where('student_sms_role.sms_role_code', 'gui-tin-nhan')->firstOrFail();
            return $this->SmsRepository->sendSms($request->list_id);     
        } catch (\Exception $ex) {
            return response(['status'  => 'fail', 'notification'=> 'Người dùng không có quyền gửi tin nhắn'], 200);
        }
    }
    public function reSendSms(Request $request)
    {
        try{
            $user_permission = ['dev'];
            if(in_array(Auth::user()->user_login, $user_permission)){
                return $this->SmsRepository->reSendSms($request->list_id);     
            }
            return response(['status'  => 'fail', 'notification'=> 'Người dùng không có quyền gửi tin nhắn'], 200);
        } catch (\Exception $ex) {
            return response(['status'  => 'fail', 'notification'=> 'Người dùng không có quyền gửi tin nhắn'], 200);
        }
    }
    public function editSmsList(Request $request)
    {
        $list_id = $request->list_id;
        $sms_id = $request->sms_id;
        return $this->SmsRepository->editSmsList($list_id, $sms_id);     
    }
    public function exportExcelSms(Request $request)
    {
        $list_id = $request->list_id;
        return $this->SmsRepository->exportExcelSms($list_id);      
    }
}
