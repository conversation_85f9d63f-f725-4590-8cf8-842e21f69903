<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\EnglishDetailLevel;
use App\Models\EnglishDetail;
use App\Models\Fee\Fee;
use App\Models\Fee\Mail;
use App\Models\Fee\Plan;
use App\Models\Fee\FeeLog;
use App\Models\Fee\FeeDetail;
use App\Models\Fee\Transaction;
use App\Models\Fee\SubjectAgain;
use App\Models\Fu\Area;
use App\Models\Fu\ServiceLog;
use App\Models\Fu\ServiceRegister;
use App\Models\Fu\Group;
use App\Models\Fu\Term;
use App\Models\Dra\CurriCulum;
use App\Models\T7\CourseResult;
use App\Models\TranferT7Course;
use App\Models\Fu\User;
use Carbon\Carbon;
use App\Utils\ResponseBuilder;

class AdministrativeController extends Controller
{
    public function exportDateFee(Request $request)
    {
        ini_set('max_execution_time', -1);
        $now = Carbon::now();

        // danh sách Trạng thái tiếng anh
        $listStatusEnglish = [
            -4 => 'Thi lại',
            -3 => 'Chưa đạt',
            -2 => 'Đang học',
            -1 => 'Trượt điểm danh',
            0 => 'Chưa học',
            1 => 'Đạt',
            2 => 'Miễn Giảm'
        ];

        $termCheck = Term::whereDate('startday', '>=', $now->format('Y-m-d'))
            ->whereDate('endday', '<=', $now->format('Y-m-d'))
            ->first();
        if (!$termCheck) {
            $termCheck = Term::orderBy('id', 'DESC')->first();
        }

        if ($request->get('export', -1) == -1) {
            $listBarnd = CurriCulum::select('brand_code', 'nganh')->groupBy('brand_code')->get();
            $status = [
                1 => "DH (Đang học)",
                2 => "HL (Học lại)",
                3 => "TN (Tạm nghỉ)",
                4 => "BH (Bỏ học)",
                5 => "CXL (Chờ xếp lớp)",
                6 => "CTN (Chờ tốt nghiệp)",
                7 => "TNG (Đã tốt nghiệp)",
            ];

            $terms = Term::orderBy('id', 'DESC')->get();
            return view(env('THEME_ADMIN') . '.export.fee_data', [
                'brands' => $listBarnd,
                'status' => $status,
                'terms' => $terms
            ]);
        } else {
            $f = fopen('php://output', 'w');
            header('Content-Type: text/csv; charset=UTF-8');
            header('Content-Disposition: attachment; filename=export2.csv;');
        
            if ($request->get('brand_code', -1) != -1) {
                $feeCount = $feeCount->where('brand_code', $request->brand_code);
            }

            if ($request->get('status', -1) != -1) {
                $feeCount = $feeCount->where('study_status', $request->status);
            } else {
                if (trim($request->get('list_user_code', '')) == '') {
                    $feeCount = $feeCount->whereIn('study_status', [1,3,10]);
                }
            }

            $listUser = [];
            if (trim($request->get('list_user_code', '')) != '') {
                $listUser = explode(',', $request->list_user_code);
                foreach ($listUser as $key => $value) {
                    $listUser[$key] = trim($value);
                }
            }
             
            if (count($listUser) > 0) {
                $feeCount = $feeCount->whereIn('user_code', $listUser);
            }
            
            $feeCount = $feeCount->count();
            $numFee = ceil($feeCount / 1000);
            $header = ['MSSV', 'TRẠNG THÁI','NGÀNH', 'KỲ THỨ', 'HỌC PHÍ', 'PHÍ SÁCH', 'P.TA', 'LEVEL TA', 'TRẠNG THÁI', 'TT P.HK', 'TT PS', 'TT P.TA', 'VÍ HỌC PHÍ', 'VÍ HỌC LẠI', 'VÍ ƯU ĐÃI'];
            fputcsv($f, $header);
            $feePlans = Plan::with('details')
            ->select([
                'id', 
                'brand_code', 
                'curriculum_id'
            ])
            ->get()
            ->toArray();
            
            for ($i = 0;$i < $numFee * 1000;$i+=1000) {
                $fees = Fee::select([
                        'user_code', 
                        'brand_code', 
                        'curriculum_id',
                        'study_wallet', 
                        'relearn_wallet', 
                        'promotion_wallet', 
                        'ki_thu', 
                        'id'
                    ]);
                if ($request->get('brand_code', -1) != -1) {
                    $fees = $fees->where('brand_code', $request->brand_code);
                }
    
                if ($request->get('status', -1) != -1) {
                    $fees = $fees->where('study_status', $request->status);
                } else {
                    if (trim($request->get('list_user_code', '')) == '') {
                        $fees = $fees->whereIn('study_status', [1,3,10]);
                    }
                }
    
                $listUser = [];
                if (trim($request->get('list_user_code', '')) != '') {
                    $listUser = explode(',', $request->list_user_code);
                    foreach ($listUser as $key => $value) {
                        $listUser[$key] = trim($value);
                    }
                }
                 
                if (count($listUser) > 0) {
                    $fees = $fees->whereIn('user_code', $listUser);
                }
                
                $fees = $fees
                    ->skip($i)
                    ->limit(1000)
                    ->get();

                foreach ($fees as $fee) {
                    $tieng_anh = 0;
                    $englishStatus = null;
                    $type_fee = [1,2,5];
                    $ki_thu = $fee->ki_thu + 1;
                    $version = 0;
                    $english_level = 0;
                    $note = null;
                    if ($ki_thu == 1) {
                        continue;
                    }
        
                    $user = User::where('user_code', $fee->user_code)->first();
                    if (!$user) {
                        continue;
                    }
        
                    $english = EnglishDetail::select('id')->where('user_code', $fee->user_code)->first();
                    $last_english = null;
                    if ($english) {
                        $last_english = EnglishDetailLevel::select('level', 'status', 'version', 'note')
                        ->where('english_id', $english->id)
                        ->orderBy('level', 'DESC')
                        ->orderBy('id', 'DESC')
                        // ->orderBy('create_time','desc')
                        ->first();
                        if ($last_english) {
                            $tieng_anh = ($last_english->level != 4 ? ($last_english->status == 1 ? 2600000 : 500000) : 0);
                            $englishStatus = $last_english->status;
                            $version = $last_english->version + $version;
                            $note = $note . $last_english->note;
                            $english_level = $last_english->level;
                        }
                    }
        
                    $fdetail = FeeDetail::where('ki_thu', $ki_thu)->whereIn('type_fee', $type_fee)->where('fee_id', $fee->id)->get();
                    $feeNeedCheck = array_values(array_filter($feePlans, function ($a) use ($fee) {
                        // return ($a['brand_code'] == $fee->brand_code);
                        return ($a['curriculum_id'] == $fee->curriculum_id);
                    }));
                        
                    $totalFee = [
                        'hoc_ky' => 0,
                        'tien_sach' => 0,
                        'tieng_anh' => null
                    ];
        
                    $totalFeeCheck = [
                        'hoc_ky' => 1,
                        'tien_sach' => 1,
                        'tieng_anh' => 0
                    ];
        
                    // dd($fdetail);
                    foreach ($fdetail as $key => $value) {
                        if ($value->type_fee == 1) {
                            $totalFee['hoc_ky'] = $value->amount;
                            if ($value->status == 1) {
                                $totalFeeCheck['hoc_ky'] = 1;
                            } else {
                                $totalFeeCheck['hoc_ky'] = 0;
                            }
                        } elseif ($value->type_fee == 2) {
                            $totalFee['tien_sach'] = $value->amount;
                            if ($value->status == 1) {
                                $totalFeeCheck['tien_sach'] = 1;
                            } else {
                                $totalFeeCheck['tien_sach'] = 0;
                                if ($value->amount == 0) {
                                    $totalFeeCheck['tien_sach'] = 1;
                                }
                            }
                        }
                    }
                    
                    // $checkPricedEng = null;
                    $checkPricedEng = Transaction::select('amount')
                    ->where('type', 'HP')
                    ->where('user_code', $fee->user_code)
                    ->where(function ($query) {
                        $query->where('note', 'like','%Tiếng anh học đi%');
                        $query->orWhere('note', 'like','%Trượt điểm danh Học lại%');
                    })->where('term_name', 'Like', $termCheck->term_name)->first();
                    
                    // "Tiếng anh học đi"
                    if (isset($checkPricedEng)) {
                        $totalFee['tieng_anh'] = $checkPricedEng->amount;
                        $totalFeeCheck['tieng_anh'] = 1;
                    }
        
                    if (!isset($feeNeedCheck[0])) {
                        continue;
                    }

                    $feeNeedCheck = $feeNeedCheck[0];
                    if ($fee->details->count()) {
                        $listFeeDetail = $feeNeedCheck['details'];
                        $listFeeDetail = array_values(array_filter($listFeeDetail, function ($a) use ($user) {
                            return ($a['ki_thu'] == ($user->kithu + 1));
                        }));
                
                        $hoc_ky = 0;
                        $tien_sach = 0;
                        // $version = $version + $fee->details->sum('version');
                        // phí sách ngành hẹp
                        foreach ($listFeeDetail as $item) {
                            if ($item['type_fee'] == 1) {
                                if (in_array($user->user_code, ['PF15011', 'PF15013', 'PF15005', 'PF15019', 'PF15035', 'PF15032', 'PF15050', 'PF15028', 'PF15030', 'PF15051', 'PF15001', 'PF15029', 'PF15098', 'PF15086', 'PF15094', 'PF15115', 'PF15102', 'PF15055', 'PF15149', 'PF15142', 'PF15166', 'PF15056'])) {
                                    $hoc_ky += $user->brand_code == 'MA' ? 3400000 : 3600000;
                                } else {
                                    $hoc_ky += $item['amount'];
                                }
                            }
        
                            if ($item['type_fee'] == 2) {
                                $tien_sach += $item['amount'];
                            }
                        }
        
                        // $so_tien = $hoc_ky + $tien_sach + $tieng_anh;
                        $totalFee = [
                            'hoc_ky' => $hoc_ky,
                            'tien_sach' => $tien_sach,
                            'tieng_anh' => $tieng_anh
                        ];
                    }
        
                    // phí tiếng anh
                    $fee_end = 0;
                    $fee_end_title = null;
                    if ($english_level < 4) {
                        $fee_end = 2600000;
                        if ($last_english == null || $englishStatus == '-1') {
                            $fee_end = 500000;
                        }
                        
                        if ($fee->brand_code == 'HDDL' || $fee->brand_code == 'QTKS') {
                            if ($english_level < 2) {
                                $fee_end = 0;
                                if ($englishStatus == '-1' || $englishStatus == '-3' || $englishStatus == '-2' || $englishStatus == '-4') {
                                    $fee_end = 500000;
                                }
        
                                if ($last_english == null) { 
                                    $fee_end = 500000;
                                }
                            } else {
                                $fee_end = 0;
                            }
                        }
        
                        if ($englishStatus == '2' || $englishStatus == 2) {
                            if ($english_level < 4) {
                                $fee_end = 2600000;
                            } 
                            
                            if ($english_level == 2 && ($fee->brand_code == 'HDDL' || $fee->brand_code == 'QTKS')) {
                                $fee_end = 0;
                            } 
                        }
        
                        if (in_array($user->user_code, ['PF15011', 'PF15013', 'PF15005', 'PF15019', 'PF15035', 'PF15032', 'PF15050', 'PF15028', 'PF15030', 'PF15051', 'PF15001', 'PF15029', 'PF15098', 'PF15086', 'PF15094', 'PF15115', 'PF15102', 'PF15055', 'PF15149', 'PF15142', 'PF15166', 'PF15056'])) {
                            // $hoc_ky += $user->user_code == 'MA' ? 3400000 : 3600000; 
                            if ($englishStatus != '1') {
                                $fee_end = 500000;
                            } else {
                                $fee_end = 0;
                            }
                        }
        
                    } else {
                        $fee_end = 0;
                        $totalFeeCheck['tieng_anh'] = 1;
                    }
        
                    if (!$checkPricedEng) {
                        $totalFee['tieng_anh'] = $fee_end;
                        // $totalFeeCheck['tieng_anh'] = ($fee->study_wallet >= 2600000 || $fee_end == 0) ? 1 : 0;
                    }
        
                    fputcsv($f, [
                        ($fee->user_code ?? ""),
                        ($user->study_status_code ?? ""),
                        // ($user->study_status ?? ""),
                        ($fee->brand_code ?? ""),
                        ($user->kithu ?? ""),
                        ($totalFee['hoc_ky'] ?? 0),
                        ($totalFee['tien_sach'] ?? 0),
                        ($totalFee['tieng_anh'] ?? 0),
                        ($english_level),
                        ($listStatusEnglish[$englishStatus] ?? ""),
                        ($totalFeeCheck['hoc_ky'] == 1 ? 'DHT' : 'CHT'),
                        ($totalFeeCheck['tien_sach'] == 1 ? 'DHT' : 'CHT'),
                        ($totalFeeCheck['tieng_anh'] == 1 ? 'DHT' : (in_array($englishStatus, [-4, -3, -2]) ? "KXD" : "CHT")),
                        $fee->study_wallet,
                        $fee->relearn_wallet,
                        $fee->promotion_wallet
                    ]);
                }

                flush();
            }
        }
    }

    public function exportDateFeeCount(Request $request) {
        ini_set('max_execution_time', '-1');
        if (count($listStatus) == 0){
            $listStatus = [1, 3, 6, 10, 11, 15, 16, 17, 18, 20];
        }
        
        $feeCount = Fee::select('id');
        if ($request->get('brand_code', -1) != -1) {
            $feeCount = $feeCount->where('brand_code', $request->brand_code);
        }

        if ($request->get('status', -1) != -1) {
            $feeCount = $feeCount->where('study_status', $request->status);
        } else {
            if (trim($request->get('list_user_code', '')) == '') {
                $feeCount = $feeCount->whereIn('study_status', $listStatus);
            }
        }

        $listUser = [];
        if (trim($request->get('list_user_code', '')) != '') {
            $listUser = explode(',', $request->list_user_code);
            foreach ($listUser as $key => $value) {
                $listUser[$key] = trim($value);
            }
        }
            
        if (count($listUser) > 0) {
            $feeCount = $feeCount->whereIn('user_code', $listUser);
        }
        
        $feeCount = $feeCount->count();
        
        return ResponseBuilder::Success($feeCount, 'Thành công');
    } 

    public function exportDateFeeAjax(Request $request)
    {
        ini_set('max_execution_time', '-1');
        $res = [];
        $limit = $request->get('limit', 10);
        $page = $request->get('page', 1);
        $listMajorTravel = [
            'HDDL', 'HDDL-T', 'HDDL01', 'QTNH-T', 'QTNH01', 'QTNH', 'QTKS', 'QTKS01', 'QTKS-T',
        ];
        if (!$termCheck) {
            $termCheck = Term::orderBy('id', 'DESC')->first();
        }

        $feePlans = Plan::with('details:fee_plan_id,ki_thu,type_fee,amount')
            ->select('id', 'brand_code', 'curriculum_id')
            ->get()
            ->toArray();
        $fees = Fee::select([
                'user_code', 
                'brand_code', 
                'curriculum_id',
                'study_wallet', 
                'relearn_wallet', 
                'promotion_wallet', 
                'ki_thu', 
                'id'
            ]);
        $listStatus = $request->get('status', []);
        if (count($listStatus) == 0){
            $listStatus = [1, 3, 6, 10, 11, 15, 16, 17, 18, 20];
        }

        if ($request->get('brand_code', -1) != -1) {
            $fees = $fees->where('brand_code', $request->brand_code);
        }

        if ($request->get('status', -1) != -1) {
            $fees = $fees->where('study_status', $request->status);
        } else {
            if (trim($request->get('list_user_code', '')) == '') {
                $fees = $fees->whereIn('study_status', $listStatus);
            }
        }

        $listUser = [];
        if (trim($request->get('list_user_code', '')) != '') {
            $listUser = explode(',', $request->list_user_code);
            foreach ($listUser as $key => $value) {
                $listUser[$key] = trim($value);
            }
        }

        if (count($listUser) > 0) {
            $fees = $fees->whereIn('user_code', $listUser);
        }
        
        $fees = $fees
            ->offset($limit * ($page - 1))
            ->limit($limit)
            ->get();

        foreach ($fees as $fee) {
            $tieng_anh = 0;
            $englishStatus = null;
            $type_fee = [1,2,5];
            $ki_thu = $fee->ki_thu + 1;
            $version = 0;
            $english_level = 0;
            $note = null;

            $user = User::where('user_code', $fee->user_code)->first();
            if (!$user) {
                Log::channel('fee')->error("dev check: $fee->user_code");
                continue;
            }

            $ki_thu = $user->kithu + 1;
            $lastEngCheck = CourseResult::select([
                't7_course_result.id', 
                't7_course_result.pterm_name', 
                't7_course_result.psubject_code', 
                't7_course_result.skill_code', 
                't7_course_result.term_id'
            ])->join('list_group', 'list_group.id', '=', 't7_course_result.groupid')
            ->where('student_login', $user->user_login)
            ->where('list_group.is_virtual', 0)
            ->whereIn('t7_course_result.skill_code', ['ENT111', 'ENT121', 'ENT211', 'ENT221'])
            ->orderBy('t7_course_result.term_id', 'DESC')
            ->orderBy('t7_course_result.id', 'DESC')
            ->first();

            if (!$lastEngCheck) {
                $lastEngCheck = TranferT7Course::select([
                    'tranfer_t7_course.id', 
                    'tranfer_t7_course.pterm_name', 
                    'tranfer_t7_course.psubject_code', 
                    'tranfer_t7_course.skill_code', 
                    DB::raw('term.id as term_id')
                ])->join('term', 'term.term_name', '=', 'tranfer_t7_course.pterm_name')
                ->where('student_login', $user->user_login)
                ->whereIn('tranfer_t7_course.skill_code', ['ENT111', 'ENT121', 'ENT211', 'ENT221'])
                ->orderBy('term.id', 'DESC')
                ->orderBy('tranfer_t7_course.id', 'DESC')
                ->first();
            }
            $english = EnglishDetail::select('id')->where('user_code', $fee->user_code)->first();
            $last_english = null;
            if ($english) {
                $last_english = EnglishDetailLevel::select('level', 'status', 'version', 'note', 'skill_code')
                ->where('english_id', $english->id);
                // if (isset($lastEngCheck->skill_code)) {
                //     $last_english = $last_english->where('skill_code', $lastEngCheck->skill_code);
                // }
                
                $last_english = $last_english->orderBy('level', 'DESC')
                ->orderBy('id', 'DESC')
                ->first();
                
                if ($last_english) {
                    $tieng_anh = ($last_english->level != 4 ? ($last_english->status == 1 ? 2600000 : 500000) : 0);
                    $englishStatus = $last_english->status;
                    $version = $last_english->version + $version;
                    $note = $note . $last_english->note;
                    $english_level = $last_english->level;
                }
            }

            $fdetail = FeeDetail::select([
                    'type_fee',
                    'amount',
                    'status'
                ])
                ->where('ki_thu', $ki_thu)
                ->whereIn('type_fee', $type_fee)
                ->where('fee_id', $fee->id)
                ->get();
            $feeNeedCheck = array_values(array_filter($feePlans, function ($a) use ($fee) {
                // return ($a['brand_code'] == $fee->brand_code);
                return ($a['curriculum_id'] == $fee->curriculum_id);
            }));
                
            $totalFee = [
                'hoc_ky' => 0,
                'tien_sach' => 0,
                'tieng_anh' => null
            ];

            $totalFeeCheck = [
                'hoc_ky' => 1,
                'tien_sach' => 1,
                'tieng_anh' => 0
            ];

            if ($ki_thu > 1) {
                foreach ($fdetail as $key => $value) {
                    if ($value->type_fee == 1) {
                        $totalFee['hoc_ky'] = $value->amount;
                        if ($value->status == 1) {
                            $totalFeeCheck['hoc_ky'] = 1;
                        } else {
                            $totalFeeCheck['hoc_ky'] = 0;
                        }
                    } elseif ($value->type_fee == 2) {
                        $totalFee['tien_sach'] = $value->amount;
                        if ($value->status == 1) {
                            $totalFeeCheck['tien_sach'] = 1;
                        } else {
                            $totalFeeCheck['tien_sach'] = 0;
                            if ($value->amount == 0) {
                                $totalFeeCheck['tien_sach'] = 1;
                            }
                        }
                    }
                }
                
                $checkPricedEng = Transaction::select('amount')
                ->where('type', 'HP')
                ->where('user_code', $fee->user_code)
                // ->where(function ($query) {
                //     $query->where('note', 'like','%Tiếng anh học đi%');
                //     $query->orWhere('note', 'like','%Trượt điểm danh Học lại%');
                // })
                ->where('type_extension', 'Like', 'Tiếng anh|%')
                ->where('term_name', $termCheck->term_name)->first();
                
                // "Tiếng anh học đi"
                if (isset($checkPricedEng)) {
                    $totalFee['tieng_anh'] = $checkPricedEng->amount;
                    $totalFeeCheck['tieng_anh'] = 1;
                }
    
                if (!isset($feeNeedCheck[0])) {
                    Log::channel('fee')->error("dev Cần check:  $fee->user_code");
                    continue;
                }

                $feeNeedCheck = $feeNeedCheck[0];
                if ($fee->details->count()) {
                    $listFeeDetail = $feeNeedCheck['details'];
                    $listFeeDetail = array_values(array_filter($listFeeDetail, function ($a) use ($user) {
                        return ($a['ki_thu'] == ($user->kithu + 1));
                    }));
            
                    $hoc_ky = 0;
                    $tien_sach = 0;
                    // $version = $version + $fee->details->sum('version');
                    // phí sách ngành hẹp
                    foreach ($listFeeDetail as $item) {
                        if ($item['type_fee'] == 1) {
                            if (in_array($user->user_code, ['PF15011', 'PF15013', 'PF15005', 'PF15019', 'PF15035', 'PF15032', 'PF15050', 'PF15028', 'PF15030', 'PF15051', 'PF15001', 'PF15029', 'PF15098', 'PF15086', 'PF15094', 'PF15115', 'PF15102', 'PF15055', 'PF15149', 'PF15142', 'PF15166', 'PF15056'])) {
                                $hoc_ky += $user->brand_code == 'MA' ? 3400000 : 3600000;
                            } else {
                                $hoc_ky += $item['amount'];
                            }
                        }
    
                        if ($item['type_fee'] == 2) {
                            $tien_sach += $item['amount'];
                        }
                    }
    
                    // $so_tien = $hoc_ky + $tien_sach + $tieng_anh;
                    $totalFee = [
                        'hoc_ky' => $hoc_ky,
                        'tien_sach' => $tien_sach,
                        'tieng_anh' => $tieng_anh
                    ];
                }
    
                // phí tiếng anh
                $checkEng = false;
                $fee_end = 0;
                $fee_end_title = null;
                if ($english_level < 4) {
                    $fee_end = 2600000;
                    if ($last_english == null || $englishStatus == '-1') {
                        $fee_end = 2600000;
                        $checkEng = true;
                    }
                    
                    if (in_array($fee->brand_code, $listMajorTravel)) {
                        if ($english_level < 2) {
                            $fee_end = 0;
                            if (in_array($englishStatus, [-1, -3, -2, -4])) {
                                $fee_end = 2600000;
                                $checkEng = true;
                            }
                            // } elseif ($englishStatus == -4) {
                            //     $fee_end = 500000;
                            // }
    
                            if ($last_english == null) { 
                                $fee_end = 2600000;
                                $checkEng = true;
                            }
                        } else {
                            $fee_end = 0;
                        }
                    } else {
                        if ($englishStatus == 2) {
                            if ($english_level < 4) {
                                $fee_end = 2600000;
                                if ($english_level == 2 && in_array($fee->brand_code, $listMajorTravel)) {
                                    $fee_end = 0;
                                } 
                            }
                        }
                        
                        // } elseif ($englishStatus == -4) {
                        //     $fee_end = 500000;
                        // }
                    }
    
                    if (in_array($user->user_code, ['PF15011', 'PF15013', 'PF15005', 'PF15019', 'PF15035', 'PF15032', 'PF15050', 'PF15028', 'PF15030', 'PF15051', 'PF15001', 'PF15029', 'PF15098', 'PF15086', 'PF15094', 'PF15115', 'PF15102', 'PF15055', 'PF15149', 'PF15142', 'PF15166', 'PF15056'])) {
                        // $hoc_ky += $user->user_code == 'MA' ? 3400000 : 3600000; 
                        if ($englishStatus != '1') {
                            $fee_end = 2600000;
                            $checkEng = true;
                            // if ($englishStatus == -4) {
                            //     $fee_end = 500000;
                            //     $checkEng = false;
                            // }
                        } else {
                            $fee_end = 0;
                        }
                    }
    
                } else {
                    $fee_end = 0;
                    $totalFeeCheck['tieng_anh'] = 1;
                }
    
                if (!$checkPricedEng) {
                    $totalFee['tieng_anh'] = $fee_end;
                }

                // if ($checkEng == true) {
                //     $countCheckX = 0;
                //     if (in_array($englishStatus, [-1]) && $lastEngCheck != null && ($listTermCheck[$lastEngCheck->term_id] + 1) >= $listTermCheck[$termCheck->id]) {
                //         $countCheckX = 1;
                //     }

                //     if ($countCheckX > 0 && in_array($fee->brand_code, $listMajorTravel)    ) {
                //         $totalFee['tieng_anh'] = $totalFee['tieng_anh'] * 0.5;
                //     }
                // }
                
                // Miễn giảm 2 lv đầu
                if (!$lastEngCheck) {
                    $totalFee['tieng_anh'] = 0;
                }

                if ($englishStatus == 2 && !in_array($fee->brand_code, $listMajorTravel)) {
                    $totalFee['tieng_anh'] = 2600000;
                }
            } else {
                $lastEngCheck = null;
                $totalFee = [
                    'hoc_ky' => 0,
                    'tien_sach' => 0,
                    'tieng_anh' => null
                ];
    
                $totalFeeCheck = [
                    'hoc_ky' => 1,
                    'tien_sach' => 1,
                    'tieng_anh' => 0
                ];
            }

            $res[] = [
                ($fee->user_code ?? ""),
                ($dataProcess[$fee->user_code]['status'] ?? ""),
                ($user->study_status_code ?? ""),
                // ($user->study_status ?? ""),
                ($fee->brand_code ?? ""),
                ($user->kithu ?? ""),
                ($totalFee['hoc_ky'] ?? 0),
                // ($totalFee['tien_sach'] ?? 0),
                ($totalFee['tieng_anh'] ?? 0),
                ($english_level),
                ($listStatusEnglish[$englishStatus] ?? ""),
                ($totalFeeCheck['hoc_ky'] == 1 ? 'DHT' : 'CHT'),
                ($totalFeeCheck['tien_sach'] == 1 ? 'DHT' : 'CHT'),
                ($totalFeeCheck['tieng_anh'] == 1 ? 'DHT' : (in_array($englishStatus, [-4, -3, -2]) ? "KXD" : "CHT")),
                $fee->study_wallet,
                $fee->relearn_wallet,
                $fee->promotion_wallet,
                $lastEngCheck->psubject_code ?? "Không tìm được",
                $lastEngCheck->pterm_name ?? "KHông tìm được"
            ];
            
        }
        
        return ResponseBuilder::Success($res, 'Thành công');
    }
}
