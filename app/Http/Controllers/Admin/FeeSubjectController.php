<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Imports\BaseModelImport;
use App\Imports\FeeSubjectImport;
use App\Models\Dra\CurriCulum;
use App\Models\Fee\FeeSubject;
use App\Models\Fee\FeeSubjectLog;
use App\Models\Fu\User;
use App\Utils\ResponseBuilder;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class FeeSubjectController extends Controller
{
    /**
     * Display a listing of subject fees
     *
     * @return \Illuminate\Http\Response
     */
    public function getFeeSubjects()
    {
        $subject_code = request('subject_code', null);
        $curriculum_id = request('curriculum_id', null);
        $per_page = request('per_page', 10);
        try {
            $feeSubjects = FeeSubject::select([
                'fee_subject.*',
                'curriculum.name as curriculum_name',
                'term.term_name'
            ])->leftJoin('curriculum', 'fee_subject.curriculum_id', '=', 'curriculum.id')
                ->leftJoin('term', 'fee_subject.term_id', '=', 'term.id')
                ->where('fee_subject.status', 1)
                ->when($subject_code, function ($query) use ($subject_code) {
                    return $query->where('fee_subject.subject_code', 'like', "%{$subject_code}%");
                })
                ->when($curriculum_id, function ($query) use ($curriculum_id) {
                    return $query->where('fee_subject.curriculum_id', $curriculum_id);
                })
                ->orderBy('fee_subject.id', 'desc')
                ->paginate($per_page);
            return ResponseBuilder::Success($feeSubjects);
        } catch (\Exception $e) {
            Log::error($e);
            return ResponseBuilder::Fail("Lỗi khi lấy danh sách phí môn học.", []);
        }
    }

    public function getCurriculums()
    {
        try {
            $curriculums = CurriCulum::select(['curriculum.*'])->orderBy('curriculum.id', 'desc')->get();
            return ResponseBuilder::Success($curriculums);
        } catch (\Exception $e) {
            Log::error($e);
            return ResponseBuilder::Fail("Lỗi khi lấy danh sách chương trình đào tạo.", []);
        }
    }

    /**
     * Store a newly created subject fee
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function importFeeSubject(Request $request)
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', -1);

        $file = $request->file;
        try {
            $import = new FeeSubjectImport;
            Excel::import($import, $file);

            $logFail = $import->getErrors();
            $success = $import->getSuccess();
            $fail = $import->getFail();

            $message = "Thành công: {$success}.\n";
            if ($fail > 0) {
                $message .= "Thất bại: {$fail}.\n";
                $message .= "Chi tiết lỗi:\n" . $logFail;
            }

            if ($success == 0) {
                return ResponseBuilder::Fail("Thất bại!", [
                    'message' => $message,
                    'success' => $success,
                    'fail' => $fail,
                ]);
            }
            return ResponseBuilder::Success([
                'message' => $message,
                'success' => $success,
                'fail' => $fail,
            ]);
        } catch (\Exception $e) {
            Log::error($e);
            return ResponseBuilder::Fail("Lỗi khi import phí môn học.", [
                'message' => "Lỗi khi import phí môn học.",
                'success' => 0,
                'fail' => 0,
            ]);
        }
    }

    /**
     * Update a subject fee
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'subject_code' => 'required|string|max:255',
            'subject_name' => 'required|string|max:255',
            'credits' => 'required|integer|min:0',
            'amount' => 'required|integer|min:0',
            'brand_code' => 'nullable|string|max:255',
            'curriculum_id' => 'nullable|string|max:255',
            'status' => 'required|boolean',
            'note' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            $feeSubject = FeeSubject::findOrFail($id);
            $oldAmount = $feeSubject->amount;
            $oldVersion = $feeSubject->version;

            // Increment version if amount changed
            $newVersion = $oldAmount != $request->amount ?
                $this->incrementVersion($oldVersion) : $oldVersion;

            // Update fee subject
            $feeSubject->subject_code = $request->subject_code;
            $feeSubject->subject_name = $request->subject_name;
            $feeSubject->credits = $request->credits;
            $feeSubject->amount = $request->amount;
            $feeSubject->brand_code = $request->brand_code;
            $feeSubject->curriculum_id = $request->curriculum_id;
            $feeSubject->version = $newVersion;
            $feeSubject->status = $request->status;
            $feeSubject->save();

            // Create log if amount changed
            if ($oldAmount != $request->amount) {
                $user = Auth::user();

                FeeSubjectLog::create([
                    'fee_subject_id' => $feeSubject->id,
                    'subject_code' => $feeSubject->subject_code,
                    'subject_name' => $feeSubject->subject_name,
                    'credits' => $feeSubject->credits,
                    'old_amount' => $oldAmount,
                    'new_amount' => $request->amount,
                    'brand_code' => $feeSubject->brand_code,
                    'curriculum_id' => $feeSubject->curriculum_id,
                    'old_version' => $oldVersion,
                    'new_version' => $newVersion,
                    'user_id' => $user ? $user->id : null,
                    'user_name' => $user ? $user->name : null,
                    'note' => $request->note,
                ]);
            }

            DB::commit();
            return response()->json(['success' => true, 'message' => 'Cập nhật biểu phí môn học thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => 'Lỗi: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get logs for a subject fee
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLogs(Request $request)
    {
        try {
            $subject_code = $request->subject_code;
            $curriculum_id = $request->curriculum_id;
            $feeSubjects = FeeSubject::select([
                'fee_subject.*',
                'curriculum.name as curriculum_name',
                'term.term_name'
            ])->leftJoin('curriculum', 'fee_subject.curriculum_id', '=', 'curriculum.id')
                ->leftJoin('term', 'fee_subject.term_id', '=', 'term.id')
                ->when($subject_code, function ($query) use ($subject_code) {
                    return $query->where('fee_subject.subject_code', $subject_code);
                })
                ->when($curriculum_id, function ($query) use ($curriculum_id) {
                    return $query->where('fee_subject.curriculum_id', $curriculum_id);
                })
                ->get();
            return ResponseBuilder::Success($feeSubjects);
        } catch (\Exception $e) {
            Log::error($e);
            return ResponseBuilder::Fail("Lỗi khi lấy danh sách phí môn học.", []);
        }
    }

    /**
     * Increment version number (e.g., 1.0 -> 1.1)
     *
     * @param string $version
     * @return string
     */
    private function incrementVersion($version)
    {
        $parts = explode('.', $version);
        if (count($parts) === 2) {
            $parts[1] = (int)$parts[1] + 1;
            return implode('.', $parts);
        }
        return $version . '.1';
    }

    public function downloadFeeSubject()
    {
        try {
            $feeSubjects = FeeSubject::select([
                'fee_subject.*',
                'curriculum.name as curriculum_name',
                'term.term_name'
            ])->leftJoin('curriculum', 'fee_subject.curriculum_id', '=', 'curriculum.id')
                ->leftJoin('term', 'fee_subject.term_id', '=', 'term.id')
                ->where('fee_subject.status', 1)
                ->get();

            $file_name = 'bieu-phi-mon-hoc.csv';
            $filePath = storage_path('app/' . $file_name);

            // Ghi dữ liệu vào file CSV
            $csvFile = fopen($filePath, 'w');

            // Thêm BOM UTF-8 để tránh lỗi font khi mở bằng Excel
            fprintf($csvFile, chr(0xEF) . chr(0xBB) . chr(0xBF));
            $header = [
                'ID',
                'Mã môn',
                'Tên môn',
                'Mã chuyển đổi',
                'ID khung đào tạo',
                'Khung đào tạo',
                'Học phí',
                'Học phí học lại',
                'Phí thi lại',
                'Học kỳ',
                'Người tạo',
                'Ngày tạo',
                'Diễn giải'
            ];

            fputcsv($csvFile, $header);
            foreach ($feeSubjects as $key => $line) {
                fputcsv($csvFile, [
                    $line->id,
                    $line->subject_code,
                    $line->subject_name,
                    $line->skill_code,
                    $line->curriculum_id,
                    $line->curriculum_name,
                    $line->tuition_fee,
                    $line->relearn_fee,
                    $line->retest_fee,
                    $line->term_name,
                    $line->created_by,
                    $line->created_at,
                    $line->detail
                ]);
            }
            fclose($csvFile);

            return response()->download($filePath)->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            Log::error($e);
        }
    }

    public function getFeeSubjectByStudent(Request $request)
    {
        $feeResult = [];
        $feeType = $request->input('fee_type', null);
        $studentCode = $request->input('student_code', null);
        if (!$studentCode) {
            return ResponseBuilder::Fail("Mã sinh viên hoặc ID sinh viên không được để trống.", []);
        }

        $student = User::where('user_code', $studentCode)->first();
        $curriculumId = $student ? $student->curriculum_id : null;

        if (!$curriculumId) {
            return ResponseBuilder::Fail("Không tìm thấy chương trình đào tạo cho sinh viên này.", []);
        }
        try {
            $feeSubjects = FeeSubject::where('fee_subject.status', 1)
                ->where('fee_subject.curriculum_id', $curriculumId)
                ->orderBy('fee_subject.id', 'desc')
                ->groupBy('fee_subject.subject_code')
                ->get()->toArray();
            if ($feeSubjects) {
                $columnTypeName = "";
                switch ($feeType) {
                    case 'HP':
                        $columnTypeName = 'tuition_fee';
                        break;
                    case 'HL':
                        $columnTypeName = 'relearn_fee';
                        break;
                    case 'TL':
                        $columnTypeName = 'retest_fee';
                        break;
                    default:
                        return ResponseBuilder::Fail("Loại phí không hợp lệ.", []);
                }

                $feeResult = array_map(function ($feeSubject) use ($columnTypeName) {
                    return [
                        'id' => $feeSubject['id'],
                        'subject_id' => $feeSubject['subject_id'],
                        'subject_code' => $feeSubject['subject_code'],
                        'subject_name' => $feeSubject['subject_name'],
                        'amount' => $feeSubject[$columnTypeName]
                    ];
                }, $feeSubjects);
            }
            return ResponseBuilder::Success($feeResult);
        } catch (\Exception $e) {
            Log::error($e);
            return ResponseBuilder::Fail("Lỗi khi lấy danh sách phí môn học cho sinh viên.", []);
        }
    }
}
