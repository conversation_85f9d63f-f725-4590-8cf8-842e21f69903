<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\DataTables\CourseDataTable;
use App\Repositories\Admin\CourseRepository;
use Illuminate\Http\Request;

class CourseController extends Controller
{
    protected $CourseRepository;
    public function __construct()
    {
        $this->CourseRepository = new CourseRepository();
    }

    public function index(Request $request) {
        return $this->CourseRepository->index($request);
    }

    /**
     * Datatable ajax request
     *
     * @return \Illuminate\Http\FeedbackdataTable
     */
    public function datatable(CourseDataTable $datatable)
    {
        return $datatable->build()->toJson();
    }

    public function store(Request $request) {
        return $this->CourseRepository->store($request);
    }

    public function import(Request $request) {
        return $this->CourseRepository->import($request);
    }

    public function edit(Request $request, $id) {
        return $this->CourseRepository->edit($request, $id);
    }

    public function update(Request $request, $id) {
        return $this->CourseRepository->updateCourse($id,$request);
    }
}