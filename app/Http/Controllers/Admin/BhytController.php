<?php


namespace App\Http\Controllers\Admin;

use App\Exports\MultipleExportBHYTService;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\BHYT\BhytAvailableRegistrationPeriod;
use Carbon\Carbon;
use App\Models\Fu\User;
use App\Models\BHYT\BhytDvKcb;
use App\Models\BHYT\InfoCountry;
use App\Models\BHYT\InfoDistrict;
use App\Models\BHYT\InfoEthnic;
use App\Models\BHYT\InfoProvince;
use App\Models\BHYT\InfoSubDistrict;
use App\Models\BHYT\BhytStudentRegister;
use App\Models\BHYT\BhytStudentFamilyMember;
use App\Models\Fee\DngRecord;
use App\Models\Fee\Transaction;
use App\Models\Fu\Term;
use App\Repositories\Admin\DngConnectRepository;
use App\Utils\ResponseBuilder;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class BhytController extends Controller
{

    /**
     * Trạng thái đợt đăng ký - Đang hoạt động
     */
    const FILTER_STATUS_AVAILABLE = 1;
    /**
     * Trạng thái đợt đăng ký - Không hoạt động
     */
    const FILTER_STATUS_UNAVAILABLE = 0;

    public function getDvKcb(Request $request)
    {
        $dv_kcb = BhytDvKcb::get();
        return response(['DvKcb' => $dv_kcb], 200);
    }

    public function getCountries()
    {
        try {
            $countries = InfoCountry::get()->groupBy('country_code')->toArray();
            $countries = array_map(function ($country) {
                return $country[0];
            }, $countries);
            return $countries;
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
        }
    }


    public function getListEthnic()
    {
        try {
            $list_ethnic = InfoEthnic::get()->groupBy('ethnic_code')->toArray();
            $list_ethnic = array_map(function ($ethnic) {
                return $ethnic[0];
            }, $list_ethnic);
            return $list_ethnic;
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
        }
    }

    public function getProvinces()
    {
        try {
            $provinces = InfoProvince::get()->groupBy('province_code')->toArray();
            $provinces = array_map(function ($province) {
                return $province[0];
            }, $provinces);
            return $provinces;
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
        }
    }

    public function getDistricts()
    {
        try {
            $districts = InfoDistrict::get()->groupBy('district_code')->toArray();
            $districts = array_map(function ($district) {
                return $district[0];
            }, $districts);
            return $districts;
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
        }
    }

    public function getSubDistricts()
    {
        try {
            $subdistricts = InfoSubDistrict::get()->groupBy('subdistrict_code')->toArray();
            $subdistricts = array_map(function ($subdistrict) {
                return $subdistrict[0];
            }, $subdistricts);
            return $subdistricts;
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
        }
    }

    public function getRegisterByFilter($filters)
    {
        try {
            $query = BhytStudentRegister::query();

            $query->join(
                'bhyt_available_registration_periods',
                'bhyt_available_registration_periods.id',
                '=',
                'bhyt_student_register.available_registration_period_id'
            );
            if (isset($filters->student_code) && $filters->student_code) {
                $query->where('bhyt_student_register.student_code', '=', $filters->student_code);
            }

            if (isset($filters->status) && $filters->status != null && $filters->status != 'null') {
                if ($filters->status == -100) {
                    $query->whereIn('bhyt_student_register.status', [BhytStudentRegister::STATUS_CANCELLED_PAID_ORDER, BhytStudentRegister::STATUS_CANCELLED_UNPAID_ORDER]);
                } else {
                    $query->where('bhyt_student_register.status', '=', $filters->status);
                }
            }

            if (isset($filters->available_period_id) && $filters->available_period_id > 0 && $filters->available_period_id != null && $filters->available_period_id != 'null') {
                $query->where(
                    'bhyt_student_register.available_registration_period_id',
                    '=',
                    $filters->available_period_id
                );
            }

            $query->select([
                'bhyt_student_register.id',
                'bhyt_student_register.fullname',
                'bhyt_student_register.student_code',
                'bhyt_student_register.available_registration_period_id',
                'bhyt_available_registration_periods.name as available_registration_period_name',
                'bhyt_student_register.fee',
                'bhyt_student_register.status',
                'bhyt_student_register.created_at'
            ]);

            $query->orderBy('id', 'DESC');

            $rowsPerPage = 10;

            if (isset($filters->rows_per_page)) {
                $rowsPerPage = $filters->rows_per_page;
            }
            if ($rowsPerPage > 0) {
                $data = $query->paginate($rowsPerPage);
            } else {
                $data = $query->get();
            }
            return $data;
        } catch (\Throwable $th) {
            Log::error("BhytController - getRegisterByFilter: " . $th->getLine() . " " . $th->getMessage());
            return [];
        }
    }

    /**
     * Lấy danh sách đơn đăng ký BHYT
     *
     * @param  mixed $request
     * @return mixed
     */
    public function getListStudentRegister(Request $request)
    {
        try {
            $filters = (object) [
                'student_code' => $request->student_code ?? "",
                'status' => $request->status ?? null,
                'available_period_id' => $request->available_period_id ?? null,
                'rows_per_page' => $request->rows_per_page ?? 0
            ];
            $data = $this->getRegisterByFilter($filters);

            return ResponseBuilder::Success($data);
        } catch (\Throwable $th) {
            return ResponseBuilder::Fail($th->getMessage());
        }
    }

    /**
     * thông tin chi tiết của 1 đơn đăng ký
     *
     * @param  mixed $request
     * @return mixed
     */
    public function getDetailStudentRegister(Request $request)
    {
        try {
            if (!$request->has('id') || $request->id <= 0) {
                return ResponseBuilder::Fail('Không xác định được đơn đăng ký');
            }

            $studentRegister = BhytStudentRegister::join(
                'bhyt_available_registration_periods',
                'bhyt_available_registration_periods.id',
                '=',
                'bhyt_student_register.available_registration_period_id'
            )
                ->leftJoin('info_ethnic', 'info_ethnic.ethnic_code', '=', 'bhyt_student_register.ethnic_code')
                ->leftJoin('info_country', 'info_country.country_code', '=', 'bhyt_student_register.country_code')
                ->leftJoin(
                    'info_province as bc_info_province',
                    'bc_info_province.province_code',
                    '=',
                    'bhyt_student_register.bc_province_id'
                )
                ->leftJoin(
                    'info_district as bc_info_district',
                    'bc_info_district.district_code',
                    '=',
                    'bhyt_student_register.bc_district_id'
                )
                ->leftJoin(
                    'info_subdistrict as bc_info_subdistrict',
                    'bc_info_subdistrict.subdistrict_code',
                    '=',
                    'bhyt_student_register.bc_subdistrict_id'
                )
                ->leftJoin(
                    'info_province as location_living_info_province',
                    'location_living_info_province.province_code',
                    '=',
                    'bhyt_student_register.location_living_province_id'
                )
                ->leftJoin(
                    'info_district as location_living_info_district',
                    'location_living_info_district.district_code',
                    '=',
                    'bhyt_student_register.location_living_district_id'
                )
                ->leftJoin(
                    'info_subdistrict as location_living_info_subdistrict',
                    'location_living_info_subdistrict.subdistrict_code',
                    '=',
                    'bhyt_student_register.location_living_subdistrict_id'
                )
                ->where('bhyt_student_register.id', '=', $request->id)

                ->select([
                    'bhyt_student_register.id',
                    'bhyt_student_register.fullname',
                    'bhyt_student_register.student_code',
                    'info_ethnic.ethnic_code',
                    'info_ethnic.ethnic_name',
                    'info_country.country_code',
                    'info_country.country_name',
                    'bc_info_province.province_code as bc_info_province_code',
                    'bc_info_province.province_name as bc_info_province_name',
                    'bc_info_district.district_code as bc_info_district_code',
                    'bc_info_district.district_name as bc_info_district_name',
                    'bc_info_subdistrict.subdistrict_code as bc_info_subdistrict_code',
                    'bc_info_subdistrict.subdistrict_name as bc_info_subdistrict_name',
                    'location_living_info_province.province_code as location_living_info_province_code',
                    'location_living_info_province.province_name as location_living_info_province_name',
                    'location_living_info_district.district_code as location_living_info_district_code',
                    'location_living_info_district.district_name as location_living_info_district_name',
                    'location_living_info_subdistrict.subdistrict_code as location_living_info_subdistrict_code',
                    'location_living_info_subdistrict.subdistrict_name as location_living_info_subdistrict_name',
                    'bhyt_student_register.attachment',
                    'bhyt_student_register.available_registration_period_id',
                    'bhyt_available_registration_periods.name as available_registration_period_name',
                    'bhyt_student_register.fee',
                    'bhyt_student_register.status',
                    'bhyt_student_register.created_at'
                ])
                ->first();
            return ResponseBuilder::Success($studentRegister);
        } catch (\Throwable $th) {
            Log::error("Controllers\Admin\BhytStudentRegisterController - detailStudentRegister: " . $th->getMessage());
            return ResponseBuilder::Fail($th->getMessage());
        }
    }

    public function exportExcel(Request $request)
    {
        try {
            $filters = (object) [
                'student_code' => $request->student_code ?? "",
                'status' => $request->status ?? null,
                'available_period_id' => $request->available_period_id ?? 0,
                'rows_per_page' => -1
            ];
            $data = $this->getRegisterByFilter($filters)->toArray();
            $ids = array_column($data, 'id');
            $dataExport = $this->exportBhytService($ids);
            $export = new MultipleExportBHYTService($dataExport);
            return Excel::download($export, 'Danh-sach-dang-ky-bhyt.xlsx');
        } catch (\Throwable $th) {
            return ResponseBuilder::Fail($th->getMessage());
        }
    }

    public function exportBhytService($list_id)
    {
        try {
            $countries = $this->getCountries();
            $list_ethnic = $this->getListEthnic();
            $provinces = $this->getProvinces();
            $districts = $this->getDistricts();
            $subDistricts = $this->getSubDistricts();
            $data = [];
            foreach (array_chunk($list_id, 1000) as $array) {
                $data_student_bhyt = BhytStudentRegister::leftJoin(
                    'user',
                    'bhyt_student_register.student_code',
                    '=',
                    'user.user_code'
                )
                    ->whereIn('bhyt_student_register.id', $array)
                    ->select([
                        'bhyt_student_register.id',
                        'bhyt_student_register.fullname',
                        'bhyt_student_register.student_code',
                        'user.user_email',
                        'bhyt_student_register.dob',
                        'bhyt_student_register.cccd',
                        DB::raw('CASE
                        WHEN bhyt_student_register.gender = 0 THEN "x"
                        ELSE ""
                    END as gender'),
                        // 'bhyt_student_register.gender',
                        'bhyt_student_register.telephone',
                        'bhyt_student_register.address',
                        'bhyt_student_register.bhxh_code',
                        'bhyt_student_register.nearest_time_bhyt',
                        'bhyt_student_register.ethnic_code',
                        'bhyt_student_register.country_code',
                        'bhyt_student_register.household_relationship',
                        'bhyt_student_register.bc_province_id',
                        'bhyt_student_register.bc_district_id',
                        'bhyt_student_register.bc_subdistrict_id',
                        'bhyt_student_register.location_living_province_id',
                        'bhyt_student_register.location_living_district_id',
                        'bhyt_student_register.location_living_subdistrict_id',
                    ])
                    ->with('members')
                    ->orderBy('bhyt_student_register.id', 'ASC')->get()->toArray();
                foreach ($data_student_bhyt as $key => $item) {
                    $data_student_bhyt[$key]['country_name'] = $countries[$item['country_code']]['country_name'] ?? null;
                    $data_student_bhyt[$key]['ethnic_name'] = $list_ethnic[$item['ethnic_code']]['ethnic_name'] ?? null;
                    $data_student_bhyt[$key]['bc_province_name'] = $provinces[$item['bc_province_id']]['province_name'] ?? null;
                    $data_student_bhyt[$key]['bc_district_name'] = $districts[$item['bc_district_id']]['district_name'] ?? null;
                    $data_student_bhyt[$key]['bc_subdistrict_name'] = $subDistricts[$item['bc_subdistrict_id']]['subdistrict_name'] ?? null;
                    $data_student_bhyt[$key]['location_living_province_name'] = $provinces[$item['location_living_province_id']]['province_name'] ?? null;
                    $data_student_bhyt[$key]['location_living_district_name'] = $districts[$item['location_living_district_id']]['district_name'] ?? null;
                    $data_student_bhyt[$key]['location_living_subdistrict_name'] = $subDistricts[$item['location_living_subdistrict_id']]['subdistrict_name'] ?? null;
                    foreach ($data_student_bhyt[$key]['members'] as $index => $member) {
                        $data_student_bhyt[$key]['members'][$index]['country_name'] = $countries[$member['country_code']]['country_name'] ?? null;
                        $data_student_bhyt[$key]['members'][$index]['ethnic_name'] = $list_ethnic[$member['ethnic_code']]['ethnic_name'] ?? null;
                        $data_student_bhyt[$key]['members'][$index]['bc_province_name'] = $provinces[$member['bc_province_id']]['province_name'] ?? null;
                        $data_student_bhyt[$key]['members'][$index]['bc_district_name'] = $districts[$member['bc_district_id']]['district_name'] ?? null;
                        $data_student_bhyt[$key]['members'][$index]['bc_subdistrict_name'] = $subDistricts[$member['bc_subdistrict_id']]['subdistrict_name'] ?? null;
                    }
                }
                $data = array_merge($data, $data_student_bhyt);
            }
            return $data;
        } catch (\Throwable $e) {
            return response(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * get danh sách đợt đăng ký bảo hiểm y tế
     *
     * @param  mixed $request
     * @return mixed
     */
    public function getListBhytAvailableRegistrationPeriods(Request $request)
    {
        try {
            $query = BhytAvailableRegistrationPeriod::query();

            if ($request->has('keyword') && $request->keyword != '') {
                $query->where('name', 'LIKE', '%' . $request->keyword . '%');
            }

            if (
                $request->has('status')
                && in_array($request->status, [self::FILTER_STATUS_AVAILABLE, self::FILTER_STATUS_UNAVAILABLE])
            ) {
                // start_date <= now <= end_date
                if ($request->status == self::FILTER_STATUS_AVAILABLE) {
                    $query->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
                        ->where('end_date', '>=', Carbon::now()->format('Y-m-d'));
                } else {
                    $query->where(function ($query) {
                        $query->where('start_date', '>', Carbon::now()->format('Y-m-d'))
                            ->orWhere('end_date', '<', Carbon::now()->format('Y-m-d'));
                    });
                }
            }

            if ($request->has('term_id') && $request->term_id > 0) {
                $query->where('term_id', $request->term_id);
            }

            $query->orderBy('id', 'DESC');

            $rowsPerPage = 10;

            if ($request->has('rows_per_page') && $request->rows_per_page > 0) {
                $rowsPerPage = $request->rows_per_page;
            }
            if ($rowsPerPage > 0) {
                $data = $query->paginate($rowsPerPage);
            } else {
                $data = $query->get();
            }


            return ResponseBuilder::Success($data);
        } catch (\Throwable $th) {
            Log::error("Controllers\Admin\BhytController - getListBhytAvailableRegistrationPeriods: "
                . $th->getLine() . " - " . $th->getMessage());
            return ResponseBuilder::Fail($th->getMessage());
        }
    }

    /**
     * update hoặc tạo mới đợt đăng ký bảo hiểm y tế
     *
     * @param  mixed $request
     * @return mixed
     */
    public function postCreateOrEditBhytAvailableRegistrationPeriod(Request $request)
    {
        try {
            $validatorMessages = [
                'name.required' => 'Tên đợt đăng ký không được để trống',
                'start_date.required' => 'Ngày bắt đầu không được để trống',
                'start_date.date' => 'Ngày bắt đầu không đúng định dạng',
                'end_date.required' => 'Ngày kết thúc không được để trống',
                'end_date.date' => 'Ngày kết thúc không đúng định dạng',
                'end_date.after' => 'Ngày kết thúc phải sau ngày bắt đầu',
                'term_id.required' => 'Học kỳ không được để trống',
                'term_id.integer' => 'Học kỳ không đúng định dạng',
                'note.string' => 'Ghi chú không đúng định dạng',
                'id.integer' => 'Không xác định được đợt đăng ký',
                'fee.integer' => 'Phí đăng ký không đúng định dạng',
                'fee.min' => 'Phí đăng ký phải lớn hơn 1000',
            ];

            $attributeNames = [
                'name' => 'Tên đợt đăng ký',
                'start_date' => 'Ngày bắt đầu',
                'end_date' => 'Ngày kết thúc',
                'term_id' => 'Học kỳ triển khai',
                'note' => 'Ghi chú',
                'id' => 'Đợt đăng ký',
                'fee' => 'Phí đăng ký',
            ];

            $validator = Validator::make($request->all(), [
                'id' => 'integer',
                'name' => 'required',
                'start_date' => 'required|date',
                'end_date' => 'required|date|after:start_date',
                'term_id' => 'required|integer',
                'note' => 'string',
                // fee kiểu integer và fee lớn
                'fee' => 'integer|min:1000',
            ], $validatorMessages, $attributeNames);

            if ($validator->fails()) {
                return ResponseBuilder::Fail($validator->errors()->first());
            }

            if ($request->has('id') && $request->id > 0) {
                $bhytAvailableRegistrationPeriod = BhytAvailableRegistrationPeriod::find($request->id);
                if (!$bhytAvailableRegistrationPeriod) {
                    return ResponseBuilder::Fail('Không tìm được đợt đăng ký cần chỉnh sửa');
                }

                // cán bộ sửa thời gian và các thông tin khác trừ số tiền bhyt

                // if (($countRegistration = $this->checkIfExistRegistrationOfAvailablePeriod($request->id)) > 0) {
                //     return ResponseBuilder::Fail(
                //         'Đợt đăng ký đã có ' . $countRegistration . ' đăng ký, không thể chỉnh sửa'
                //     );
                // }

            } else {
                $bhytAvailableRegistrationPeriod = new BhytAvailableRegistrationPeriod();
            }

            if (($countPeriod = $this->checkIfExistAvailablePeriodWithDuplicateDate($request->start_date, $request->end_date, $request->id)) > 0) {
                return ResponseBuilder::Fail(
                    'Đã tồn tại ' . $countPeriod . ' đợt đăng ký khác trong khoảng thời gian này'
                );
            }

            $bhytAvailableRegistrationPeriod->name = $request->name;
            $bhytAvailableRegistrationPeriod->start_date = $request->start_date;
            $bhytAvailableRegistrationPeriod->end_date = $request->end_date;
            $bhytAvailableRegistrationPeriod->term_id = $request->term_id;
            $bhytAvailableRegistrationPeriod->note = $request->note;
            $bhytAvailableRegistrationPeriod->fee = $request->fee;
            $bhytAvailableRegistrationPeriod->save();

            return ResponseBuilder::Success($bhytAvailableRegistrationPeriod);
        } catch (\Throwable $th) {
            Log::error("Controllers\Admin\BhytController - postCreateBhytAvailableRegistrationPeriod: "
                . $th->getLine() . " - " . $th->getMessage());
            return ResponseBuilder::Fail($th->getMessage(), 500);
        }
    }

    /**
     * xoá đợt đăng ký bảo hiểm y tế
     *
     * @param  mixed $request
     * @return mixed
     */
    public function deleteBhytAvailableRegistrationPeriod(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id' => 'required|integer'
            ], [
                'id.required' => 'Không xác định được đợt đăng ký',
                'id.integer' => 'Không xác định được đợt đăng ký',
            ]);

            if ($validator->fails()) {
                return ResponseBuilder::Fail($validator->errors()->first());
            }

            $bhytAvailableRegistrationPeriod = BhytAvailableRegistrationPeriod::find($request->id);
            if (!$bhytAvailableRegistrationPeriod) {
                return ResponseBuilder::Fail('Không tìm được đợt đăng ký cần xóa');
            }

            if (($count = $this->checkIfExistRegistrationOfAvailablePeriod($request->id)) > 0) {
                return ResponseBuilder::Fail('Đợt đăng ký đã có ' . $count . ' đăng ký, không thể xóa');
            }

            $bhytAvailableRegistrationPeriod->delete();

            return ResponseBuilder::Success();
        } catch (\Throwable $th) {
            Log::error("Controllers\Admin\BhytController - deleteBhytAvailableRegistrationPeriod: "
                . $th->getLine() . " - " . $th->getMessage());
            return ResponseBuilder::Fail($th->getMessage(), 500);
        }
    }

    /**
     * kiểm tra xem đợt đăng ký đã có người đăng ký chưa
     *
     * @param  integer $availablePeriodId
     * @return integer
     */
    private function checkIfExistRegistrationOfAvailablePeriod($availablePeriodId)
    {
        try {
            return BhytStudentRegister::where('available_registration_period_id', $availablePeriodId)
                ->whereIn('status', [
                    BhytStudentRegister::STATUS_PENDING_PAYMENT,
                    BhytStudentRegister::STATUS_COMPLETED_PAYMENT
                ])
                ->count();
        } catch (\Throwable $th) {
            Log::error("Controllers\Admin\BhytController - checkIfExistRegistrationOfAvailablePeriod: "
                . $th->getLine() . " - " . $th->getMessage());
            return 0;
        }
    }

    /**
     * kiểm tra xem có đợt đăng ký nào trùng ngày không
     *
     * @param  string $start_date
     * @param  string $end_date
     * @param  integer $id
     * @return integer
     */
    private function checkIfExistAvailablePeriodWithDuplicateDate($start_date, $end_date, $id = 0)
    {
        try {
            $query = BhytAvailableRegistrationPeriod::where('start_date', '<=', $end_date)
                ->where('end_date', '>=', $start_date);

            if ($id > 0) {
                $query->where('id', '<>', $id);
            }

            return $query->count();
        } catch (\Throwable $th) {
            Log::error("Controllers\Admin\BhytController - checkIfExistAvailablePeriodWithDuplicateDate: "
                . $th->getLine() . " - " . $th->getMessage());
            return 0;
        }
    }

    public function postPaymentConfirmationBhyt(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id' => 'required|integer',
                'note' => 'required|string',
            ], [
                'id.required' => 'Không xác định được đơn đăng ký',
                'id.integer' => 'Không xác định được đơn đăng ký',
                'note.required' => 'Phải nhập ghi chú',
                'note.string' => 'Ghi chú sai định dạng',
            ], [
                'id' => 'Đơn đăng ký',
                'note' => 'Ghi chú',
            ]);

            if ($validator->fails()) {
                return ResponseBuilder::Fail($validator->errors()->first());
            }

            $this->processPaymentBhyt($request->id, $request->note);


            return ResponseBuilder::Success();
        } catch (\Throwable $th) {
            Log::error("Controllers\Admin\BhytController - paymentConfirmationBhyt: "
                . $th->getLine() . " - " . $th->getMessage());
            return ResponseBuilder::Fail($th->getMessage(), 500);
        }
    }

    public function processPaymentBhyt($id, $note = '')
    {
        try {
            $bhytStudentRegister = BhytStudentRegister::find($id);
            if (!$bhytStudentRegister) {
                throw new Exception('Không tìm thấy đơn đăng ký');
            }
            $dngRecord =  DngRecord::where('item_id', '=', $id)
                ->where('dng_type', '=', 'BHYT')
                ->firstOrFail();
            $dngRecord->note = $note;
            $dngRecord->is_paid = 1;
            $dngRecord->problem = 0;
            $dngRecord->save();

            $info = User::where('user_code', '=', $bhytStudentRegister->student_code)->firstOrFail();
            $time = Carbon::now();
            $now = $time->toDateString();

            $term = Term::whereDate('startday', '<=', $now)
                ->whereDate('endday', '>=', $now)
                ->firstOrFail();

            Transaction::insertGetId([
                'user_code' =>  $info->user_code,
                'user_login' => $info->user_login,
                'type' => $dngRecord->dng_type,
                'execute' => 1,
                'type_extension' =>
                '[Nạp tiền] ' . $dngRecord->wallet_type . ' ' . $dngRecord->code . ' #' . $dngRecord->item_id,
                'amount' => $dngRecord->amount,
                'term_name' => $term->term_name,
                'created_by' =>  $info->user_login,
                'in_out' => 1,
                'note' =>
                '[Nạp tiền qua Dng' .
                    ' | DNG_ID #' .
                    $dngRecord->dng_id . '] ' .
                    $dngRecord->wallet_type . ' ' .
                    'BHYT' . ' #' . $dngRecord->item_id,

                'dng_transaction_id' => $dngRecord->transaction_id,
                'invoice_date_create' => $now,
                'dng_id' => $dngRecord->dng_id
            ]);
            Transaction::insertGetId([
                'user_code' =>  $info->user_code,
                'user_login' => $info->user_login,
                'type' => $dngRecord->dng_type,
                'execute' => 1,
                'type_extension' => '[Thanh toán dịch vụ] ' . 'BHYT' . ' #' . $dngRecord->item_id,
                'amount' => $dngRecord->service_amount,
                'term_name' => $term->term_name,
                'created_by' => 'system',
                'in_out' => 0,
                'note' => '[Thanh toán dịch vụ] ' . 'BHYT' . ' #' . $dngRecord->item_id,
                'dng_transaction_id' => '',
                'invoice_date_create' => $now,
                'dng_id' => $dngRecord->dng_id
            ]);

            $bhytStudentRegister->status = BhytStudentRegister::STATUS_COMPLETED_PAYMENT;
            $bhytStudentRegister->save();
            return true;
        } catch (\Throwable $th) {
            Log::error("Controllers\Admin\BhytController - processPaymentBhyt: "
                . $th->getLine() . " - " . $th->getMessage());
            throw new Exception("Controllers\Admin\BhytController - processPaymentBhyt: "
                . $th->getLine() . " - " . $th->getMessage());
        }
    }
    // huỷ đơn ở trạng thái chưa thanh toán
    public function postCancelBhyt(Request $request)
    {
        try {
            $query = BhytStudentRegister::query();
            $query->where('id', '=', $request->id);
            $record = $query->first();

            if ($record) {
                if ($record->status == 0) {
                    $record->status = BhytStudentRegister::STATUS_CANCELLED_UNPAID_ORDER;
                }
                if ($record->save()) {
                    return response(['status' => 'success'], 200);
                } else {
                    return response(['status' => 'error_saving_data'], 500);
                }
            }

            return response(['status' => 'record_not_found'], 404);
        } catch (\Throwable $th) {
            Log::error("Controllers\Admin\BhytController - postCancelBhyt: "
                . $th->getLine() . " - " . $th->getMessage());
            return ResponseBuilder::Fail($th->getMessage(), 500);
        }
    }

    public function postCheckPayment(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id' => 'required|integer',
            ], [
                'id.required' => 'Không xác định được đơn đăng ký',
                'id.integer' => 'Không xác định được đơn đăng ký',
            ], [
                'id' => 'Đơn đăng ký',
            ]);

            if ($validator->fails()) {
                return ResponseBuilder::Fail($validator->errors()->first());
            }

            $dngRecord =  DngRecord::where('item_id', '=', $request->id)
                ->where('dng_type', '=', 'BHYT')
                ->firstOrFail();
            $repository = new DngConnectRepository();
            $response = $repository->getResultDng($dngRecord);
            if ($response['is_paid'] == 1) {
                $this->processPaymentBhyt($request->id);
                return ResponseBuilder::Success(['is_paid' => 1]);
            } else {
                return ResponseBuilder::Success(['is_paid' => 0]);
            }
        } catch (\Throwable $th) {
            Log::error("Controllers\Admin\BhytController - postCheckPayment: "
                . $th->getLine() . " - " . $th->getMessage());
            return ResponseBuilder::Fail($th->getMessage(), 500);
        }
    }
}
