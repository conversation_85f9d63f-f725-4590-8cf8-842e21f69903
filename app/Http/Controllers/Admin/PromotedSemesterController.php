<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\Admin\PromotedSemesterRepository;
use Illuminate\Http\Request;

class PromotedSemesterController extends Controller
{
    private $PromotedSemesterRepository;

    public function __construct(PromotedSemesterRepository $promotedSemesterRepository)
    {
        $this->PromotedSemesterRepository = $promotedSemesterRepository;
    }

    public function index(){
        return view('admin_v1.promoted_semester.index');
    }
    public function getListPromotedSemesterStudent(Request $request){
        return $this->PromotedSemesterRepository->getListPromotedSemesterStudent($request);
    }
    public function getOptions(){
        return $this->PromotedSemesterRepository->getOptions();
    }
    public function uploadImportData(Request $request){
        return $this->PromotedSemesterRepository->uploadImportData($request);
    }
    public function exportPromotedSemesterStudentList(Request $request){
        return $this->PromotedSemesterRepository->exportPromotedSemesterStudentList($request);
    }
}
