<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\Admin\DepartmentRepository;
use Illuminate\Http\Request;

class DepartmentController extends Controller
{
    protected $departmentRepository;

    public function __construct(DepartmentRepository $departmentRepository)
    {
        $this->departmentRepository = $departmentRepository;
    }

    public function list(Request $request)
    {
        return $this->departmentRepository->list($request);
    }

    public function create(Request $request)
    {
        return $this->departmentRepository->createDepartment($request);
    }

    public function update(Request $request, $id)
    {
        return $this->departmentRepository->updateDepartment($request, $id);
    }

    public function delete(Request $request, $id)
    {
        return $this->departmentRepository->deleteDepartment($request, $id);
    }

    public function updateDean(Request $request, $id)
    {
        return $this->departmentRepository->updateDean($request, $id);
    }

    public function updateAssociateDean(Request $request, $id)
    {
        return $this->departmentRepository->updateAssociateDean($request, $id);
    }
}
