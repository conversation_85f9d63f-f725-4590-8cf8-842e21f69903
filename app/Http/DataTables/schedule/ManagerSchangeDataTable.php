<?php

namespace App\Http\DataTables\schedule;

use App\Http\DataTables\DataTable;
use App\Models\Fu\Subject;
use Yajra\DataTables\Facades\DataTables;

class ManagerSchangeDataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'id',
        'subject_name',
        'subject_code',
        'skill_code',
        'department',
        'min_student',
        'max_student',
        'action',
    ];

    /**
     * DataTable raw columns
     */
    protected $columns =[
        'min_student',
        'max_student',
        'action',
    ];

    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        // Check for filter:search
        if ($search = request()->input('search.value')) {
            $listField = [
                'subject.subject_name',
                'subject.subject_code',
                'subject.skill_code',
                'department.department_name',
            ];

            $query->where(function ($q) use ($listField, $search) {
                $operator = 'like';
                foreach($listField as $field) {
                    $field = trim($field);
                    $q->orWhere($field, $operator, '%' . trim($search) . '%');
                }
            });
        }
    }

    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {
        /**
         * Build query for datatable
         */
        $surveyStatus = [
            0 => "Đã kết thúc",
            1 => "Đang hoạt động"
        ];

        $query = Subject::select([
            'subject.id',
            'subject.subject_name',
            'subject.subject_code',
            'subject.skill_code',
            'department.department_name as department',
            'subject.min_student',
            'subject.max_student',
        ])
        ->join('department', 'subject.department_id', '=', 'department.id');


        $datatable = DataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns);
        $departmentId = request()->input('decision_num', null);


        /** ================== Filter ===================== */
        if ($departmentId != null && $departmentId != -1) {
            $query->where('department.id', $departmentId);
        }

        
        /** ================== format Column ===================== */
        $datatable->editColumn('min_student', function($node) {
            return "<div>
                <input name=\"min_student\" type=\"text\" class=\"form-control\" value=\"$node->min_student\">
            </div>";
        });

        $datatable->editColumn('max_student', function($node) {
            return "<div>
                <input name=\"max_student\" type=\"text\" class=\"form-control\" value=\"$node->max_student\">
            </div>";
        });
        
        $datatable->addColumn('action', function($node) {
            return "<button class=\"btn btn-primary btn-sync-limit-student kt-spinner--right kt-spinner--sm kt-spinner--light spin_1\" data-id=\"$node->id\" data-subject_code=\"$node->subject_code\">
            Cập nhập
            </button>";
        });

        $datatable->filter(function ($query) {
            return $this->filter($query);
        });
        
        return $datatable;
    }
}