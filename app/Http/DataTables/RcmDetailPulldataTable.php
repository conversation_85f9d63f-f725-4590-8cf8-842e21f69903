<?php

namespace App\Http\DataTables;

use App\Models\Crm\Rcm;
use App\Models\Fu\User;
use App\Models\Iaps\RcmPullUser;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class RcmDetailPulldataTable extends DataTables
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'id',
        'rcm_name',
        'campus_code',
        'khoa',
        'user_code',
        'full_name',
        'status',
        'created_by',
        'updated_at_new',
        'action',
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
        'rcm_name',
        'status',
        'updated_at_new',
        'action',
    ];
    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        $rcmId = request()->input('rcm_id', null);
        $campusCodeCheck = request()->input('campus_code', null);
        
        // check chiến dịch
        if ($rcmId != null && $rcmId != -1) {
            $query->where('rcm_pull.rcm_id', $rcmId);
        }

        // check cơ sở
        if ($campusCodeCheck != null && $campusCodeCheck != -1) {
            $query->where('rcm_pull.campus_code', $campusCodeCheck);
        }

        // Check for filter:search
        if ($search = request()->input('search.value')) {
            $field = 'name';
            $listField = [
                'rcm_pull_user.user_code',
                'rcm_pull_user.full_name',
            ];

            $query->where(function ($q) use ($listField, $field, $search) {
                $field = trim($field);
                $operator = 'like';
                foreach($listField as $field) {
                    $q->orWhere($field, $operator, '%' . trim($search) . '%');
                }
            });
        }
    }

    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {

        /**
         * Build query for datatable
         */

        //Lấy danh sách các chiến dịch của Rcm
        $listRcm = Rcm::select([
            'rcm.id',
            'rcm.name',
            'fu_campus.campus_code',
            'fu_campus.campus_name'
        ])
        ->join('fu_campus', 'fu_campus.id', '=', 'rcm.CampusId')
        ->orderBy('CampusId')
        ->orderBy('rcm.id', 'DESC')
        ->get();

        //Danh sách các sinh viên được kéo về
        $query = RcmPullUser::select([
            'rcm_pull_user.id',
            'rcm_pull_user.status as pull_status',
            'rcm_pull_user.user_code',
            'rcm_pull_user.full_name',
            'rcm_pull.rcm_id',
            'rcm_pull.campus_code',
            'rcm_pull.khoa',
            'rcm_pull.created_by',
            DB::raw("DATE_FORMAT(rcm_pull_user.updated_at, '%d\/%m\/%Y %H:%I:%S') as updated_at_new"),
            'rcm_pull.status',
        ])
        ->join('rcm_pull', 'rcm_pull_user.rcm_pull_id', '=', 'rcm_pull.id')
        ->orderBy('rcm_pull_user.id', 'DESC');
        
        $datatable = DataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns);

        /** ================== format Column ===================== */
        $datatable->editColumn('status', function($node){
            $rcmUser = User::on($node->campus_code)->where('user_code', $node->user_code)->first();
            if ($rcmUser) {
                return "<b><p class='text-success'>Đã kéo về</p></b>";
            } elseif (!($rcmUser)) {
                return "<b><p class='text-danger'>Đã kéo về (Chưa có tài khoản AP)</p></b>";
            }
        });

        $datatable->editColumn('rcm_name', function($node) use ($listRcm) {
            $rcmDetail = $listRcm->where('id', $node->rcm_id)->first();
            if ($node->status == 1) {
                return ($rcmDetail->name ?? "Không xác định") ;
            }

            return "";
        });

        $datatable->editColumn('action', function($node) {
            $rcmUser = User::on($node->campus_code)->where('user_code', $node->user_code)->first();
            if ($rcmUser) {
                return "<button id='user_code' class='btn btn-primary pull-student-by-user-code' value='$node->user_code'>Kiểm tra</button>";
            } elseif (!($rcmUser)) {
                return "<button id='user_code' class='btn btn-danger pull-student-by-user-code' value='$node->user_code'>Kiểm tra</button>";   
            }

            return "";
        });
        
        $datatable->filter(function ($query) {
            return $this->filter($query);
        });

        return $datatable;
    }
}