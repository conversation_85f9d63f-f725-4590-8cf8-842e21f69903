<?php

namespace App\Http\DataTables;

use App\Models\Dra\Survey;
use App\Models\Dra\SurveyStudent;
use App\Models\LimeSurvey\Questions;
use App\Models\LimeSurvey\Surveys;
use Illuminate\Support\Facades\DB;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class SurveydataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'id',
        'survey_id',
        'survey_name',
        'time',
        'question_num',
        'is_active',
        'created_at_new',
        'ratio',
    ];

    /**
     * DataTable raw columns
     */
    protected $columns =[
        'survey_name',
        'time',
        'question_num',
        'is_active',
        'created_at_new',
        'ratio',
    ];

    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        $survey_status = request()->input('survey_status', null);

        // check theo trạng thái
        if ($survey_status != null && $survey_status != -1) {
            $query->where('survey.is_active', $survey_status);
            // dd($query->get());
        }

        // Check for filter:search
        if ($search = request()->input('search.value')) {
            $field = 'name';
            $listField = [
                'survey.survey_id',
                'survey.survey_name',
            ];

            $query->where(function ($q) use ($listField, $field, $search) {
                $field = trim($field);
                $operator = 'like';
                foreach($listField as $field) {
                    $q->orWhere($field, $operator, '%' . trim($search) . '%');
                }
            });
        }
    }

    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {
        /**
         * Build query for datatable
         */
        $surveyStatus = [
            0 => "Đã kết thúc",
            1 => "Đang hoạt động"
        ];

        $surveysDetail = Surveys::select([
            'surveys.sid',
            'surveys.datecreated',
            'surveys_languagesettings.surveyls_title',
        ])
        ->join('surveys_languagesettings', 'surveys_languagesettings.surveyls_survey_id', '=', 'surveys.sid')
        ->join('questions', 'questions.sid', '=', 'surveys.sid')
        ->orderBy('surveys.sid', 'DESC')
        ->get();

        $question = Questions::select([
            'questions.qid',
            'questions.sid',
            DB::raw("COUNT('questions.sid') AS questions_num"),
        ])
        ->join('surveys', 'surveys.sid', '=', 'questions.sid')
        ->groupBy('questions.sid')
        ->orderBy('questions.qid', 'DESC')
        ->get();

        $query = Survey::select([
            'survey.id',
            'survey.survey_id',
            'survey.survey_name',
            'survey.is_active',
            'survey.question_num',
            DB::raw("COUNT('survey_student.status') AS ratio"),
            DB::raw("DATE_FORMAT(survey.start_date, '%d\/%m\/%Y') as start_date"),
            DB::raw("DATE_FORMAT(survey.end_date, '%d\/%m\/%Y') as end_date"),
        ])
        ->join('survey_student', 'survey_student.survey_id', '=', 'survey.id')
        ->groupBy('survey.id')
        ->orderBy('survey.id', 'DESC');

        $datatable = DataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns);


        /** ================== format Column ===================== */

        $datatable->editColumn('is_active', function($node) {
            return "<div class=\"custom-control custom-switch custom-switch-md\">
                <input data-id=\"$node->id\" type=\"checkbox\" class=\"custom-control-input change-status\" id=\"switch-$node->id\" " . ($node->is_active == 1 ? "checked" : "") . ">
                <label class=\"custom-control-label\" for=\"switch-$node->id\"></label>
            </div>";
        });

        $datatable->editColumn('survey_name', function($node) use ($surveysDetail) {
            $surveyName = $surveysDetail->where('sid', $node->survey_id)->first();
            if ($node){
                return "<a value='$node->id' href='". (route('admin.survey.detailSurvey', ['survey_id' => $node->id])) ."' target='_blank'>". ($surveyName->surveyls_title ?? "Không xác định") ."</a>";
            }
        });

        $datatable->editColumn('time', function($node) {
            if ($node)
                return "<b><p>" . $node->start_date . " - " . $node->end_date . "</p></b>";
            else {
                return "<b><p class='text-danger'>Không xác định</p></b>";
            }
        });

        $datatable->editColumn('question_num', function($node) use ($question) {
            $countQuestion = $question->where('sid', $node->survey_id)->first();
            if ($node){
                return "<b><p>" . ($countQuestion->questions_num ?? "Không xác định") . "</p></b>";
            }
        });

        $datatable->editColumn('created_at_new', function($node) use ($surveysDetail) {
            $surveyCreatedAt = $surveysDetail->where('sid', $node->survey_id)->first();
            if ($node){
                return "<b><p>" . ($surveyCreatedAt->datecreated ?? "Không xác định") . "</p></b>";
            }
        });

        $datatable->editColumn('ratio', function($node)  {
            $totalFinish = $node->ratio;
            $totalStudent = SurveyStudent::where('survey_id', $node->id)->where('status', 1)->count();
            if ($totalFinish <= 0) {
                return 0;
            } else {
                return "<b><p class='text-success'> $totalStudent / $totalFinish (" . round(($totalStudent / $totalFinish) * 100 , 2) . " %) </p></b>";
            }
        });

        $datatable->filter(function ($query) {
            return $this->filter($query);
        });
        
        return $datatable;
    }
}