<?php

namespace App\Http\DataTables;

use App\Models\Crm\Rcm;
use Auth;
// use DataTables;
use Yajra\DataTables\Facades\DataTables;

use App\User;
use App\Models\Fee\FeeMail;
use App\Models\Iaps\RcmPull;
use Illuminate\Support\Facades\DB;

class RcmPulldataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'id',
        'rcm_name',
        'campus_code',
        'khoa',
        'number_student',
        'status',
        'created_by',
        'created_at_new',
        'action',
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
        'rcm_name',
        'status',
        'action',
        'number_student'
    ];

    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        $rcmId = request()->input('rcm_id', null);
        $campusCodeCheck = request()->input('campus_code', null);
        

        // check chiến dịch
        if ($rcmId != null && $rcmId != -1) {
            $query->where('rcm_pull.rcm_id', $rcmId);
        }

        // check cơ sở
        if ($campusCodeCheck != null && $campusCodeCheck != -1) {
            $query->where('rcm_pull.campus_code', $campusCodeCheck);
        }

        // Check for filter:search
        if ($search = request()->input('search.value')) {
            $field = 'name';
            $listField = [
                'rcm_pull.campus_code',
                'rcm_pull.khoa',
            ];

            $query->where(function ($q) use ($listField, $field, $search) {
                $field = trim($field);
                $operator = 'like';
                foreach($listField as $field) {
                    $q->orWhere($field, $operator, '%' . trim($search) . '%');
                }
            });
        }
    }


    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {
        /**
         * Build query for datatable
         */
        $rcmStatus = [
            0 => "Vô hiệu hóa",
            1 => "Đang hoạt động"
        ];
        $listRcm = Rcm::select([
            'rcm.id',
            'rcm.name',
            'fu_campus.campus_code',
            'fu_campus.campus_name'
        ])
        ->join('fu_campus', 'fu_campus.id', '=', 'rcm.CampusId')
        ->orderBy('CampusId')
        ->orderBy('rcm.id', 'DESC')
        ->get();

        $query = RcmPull::select([
            'rcm_pull.id',
            'rcm_pull.rcm_id',
            'rcm_pull.rcm_name',
            'rcm_pull.campus_code',
            'rcm_pull.khoa',
            DB::raw("SUM(CASE WHEN (rcm_pull_user.status = 1) THEN 1 END) AS number_student_pull"),
            DB::raw("COUNT('rcm_pull_user.id') AS number_student"),
            'rcm_pull.created_by',
            DB::raw("DATE_FORMAT(rcm_pull.created_at, '%d\/%m\/%Y %H:%I:%S') as created_at_new"),
            'rcm_pull.status',
        ])
        ->leftJoin('rcm_pull_user', 'rcm_pull_user.rcm_pull_id', '=', 'rcm_pull.id')
        ->groupBy('rcm_pull.id')
        ->orderBy('rcm_pull.id', 'DESC');

        $datatable = DataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns);


        /** ================== format Column ===================== */

        $datatable->editColumn('status', function($node) use ($rcmStatus) {
            if (isset($rcmStatus[$node->status]))
                if ($node->status == 1) {
                    return "<b><p class='text-success'>" . $rcmStatus[$node->status] . "</p></b>";
                } else {
                    return "<b><p class='text-danger'>" .$rcmStatus[$node->status] . "</p></b>";
                }
            else {
                return "<b><p class='text-danger'>Không xác định</p></b>";
            }
        });

        
        $datatable->editColumn('action', function($node) {
            $baseButton = "<button class='btn btn-primary dowload-list-user' data-id='$node->id'>Tải file email</button><br>";
            $baseButton .= "<div style='height: 5px;'></div>";
            if ($node->status == 1) {
                return $baseButton . "<button class='btn btn-primary pull-student-by-rcm-id'  
                data-campus_code='$node->campus_code'
                data-ma_chien_dich='$node->rcm_id'
                data-grade='$node->khoa'
                data-id='$node->id' value ='$node->id'>Kéo sinh viên về</button>";
            }

            return $baseButton;
        });

        
        $datatable->editColumn('number_student', function($node) {
            $numberPull = 0;
            if ($node->number_student_pull == 0 || $node->number_student_pull == null) {
                return "<b><p class='text-success'> - </p></b>";
            } else if ($node->number_student_pull != null) {
                $numberPull = $node->number_student_pull;
            }

            if ($node->status == 1) {
                return "<b><p class='text-success'> $numberPull / $node->number_student ( " . round((100 * $numberPull / $node->number_student), 2) . " % )</p></b>";
            }

            return "";
        });
        
        $datatable->editColumn('rcm_name', function($node) use ($listRcm) {
            $rcmDetail = $listRcm->where('id', $node->rcm_id)->first();
            if ($node->status == 1) {
                return "<a href='". (route('admin.user.detailPullUser', ['rcm_pull_id' => $node->id])) ."' target='_blank'>[" . ($rcmDetail->campus_code ?? "") . "-" . ($rcmDetail->campus_name ?? "") . "] - " . ($rcmDetail->name ?? "Không xác định") ."</a>";
            }

            return "";
        });
        
        $datatable->filter(function ($query) {
            return $this->filter($query);
        });

        return $datatable;
    }
}