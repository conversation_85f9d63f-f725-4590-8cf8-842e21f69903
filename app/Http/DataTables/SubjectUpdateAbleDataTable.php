<?php

namespace App\Http\DataTables;

use Auth;
use DataTables;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

use App\Models\Fu\Decision;
use App\Models\Fu\SubjectUpdateAble;



class SubjectUpdateAbleDataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        '_id',
        'subject_name',
        'subject_name_change',
        'file',
        'action'
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
        'id',
        'subject_name',
        'subject_name_change',
        'file',
        'action'
    ];


    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        $termCheck = request()->input('term_id', null);
        $decisionType = request()->input('decision_type', null);
        $decisionNum = request()->input('decision_num', null);
        $oldSubjectCode = request()->input('old_subject_code', null);
        $newSubjectCode = request()->input('new_subject_code', null);
        if ($termCheck != null && $termCheck != -1) {
            $query->where('term_id', $termCheck);
        }

        if ($decisionType != null && $decisionType != -1) {
            $query->where('type', $decisionType);
        }

        if ($decisionNum != null && $decisionNum != -1) {
            $query->where('decision_id', $decisionNum);
        }

        if ($oldSubjectCode != null && $oldSubjectCode != -1) {
            $query->where('old_subject_code', $oldSubjectCode);
        }

        if ($newSubjectCode != null && $newSubjectCode != -1) {
            $query->where('new_subject_code', $newSubjectCode);
        }
        
        // Check for filter:search
        if ($search = request()->input('search.value')) {
            $field = 'name';
            $listField = [
                // 'decision_user.user_code',
                'old_subject_code',
                'new_subject_code'
            ];

            $query->where(function ($q) use ($listField, $field, $search) {
                $field = trim($field);
                $search = '%' . trim($search) . '%';
                $operator = 'like';
                foreach($listField as $field) {
                    if ($field == 'full_name') {
                        $q->orWhere(DB::raw('CONCAT(user_surname," ",user_middlename," ", user_givenname)'), $operator, $search);
                    } else {
                        $q->orWhere($field, $operator, $search);
                    }
                }
            });
        }
    }


    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {

        /**
         * Build query for datatable
         */
        $query = SubjectUpdateAble::select([
            'id', 
            'old_subject_code', 
            'decision_id', 
            'new_subject_code', 
            'old_subject_name', 
            'new_subject_name'
        ])
        ->orderBy('updated_at', 'DESC')
        ->groupBy('old_subject_code', 'decision_id');

        $decisionObj = Decision::where('type', Decision::TYPE_MIEN_GIAM)->get();
        $decisions = call_user_func(function() use ($decisionObj) {
            $res = [];
            foreach ($decisionObj as $value) {
                $res[$value->id] = $value->toArray();
            }

            return $res;
        });

        // $test = $query->first();
        $datatable = DataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns);
        
        // format Data
        $datatable->addColumn('_id', function(SubjectUpdateAble $node) { return $node->id; });

        $datatable->addColumn('subject_name', function(SubjectUpdateAble $node)  {
            return "<b>$node->old_subject_code</b>" . ' - ' . $node->old_subject_name;
        });
      
        $datatable->addColumn('subject_name_change', function(SubjectUpdateAble $node) use ($decisions)  {
            $res = "";
            if ($node->decision_id == null) {
                $listSubject = SubjectUpdateAble::where('old_subject_code', $node->old_subject_code)
                ->whereNull('decision_id')
                ->get();
                foreach ($listSubject as $value) {
                    $deleteBtn = '';
                    $res .= "<span><b>$value->new_subject_code</b> - $value->new_subject_name $deleteBtn</span><br>";
                }        
            } else {
                $listSubject = SubjectUpdateAble::where('old_subject_code', $node->old_subject_code)
                ->where('decision_id', $node->decision_id)
                ->get();
                foreach ($listSubject as $value) {
                    $deleteBtn = '';

                    // $res .= "<span>$value->new_subject_code - $value->new_subject_name</span><br>";
                    $res .= "<span><b>$value->new_subject_code</b> - $value->new_subject_name $deleteBtn</span><br>";
                }        
            }

            return $res;      
        });
      
        $datatable->addColumn('file', function(SubjectUpdateAble $node) use ($decisions) {
            if (isset($decisions[$node->decision_id])) {
                $decision = $decisions[$node->decision_id];
        
                return '<a target="_blank" href="' . asset('storage/decision') . '/' .$decision['file'] . '">' . $decision['file'] . '</a>';
            } else {
                if ($node->decision_id != null) {
                    return  "<span>Không xác định</span>";
                } else {
                    return  "<span>-</span>";
                }
            }
            

        });
        
        $datatable->addColumn('action', function(SubjectUpdateAble $node) {
            return "<a href='" . route('admin.subject_update_able.edit', [ 
                'old_subject_code' => $node->old_subject_code
            ]) . "?decision_id=$node->decision_id'>" . "Chỉnh sửa" . "</a>";
        });
        
        /** ================== Sort ===================== */
    
        $datatable ->orderColumn('subject_name', function ($query, $order) {
            $query->orderBy('old_subject_name', $order);
        });

        $datatable ->orderColumn('subject_name_change', function ($query, $order) {
            $query->orderBy('new_subject_name', $order);
        });
        $datatable ->orderColumn('file', function ($query, $order) {
            $query->orderBy('decision_id', $order);
        });

        $datatable->filter(function ($query) {
            return $this->filter($query);
        });

        return $datatable;
    }


}