<?php

namespace App\Http\DataTables;

use Auth;

use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

use App\Models\Fu\User;
use App\Models\Fu\Decision;
use App\Models\Fu\DecisionUser;
use App\Models\Fu\Term;
use App\Repositories\Admin\DecisionRepository;
use App\Utils\ResponseBuilder;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;

class DecisionDataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        '_id',
        'decision_name',
        'type',
        'term_name',
        'decision_num',
        'signer',
        'sign_day',
        'effective_time',
        'created_by',
        'total_student',
        'file',
        'action',
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
        '_id',
        'decision_name',
        'type',
        'term_name',
        'decision_num',
        'signer',
        'sign_day',
        'effective_time',
        'created_by',
        'total_student',
        'file',
        'action',
    ];


    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        try {
            $termCheck = request()->input('term_id', null);
            $decisionType = request()->input('decision_type', null);
            $decisionNum = request()->input('decision_num', null);
            if ($termCheck != null && $termCheck != -1) {
                $query->where('term_id', $termCheck);
            }
    
            if ($decisionType != null && $decisionType != -1) {
                $query->where('type', $decisionType);
            }
    
            if ($decisionNum != null && $decisionNum != -1) {
                $query->where('decision.id', $decisionNum);
            }
            
            // Check for filter:search
            if ($search = request()->input('search.value')) {
                $field = 'name';
                $listField = [
                    'decision.name',
                    'decision.signer',
                    'decision.decision_num',
                ];
                $query->where(function ($q) use ($listField, $field, $search) {
                    $field = trim($field);
                    $search = '%' . trim($search) . '%';
                    $operator = 'like';
                    foreach($listField as $field) {
                        $q->orWhere($field, $operator, $search);
                    }
                });
            }
        } catch (\Throwable $th) {
            Log::error($th);
        }
    }

    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {
        $query = Decision::select([
            'decision.id',
            'decision.name',
            'decision.decision_num',
            'decision.term_id',
            'term.term_name',
            'decision.from',
            'decision.type',
            'decision.signer',
            'decision.sign_day',
            'decision.effective_time',
            'decision.created_by',
            'decision.file',
            'decision.file_status',
            'decision.note',
        ])->with('decisionUser')
        ->leftJoin('term', 'term.id', '=', 'decision.term_id')
        ->orderBy('decision.id', 'desc');
        // init data
        $currentUser = auth()->user();
        $datatable = DataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns);

        // format Data
        $datatable->addColumn('_id', function(Decision $node) {
            return $node->id; 
        });
        $datatable->addColumn('decision_name', function(Decision $node) { return $node->name; });
        $datatable->addColumn('type', function(Decision $node) { return (Decision::TYPES[$node->type]['name'] ?? ""); });
        $datatable->addColumn('term_name', function(Decision $node) { return $node->term_name ?? "";});
        $datatable->addColumn('decision_num', function(Decision $node) { return $node->from . " " . $node->decision_num;});
        $datatable->addColumn('signer', function(Decision $node) { return $node->signer; });
        $datatable->addColumn('sign_day', function(Decision $node) { return $node->sign_day; });
        $datatable->addColumn('effective_time', function(Decision $node) { return $node->effective_time; });
        $datatable->addColumn('created_by', function(Decision $node) { return $node->created_by; });

        $datatable->addColumn('total_student', function(Decision $node) { return count($node->decisionUser) ?? 0; });
        $datatable->addColumn('file', function(Decision $node) {
            $file = $node->file;
            return '<a target="_blank" href="' . asset(('storage/decision') . '/' . $file) . '">' . $file . '</a>';
        });

        $datatable->addColumn('action', function(Decision $node) use ($currentUser){
            $html = "";

            if ($currentUser->user_login == $node->created_by || $currentUser->user_level == 1) {
                if( count($node->decisionUser) == 0 ) {
                    $html .= "<button type='button' data-id='$node->id' class='btn btn-danger btn-delete-decision w-100 mb-2'>
                        <i class='fa-solid fa-trash-can ml-2'></i>
                    </button>";
                }
            }
            $html .= "<button type='button' data-id='$node->id' class='btn btn-primary btn-edit-decision w-100 mb-2'>
                <i class='fa-solid fa-pen-to-square ml-2'></i>
            </button>";
            
            // // chỉ hiện nút với các type hạ loại (kỷ luật)
            // if($node->type == 28) {
            //     $html .= "<button type='button' data-id='$node->id' data-code='$node->user_code' class='btn btn-primary'>Xem chi tiết</button>";
            // }
            return $html;
        });

        $keyword = request()->input('search.value');
        $datatable->filter(function ($query) {
            return $this->filter($query);
        });

        return $datatable;
    }
}