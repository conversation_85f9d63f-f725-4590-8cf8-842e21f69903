<?php

namespace App\Http\DataTables;

use App\Models\Fu\Group;
use App\Models\Fu\Term;
use App\Models\GroupGraduate;
use Carbon\Carbon;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;

class GroupGraduatedataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'group_id',
        'group_name',
        'skill_code',
        'psubject_code',
        'group_time',
        'detail_slot',
        'number_student',
        'number_student_fail',
        'rotio_fail',
        'action'
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
        'group_name',
        'group_time',
        'rotio_fail',
        'subject_info',
        'detail_slot',
        'action',
    ];


    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        $subjectCode = request()->input('subject_code', null);
        $termId = request()->input('term_id', null);
        if ($termId != null) {
            $query = $query->where('list_group.pterm_id', $termId);
        } else {
            $lastTerm = Term::orderBy('id', 'DESC')->first();
            $query = $query->where('list_group.pterm_id', $lastTerm->id);
        }
        
        if ($subjectCode != null && $subjectCode != -1) {
            $query->where('list_group.psubject_code', $subjectCode);
        }
        
        if ($search = request()->input('search.value')) {
            $listField = [
                'list_group.skill_code',
                'list_group.psubject_code',
                'list_group.group_name',
            ];

            $query->where(function ($q) use ($listField, $search) {
                $operator = 'like';
                foreach($listField as $field) {
                    $q->orWhere($field, $operator, ('%' . trim($search) . '%'));
                }
            });
        }
    }


    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {
        $query = Group::select([
            'group_graduate.group_id',
            'list_group.skill_code',
            'list_group.psubject_code',
            'list_group.pterm_id',
            'list_group.body_id',
            'list_group.department_id',
            'list_group.group_name',
            'list_group.start_date',
            'list_group.end_date',
            DB::raw('COUNT(group_member.id) AS `number_student`'),
            // DB::raw('COUNT(group_graduate.id) AS `total_student_in_schedule`'),
            DB::raw('SUM(CASE WHEN group_graduate.`status` > 0 THEN 1 ELSE 0 END) AS `number_student_fail`')
        ])
        ->join('group_graduate', 'list_group.id', '=','group_graduate.group_id')
        ->join('group_member', function ($q) {
            $q->on('list_group.id', '=','group_member.groupid')
            ->on('group_member.member_login', '=','group_graduate.student_login');
        })
        ->where('is_virtual', 0)
        ->groupBy('list_group.id');


        $datatable = DataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns)
        ->setTotalRecords(1000000);

        $datatable->addColumn('group_time', function($node) {
            return "<span>$node->start_date - $node->end_date</span>";
        });

        $datatable->addColumn('rotio_fail', function($node) {
            if ($node->number_student == 0) {
                return "0 %";
            };

            return round(($node->number_student_fail / $node->number_student) * 100, 2) . " %";
        });
        
        $datatable->editColumn('detail_slot', function($node) {
            $data = DB::select("SELECT
                    group_graduate.group_id,
                    group_graduate.course_session AS 'Slot',
                    COUNT( group_member.id ) AS number_student,
                    SUM( CASE WHEN group_graduate.status > 0 THEN 1 ELSE 0 END ) AS number_student_fail 
                FROM
                    list_group
                    INNER JOIN group_graduate ON list_group.id = group_graduate.group_id
                    INNER JOIN group_member ON list_group.id = group_member.groupid 
                    AND group_member.member_login = group_graduate.student_login 
                WHERE group_graduate.group_id = ?
                ORDER BY list_group.id, group_graduate.course_session", [$node->group_id]);
            $str = "<table class='table-bordered table' ><tbody>";
            foreach ($data as $key => $value) {
                $str .= "<tr>";
                $str .= "<td>Slot $value->Slot</td>";
                $str .= "<td>$value->number_student</td>";
                $str .= "<td>$value->number_student_fail</td>";
                $str .= "</tr>";
            }
            
            $str = $str . "</tbody></table>";
            return $str;
        });
        
        $datatable->editColumn('group_name', function($node) {
            return "<a target='_blank' href=" . route('admin.group.index.edit', ['id' => $node->group_id]) . ">$node->group_name</a>";
        });

        $datatable->addColumn('action', function($node) {
            $str = "<button class=\"btn btn-primary btn-sm btn-sync-group \" data-id=\"$node->group_id\" style=\"width: 100px;\">Cập nhập</button><br>";
            $str .= "<button type='submit' class='btn btn-primary btn-sm btn-export-danh-sach-thi2' ";
            $str .= "style='margin-top:10px'";
            $str .= "data-group_id = '$node->group_id'";
            $str .= "data-pterm_id = '$node->pterm_id'";
            $str .= "data-body_id = '$node->body_id'";
            $str .= "data-department_id = '$node->department_id'";
            $str .= "data-psubject_code = '$node->psubject_code'>";
            $str .= "Tải danh sách thi</button>";

            return $str;
        });

        $datatable->filter(function ($query) {
            return $this->filter($query);
        });

        return $datatable;
    }
}