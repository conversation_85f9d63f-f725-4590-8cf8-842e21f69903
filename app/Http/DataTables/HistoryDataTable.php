<?php

namespace App\Http\DataTables;

use Auth;
use DataTables;

use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

use App\Models\Fu\User;
use App\Models\SystemLog;
use App\Models\Fu\Feedback;
use App\Models\Fu\FeedbackDetail;
use App\Models\Fu\Group;
use App\Models\Fu\Term;



class HistoryDataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'id', 
        'actor', 
        'log_time', 
        'description', 
        'subject_code', 
        'group_name', 
        'brief', 
        'from_ip', 
        'relation_login',
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
        'group_name', 
        'subject_code', 
        'log_time'
    ];


    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        $author = request()->input('author', null);
        $subjectCode = request()->input('subject_code', null);
        $relationLogin = request()->input('relation_login', null);
        $objectId = request()->input('object_id', null);
        $objectName = request()->input('object_name', null);
        $termId = request()->input('term_id', null);
        if ($author != null && $author != -1) {
            $query->where('actor', $author);
        }

        if ($objectName != null && $objectName != -1) {
            $query->where('object_name', $objectName);
        }
        
        if ($objectId != null && $objectId != -1) {
            $query->where('object_id', $objectId);
        }
        
        if ($relationLogin != null && $relationLogin != -1) {
            $query->where('relation_login', $relationLogin);
        }
        
        if ($subjectCode != null && $subjectCode != -1) {
            $listId = null;
            $listGroup = Group::select('id')->where('psubject_code', $subjectCode)->where('list_group.is_virtual', 0);
            $search = request()->input('search.value');
            if ($termId != null) {
                $listGroup = $listGroup->where('pterm_id', $termId);
            }

            $listGroup = $listGroup->get('id');
            if (count($listGroup) != 0) {
                $listId = array_column($listGroup->toArray(), 'id');
            }
            
            if ($listId != null) {
                $query->whereIn('object_id', $listId);
            }
        }
        
        if ($search = request()->input('search.value')) {
            if (trim($search) != '') {
                $listId = null;
                $listGroup = Group::select('id')->where('list_group.is_virtual', 0)
                ->where('group_name', 'LIKE', "%$search%");
                $listGroup = $listGroup->get('id');
                if (count($listGroup) != 0) {
                    $listId = array_column($listGroup->toArray(), 'id');
                }
                
                if ($listId != null) {
                    $query->whereIn('object_id', $listId);
                }
            }

            $listField = [
                'actor',
                'relation_login',
                'description'
            ];

            $query->where(function ($q) use ($listField, $search) {
                $operator = 'like';
                foreach($listField as $field) {
                    $q->orWhere($field, $operator, '%' . trim($search) . '%');
                }
            });
        }
    }


    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {
        /**
         * Build query for datatable
         */
        $query = SystemLog::select([
            'system_log.id', 
            'system_log.actor', 
            'system_log.log_time', 
            'system_log.description', 
            'system_log.object_id', 
            'system_log.brief', 
            'system_log.from_ip', 
            'system_log.relation_login',
            'list_group.psubject_code as subject_code',
            'list_group.group_name'
        ])->leftJoin('list_group', 'list_group.id', '=', 'system_log.object_id');

        $datatable = DataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns)
        ->setTotalRecords(100)
        ->setFilteredRecords(1000000);

        // $datatable->addColumn('group_name', function($node) {
        //     $group = Group::select('id', 'group_name')->where('list_group.is_virtual', 0)
        //     ->where('id', $node->object_id)->first();
        //     if ($group) {
        //         return "<div>$group->group_name ($group->id)</div>";
        //     }

        //     return "";
        // });

        // $datatable->addColumn('subject_code', function($node) {
        //     return "<div>$group->psubject_code</div>";
        // });

        $datatable->addColumn('log_time', function($node) {
            $style = $node->brief != 'remove member' ? 'text-success' : 'text-danger';
            return "<b class='$style'>" . Carbon::createFromDate($node->log_time)->format("d/m/Y (H:i:s)") . '</b>';
        });

        $datatable->filter(function ($query) {
            return $this->filter($query);
        });

        return $datatable;
    }
}