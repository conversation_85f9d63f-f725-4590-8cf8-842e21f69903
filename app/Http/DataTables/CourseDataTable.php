<?php

namespace App\Http\DataTables;

use App\Models\Fu\Course;
use App\Models\Fu\Subject;
use App\Models\Fu\Term;
use App\Models\T7\SyllabusPlan;
use Yajra\DataTables\Facades\DataTables as FacadesDataTables;

class CourseDataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'lastmodifier_login', 
        'id', 
        'subject_id',
        'term_id', 
        'psubject_name',
        'psubject_code',
        'num_of_credit',
        'syllabus_id',
        'syllabus_code',
        'syllabus_name',
        'attendance_required',
        'grade_required',
        'pterm_name',
        'num_of_group',
        'is_started',
        'pull_insert',
        'pull_last_update',
        'course_name',
        'group_number',
        'is_vhpt'
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
    ];


    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        $input = request()->all();
        if(!empty($input['term_id']) && $input['term_id'] != null) {
            $query->where('term_id', $input['term_id']);
        }else{
            $term_now = Term::whereRaw('startday <= CURRENT_DATE')
            ->whereRaw('endday >= CURRENT_DATE')
            ->first();
            if (!$term_now ) {
                $term_now = Term::orderBy('id', 'desc')->first();
            }
            
            $query->where('term_id', $term_now->id);
        }

        if(!empty($input['department_id'])  && $input['department_id'] != null) {
            $subject_ids = Subject::where('department_id', $input['department_id'])->pluck('id')->toArray();
            $query->whereIn('subject_id', $subject_ids);
        }

        // chọn buổi học
        if(!empty($input['seesion_type'])  && $input['seesion_type'] != null) {
            // buổi học online
            if($input['seesion_type'] != 'offline') {
                // lấy tất cả slyllabus_id
                $syllabus_ids = SyllabusPlan::where('session_type', $input['seesion_type'])->pluck('syllabus_id')->toArray();
                $syllabus_ids = array_unique($syllabus_ids);

                $query->whereIn("syllabus_id", $syllabus_ids);
            }else{
                $syllabus_ids = SyllabusPlan::where('session_type', '!=' ,18)->pluck('syllabus_id')->toArray();
                $syllabus_ids = array_unique($syllabus_ids);

                $query->whereIn("syllabus_id", $syllabus_ids);
            }
        }

        // Check for filter:search
        if ($search = request()->input('search.value')) {
            $field = 'name';
            $listField = [
                'course.psubject_name',
                'course.psubject_code',
                'course.syllabus_name',
                'course.pterm_name',
            ];

            $query->where(function ($q) use ($listField, $field, $search) {
                $field = trim($field);
                $operator = 'like';
                foreach($listField as $field) {
                    $q->orWhere($field, $operator, '%' . trim($search) . '%');
                }
            });
        }

        $query->orderBy('id', 'desc');
    }


    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {
        $query = Course::query();

        $datatable = FacadesDataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns);
        $datatable->addColumn('course_name', function($data) {
            $html = "{$data->psubject_name} {$data->pterm_name}";
            
            // $data->psubject_name.' '.$data->pterm_name;
            return $html;
        });

        $datatable->addColumn('group_number', function($data) {
            $group_number = count($data->groups);
            return $group_number;
        });

        $datatable->filter(function ($query) {
            return $this->filter($query);
        });
        return $datatable;
    }
}