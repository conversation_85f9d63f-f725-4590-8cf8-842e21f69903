<?php

namespace App\Http\DataTables;

use App\Models\Fu\Block;

use Illuminate\Support\Facades\DB;
use App\Models\Fu\Feedback;
use App\Models\Fu\GroupMember;
use App\Models\Fu\Term;
use Illuminate\Support\Carbon;
use Ya<PERSON>ra\DataTables\DataTables;

class FeedbackBlockDataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'stt',
        'group_id',
        'student_login',
        'group_name',
        'is_reviewed',
        'term_name',
        'term_id',
        'block_id',
        'block_name',
        'start_date',
        'end_date',
        'subject_name',
        'subject_code',
        'evaluation_norm',
        'teacher_login',
        'group_fb_comment',
        'teacher_surname',
        'teacher_middlename',
        'teacher_givenname',
        'student_user_code',
        'student_surname',
        'student_middlename',
        'student_givenname',
        'department_name',
        'note',
        'evaluation_note',
        'time_evaluate',
        'teacher_full_name',
        'tieu_chi'
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
        'teacher_login',
        'group_fb_comment',
        'time_evaluate',
        'is_reviewed'
    ];


    /**
    * Build datatable filter query
    */
    public function filter($query_GroupMember)
    {
        // Check for filter:search
        if ($search = request()->input('search.value')) {
            $field = 'name';
            $listField = [
                'list_group.group_name',
                'group_member.member_login'
            ];

            $query_GroupMember->where(function ($q) use ($listField, $field, $search) {
                $field = trim($field);
                $operator = 'like';
                foreach($listField as $field) {
                    $q->orWhere($field, $operator, '%' . trim($search) . '%');
                }
            });
        }
    }


    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {     
        $teacher_login = isset(request()->inputTeacher) ? request()->inputTeacher : null;
        $term_id = isset(request()->inputSemester) ? request()->inputSemester : null;
        $department_id = isset(request()->inputDepartment) ? request()->inputDepartment : null;
        $is_review = isset(request()->inputIsReviewChecked) ? request()->inputIsReviewChecked : null;
        $query_GroupMember = GroupMember::query()->select([
            'list_group.id as group_id', 
            'group_member.member_login as student_login', 
            'list_group.group_name as group_name', 
            'list_group.danh_gia as is_reviewed', 
            'list_group.pterm_name as term_name', 
            'list_group.pterm_id as term_id',
            'list_group.block_id as block_id',
            'list_group.block_name as block_name',
            'list_group.start_date as start_date',
            'list_group.end_date as end_date',
            'list_group.psubject_name as subject_name', 
            'list_group.psubject_code as subject_code', 
            'group_member.loai as evaluation_norm', 
            'list_group.teacher as teacher_login', 
            'list_group.group_fb_comment as group_fb_comment',
            'student.user_code as student_user_code', 
            'student.user_surname as student_surname', 
            'student.user_middlename as student_middlename', 
            'student.user_givenname as student_givenname', 
            'department.department_name', 
            'group_member.note as note', 
            'group_member.note_comment as evaluation_note',
            'group_member.time_evaluate as time_evaluate'
        ]);
        $query_GroupMember->leftjoin('list_group', 'list_group.id', '=', 'group_member.groupid');
        $query_GroupMember->leftjoin('user as teacher', 'teacher.user_login', '=', 'group_member.leader_login');
        $query_GroupMember->leftjoin('user as student', 'student.user_login', '=', 'group_member.member_login');
        $query_GroupMember->leftjoin('department', 'department.id', '=', 'list_group.department_id');  
        
        if($teacher_login != null && $teacher_login != '')
        {
            $query_GroupMember->where('list_group.teacher', $teacher_login);
        }

        if($term_id)
        {
            $query_GroupMember->where('list_group.pterm_id', $term_id);
        }

        if($department_id)
        {
            $query_GroupMember->where('list_group.department_id', $department_id);
        }
        if ($is_review != null && $is_review != '') {
            switch ($is_review) {
                case 0:
                    $query_GroupMember->where('list_group.danh_gia', '=', 0);
                    break;
                case 1:
                    $query_GroupMember->where('list_group.danh_gia', '>', 0);
                    break;
                default:
                    break;
            }
        }
        
        $query_GroupMember = $query_GroupMember->orderBy('group_member.groupid', 'desc');
        $datatable = DataTables::of($query_GroupMember);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns)
        ->setTotalRecords(100);
        
        /** ================== format Column ===================== */
        $datatable->addColumn('teacher_login', function($node) {
            $teacher_login = $node->teacher_login ?? "";
            return $teacher_login;
        });
        
        $datatable->addColumn('is_reviewed', function($node) {
            $html = "<div >";
            if($node->is_reviewed > 0) {
                $html .= "<p style='color: green'>Đã được đánh giá</p>";
            } else {
                $html .= "<p style='color: red'>Chưa được đánh giá</p>";
            }
            $html .= "</div>";
            
            return $html;
        });
        
        $datatable->addColumn('group_fb_comment', function($node) {
            $group_fb_comment = $node->group_fb_comment ?? "";
            return $group_fb_comment;
        }); 

        $datatable->addColumn('time_evaluate', function($node) {
            return $node->time_evaluate == '1970-01-01' ? "" : $node->time_evaluate;
        });
        
        /** ================== Sort ===================== */
        $datatable->filter(function ($query) {
            return $this->filter($query);
        });

        return $datatable;
    }
}