<?php

namespace App\Http\DataTables;

use App\Models\Fu\Group;
use App\Models\Fu\Term;
use App\Models\GroupGraduate;
use Carbon\Carbon;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;

class GroupEosDataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'group_id',
        'group_name',
        'skill_code',
        'psubject_code',
        'group_time',
        'number_student',
        'number_student_fail',
        'rotio_fail',
        'action'
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
        'group_name',
        'group_time',
        'rotio_fail',
        'subject_info',
        'action',
    ];


    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        $subjectCode = request()->input('subject_code', null);
        $termId = request()->input('term_id', null);
        if ($termId != null) {
            $query = $query->where('list_group.pterm_id', $termId);
        } else {
            $lastTerm = Term::orderBy('id', 'DESC')->first();
            $query = $query->where('list_group.pterm_id', $lastTerm->id);
        }
        
        if ($subjectCode != null && $subjectCode != -1) {
            $query->where('list_group.psubject_code', $subjectCode);
        }
        
        if ($search = request()->input('search.value')) {
            $listField = [
                'list_group.skill_code',
                'list_group.psubject_code',
                'list_group.group_name',
                'list_group.id'
            ];

            $query->where(function ($q) use ($listField, $search) {
                $operator = 'like';
                foreach($listField as $field) {
                    $q->orWhere($field, $operator, ('%' . trim($search) . '%'));
                }
            });
        }
    }


    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {
        $query = Group::select([
            'group_eos.group_id',
            'list_group.skill_code',
            'list_group.psubject_code',
            'list_group.group_name',
            'list_group.start_date',
            'list_group.end_date',
            DB::raw('COUNT(group_member.id) AS `number_student`'),
            // DB::raw('COUNT(group_graduate.id) AS `total_student_in_schedule`'),
            DB::raw('SUM(CASE WHEN group_eos.`status` > 0 THEN 1 ELSE 0 END) AS `number_student_fail`'),
        ])
        ->join('group_eos', 'list_group.id', '=','group_eos.group_id')
        ->join('group_member', function ($q) {
            $q->on('list_group.id', '=','group_member.groupid')
            ->on('group_member.member_login', '=','group_eos.student_login');
        })
        ->where('is_virtual', 0)
        ->groupBy('list_group.id');


        $datatable = DataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns)
        ->setTotalRecords(1000000);

        $datatable->addColumn('group_time', function($node) {
            return "<span>$node->start_date - $node->end_date</span>";
        });

        $datatable->addColumn('rotio_fail', function($node) {
            if ($node->number_student == 0) {
                return "0 %";
            };

            return round(($node->number_student_fail / $node->number_student) * 100, 2) . " %";
        });
        
        
        $datatable->editColumn('group_name', function($node) {
            return "<a target='_blank' href=" . route('admin.group.index.edit', ['id' => $node->group_id]) . ">$node->group_name</a>";
        });

        $datatable->addColumn('action', function($node) {
            return "<button class=\"btn btn-primary btn-sm btn-sync-group \" data-id=\"$node->group_id\" style=\"width: 100px;\">Cập nhập</button>";
        });

        $datatable->filter(function ($query) {
            return $this->filter($query);
        });

        return $datatable;
    }
}