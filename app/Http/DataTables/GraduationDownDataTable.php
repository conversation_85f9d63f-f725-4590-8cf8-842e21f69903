<?php

namespace App\Http\DataTables;

use Auth;
use DataTables;


use App\User;
use App\Models\Fu\Decision;
use App\Models\Fu\DecisionUser;
use App\Models\Fu\GraduationCampaign;
use App\Models\Iaps\GraduationUserRank;
use App\Repositories\Admin\DecisionRepository;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables as FacadesDataTables;

use function MongoDB\select_server;

class GraduationDownDataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'id',
        'user_code',
        'user_login',
        'type',
        'note',
        'rank_id',
        'rank_old_id',
        'is_downgrade',
        'lock_sync',
        'rank_name',
        'rank_name_old',
        'term_id',
        'term_name',
        'grade_create',
        'full_name',
        'percented_relearn',
        'reason_downgrade',
        'decision_num',
        'hinh_thuc_ky_luat_name',
        'decision_description',
        'note',
        'is_downgrade_text'
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
        'percented_relearn',
        'is_downgrade_text'
    ];


    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        $input = request()->all();

        // Check for filter:search
        if ($search = request()->input('search.value')) {
            $field = 'name';
            $listField = [
                'fu_graduation_user_rank.user_code'
            ];

            $query->where(function ($q) use ($listField, $field, $search) {
                $field = trim($field);
                $operator = 'like';
                foreach($listField as $field) {
                    $q->orWhere($field, $operator, '%' . trim($search) . '%');
                }
            });
        }

        $query->orderBy('id', 'desc');
    }


    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {
        $query = GraduationUserRank::query();
        $campaignId = request('campaign_id');
        $graduationCampaign = GraduationCampaign::where('id', $campaignId)->first();

        $query->select([
                'fu_graduation_user_rank.id',
                'fu_graduation_user_rank.user_code',
                'fu_graduation_user_rank.user_login',
                'fu_graduation_user_rank.type',
                'fu_graduation_user_rank.note',
                'fu_graduation_user_rank.rank_id',
                'fu_graduation_user_rank.rank_old_id',
                'fu_graduation_user_rank.reason_downgrade',
                'fu_graduation_user_rank.is_downgrade',
                'fu_graduation_user_rank.lock_sync',
                'fu_graduation_rank.name as rank_name',
                'fu_graduation_rank_old.name as rank_name_old',
                'fu_graduation_campaign_users.percented_relearn'
            ])
            ->with(['user'])
            ->join('fu_graduation_campaign_users', 'fu_graduation_campaign_users.user_code', '=', 'fu_graduation_user_rank.user_code')
            ->join('fu_graduation_rank', 'fu_graduation_rank.id', '=', 'fu_graduation_user_rank.rank_id')
            ->join('fu_graduation_rank as fu_graduation_rank_old', 'fu_graduation_rank_old.id', '=', 'fu_graduation_user_rank.rank_old_id')
            // ->join('fu_user', 'fu_user.user_code', '=', 'fu_graduation_user_rank.user_code')
            ->where(function($q) {
                $q->where('fu_graduation_user_rank.is_downgrade', 1)
                ->orWhere('fu_graduation_user_rank.lock_sync', 1);
            })
            ->where('fu_graduation_campaign_users.campaign_id', $campaignId);

        $datatable = FacadesDataTables::of($query);
        $listUserCode = $datatable->getQuery()->pluck('user_code')->toArray();
        // lấy danh sách quyết định và danh sách sinh viên dựa vào mã sinh viên
        [$listDecisionUser, $users]= call_user_func(function () use ($graduationCampaign, $listUserCode) {
            $res = [];
            $decisionUser = DecisionUser::on($graduationCampaign->campus_code)
                ->select([
                    'fu_decision_user.*',
                    'fu_term.term_name',
                    'fu_decision.decision_num'
                ])
                ->join('fu_decision', 'fu_decision.id', '=', 'fu_decision_user.decision_id')
                ->join('fu_term', 'fu_term.id', '=','fu_decision.term_id')
                ->where('fu_decision.type', 28)
                ->whereIn('user_code', $listUserCode)
                ->get();
            foreach ($decisionUser as $value) {
                $res[$value->user_code] = $value;
            }

            $listUser = User::on($graduationCampaign->campus_code)
                ->whereIn('user_code', $listUserCode)
                ->get();
            return [$res, $listUser];
        });

        $datatable->only($this->only);
        $datatable->rawColumns($this->columns);
        // format Data
        $datatable->addColumn('term_name', function($node) use ($listDecisionUser){
            if(!isset($listDecisionUser[$node->user_code])) {
                return "";
            }

            return $listDecisionUser[$node->user_code]->term_name;
        });

        $datatable->addColumn('decision_num', function($node) use ($listDecisionUser){
            if(!isset($listDecisionUser[$node->user_code])) {
                return "";
            }

            return $listDecisionUser[$node->user_code]->decision_num;
        });

        $datatable->addColumn('decision_description', function($node) use ($listDecisionUser){
            if(!isset($listDecisionUser[$node->user_code])) {
                return "";
            }

            return $listDecisionUser[$node->user_code]->decision_description;
        });

        $datatable->addColumn('hinh_thuc_ky_luat_name', function($node) use ($listDecisionUser){
            if(!isset($listDecisionUser[$node->user_code])) {
                return "";
            }

            return $listDecisionUser[$node->user_code]->hinh_thuc_ky_luat;
        });

        $datatable->addColumn('grade_create', function($node) use ($users) {
            $user = $users->where('user_code', $node->user_code)->first();
            if (!$user) {
                return "";
            }

            return $user->grade_create;
        });

        $datatable->addColumn('full_name', function($node) use ($users) {
            $user = $users->where('user_code', $node->user_code)->first();
            if (!$user) {
                return "";
            }

            return "{$user->user_surname} {$user->user_middlename} {$user->user_givenname}";
        });

        $datatable->editColumn('percented_relearn', function($node) use ($users) {
            return "<div class='text-right'>" . round($node->percented_relearn, 1) . "%</div>";
        });

        $datatable->addColumn('is_downgrade_text', function($node) use ($users) {
            $text = "";

            if($node->is_downgrade == 1) {
                $text = "Đã hạ loại";
            }

            return "<div class='text-right'>$text</div>";
        });

        $datatable->filter(function ($query) {
            return $this->filter($query);
        });


        return $datatable;
    }
}
