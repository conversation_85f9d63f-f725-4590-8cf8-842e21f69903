<?php

namespace App\Http\DataTables;

use Auth;
use DataTables;

use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

use App\Models\Fu\User;
use App\Models\SystemLog;
use App\Models\Fu\Feedback;
use App\Models\Fu\FeedbackDetail;
use App\Models\Fu\Group;
use App\Models\Fu\Term;



class ManagementStudentDataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'id', 
        'actor', 
        'log_time', 
        'description', 
        'subject_code', 
        'group_name', 
        'brief', 
        'from_ip', 
        'relation_login',
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
        'group_name', 
        'subject_code', 
        'log_time'
    ];


    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        $author = request()->input('author', null);
        $subjectCode = request()->input('subject_code', null);
        $relationLogin = request()->input('relation_login', null);
        $termId = request()->input('term_id', null);
        
        if ($author != null && $author != -1) {
            $query->where('actor', $author);
        }
        
        if ($relationLogin != null && $relationLogin != -1) {
            $query->where('relation_login', $relationLogin);
        }
        
        if ($subjectCode != null && $subjectCode != -1) {
            $listId = null;
            $listGroup = Group::select('id')->where('psubject_code', $subjectCode)->where('list_group.is_virtual', 0);
            $search = request()->input('search.value');
            if ($termId != null) {
                $listGroup = $listGroup->where('pterm_id', $termId);
            }

            $listGroup = $listGroup->get('id');
            if (count($listGroup) != 0) {
                $listId = array_column($listGroup->toArray(), 'id');
            }
            
            if ($listId != null) {
                $query->whereIn('object_id', $listId);
            }
        }
        
        if ($search = request()->input('search.value')) {
            if (trim($search) != '') {
                $listId = null;
                $listGroup = Group::select('id')->where('list_group.is_virtual', 0)
                ->where('group_name', 'LIKE', "%$search%");
                $listGroup = $listGroup->get('id');
                if (count($listGroup) != 0) {
                    $listId = array_column($listGroup->toArray(), 'id');
                }
                
                if ($listId != null) {
                    $query->whereIn('object_id', $listId);
                }
            }
        }
    }


    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {
        /**
         * Build query for datatable
         */
        $query = SystemLog::select([
            'id', 
            'actor', 
            'log_time', 
            'description', 
            'object_id', 
            'brief', 
            'from_ip', 
            'relation_login'
        ])->whereIn('brief', ['remove member', 'add member', 'change member']);

        $datatable = DataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns)
        ->setTotalRecords(100)
        ->setFilteredRecords(1000000);

        $datatable->addColumn('group_name', function($node) {
            $group = Group::select('id', 'group_name')->where('list_group.is_virtual', 0)
            ->where('id', $node->object_id)->first();
            if ($group) {
                return "<a target='_blank' href=" . route('admin.group.index.edit', ['id' => $group->id]) . ">$group->group_name ($group->id)</a>";
            }

            return "<div>Không tồn tại lớp có id: $node->object_id</div>";
        });

        $datatable->addColumn('subject_code', function($node) {
            $group = Group::select('psubject_code', 'group_name')->where('list_group.is_virtual', 0)
            ->where('id', $node->object_id)->first();
            if ($group) {
                return "<a href=" . route('admin.group.index.edit', ['id' => $node->object_id]) . ">$group->psubject_code</a>";
            }

            return "<div>Không xác định</div>";
        });

        $datatable->addColumn('log_time', function($node) {
            $style = $node->brief != 'remove member' ? 'text-success' : 'text-danger';
            return "<b class='$style'>" . Carbon::createFromDate($node->log_time)->format("d/m/Y (H:i:s)") . '</b>';
        });

        $datatable->filter(function ($query) {
            return $this->filter($query);
        });

        return $datatable;
    }
}