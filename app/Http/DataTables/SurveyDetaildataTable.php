<?php

namespace App\Http\DataTables;

use App\Models\Dra\SurveyStudent;
use Illuminate\Support\Facades\DB;

use Yajra\DataTables\Facades\DataTables;

class SurveyDetaildataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'id',
        'user_code',
        'user_login',
        'status',
        'created_at',
        'updated_at'
    ];

    /**
     * DataTable raw columns
     */
    protected $columns =[
        'status'
    ];

    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        $survey_id = request()->input('survey_id', null);

        // check theo survey
        if ($survey_id != null && $survey_id != -1) {
            $query->where('survey_student.survey_id', $survey_id);
        }
        
        // Check for filter:search
        if ($search = request()->input('search.value')) {
            $field = 'name';
            $listField = [
                'survey_student.user_code',
                'survey_student.user_login',
            ];

            $query->where(function ($q) use ($listField, $field, $search) {
                $field = trim($field);
                $operator = 'like';
                foreach($listField as $field) {
                    $q->orWhere($field, $operator, '%' . trim($search) . '%');
                }
            });
        }
    }

    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {
        /**
         * Build query for datatable
         */

        $query = SurveyStudent::select([
            'survey_student.id',
            'survey_student.survey_id',
            'survey_student.user_code',
            'survey_student.user_login',
            'survey_student.status',
            DB::raw("DATE_FORMAT(survey_student.created_at, '%d\/%m\/%Y') as created_at"),
            DB::raw("DATE_FORMAT(survey_student.updated_at, '%d\/%m\/%Y') as updated_at"),
        ])
        ->join('survey', 'survey.id', '=', 'survey_student.survey_id')
        ->orderBy('survey_student.id', 'DESC');

        $datatable = DataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns);


        /** ================== format Column ===================== */

        $datatable->editColumn('status', function($node) {
            if($node->status == 1){
                return "<b><p class='text-success'>Đã làm survey</p></b>";
            } else {
                return "<b><p class='text-danger'>Chưa làm survey</p></b>";
            }
        });
        
        $datatable->filter(function ($query) {
            return $this->filter($query);
        });

        return $datatable;
    }
}