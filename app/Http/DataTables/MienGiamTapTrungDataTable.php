<?php

namespace App\Http\DataTables;

use Auth;
use DataTables;

use Illuminate\Support\Facades\DB;

use App\Models\MienGiamTapTrung;
use Yajra\DataTables\Facades\DataTables as FacadesDataTables;

class MienGiamTapTrungDataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'id',
        'student_login',
        'subject_code',
        'skill_code',
        'subject_code_new',
        'skill_code_new',
        'type',
        'so_quyet_dinh',
        'full_name',
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
        'id',
        'student_login',
        'subject_code',
        'skill_code',
        'subject_code_new',
        'skill_code_new',
        'type',
        'so_quyet_dinh',
        'full_name'
    ];


    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        $type = request()->input('_type', null);
        $student_login = request()->input('student_login', null);
        $subject_code = request('subject_code');
        $subject_code_new = request('subject_code_new');
        if ($type != null && $type != -1) {
            $query->where('type', $type);
        }

        // Check for filter:search
        if ($search = request()->input('search.value')) {
            $field = 'name';
            $listField = [
                'mien_giam_tap_trung.student_login',
                'mien_giam_tap_trung.subject_code',
                'mien_giam_tap_trung.skill_code',
                'mien_giam_tap_trung.subject_code_new',
                'mien_giam_tap_trung.skill_code_new',
            ];
            
            $query->where(function ($q) use ($listField, $field, $search) {
                $field = trim($field);
                $operator = 'like';
                $q->orWhere(DB::raw('CONCAT(u.user_surname," ",u.user_middlename," ", u.user_givenname)'), 'like', "%$search%");
                foreach($listField as $field) {
                    $q->orWhere($field, $operator, '%' . trim($search) . '%');
                }
            });
        }

        if (request()->has('search.value') && !empty(request()->input('search.value'))) {
        }

        if($student_login) {
            // $query->where('student_login', $student_login);
            $student_login = explode(',',$student_login);
            $query->where(function ($q) use ($student_login) {
                $q->whereIn('student_login', $student_login);
            }); 
        }

        if($subject_code) {
            $subject_code = explode(',',$subject_code);
            if (is_array($subject_code)) {
                $query->whereIn('subject_code', $subject_code);
            } else {
                $query->where('subject_code', 'like', "%$subject_code%");
            }
        }

        if($subject_code_new) {
            $subject_code_new = explode(',', $subject_code_new);
            if (is_array($subject_code_new)) {
                $query->whereIn('subject_code_new', $subject_code_new);
            } else {
                $query->where('subject_code_new', 'like', "%$subject_code_new%");
            }
        }
    }


    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build($input = [])
    {
        // $input['limit'] = $input['length'] ?? 10;
        // $input['page'] = $input['draw'] ?? 1;
        $query = MienGiamTapTrung::query()
            ->select([
            'mien_giam_tap_trung.id',
            'mien_giam_tap_trung.student_login',
            'mien_giam_tap_trung.subject_code',
            'mien_giam_tap_trung.skill_code',
            'mien_giam_tap_trung.subject_code_new',
            'mien_giam_tap_trung.skill_code_new',
            'mien_giam_tap_trung.so_quyet_dinh',
            'mien_giam_tap_trung.type',
            DB::raw('CONCAT(u.user_surname," ",u.user_middlename," ", u.user_givenname) as full_name')
        ])
            ->leftJoin("user as u", 'mien_giam_tap_trung.student_login', '=', 'u.user_login');
            // dd($query->get());

        $datatable = FacadesDataTables::of($query);
        // $datatable->rawColumns($this->columns)
        // ->setTotalRecords(100);

        $datatable->addColumn('full_name', function($node){
            return $node->full_name;
        });

        $datatable->editColumn('subject_code_new', function($node){
            if ($node->subject_code_new == '' || $node->subject_code_new == null) {
                return '-';
            }

            return $node->subject_code_new;
        });

        $datatable->editColumn('skill_code_new', function($node){
            if ($node->skill_code_new == '' || $node->skill_code_new == null) {
                return '-';
            }

            return $node->skill_code_new;
        });
        
        $datatable->editColumn('type', function($node){
            if($node->type == 1) {
                return "Thay thế";
            }

            if($node->type == 2) {
                return 'Miễn Giảm';
            }

            if($node->type == 3) {
                return 'Tốt nghiệp';
            }

            return '';
        });

        $datatable->filter(function ($query) {
            return $this->filter($query);
        });

        return $datatable->make(true);
    }

}