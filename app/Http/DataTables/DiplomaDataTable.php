<?php

namespace App\Http\DataTables;

use App\Models\Fu\Graduation;
use App\Models\Fu\GraduationCampaignUser;
use Auth;
use DataTables;

use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

use App\Models\Fu\User;
use Yajra\DataTables\Facades\DataTables as FacadesDataTables;

class DiplomaDataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'id',
        'user_code',
        'user_login',
        'so_bang',
        'full_name',
        'is_van_bang'
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
        'is_van_bang'
    ];


    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        $input = request()->all();
        
        if(isset($input['curiculumn_id']) && $input['curiculumn_id'] != 0) {
            $query->where('curriculum_id', $input['curiculumn_id']);
        }

        if(isset($input['study_status'])) {
            $query->where('study_status', (int) $input['study_status']);
        }

        if(isset($input['is_van_bang'])) {
            if($input['is_van_bang'] == 1) {
                $query->whereNotNull('so_bang');
            }else if ($input['is_van_bang'] == 0) {
                $query->whereNull('so_bang');              
            }

        }

        if(isset($input['graduation_campaign']) && count($input['graduation_campaign']) > 0) {
            $graduation_campaign_users = GraduationCampaignUser::select([
                'campaign_id',
                'user_code',
                'user_login'
            ])->whereIn('campaign_id', $input['graduation_campaign'])->get();

            $campaign_user_code = $graduation_campaign_users->pluck('user_code')->toArray();

            $query->whereIn('user_code', $campaign_user_code);
        }

        // Check for filter:search
        if ($search = request()->input('search.value')) {
            $field = 'name';
            $listField = [
                'user.user_login',
                'user.user_code',
                'user.so_bang',
            ];

            $query->where(function ($q) use ($listField, $field, $search) {
                $field = trim($field);
                $operator = 'like';
                $q->orWhere(DB::raw('CONCAT(user_surname," ",user_middlename," ", user_givenname)'), 'like', "%$search%");
                foreach($listField as $field) {
                    $q->orWhere($field, $operator, '%' . trim($search) . '%');
                }
            });
        }

        $query->orderBy('id', 'desc');
    }


    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {
        $query = User::query();

        $query->select([
            'id',
            'user_code',
            'user_login',
            'so_bang',
            'user_surname',
            'user_middlename',
            'user_givenname',
            DB::raw('CONCAT(TRIM(user.user_surname)," ", TRIM(user.user_middlename)," ",TRIM(user.user_givenname)) as full_name')
        ])->where('user_level', 3);

        $datatable = FacadesDataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns);

        $datatable->addColumn('is_van_bang', function($data) {
            $html = "";

            if($data->so_bang == null) {
                $html = '<p class="text-danger font-weight-bold">Chưa có bằng<p/>';
            }else {
                $html = '<p class="text-success font-weight-bold">Đã có bằng<p/>';
            }
            
            // $data->psubject_name.' '.$data->pterm_name;
            return $html;
        });

        $datatable->addColumn('full_name', function($data) {
            $html = $data->fullname();
            
            // $data->psubject_name.' '.$data->pterm_name;
            return $html;
        });

        $datatable->filter(function ($query) {
            return $this->filter($query);
        });
        return $datatable;
    }
}