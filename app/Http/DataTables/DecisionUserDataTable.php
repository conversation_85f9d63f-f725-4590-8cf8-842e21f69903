<?php

namespace App\Http\DataTables;

use Illuminate\Support\Facades\DB;

use App\User;
use App\Models\Fu\Decision;
use App\Models\Fu\DecisionUser;
use App\Models\Fu\Term;
use Yajra\DataTables\Facades\DataTables;

class DecisionUserDataTable extends DataTable
{
    const ROLE_TP_CTSV = 49;
    /**
     * DataTable only columns
     */
    protected $only = [
        '_id',
        'user_code',
        'full_name',
        'type',
        'term_name',
        'decision_num',
        'signer',
        'sign_day',
        'file',
        'action',
        'hinh_thuc_ky_luat_name',
        'decision_description',
        'created_by',
        'updated_at'
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
        'id',
        'user_code',
        'file',
        'action',
        'hinh_thuc_ky_luat_name'
    ];


    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        $termCheck = request()->input('term_id', null);
        $decisionType = request()->input('decision_type', null);
        $decisionNum = request()->input('decision_num', null);
        if ($termCheck != null && $termCheck != -1) {
            $query->where('term_id', $termCheck);
        }

        if ($decisionType != null && $decisionType != -1) {
            $query->where('type', $decisionType);
        }

        if ($decisionNum != null && $decisionNum != -1) {
            $query->where('decision.id', $decisionNum);
        }

        // Check for filter:search
        if ($search = request()->input('search.value')) {
            $field = 'name';
            $listField = [
                'decision_user.user_code',
                'full_name',
                'decision.decision_num',
                'decision.signer',
            ];

            $query->where(function ($q) use ($listField, $field, $search) {
                $field = trim($field);
                $search = '%' . trim($search) . '%';
                $operator = 'like';
                foreach($listField as $field) {
                    if ($field == 'full_name') {
                        $q->orWhere(DB::raw('CONCAT(user_surname," ",user_middlename," ", user_givenname)'), $operator, $search);
                    } else {
                        $q->orWhere($field, $operator, $search);
                    }
                }
            });
        }
    }


    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {

        /**
         * Build query for datatable
         */
        $query = DecisionUser::select([
            'decision_user.id',
            'decision_user.user_code',
            'decision_user.hinh_thuc_ky_luat',
            'decision_id',
            DB::raw('CONCAT(user_surname," ",user_middlename," ", user_givenname) as full_name'),
            'decision.name', // Tên quyết định
            'decision.decision_num', // số quyết định
            'decision.term_id', // Kỳ triển khai
            'decision.from', // loại quyết định từ CĐ hay ĐH
            'decision.type', // Loại
            'decision.signer', // Người ký
            'decision.sign_day', // Ngày ký
            'decision.effective_time', // Thời bắt đầu có hiệu lực
            'decision.file', // File Quyết định
            'decision.file_status', //  Trạng thái file
            'decision.note', // ghi chú,
            'decision_user.decision_description', // nội dung kỷ luật,
            'decision.created_by',
            'decision_user.updated_at'
        ])->leftJoin('decision', 'decision.id', '=', 'decision_user.decision_id')
        ->join('user', 'user.user_code', '=', 'decision_user.user_code');

        // init data
        $terms = Term::all();
        $roles = session('roles');
        $currentUser = auth()->user();
        $decisions = array_combine(Decision::pluck('id')->toArray(), Decision::all()->toArray());
        $datatable = DataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns);

        // format Data
        $datatable->addColumn('_id', function(DecisionUser $node) { return $node->id; });
        $datatable->addColumn('full_name', function(DecisionUser $node) {
            return $node->full_name ?? User::getFullnameByCode($node->user_code);
        });

        $datatable->addColumn('type', function(DecisionUser $node) {
            return (Decision::TYPES[$node->type]['name'] ?? "");
        });
        
        $datatable->addColumn('updated_at', function(DecisionUser $node) {
            return $node->updated_at->format('d/m/Y H:i:s');
        });

        $datatable->addColumn('term_name', function(DecisionUser $node) use ($decisions, $terms) {
            $decision = $decisions[$node->decision_id];
            $realTerm = null;
            foreach ($terms as $term) {
                if ($term->id == $decision['term_id']) $realTerm = $term;
            }

            return ($realTerm->term_name ?? "");
        });

        $datatable->addColumn('decision_num', function(DecisionUser $node) use ($decisions) {
            $decision = $decisions[$node->decision_id];
            return $decision['decision_num'];
        });

        $datatable->addColumn('signer', function(DecisionUser $node) use ($decisions) {
            $decision = $decisions[$node->decision_id];
            return $decision['signer'];
        });

        $datatable->addColumn('sign_day', function(DecisionUser $node) use ($decisions) {
            $decision = $decisions[$node->decision_id];
            return $decision['sign_day'];
        });


        $datatable->addColumn('hinh_thuc_ky_luat_name', function(DecisionUser $node) {
            return $node->hinh_thuc_ky_luat ?? '';
        });

        $datatable->addColumn('decision_description', function(DecisionUser $node) {
            return $node->decision_description ?? '';
        });

        $datatable->addColumn('file', function(DecisionUser $node) use ($decisions) {
            $decision = $decisions[$node->decision_id];
            return '<a target="_blank" href="' . asset(('storage/decision/' . (session('campus_db') ?? 'ho')) . '/' .$decision['file']) . '">' . $decision['file'] . '</a>';
        });

        $datatable->addColumn('action', function(DecisionUser $node) use ($currentUser){
            $html = "";
            if ($currentUser->user_login == $node->created_by || $currentUser->user_level == 1) {
                $html .= "<button type='button' data-id='$node->id' data-code='$node->user_code' class='btn btn-danger btn-remove-user w-100 mb-2'>
                    Rút sinh viên
                </button>";
            } 
            // chỉ hiện nút với các type hạ loại (kỷ luật)
            if($node->type == 28) {
                $html .= "<button type='button' data-id='$node->id' data-code='$node->user_code' class='btn btn-primary'>Xem chi tiết</button>";
            }
            return $html;
        });

        $keyword = request()->input('search.value');
        $datatable->filter(function ($query) {
            return $this->filter($query);
        });

        return $datatable;
    }

}
