<?php

namespace App\Http\DataTables;

use Auth;
// use DataTables;
use Yajra\DataTables\Facades\DataTables;

use App\Models\Fu\User;
use App\Models\Fee\FeeMail;



class FeeMaildataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'id',
        'user_code',
        'type_student',
        'brand_code',
        'term_name',
        'ki_thu',
        'study_status',
        'study_wallet',
        'hoc_ky',
        'tien_sach',
        'tieng_anh',
        'amount',
        'english_level',
        'status_fee',
        'is_locked',
        // 'action',
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
        'status_fee',
        // 'action',
        'is_locked',
        'type_student',
        'user_code',
    ];
 
    protected $status = [

    ];

    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        $termCheck = request()->input('term_id', null);
        $statusFee = request()->input('status_fee', null);
        $typeStudent = request()->input('type_student', null);
        $brandCode = request()->input('brand_code', null);
        

        // check term
        if ($termCheck != null && $termCheck != -1) {
            $query->where('fee_mails.term_id', $termCheck);
        }

        if ($statusFee != null && $statusFee != -1) {
            $query->where('status_fee', $statusFee);
        }

        if ($typeStudent != null && $typeStudent != -1) {
            $query->where('type_student', $typeStudent);
        }

        if ($brandCode != null && $brandCode != -1) {
            $query->where('fee_mails.brand_code', $brandCode);
        }

        // Check for filter:search
        if ($search = request()->input('search.value')) {
            $field = 'name';
            $listField = [
                'fee_mails.user_code',
                'fee_mails.brand_code'
            ];

            $query->where(function ($q) use ($listField, $field, $search) {
                $field = trim($field);
                $operator = 'like';
                foreach($listField as $field) {
                    $q->orWhere($field, $operator, '%' . trim($search) . '%');
                }
            });
        }
    }


    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {
        /**
         * Build query for datatable
         */
        $query = FeeMail::select([
            'fee_mails.id', // ID 
            'fee_mails.user_code', // mã sinh viên
            'fee_mails.term_name', // Tên kỳ
            'fee_mails.brand_code', // Mã ngành
            'fees.study_wallet', // ví học phí
            'fee_mails.ki_thu', // Kỳ thu phí
            'fee_mails.hoc_ky', // Phí học kỳ
            'fee_mails.tien_sach', // phí sách
            'fee_mails.tieng_anh', // phí tiếng anh
            'fee_mails.amount', // Phí dự kiến
            'fee_mails.english_level', // Lv tiếng nah thu
            'fee_mails.status', // Trạng thái gửi mail
            'fee_mails.status_fee', // Trạng thái hoàn thành phí
            'fee_mails.type_student', // Loại sinh viên - Học đi, Chuyển ngành, Học Lại, Nhập Học từ đầu
            'fee_mails.is_locked', // Trạng thái chốt phí
            // 'fee_mails.note', // ghi chú
            'fee_mails.study_status',
        ])
        ->join('fees', 'fees.user_login', '=', 'fee_mails.user_login');

        $datatable = DataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns)
        ->setTotalRecords(100);

        // $datatable->setRowClass(function ($node) {
        //     return $node->status_fee == 1 ? 'alert-success' : '';
        // });

        /** ================== format Column ===================== */

        $datatable->addColumn('status_fee', function($node) {
            // return "<div class=\"custom-control custom-switch custom-switch-md\">
            //     <input data-id=\"$node->id\" type=\"checkbox\" class=\"custom-control-input change-status\" id=\"switch-$node->id\" " . ($node->status_fee == 1 ? "checked" : "") . ">
            //     <label class=\"custom-control-label\" for=\"switch-$node->id\"></label>
            // </div>";
            $statusFeeMail = $node->status_fee == 1 ? "Đã hoàn thành" : "Chưa hoàn thành";
            $classFeeMail = $node->status_fee == 1 ? "btn-label-success" : "btn-label-danger";
            return "<span class='btn btn-bold btn-sm btn-font-sm $classFeeMail'>
                    $statusFeeMail
                </span>";
        });

        $datatable->addColumn('is_locked', function($node) {
            $statusFeeMail = $node->is_locked == 1 ? "Đã chốt" : "Chưa chốt";
            $classFeeMail = $node->is_locked == 1 ? "btn-label-success" : "btn-label-danger";
            return "<span class='btn btn-bold btn-sm btn-font-sm $classFeeMail'>
                    $statusFeeMail
                </span>";
        });

        
        $datatable->editColumn('user_code', function($node) {
            return "<a href='" . route('admin.fee.feemail.detail', ['id' => $node->id]) . "' target='_blank'>$node->user_code</a>";
        });
        
        $datatable->addColumn('type_student', function($node) {
            $listType = FeeMail::LIST_TYPE_STUDENT;
            $style = null;
            switch ($node->type_student) {
                case 0:
                case 2:
                case 1:
                    $style = 'btn-label-primary';
                    break;
                case 3:
                case 8:
                    $style = 'btn-label-warning';
                    break;
                case 6:
                case 4:
                    $style = 'btn-label-danger';
                    break;
                case 5:
                    $style = 'btn-label-success';
                    break;
                case 7:
                case 9:
                    $style = 'btn-label-info';
                    break;
                default:
                    $style = 'btn-label-info';
                    break;
            }
            
            return "<span class='btn btn-bold btn-sm btn-font-sm $style'>" . $listType[$node->type_student] . "</span>";
        });
        
        
        // $datatable->addColumn('action', function($node) {
        //     // return "<button class=\"btn btn-primary sync-mail-by-id\" data-id=\"$node->id\" style=\"min-width: 100px;\">Đồng bộ</button><br>
        //     // <button class=\"btn btn-primary send-mail-by-id\" data-id=\"$node->id\" style=\"min-width: 100px; margin-top: 5px;\">Gửi mail</button>";
        //     return "<button class=\"btn btn-primary send-mail-by-id\" data-id=\"$node->id\" style=\"min-width: 100px; margin-top: 5px;\">Gửi mail</button>";
        // });
        
        $datatable->editColumn('hoc_ky', function($node) {
            return number_format($node->hoc_ky);
        });
        
        $datatable->editColumn('tien_sach', function($node) {
            return number_format($node->tien_sach);
        });
        
        $datatable->editColumn('tieng_anh', function($node) {
            return number_format($node->tieng_anh);
        });
        
        $datatable->editColumn('amount', function($node) {
            return number_format($node->amount);
        });
        
        $datatable->editColumn('study_wallet', function($node) {
            return number_format($node->study_wallet);
        });
        
        $datatable->editColumn('study_status', function($node) {
            return ($this->status[$node->study_status] ?? "Không xác định");
        });

        $datatable ->orderColumn('type_student', function ($query, $order) {
            $query->orderBy('fee_mails.type_student', $order);
        });

        // $datatable ->orderColumn('group', function ($query, $order) {
        //     $query->orderBy('group_name', $order);
        // });

        // $datatable ->orderColumn('subject', function ($query, $order) {
        //     $query->orderBy('psubject_code', $order);
        // });

        //     $query->orderBy('psubject_code', $order);
        $datatable->filter(function ($query) {
            return $this->filter($query);
        });

        return $datatable;
    }
}