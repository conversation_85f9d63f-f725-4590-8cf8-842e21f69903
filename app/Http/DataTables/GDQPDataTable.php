<?php

namespace App\Http\DataTables;

use Auth;
use DataTables;

use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

use App\Models\Fu\User;
use App\Models\Fu\Block;
use App\Models\Fu\Course;
use App\Models\Fu\Feedback;
use App\Models\Fu\FeedbackDetail;
use App\Models\Fu\Group;
use App\Models\Fu\Subject;
use App\Models\Fu\Term;
use App\Models\GradeGdqp;
use App\Models\Ho\Fu\Term as FuTerm;
use App\Models\T7\SyllabusPlan;
use Illuminate\Support\HtmlString;
use Yajra\DataTables\Facades\DataTables as FacadesDataTables;

class GDQPDataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'id',
        'student_login',
        'student_code',
        'study_status',
        'status',
        'ky',
        'term_name'
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
    ];


    /**
    * Build datatable filter query
    */
    public function filter($query)
    {
        $input = request()->all();

        if(!empty($input['term_id']) && $input['term_id'] != null) {
            $query->where('term_name', $input['term_id']);
        }

        if(!empty($input['study_status'])  && $input['study_status'] != null) {
            $query->where('study_status', $input['study_status']);
        }

        if(!empty($input['study_status'])  && $input['study_status'] != null) {
            $query->where('study_status', $input['study_status']);
        }

        // Check for filter:search
        if ($search = request()->input('search.value')) {   
            $query->where('student_code','Like', '%'.$search.'%');
        }

    }


    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {
        $query = GradeGdqp::query();

        $datatable = FacadesDataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns);
        // format Data
        $datatable->addColumn('_id', function(GradeGdqp $node) { return $node->id; });
        $datatable->addColumn('student_code', function(GradeGdqp $node) { return $node->student_code; });
        $datatable->addColumn('study_status', function(GradeGdqp $node) {
            $status = config('status')->trang_thai_hoc;
            return new HtmlString("<div style='color: ".$status[$node->study_status]['style']."'>".$status[$node->study_status]['value']."</div>");
        });
        $datatable->addColumn('status', function(GradeGdqp $node) {
            if($node->status == 1) {
                $status = "<span style='color:green'>Pass</span>";
            }else if($node->status == 2) {
                $status = "<span style='color:red'>Fail</span>";
            }
            
            return new HtmlString($status); 
        });
        $datatable->addColumn('ky', function(GradeGdqp $node) { return $node->ky; });
        $datatable->addColumn('term_name', function(GradeGdqp $node) { return $node->term_name; });

        $datatable->filter(function ($query) {
            return $this->filter($query);
        });
        return $datatable;
    }
}