// Class definition

var KTBootstrapDatetimepicker = function () {
    
    // Private functions
    var demos = function () {
        // minimal setup
        $('#kt_datetimepicker_1').datetimepicker({
            todayHighlight: true,
            autoclose: true,
            format: 'yyyy.mm.dd hh:ii'
        });

        $('#kt_datetimepicker_1_modal').datetimepicker({
            todayHighlight: true,
            autoclose: true,
            format: 'yyyy.mm.dd hh:ii'
        });

        // input group demo
        $('#kt_datetimepicker_2, #kt_datetimepicker_1_validate, #kt_datetimepicker_2_validate, #kt_datetimepicker_3_validate').datetimepicker({
            todayHighlight: true,
            autoclose: true,
            pickerPosition: 'bottom-left',
            format: 'yyyy/mm/dd hh:ii'
        });
        $('#kt_datetimepicker_2_modal').datetimepicker({
            todayHighlight: true,
            autoclose: true,
            pickerPosition: 'bottom-left',
            format: 'yyyy/mm/dd hh:ii'
        });

        // today button
        $('#kt_datetimepicker_3').datetimepicker({
            todayHighlight: true,
            autoclose: true,
            pickerPosition: 'bottom-left',
            todayBtn: true,
            format: 'yyyy/mm/dd hh:ii'
        });
        $('#kt_datetimepicker_3_modal').datetimepicker({
            todayHighlight: true,
            autoclose: true,
            pickerPosition: 'bottom-left',
            todayBtn: true,
            format: 'yyyy/mm/dd hh:ii'
        });

        // orientation
        $('#kt_datetimepicker_4_1').datetimepicker({
            todayHighlight: true,
            autoclose: true,
            pickerPosition: 'bottom-left',
            format: 'yyyy.mm.dd hh:ii'
        });

        $('#kt_datetimepicker_4_2').datetimepicker({
            todayHighlight: true,
            autoclose: true,
            pickerPosition: 'bottom-right',
            format: 'yyyy/mm/dd hh:ii'
        });

        $('#kt_datetimepicker_4_3').datetimepicker({
            todayHighlight: true,
            autoclose: true,
            pickerPosition: 'top-left',
            format: 'yyyy-mm-dd hh:ii'
        });

        $('#kt_datetimepicker_4_4').datetimepicker({
            todayHighlight: true,
            autoclose: true,
            pickerPosition: 'top-right',
            format: 'yyyy-mm-dd hh:ii'
        });

        $('#kt_datetimepicker_5').datetimepicker({
            format: "dd MM yyyy - HH:ii P",
            showMeridian: true,
            todayHighlight: true,
            autoclose: true,
            pickerPosition: 'bottom-left'
        });

        $('#kt_datetimepicker_6').datetimepicker({
            format: "yyyy/mm/dd",
            todayHighlight: true,
            autoclose: true,
            startView: 2,
            minView: 2,
            forceParse: 0,
            pickerPosition: 'bottom-left'
        });

        $('#kt_datetimepicker_7').datetimepicker({
            format: "hh:ii",
            showMeridian: true,
            todayHighlight: true,
            autoclose: true,
            startView: 1,
            minView: 0,
            maxView: 1,
            forceParse: 0,
            pickerPosition: 'bottom-left'
        });
    }

    return {
        // public functions
        init: function() {
            demos(); 
        }
    };
}();

jQuery(document).ready(function() {
    KTBootstrapDatetimepicker.init();
});