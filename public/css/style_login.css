@import url('https://fonts.googleapis.com/css2?family=Raleway:wght@100;500&display=swap');

:root {
	--primary-color: #2D62ED;
	--secondary-color: #5A82F0;
	--danger-color: #FF0000;
	--warning-color: #FFC107;
	--google-color: #eb442d;
}

* {
	box-sizing: border-box;
}

body {
	background: #EDEDEE;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	font-family: 'Raleway', sans-serif;
	height: 100vh;
	margin: -20px 0 50px;
}

h1 {
	font-weight: bold;
	margin: 0;
}

h2 {
	text-align: center;
}

p {
	font-size: 14px;
	font-weight: 100;
	line-height: 20px;
	margin: 20px 0 30px;
}

a {
	color: #333;
	font-size: 14px;
	text-decoration: none;
	margin: 15px 0;
}

button {
	border-radius: 20px;
	border: 1px solid var(--primary-color);
	background-color: var(--secondary-color);
	color: #FFFFFF;
	font-size: 12px;
	font-weight: bold;
	padding: 12px 45px;
	text-transform: uppercase;
	cursor: pointer;
}

button:hover {
	transform: scale(1.05);
}

button.signup_btn {
	position: absolute;
	background-color: transparent;
	border-color: #FFFFFF;
	bottom: 8%;
	left: 50%;
	transform: translateX(-50%);
	cursor: pointer;
}

.forgot-password a{
	cursor: pointer;
}

form {
	background-color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: start;
	flex-direction: column;
	padding: 50px;
	height: 100%;
	text-align: center;
}

form .form-input{
    margin-top: 40px;
    width: 100%;
}

input {
	background-color: #EDEDEE;
	border: none;
	padding: 12px 15px;
	margin: 8px 0;
	width: 100%;
}

.message-box {
	display: flex;
	font-style: italic;
}
.text-danger{
	color: var(--danger-color);
}

.container {
	background-color: #fff;
	border-radius: 10px;
  	box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
	position: relative;
	overflow: hidden;
	width: 800px;
	max-width: 100%;
	min-height: 500px;
}

.form {
	position: absolute;
	top: 0;
	height: 100%;
}

.sign-in-container {
	left: 0;
	width: 50%;
}
.campus_list{
	position: relative;
	width: 100%;
	margin-top: 20px;
	border: 1px solid var(--primary-color);
	border-radius: 10px;
	/* background-color: #EDEDEE; */
	height: 200px;
}
.campus_list:hover{
	box-shadow: 0px 0px 10px var(--primary-color);
}
.campus_list .title{
	position: absolute;
	top: 0px;
	width: 100%;
	background-color: var(--primary-color);
	border: 1px solid var(--primary-color);
	box-shadow: 0px 0px 5px var(--primary-color);
	border-radius: 10px;
	height: 30px;
	line-height: 30px;
	color: white
}
.campus_list .campus_list_container{
	/* background-color: #fff; */
	margin-top: 30px;
	width: 100%;
	height: 100%;
	padding: 10px;
}
.campus_list .campus_list_container a{
	text-align: left;
	display: block;
	margin: 0px;
	padding: 5px;
	color: #333;
}
.campus_list a:hover {
	background-color: #34495E;
	color: white;
	cursor: pointer;

}
.campus_list .selected{
	background-color: #34495E;
	color: var(--primary-color) !important;
}
.remember-forgot-container {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	margin: 10px 0;
}
.remember-forgot-container button{
	width: 100%;
	margin-top: 10px;
}
.remember-me {
	display: flex;
	align-items: center;
}

.remember-me input[type="checkbox"] {
	width: auto;
	margin-right: 5px;
}

.forgot-password a {
	font-size: 13px;
	color: var(--secondary-color);
	transition: color 0.3s ease;
}

.forgot-password a:hover {
	color: var(--secondary-color);
	text-decoration: underline;
}

.overlay-container {
	position: absolute;
	top: 0;
	left: 50%;
	width: 50%;
	height: 100%;
	overflow: hidden;
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(0%,0,0);
	transform: translate3d(0%,0,0);
}
.switch-container{
	left: 50%;
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(0%,0,0);
	transform: translate3d(-100%,0,0);
	z-index: 2;
}

.overlay {
	background: var(--primary-color) !important;
	background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
	background-repeat: no-repeat;
	background-size: cover;
	background-position: 0 0;
	color: #FFFFFF;
	position: relative;
	left: -100%;
	height: 100%;
	width: 200%;
}

.overlay-panel {
	position: absolute;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	padding: 0 40px;
	text-align: center;
	top: 0;
	height: 100%;
	width: 50%;
}

.overlay-right {
	right: 0;
}

.social-container button{
	width: 100%;
}

.social-container a {
	border: 1px solid var(--google-color);
	border-radius: 20px;
	display: inline-flex;
	justify-content: center;
	align-items: center;
	margin: 0 5px;
	height: 40px;
	width: 40px;
	position: relative;
	padding-left: 40px;
}
.social-container .google--icon{
	border: 1px solid var(--google-color) !important;
	height: 40px;
	width: 40px;
	border-radius: 20px;
	position: absolute;
	line-height: 40px;
	left: 0px;
	color: #fff;
	background-color: var(--google-color) !important;
}
.social-container a:hover{
	transform: scale(1.08);
}

.slad-space{
	position: relative;;
	display: flex;
	justify-content: center;
	align-items: center;
	height: 50px;
}
.slad{
	position: absolute;
	width: 35%;
	flex: 1 1 0%;
}
.middle-slad{
	margin: 0px 10px;
	font-size: 12px;
}
#hr-left{
	left: 0px;
}
#hr-right{
	right: 0px;
}
.font-weight-bold{
	font-weight: bold;
}
.font-size-15{
	font-size: 15px;
}
.font-size-40{
	font-size: 40px;
}
.margin-bottom-10{
	margin-bottom: 10px;
}
.margin-top-10{
	margin-top: 10px;
}
.hidden{
	display: none;
}
.form-control{
	display: flex;
	align-items: center;
}
