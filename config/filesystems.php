<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default filesystem disk that should be used
    | by the framework. The "local" disk, as well as a variety of cloud
    | based disks are available to your application. Just store away!
    |
    */

    'default' => env('FILESYSTEM_DRIVER', 'local'),

    /*
    |--------------------------------------------------------------------------
    | Default Cloud Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Many applications store files both locally and in the cloud. For this
    | reason, you may specify a default "cloud" driver here. This driver
    | will be bound as the Cloud disk implementation in the container.
    |
    */

    'cloud' => env('FILESYSTEM_CLOUD', 's3'),

    /*
    |--------------------------------------------------------------------------
    | Filesystem Disks
    |--------------------------------------------------------------------------
    |
    | Here you may configure as many filesystem "disks" as you wish, and you
    | may even configure multiple disks of the same driver. Defaults have
    | been setup for each driver as an example of the required options.
    |
    | Supported Drivers: "local", "ftp", "sftp", "s3", "rackspace"
    |
    */

    'disks' => [

        'local' => [
            'driver' => 'local',
            'root' => storage_path('app'),
        ],

        'public' => [
            'driver' => 'local',
            'root' => storage_path('app/public'),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
        ],

        'fuge' => [
            'driver' => 'local',
            'root' => storage_path('app/public/fuge'),
            'url' => env('APP_URL').'/storage/fuge',
            'visibility' => 'public',
        ],

        's3' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_BUCKET'),
            'url' => env('AWS_URL'),
        ],

        'gradebook' => [
            'driver' => 'local',
            'root' => storage_path('app/public/gradebook'),
            'url' => env('APP_URL').'/storage/gradebook',
            'visibility' => 'public',
        ],
        'thieu_no_mon' => [
            'driver' => 'local',
            'root' => storage_path('app/public/thieu_no_mon'),
            'url' => env('APP_URL').'/storage/thieu_no_mon',
            'visibility' => 'public',
        ],
        'dropdown_import_file' => [
            'driver' => 'local',
            'root' => storage_path('app/public/dropdown_import_file'),
            'url' => env('APP_URL').'/storage/dropdown_import_file',
            'visibility' => 'public',
        ],
        'custom-editor-upload' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_BUCKET'),
            'url' => env('AWS_URL'),
            'root' => 'custom-editor-upload',
            'visibility' => 'public',
        ],

        'bi-files' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_BI_BUCKET'),
            'url' => env('AWS_BI_URL'),
            'root' => env('AWS_BI_ROOT'),
            'endpoint' => env('AWS_ENDPOINT'),
        ],
        'bi-files-local' => [
            'driver' => 'local',
            'root' => storage_path('app/public/bi-files'),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
            'throw' => false,
        ],
    ],

];
